// post author block

// Comment form.
.wp-block-post-comments-form .comment-reply-title {
    font-size: 24px;
    font-weight: 500;
    display: block;
    margin-bottom: 16px;
    line-height: 29px;
    margin-top: 0;
}

.comment-form .comment-notes{
    font-size: 15px;
    font-weight: 400;
    line-height: 30px;
    letter-spacing: 0em;
    text-align: left;
}

.wp-block-post-comments-form input[type=submit]{
    border-radius:0;
    width: 100%;
    // background-color: var(--wp--preset--color--secondary) !important;
    font-weight: 400;
    padding: 10px 30px !important;
    font-size: 15px;
    line-height: 26px;
    // height:56px !important;
    color:var(--wp--preset--color--base) !important;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    &:hover,
    &:focus {
        background-color: var(--wp--preset--color--secondary) !important;
        border-color: var(--wp--preset--color--secondary) !important;
    }
}

.comment-form .comment-form-cookies-consent {
    gap: 10px !important;
    align-items: center;

    input[type=checkbox] {
        margin-top: 0px !important;
    }

    label {
        font-size: 15px;
    }
}

.comment-form .comment-form-url {
    margin-bottom: 25px!important;
}


.wp-block-comment-content {
    margin-top: 0px;
    margin-bottom: 0px;
}

.comment-form .comment-form-comment label,
.comment-form .comment-form-author label,
.comment-form .comment-form-email label,
.comment-form .comment-form-url label {
    
    margin-bottom: 10px !important;
    font-size: 16px;
    font-weight: 500;
    line-height: 21px;
    font-family: var(--wp--preset--font-family--heading);
}

blockquote {
    border-width: 0 0 0 5px;
    border-color: var(--wp--preset--color--primary);
    background-color: var(--wp--preset--color--bg-1);
    border-style: solid;
    position: relative; /* Ensure positioning context */
    
    p {
        font-style: normal;
        line-height: 1.7; 
        font-size: 21px !important; 
    }
    cite {
        color: var(--wp--preset--color--contrast)!important;
        font-size: 15px !important;
    }
    @media (min-width:1023px) {
        padding: 55px 75px 57px 75px !important;
    }  
    @media (max-width:1023px) {
        padding: calc(var(--wp--preset--spacing--30) + 0.75rem);
    } 
}

blockquote::after {
    position: absolute;
    right: 6.3%;
    bottom: 11.5%;
    transform: translateY(-50%);
    display: inline-block;
    content: " ";
    background-image: url('./assets/images/content-quote.svg');
    background-size: contain;

    @media (min-width:1023px) {
        width: 45px;
        height: 44px;
    }

    @media (max-width:1023px) {
        width: 45px;
        height: 44px;
    }
}


.gl-single-post {
    .single-social-icons {
        img {
            margin-right: 12px;
        }

        .wp-social-link {
            font-size: 17px;
        }
    }

    .post-meta > div{
        display: flex;
        align-items: center;
        &::before {
            content:'\00B7';
            padding-right: 8px;
            font-size: 24px;
            font-weight: 600;
            color: var(--wp--preset--color--secondary);
        }

        &:first-child::before{
            display: none;
        }
    }


    .wp-block-post-navigation-link {
        display: flex;

        .post-navigation-link__title {
            display: block;
            font-size: 15px;
            font-weight: normal;
            line-height: 28px;
            text-transform: none;
            padding-top: 5px;
            font-family: var(--wp--preset--font-family--body);
            color: var(--wp--preset--color--contrast);
        }


        .wp-block-post-navigation-link__arrow-previous, .wp-block-post-navigation-link__arrow-next {
            height: 100%;
            display: flex;
            justify-content: start;
            align-items: center;
            border-color: var(--wp--preset--color--primary) !important;
            border-radius: 0px;
            border: 1px solid;

            &:hover,
            &:focus {
                border-color: var(--wp--preset--color--primary);
                color: var(--wp--preset--color--primary);
                background-color: var(--wp--preset--color--bg-5);
            }

            &.is-arrow-arrow {
                font-size: 0px;
                padding: 17px 17px;
            }
        }
    }

    .post-navigation-link-previous {
        .wp-block-post-navigation-link__arrow-previous {
            margin-right: 20px;

            &.is-arrow-arrow:after {
                content:"\F12F";
                font-family: "bootstrap-icons";
                font-size: 18px;
                color: var(--wp--preset--color--primary);
            }
        }
    }

    .post-navigation-link-next {
        justify-content: end;
        .post-navigation-link__label, .post-navigation-link__title {
            display: block;
            text-align: end;
        }
        .wp-block-post-navigation-link__arrow-next {
            margin-left: 20px;

            &.is-arrow-arrow {
                transform: none;
            }

            &.is-arrow-arrow:after {
                content:"\F138";
                font-family: "bootstrap-icons";
                font-size: 18px;
                color: var(--wp--preset--color--primary);
            }
        }
    }

    .wp-block-latest-posts {
        column-gap: 20px;
        row-gap: 20px;
        li {
            margin: 0;
        }
        .wp-block-latest-posts__featured-image img{
            height: 330px;
            width: 440px;
            padding-bottom: 15px;
        }
        .wp-block-latest-posts__post-date {
            padding-top: 15px;
            font-size: 15px;
            font-weight: 400;
            color: var(--wp--preset--color--secondary) !important;

        }
    }

   .pagination-img {
        text-align: center;
    }

    .single-tags {
        display: flex;
        flex-wrap: wrap;
        span {
            display: none;
        }
        a {
            border: 1px solid var(--wp--preset--color--primary);
            color: var(--wp--preset--color--primary);
            margin-right: 10px;
            margin-bottom: 10px;

            &:hover {
                background-color: var(--wp--preset--color--bg-5);
            }
        }
    }

    .avatar-img img {
        @media (min-width:768px) {
            min-width: 112px;
        }
    }
}

