{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Item sold": ["<PERSON><PERSON><PERSON>", "Satılan ürünler"], "Advanced filters": ["<PERSON><PERSON>ş<PERSON>ş filtreler"], "Single category": ["Tek kategori"], "A sentence describing filters for Categories. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Categories match <select/> filters": ["Kategoriler <select/> filtre ile eşleşiyor"], "Check at least two categories below to compare": ["Karşılaştırmak için aşağıdan en az iki kategori işaretleyin"], "Check at least two products below to compare": ["Karşılaştırmak için aşağıdan en az iki ürün işaretleyin"], "categories\u0004+%d more": ["+%d daha"], "Indication of a low quantity\u0004Low": ["Düşük"], "%d products": ["%d <PERSON><PERSON><PERSON><PERSON>"], "%d categories": ["%d kate<PERSON>i"], "Search by category name": ["<PERSON><PERSON><PERSON> adına göre ara"], "Search for categories to compare": ["Karşılaştırmak için kategori ara"], "Type to search for a category": ["Bir kategoriyi aramak için yazın"], "Search by product name or SKU": ["Ürün adına veya SKU'ya göre ara"], "Comparison": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Compare Categories": ["Kategorileri ka<PERSON>şılaştır"], "Single Category": ["Tek kategori"], "Items sold": ["Satılan ürünler"], "Net sales": ["Net satışlar"], "Compare": ["Karş<PERSON>laştır"], "Product title": ["<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı"], "All categories": ["<PERSON><PERSON><PERSON>"], "Category": ["<PERSON><PERSON><PERSON>"], "Show": ["<PERSON><PERSON><PERSON>"], "Products": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "N/A": ["Yok"], "Variations": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Stock": ["Stok"], "Categories": ["<PERSON><PERSON><PERSON>"], "Status": ["Durum"], "Order": ["Sipariş"], "Orders": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "SKU": ["Stok kodu (SKU)"], "Product": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-categories.js"}}