<?php
/**
 * Title: Home v2 Hero
 * Slug: glowess/home-v2-hero
 * Categories: featured
 * Keywords: Home v2 Hero
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v2-hero"},"align":"full","className":"v2-hero","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull v2-hero"><!-- wp:columns {"style":{"spacing":{"padding":{"top":"0px","bottom":"0px","left":"0px","right":"0px"},"margin":{"top":"0px","bottom":"0px"}}},"backgroundColor":"bg-5"} -->
<div class="wp-block-columns has-bg-5-background-color has-background" style="margin-top:0px;margin-bottom:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><!-- wp:column {"verticalAlignment":"top","width":"38.1%","style":{"spacing":{"padding":{"right":"16px","left":"16px"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-right:16px;padding-left:16px;flex-basis:38.1%"><!-- wp:group {"style":{"spacing":{"padding":{"bottom":"5px","top":"var:preset|spacing|40"},"margin":{"top":"var:preset|spacing|50"}}},"layout":{"type":"constrained","contentSize":"401px"}} -->
<div class="wp-block-group" style="margin-top:var(--wp--preset--spacing--50);padding-top:var(--wp--preset--spacing--40);padding-bottom:5px"><!-- wp:spacer {"height":"var:preset|spacing|50","className":"d-none d-xl-block","style":{"spacing":{"margin":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}}} -->
<div style="margin-top:var(--wp--preset--spacing--20);margin-bottom:var(--wp--preset--spacing--20);height:var(--wp--preset--spacing--50)" aria-hidden="true" class="wp-block-spacer d-none d-xl-block"></div>
<!-- /wp:spacer -->

<!-- wp:heading {"textAlign":"center","level":6,"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.8"},"spacing":{"padding":{"top":"8px"}}},"textColor":"secondary"} -->
<h6 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="padding-top:8px;font-size:16px;font-style:normal;font-weight:400;line-height:1.8"><?php echo esc_html__( 'WHAT’S NEW', 'glowess' ); ?></h6>
<!-- /wp:heading -->

<!-- wp:heading {"textAlign":"center","level":1,"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"60px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"spacing":{"margin":{"top":"14px"}}},"textColor":"secondary"} -->
<h1 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:14px;font-size:60px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'New Skincare Collection', 'glowess' ); ?></h1>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"34px"},"padding":{"bottom":"11px"}}},"textColor":"secondary"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:34px;padding-bottom:11px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Discover our new collection and click all the items you like in your shopping basket.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"var:preset|spacing|30"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:var(--wp--preset--spacing--30)"><!-- wp:button {"backgroundColor":"secondary","textColor":"base","className":"inline-img","style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.8"},"spacing":{"padding":{"left":"53px","right":"53px","top":"12px","bottom":"12px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"borderColor":"secondary"} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.8"><a class="wp-block-button__link has-base-color has-secondary-background-color has-text-color has-background has-link-color has-border-color has-secondary-border-color wp-element-button" href="#" style="padding-top:12px;padding-right:53px;padding-bottom:12px;padding-left:53px"><?php echo esc_html__( 'SHOP NOW', 'glowess' ); ?><img class="wp-image-198" style="width: 15px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons -->

<!-- wp:spacer {"height":"var:preset|spacing|30","style":{"spacing":{"margin":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}}} -->
<div style="margin-top:var(--wp--preset--spacing--20);margin-bottom:var(--wp--preset--spacing--20);height:var(--wp--preset--spacing--30)" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"62.1%"} -->
<div class="wp-block-column" style="flex-basis:62.1%"><!-- wp:group {"className":"gl-slick-single","layout":{"type":"default"}} -->
<div class="wp-block-group gl-slick-single"><!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":405,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-1.png" alt="" class="wp-image-405" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":497,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-2.png" alt="" class="wp-image-497" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":503,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-3.png" alt="" class="wp-image-503" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":504,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-4.png" alt="" class="wp-image-504" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":500,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-5.png" alt="" class="wp-image-500" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:image {"id":501,"width":"auto","height":"928px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/v2-hero-slide-6.png" alt="" class="wp-image-501" style="width:auto;height:928px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
