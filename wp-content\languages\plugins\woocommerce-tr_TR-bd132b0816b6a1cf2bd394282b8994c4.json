{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Upgrade to the Add to Cart + Options block": ["Sepete Ekle + Seçenekler blokuna y<PERSON>"], "blockified experience": ["bloklanmış deneyim"], "Gain access to more customization options when you upgrade to the <strongText />.": ["Şuna yükselttiğinizde daha fazla özelleştirme seçeneğine erişebilirsiniz: <strongText />."], "Stepper": ["<PERSON><PERSON><PERSON><PERSON>"], "Input": ["<PERSON><PERSON><PERSON>"], "Quantity Selector": ["<PERSON><PERSON><PERSON>"], "Shoppers can use buttons to change the number of items to add to cart.": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, sepete ekleyecekleri ürün sayısını değiştirmek için düğmeleri kullanabilir."], "Shoppers can enter a number of items to add to cart.": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, sepete ekleyecekleri ürün sayı<PERSON>ını girebilir."], "Customer will see product add-to-cart options in this space, dependent on the product type.": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, ürün türüne bağlı olarak bu alanda sepete ürün ekleme seçeneklerini görür."], "Add to cart": ["Sepete Ekle"]}}, "comment": {"reference": "assets/client/blocks/add-to-cart-form.js"}}