{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Previous year:": ["Geçen yıl:"], "Single coupon": ["Tek kupon"], "Advanced filters": ["<PERSON><PERSON>ş<PERSON>ş filtreler"], "Previous period:": ["<PERSON>nce<PERSON> d<PERSON>"], "A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Coupons match <select/> filters": ["<PERSON><PERSON><PERSON><PERSON> <select/> filtre ile eşleşiyor"], "Percentage": ["<PERSON><PERSON>z<PERSON>"], "Amount": ["<PERSON><PERSON><PERSON>"], "%d coupons": ["%d kupon"], "Fixed product": ["Sabit <PERSON>"], "Fixed cart": ["Sabit sepet"], "Check at least two coupon codes below to compare": ["Karşılaştırmak için aşağıdan en az iki kupon kodunu işaretleyin"], "Type to search for a coupon": ["<PERSON><PERSON> kuponu aramak için yazın"], "Comparison": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "No data for the selected date range": ["Seçilen tarih aralığı için veri yok"], "No data for the current search": ["Mevcut arama için veri yok"], "Single Coupon": ["Tek kupon"], "Compare Coupon Codes": ["Ku<PERSON>n kodlarını karşılaştır"], "Created": ["<PERSON><PERSON>ş<PERSON><PERSON><PERSON>"], "Amount discounted": ["İndirim tutarı"], "Discounted orders": ["İndi<PERSON>li <PERSON>ş<PERSON>"], "Compare": ["Karş<PERSON>laştır"], "TAX": ["<PERSON><PERSON><PERSON>"], "Expires": ["Süresi doluyor"], "Any %s": ["Herhangi %s"], "Coupon": ["<PERSON><PERSON><PERSON>"], "All coupons": ["<PERSON><PERSON><PERSON>"], "Show": ["<PERSON><PERSON><PERSON>"], "N/A": ["Yok"], "Order": ["Sipariş"], "Coupon code": ["<PERSON><PERSON><PERSON>"], "Orders": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Type": ["<PERSON><PERSON><PERSON>"], "Coupons": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-coupons.js"}}