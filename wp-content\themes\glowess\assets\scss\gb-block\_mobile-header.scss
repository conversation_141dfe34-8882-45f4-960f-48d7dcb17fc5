/*
################
* === Mobile Header STYLE  ===
################
*/

.mobile-header {
    .wp-block-navigation__responsive-container-content {
        > ul > li {
            border-bottom: 1px solid var(--wp--preset--color--gray-100);
            padding-bottom: 6px;
        }
    }

    .wp-block-navigation:not(.has-text-color) .wp-block-navigation__responsive-container.is-menu-open {
        color: var(--wp--preset--color--contrast);
    }

    .wp-block-navigation__responsive-container-close {
        top: 10px;
        right: 14px;
        z-index: 3;
        left: auto;
        padding: 4px;
        border-radius: 550px;
        background-color: var(--wp--preset--color--danger);
        color: var(--wp--preset--color--base);
        opacity: 1;

        svg {
            width: 20px;
            height: 20px;
        }

        &:hover {
            background-color: var(--wp--preset--color--danger);
        }

        &:not(:hover) {
            opacity: 1;
        }
    }

    .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container:before {
        display: none;
    }

    .wp-block-navigation__submenu-container {
        transform: none !important;
    }

    .wp-block-navigation-submenu__toggle[aria-expanded=true] ~ .wp-block-navigation__submenu-container {
        display: flex;
    }

    .wp-block-navigation-submenu__toggle[aria-expanded=false] ~ .wp-block-navigation__submenu-container:not(.hide),
    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation-item__content,
    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation-submenu__toggle {
        display: none;
    }

    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation__submenu-container {
        padding-top: 0;
    }

    .wp-block-navigation__responsive-dialog {
        padding: 3.6rem 1rem 1rem 1rem;
        background-color: var(--wp--preset--color--base);
    }

    .wp-block-navigation {
        .has-child.has-mega-menu {
            > .wp-block-navigation__submenu-container {
                flex-direction: column;
                position: relative;
                top: 0;
    
                > .wp-block-navigation-item > .wp-block-navigation-item__content {
                    font-weight: 500;
                }
    
                .wp-block-navigation__submenu-container {
                    padding: 1rem;
                    margin-bottom: 10px;
                }
            }
        }
        
        .wp-block-navigation-item .wp-block-navigation__submenu-container {
            @media (max-width:1023px) {
                border-radius: 8px;
                box-shadow: 0 4px 4px rgba(0, 0, 0, 0.04);
            }
        }
    }
    
    .has-mega-menu {
        .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {
            padding: 1rem;
        }

        .wp-block-navigation__submenu-container .mega-menu-img.wp-block-navigation-item:last-child .wp-block-navigation-item__label {
            align-items: flex-start;
        }
    }

    .wp-block-navigation__responsive-container{
        .wp-block-navigation__container {
            width: 100%;
        }

        &.is-menu-open {
            .wp-block-navigation__responsive-container-content {
                padding-top: 0;
                background-color: var(--wp--preset--color--base);

                .has-child:not(.off-canvas-menu-head) .wp-block-navigation__submenu-container {
                    padding: 16px 12px;
                    width: 100%;
                    border: 1px solid var(--wp--preset--color--gray-100);
                    margin-top: 10px;
                    margin-bottom: 8px;
                    gap:6px;
                }

                .wp-block-navigation-item__content {
                    padding: 2px 10px;
                }

                .wp-block-navigation__submenu-container {
                    padding-top: 16px;
                    padding-bottom: 18px;
                }

                .wp-block-navigation__container {
                    gap: 6px;
                    font-size: 14px;

                    > .wp-block-navigation-item {
                        > .wp-block-navigation-item__content {
                            padding: 2px 0;
                            color: var(--wp--preset--color--contrast);   
                        }

                        s {
                            text-decoration: none;
                            background-color: var(--wp--preset--color--danger);
                            font-size: 12px;
                            font-weight: 400;
                            padding: 4px 13px;
                            border-radius: 14px;
                            color: var(--wp--preset--color--base);
                            line-height: 1;
                            letter-spacing: -0.4px;
                            margin-left: 4px;
                        }
                    }
                }

                .open-on-click .wp-block-navigation-submenu__toggle {
                    width: 100%;

                    &:hover {
                        background-color: transparent;
                    }
                }

                .wp-block-navigation__submenu-icon {
                    display: inline-block;
                    flex-grow: 1;
                    text-align: right;
                    position: absolute;
                    inset: 0;
                    width: 100%;
                    height: 18px;
                    margin-right: 0;
                    margin-top: 4px;
                    padding-right: 10px;
                    align-self: flex-start;

                    &:hover {
                        color: var(--wp--preset--color--contrast);
                        background-color: transparent;
                    }

                    &[aria-expanded=true] ~ .wp-block-navigation__submenu-icon {
                        svg {
                            color: var(--wp--preset--color--contrast);
                            width: 10px;
                            margin: 0 10px 0 0;
                        }
                    }

                    svg {
                        width: 10px;
                        margin: 0;
                    }
                }

                .wp-block-navigation-item {
                    flex-wrap: wrap;
                    flex-direction: row;
                    justify-content: space-between;
                    width: 100%;
                }
            }
        }
    }

    .wp-block-navigation:not(.has-background) {
        .wp-block-navigation__responsive-container.is-menu-open {
            background-color: #00000080;
            padding: 0;
        }
    }

    .has-modal-open {
        .wp-block-navigation__responsive-close {
            animation-name: leftslidein;
            animation-duration: .2s;
        }
    }

    .wp-block-navigation__responsive-close {
        background-color: var(--wp--preset--color--base);
        transition: transform .3s ease-in-out;
        margin-left: 0;
        height: 100vh;
        padding: 0;

        @media (min-width:600px) {
            width: 400px;
        }
    }
}

.has-modal-open .admin-bar .mobile-header .is-menu-open .wp-block-navigation__responsive-dialog {
    margin-top: 20px;
}

@keyframes leftslidein {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}
