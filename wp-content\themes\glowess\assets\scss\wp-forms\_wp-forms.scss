/*
################
* === WP - Forms style  ===
################
*/
@media (min-width: 768px){
    .glowess-contact-form .w-50 {
        width: 48.3%!important;
        padding-bottom:15px !important;
    }
}


.glowess-contact-form .wpforms-submit-container{
    margin-top:0px !important;
    padding-top:15px !important;

}

.glowess-contact-form .wpforms-submit-container button[type=submit] {
    width:100%;
    background-color: var(--wp--preset--color--primary) !important;
    font-weight: 400;
    padding: 20px 50px !important;
    font-size: 15px;
    text-align: center;
    box-shadow: none;
    border:transparent;
    border-radius: 0px;
    text-transform: uppercase;
    color:var(--wp--preset--color--base) !important;
    height:54px !important;
    cursor: pointer;
        &:focus:after {
            border:none !important;
        }
    
    &:hover {
                background-color: var(--wp--preset--color--contrast) !important;
                color: var(--wp--preset--color--base) !important;
            }
}

@media (min-width: 768px){
    .glowess-contact-form .wpforms-field:nth-child(2),   .glowess-contact-form .wpforms-field:nth-child(4) {
        margin-left: 3.4%;
    }
}

@media (min-width: 768px){
    .glowess-contact-form .wpforms-field-container {
        display: flex!important;
        flex-wrap: wrap!important;
    }
}

@media (min-width: 768px){
    .glowess-contact-form .w-100 {
        width: 100%!important;
        padding-bottom:15px !important;         
    }
}

.glowess-contact-form .wpforms-field-container textarea{
 min-height: 229px !important;
 }

/* Glowess Sign-Up Form */

.glowess-sign-up-form .wpforms-form {
    position: relative !important;
       }

.glowess-sign-up-form .wpforms-field-container .wpforms-field {
    padding: 0px !important;
}

.glowess-sign-up-form .wpforms-field-container input[type=text] {
    min-height: 59.05px !important;
    padding-left: 20px !important;
    border: 1px solid transparent !important;
    font-size: 15px;
    font-weight: 300;
    font-family: inherit;
    line-height: 35px;
    color: var(--wp--preset--color--contrast)!important;
    background-color: var(--wp--preset--color--white)!important;
       &:focus {
        border: 1px solid transparent;
        outline: 0;
        box-shadow: unset;
    }
}

.glowess-sign-up-form .wpforms-field-large {
    padding: 5px !important;
 }

@media (min-width:320px) {
       .glowess-sign-up-form .wpforms-submit-container {
                    padding: 5px !important;
                    margin: 0px !important;
       }        
}

.glowess-sign-up-form .wpforms-field-container input[type=text]::placeholder {
      color: var(--wp--preset--color--contrast)!important;
}

.glowess-sign-up-form.wpforms-container .wpforms-form .wpforms-submit{
       font-family: inherit;
}

.glowess-sign-up-form .wpforms-submit-container button[type=submit] {
       background-color: #FFFFFF !important;
       font-size: 0; /* Hide the text */
       color: var(--wp--preset--color--contrast) !important;
       border:transparent;
       cursor:pointer;
       margin-right: 3px !important;
         &:hover,
                &:focus {
                 background: var(--wp--preset--color--white)!important;
                 border: 1px solid transparent !important;
                }
}

@media (min-width:320px) {
       .glowess-sign-up-form .wpforms-submit-container{
              text-align: center;
              position: absolute !important;
              right: 0px !important;
              top: 5px !important;
       }
}

/* Add an arrow icon using Unicode or Font Awesome */
.glowess-sign-up-form .wpforms-submit::after {
    content: " \F138"; /* Unicode for right arrow */
    font-size: 16px; /* Adjust size as needed */
    font-family: "bootstrap-icons";
    display: inline-block;
     &:hover,
                &:focus {
                 background: var(--wp--preset--color--contrast)!important;
                 border: 1px solid transparent !important;
                }
}

/* Home V3 subscribe form */

.footer-v3 .wpforms-field-container input[type=text] {
    border-radius: 12px;
    border: 1px solid  var(--wp--preset--color--gray-100) !important;
}

/* Home V4 newsletter subscribe form */
.home-v4-newsletter .wpforms-field-container input[type=text] {
    border-radius: 16px !important;
    border: 1px solid  var(--wp--preset--color--gray-100) !important;
}