/*
################
* === GB STYLE === Write oly WP blocks style
################
*/

.wp-block-quote {
    cite {
        padding-right: 50px;
    }
}

.wp-block-pullquote.has-background {
    blockquote {
        background-color: transparent !important;
    }
}

.wp-block-post-comments-form textarea {
    padding: 14px 20px;
}

.avatar-img {
    @media (max-width:767px) {
        padding-left: var(--wp--preset--spacing--50) !important;

        .wp-block-avatar {
            width: 60px;
        }
    }
}
    
//wp-test markup-image-alignment 
img.alignright,
.mceTemp .wp-caption.alignright {
    float: right;
    margin-left: 32px;
}

img.alignright,
.mceTemp .wp-caption.alignright,
img.alignleft,
.mceTemp .wp-caption.alignleft,
.wp-block-image .alignleft,
.wp-block-image .alignright {
    margin-top: 10px;
    margin-bottom: 10px;
}

img.aligncenter {
    display: block;
    margin: auto;
}

img.alignleft,
.mceTemp .wp-caption.alignleft {
    float: left;
    margin-right: 32px;
}

.wp-block-image .alignleft {
    margin-right: 32px;
}

.wp-block-image .alignright {
    margin-left: 32px;
}

//wp-test post-format-gallery
.gallery-item {
    display: inline-block;
    vertical-align: top;
    width: 100%;
}

@media (min-width: 768px) {
    .gallery {
        display: grid;
        gap: 30px;
        grid-template-columns: repeat(3,minmax(0,1fr));
    }
}

.wp-block-social-links.has-small-icon-size {
    font-size: 12px;
}

.wp-site-blocks {
    > .wp-block-group {
        position: relative;
        z-index: 1;
    }
}

.wp-block-button.is-style-squared,
.wp-block-search__button  {
    &:hover,
    &:focus {
        color: var(--wp--preset--color--base) !important;
        transition: 0.3s ease-in-out;
    }
}

.wp-block-search__input {
    border-color:var(--wp--preset--color--gray-100) !important;

    .wp-block-button.is-style-squared,
    .wp-block-button__link {
        border-radius: 0;
    }
}

.wp-block-query-no-results {
    padding-top: 0;
    font-size: 15px;

    p {margin: 0;}
}

.wp-block-comment-template {
    .trackback,
    .pingback {
        .wp-block-columns > .wp-block-column:first-child {
            display: none;
        }
    }
}

.post-password-form input[type=submit] {
    background-color: var(--wp--preset--color--primary);
    border-width:0;
    color: var(--wp--preset--color--base);
    font-family: inherit;
    font-size: var(--wp--preset--font-size--medium);
    font-style: normal;
    font-weight: 400;
    padding-top: 12px;
    padding-right: 1rem;
    padding-bottom: 12px;
    padding-left: 1rem;
    text-decoration: none;
    text-transform: uppercase;
    cursor: pointer;
    min-height: 54px;
    
    &:hover {
        background-color: var(--wp--preset--color--secondary);
    }
}

.wp-block-file__button {
    text-align: center;
}

.wp-block-file__button,
.post-password-form input[type=submit] {
    min-width: 150px;
}

.post-password-form {
    p:last-child {
        display: flex;
        align-items: flex-end;
        gap: 20px;
    }
}
@media (max-width: 768px) {
    .min-height-img {
        min-height: 365px !important;
    }
}
@media (max-width: 1023px) {
    .height-img img {
        height: 100% !important;
    }
    .v1-box-1 .height-img img {
        width: 100% !important;
    }
}

.inline-img a img {
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle;
}

.home-v1-hero {
    .gradient .wp-block-cover__background {
        background: linear-gradient(180deg, rgba(19, 28, 25, 0.5) 0%, rgba(19, 28, 25, 0) 24.58%, rgba(19, 28, 25, 0) 100%);
        opacity: .5 !important;
    }
}

.home-v1-hero, .sp-home-v1-banner, .home-v4-hero {
    .wp-block-button a:hover {
        background-color: var(--wp--preset--color--secondary) !important;
        border-color: var(--wp--preset--color--secondary) !important;
        color: var(--wp--preset--color--base) !important;
        img{
            filter: brightness(0) invert(1) !important;
        }
    }
}

.glowess-cat {
    .wc-block-product-categories-list--depth-0 {
        display: flex;
        flex-wrap: wrap;
        padding: 0px;
        margin: 0px;
        gap: 25px;
        justify-content: space-between;

        .wc-block-product-categories-list-item {
            display: flex;
            flex-direction: column;
            margin: 0px;
            flex-wrap: wrap;
            width: 18.5%;
            text-align: center;
        }
        
        .wc-block-product-categories-list-item a {
            display: flex;
            flex-direction: column;

            .wc-block-product-categories-list-item__image {
                margin: 0px;
                border-width: 0px;
                max-width: 100%;

                img {
                    aspect-ratio: 1/1;
                    
                    @media (min-width:1200px) {
                        min-height: 300px;
                        object-fit: cover;
                    }
                }
            }

            .wc-block-product-categories-list-item__name {
                font-size: 20px;
                font-family: Sen;
                font-weight: 500;
                text-transform: capitalize;
                text-decoration: none;
                margin-top: 23px;
            }
        }

        .wc-block-product-categories-list-item-count:after, .wc-block-product-categories-list-item-count:before {
            content: "";
        }

        .wc-block-product-categories-list-item-count{
            font-size: 15px;
            margin-top: 3px;
            span[aria-hidden="true"]::after {
                content: " Items";
            }
        }
    }

    @media (min-width: 600px) {
        .slick-slide > div {
            padding: 20px 12.5px;
        }
    }

    @media (min-width: 600px) {
        .slick-list {
            margin: 0 -12.5px;
        }
    }

    .slick-dots {
        bottom: -60px;
    }
}


.gl-home-v4 {
    .about-yours {
        padding-bottom: var(--wp--preset--spacing--30);
    }

    .v1-best-selling-products {
        padding-bottom: var(--wp--preset--spacing--10) !important;
    }

    .home-v4-banner {
        .wp-block-button .wp-block-button__link {
            min-width: 100px;
        }
    }

    .home-v4-newsletter .v4-form {
        @media (max-width:768px) {
            max-width: 100%;
        }
    }

    .home-blog {
        margin-top: var(--wp--preset--spacing--50) !important;

        .wp-block-post-featured-image img{
            border-radius: 16px !important;
        }
    }

    .v2-marque-brands {
        padding-top: var(--wp--preset--spacing--30) !important;
        padding-bottom: var(--wp--preset--spacing--30) !important;
    }

    .contact-map {
        padding-top: var(--wp--preset--spacing--10) !important;
        padding-bottom: var(--wp--preset--spacing--10) !important;
        iframe, .map-inner-box {
            border-radius: 15px;
        }
    }
}

.home-v4-hero {
    padding-bottom: 14px;

    .slick-slide > div {
        padding-top: 0;
        padding-bottom: 0;
    }

    .wp-block-cover {
        @media (max-width:1830px) {
            border-radius: 0 !important;
        }
    }
}

.home-v4-card-1,
.home-v4-card-2 {
    .card-banner-image {
        height: 100%;
        img {
            height: 100%;
        }
    }
    .wp-block-button a{
        width: 254px;
    }
}

.v4-single-product {
    .product-detail {
        opacity:0.3;
    }

    .wc-block-grid__product-image {
        background-color: transparent;
    }

    .wp-block-post-title:hover{
        text-decoration: none;
    }

    .wp-block-columns {
        @media (min-width:1400px) {
            column-gap: 150px;
        }
    }

    .wc-block-grid__product-rating__stars {
        margin-bottom: 0 !important;
        color: var(--wp--preset--color--secondary) !important;
        font-size: 11px;

        &:before {
            color: var(--wp--preset--color--secondary) !important;
        }
    }

    .product-title a{
        @media (min-width:1400px) {
            border-bottom: 2px solid var(--wp--preset--color--secondary);
        }
    }

    .wc-block-components-product-image {
        position: relative;
        @media (min-width:1024px) {
            text-align: end;
        }

        img {
            @media (min-width:1400px) {
                width: 565px;
                height: 650px;
            }
        }
    }

    figure {
        top: 30%;
        left: 0%;

        @media (max-width:1400px) {
            display:none;
        }
    }
}

.v4-product-list {
    .wp-block-woocommerce-product-price {
        margin-top: 0;
    }

    .wc-block-components-product-image img{
        @media (min-width:1400px) {
            height: 120px;
        }
    }

    .product-list-col {
        @media (min-width:1400px) {
            column-gap: 123px;
        }
    }

    .product-list-img img{
        @media (min-width:1400px) {
            width: 686px;
            height: 805px;
        }
    }

    .wc-block-grid__product-rating__stars {
        margin-bottom: 0 !important;
        color: var(--wp--preset--color--secondary) !important;
        font-size: 11px;

        &:before {
            color: var(--wp--preset--color--secondary) !important;
        }
    }
}


.before-inline-img a img {
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
}


.v2-hero, .v3-collection, .v3-hero {
    .wp-block-button a:hover {
        background-color: var(--wp--preset--color--primary) !important;
        border-color: var(--wp--preset--color--primary) !important;
    }
} 

.v2-best-seller, .v2-latest-arrivals, .home-v2-blog{
    .before-inline-img .wp-block-button__link {
        min-width: 98px;
    }
}

@media only screen and (device-width: 1024px) {
    .v2-hero .height-img img {
        height: 600px !important; ;
    }
    .v2-best-skin-banner{
        .height-img img {
            height: 535px !important; ;
        }
    } 
}

@media (max-width: 1024px) {
    .height-auto img {
        height: 100% !important;
    }
}

@media (min-width: 1024px) {
    .button-width, .v2-single-product-2 .wp-block-button {
        a {
            width: 308px;
        }
    }
}

@media (max-width: 767px) {
    .flex-sm-wrap.wp-block-columns.is-not-stacked-on-mobile, .flex-sm-wrap.is-nowrap {
            flex-wrap: wrap !important;
    }
    .flex-sm-wrap .wp-block-column {
        flex-basis: 100% !important;
    }
}

@media (max-width: 768px) {
    .glowess-scroll {
        flex-wrap: nowrap !important;
        overflow-x: auto;
        display: flex;
        overflow-y: hidden;
    }


    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{
        flex-shrink: 0;
        flex-basis: 48% !important;
    }
}

@media (max-width: 425px) {

    .glowess-scroll {
        flex-wrap: nowrap !important;
        overflow-x: auto;
        display: flex;
        overflow-y: hidden;
    }
    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{
        flex-shrink: 0;
        flex-basis: 100% !important;
    }
}

@media (max-width: 1024px) {
    .glowess-scroll {
        flex-wrap: nowrap !important;
        overflow-x: auto;
        display: flex;
        overflow-y: hidden;
    }
    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{
        flex-shrink: 0;
        flex-basis: 25% !important;
        }
    }


.page-template-home-v2{
    .v2-marque-brands {
        margin-top: 0;
        padding-top: 33px;
        padding-bottom: 38px;
        border-bottom: 1px solid #D7DAD9
    }
    @media (min-width: 1440px) {
        .footer-v1 {
            margin-top: 87px !important;
        }
    }
}

.v1-best-selling-products, .v2-best-seller, .v2-latest-arrivals {
    .slick-nav-wrap {
        padding-top: 36px;
    }
}

.mb-0 {
    margin-bottom: 0;
}

.page-template-home-v1 {
    .about-instagram {
        padding-top: 36px !important;
        @media (max-width: 768px) {
            padding-bottom: 36px !important;
        }
    }
    .v1-before-after{
        @media (max-width: 768px) {
            padding-top: 0 !important;
      }
      @media (max-width: 1024px) {
        .pt-0, .pt-0 p{
            padding-top: 0 !important;
        }
      }
  }
  .v2-marque-brands {
    margin-top: 20px !important;
    margin-bottom: var(--wp--preset--spacing--50);
    @media (min-width: 1024px) {
        padding-bottom: 54px;
    }
  }
}
  
.wp-block-details summary::marker {
    content: "";
}

.wp-block-details {
    summary {
        &::after {
            content: "\F282";
            font-family: "bootstrap-icons";
            color: #202025;
            position: absolute;
            right: 0;
            font-weight: 700;
            font-size: medium;
       }
    }
    &[open] {
        > summary {
            &::after {
                content: "\F286";
                font-family: "bootstrap-icons";
                color: #202025;
                font-weight: 700;
                font-size: medium;
            }
        }
    }
}

@media (max-width: 1024px) {
   .gl-scroll {
       flex-wrap: nowrap !important;
       overflow-x: auto;
       display: flex;
       overflow-y: hidden;
   }
}
@media (min-width: 768px) and (max-width: 1024px) {
    .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {
        flex-shrink: 0;
        flex-basis: 30% !important;
    }
 }

@media (min-width: 768px) and (max-width: 1023px) {
   .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {
       flex-shrink: 0;
       flex-basis: 50% !important;
   }
}

@media (max-width: 767px) {
   .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {
       flex-shrink: 0;
       flex-basis: 100% !important;
   }
}


.map-inner-box{
    position: relative;
    left: 11.1%;
    top: 12.8%;
}

@media (max-width: 767px){
	.map-inner-box{
		position: relative;
    left: 10.1%;
    width: 220px;
	}
}
@media (min-width: 768px){
	.contact-map .wp-block-button__link{
		min-width:320px !important;
	}
}

.before-inline-img a img {
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
}

.v2-hero .wp-block-button a:hover {
    background-color: var(--wp--preset--color--primary) !important;
    border-color: var(--wp--preset--color--primary) !important;
}

.v2-best-seller, .v2-latest-arrivals, .home-v2-blog{
    .before-inline-img .wp-block-button__link {
        min-width: 98px;
    }
}

@media (max-width: 1023px) {
    .height-img img {
        height: 100% !important;
    }
}

@media (max-width: 1024px) {
    .cover-img {
        min-height: 430px !important;
    }
}

@media only screen and (device-width: 1024px) {
    .v2-hero .height-img img {
        height: 600px !important; ;
    }
    .v2-best-skin-banner{
        .height-img img {
            height: 535px !important; ;
        }
    } 
}
@media (max-width: 1024px) {
    .height-auto img {
        height: 100% !important;
    }
}
@media (min-width: 1024px) {
    .button-width a {
        width: 308px;
    }
}

@media (max-width: 767px) {
    .flex-sm-wrap.wp-block-columns.is-not-stacked-on-mobile, .flex-sm-wrap.is-nowrap {
            flex-wrap: wrap !important;
    }
    .flex-sm-wrap .wp-block-column {
        flex-basis: 100% !important;
    }
}

@media (max-width: 768px) {
    .glowess-scroll {
        flex-wrap: nowrap !important;
        overflow-x: auto;
        display: flex;
        overflow-y: hidden;
    }

    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{
        flex-shrink: 0;
        flex-basis: 48% !important;
    }
}

@media (max-width: 425px) {
    .glowess-scroll {
        flex-wrap: nowrap !important;
        overflow-x: auto;
        display: flex;
        overflow-y: hidden;
    }

    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{
        flex-shrink: 0;
        flex-basis: 100% !important;
    }
}

.page-template-home-v2{
    .v2-marque-brands {
        margin-top: 0;
        padding-top: 33px;
        padding-bottom: 38px;
        border-bottom: 1px solid #D7DAD9
    }
    .v2-best-seller, .v2-latest-arrivals{
        .slick-nav-wrap {
            padding-top: 36px;
        }
    }
}

.image-width img {
    width: 100% !important;
}

.v2-testimonial {
    @media (max-width: 425px) {
        .image-height img {
            height: 100% !important;
        }
    }
    @media (max-width: 768px) {
        .image-height {
            float: left !important;
            margin-left: 0 !important;
            img {
                width: 100% !important;
            }
        }
    }

    .gl-slick-single {
        .slick-list {
            margin: 0;
        }

        .slick-slide > div {
            padding: 0;
        }
    }

    .slick-prev:before, .slick-next:before {
        width: 35px;
        height: 34px;
    }

    .slick-prev {
        left: 0;

        &::before {
            background-image: url('data:image/svg+xml,<svg width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M35 17H2.02708" stroke="%23131C19"/><path d="M8.96808 24.8094C7.02916 20.5422 5.49308 18.7508 1.66599 17.0118C5.57849 15.086 7.09716 13.288 8.96808 9.19067" stroke="%23131C19"/></svg>');
        }
    }

    .slick-next {
        left: 65px;
        right: auto;
        
        &::before {
            background-image: url('data:image/svg+xml,<svg width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 17H32.9729" stroke="%23131C19"/><path d="M26.0319 24.8094C27.9708 20.5422 29.5069 18.7508 33.334 17.0118C29.4215 15.086 27.9028 13.288 26.0319 9.19067" stroke="%23131C19"/></svg>');
        }
    }

    .slick-prev, .slick-next {
        top: auto;
        transform: none;
        margin: 0;
        bottom: 60px;
    }
}

.v1-testimonial {
    .slider-nav {
        .slick-slide {
            img {
                width: 80px;
                height: 80px;
                border-radius: 30px;
                transition: all 0.4s ease-in-out;
                opacity: .6;
                cursor: pointer;
            }
    
            &.current-before,
            &.current-after {
                img {
                    width: 100px;
                    height: 100px;
                    border-radius: 35px;
                }
            }
    
            &.slick-current {
                img {
                    width: 120px;
                    height: 120px;
                    border-radius: 40px;
                    opacity: 1;
                }
            }
        }
    }

    .slider-nav {
        .slick-track {
            display: flex;
            align-items: center;
        }

        .slick-list {
            padding: 0 !important;
        }

        .slick-slide > div {
            padding: 0 14px;

            figure {
                display: flex !important;
                justify-content: center;
            }
        }
    }

    .slick-prev:before, .slick-next:before {
        width: 35px;
        height: 34px;
    }

    .slick-prev:before {
        background-image: url('data:image/svg+xml,<svg width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M35 17H2.02708" stroke="%23131C19"/><path d="M8.96808 24.8094C7.02916 20.5422 5.49308 18.7508 1.66599 17.0118C5.57849 15.086 7.09716 13.288 8.96808 9.19067" stroke="%23131C19"/></svg>');
    }

    .slick-next:before {
        background-image: url('data:image/svg+xml,<svg width="35" height="34" viewBox="0 0 35 34" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 17H32.9729" stroke="%23131C19"/><path d="M26.0319 24.8094C27.9708 20.5422 29.5069 18.7508 33.334 17.0118C29.4215 15.086 27.9028 13.288 26.0319 9.19067" stroke="%23131C19"/></svg>');
    }
 }

 .home-v3-love-skin {
	@media (min-width: 1540px){
        .row {
			column-gap: 147px;
		}
	}
	@media (min-width: 1440px) {
		
		.slick-nav-wrap{
			position: absolute;
			right: -6.2%;
			rotate: 90deg;
			top: 41%;
			margin-top: 0;
		}
	}
	@media only screen and (device-width: 1024px) {
		.slick-nav-wrap{
			position: absolute;
			right: -10.2%;
			rotate: 90deg;
			top: 41%;
			margin-top: 0;
		}
	}
	
	.wc-block-components-product-rating__stars {
        margin-bottom: 0 !important;
    }
	@media (max-width: 425px) {
		.pl-0{
				padding-left:0 !important;
		}
	}
}

@media (max-width: 1023px) {
    .height-img img {
        height: 100%!important;
    }
}

.glowess-list {
    padding: 0;
    display: flex;
    overflow-x: auto;
    gap: 30px;
    justify-content: center;
    list-style: none;
    li {
        text-align: center;
    }
	li.active a, li a:hover {
        color: var(--wp--preset--color--secondary) !important;
		text-decoration: underline !important;
    }
}

.cat-img-gradient .wp-block-cover__background {
	background: linear-gradient(180deg, rgba(88, 130, 116, 0) 0%, rgba(19, 28, 25, 0.4) 100%);
	background-color: transparent !important;
	opacity: .8 !important;
}

.v3-collection {
	@media (min-width: 1024px) {
		.wp-block-button a{
			min-width: 308px !important;
		}
	}
}

.v3-hero{
    .position-image-left {
        @media (min-width: 1440px) {
            position: absolute;
            bottom: 0;
            top: 71.5%;
            z-index: 9999;
            right: -28.5%;
            }
	}
    .position-image-right {
        @media (min-width: 1440px) {
            position: relative;
            bottom: 0px;
            left: -14.5%;
            top: -2%;
            right: 0%;
            .ml-22 {
                margin-left: 8px !important;
            }
        }
    }
}

.page-template-home-v3 {
    .v1-product-categories {
        padding-top: 44px !important;
    }
    .v1-testimonial {
        margin-bottom: 141px !important;
    }
    .about-instagram {
        padding-top: 27px !important;
        padding-bottom: 55px !important;
    }
    .home-blog .wp-block-post-featured-image img {
        border-radius: 12px !important;
    }
    .footer-v3 {
        .wpforms-container-full {
            margin-top: 0 !important;
            @media (min-width:1448px) {
                width: 400px;
            }
        }
    }
}
 

@media (max-width:767px) {
    .shop-best-skin .content-wrap {
		padding-top: 30px;
	}
}

.woo-sctr-shortcode-wrap-wrap {
	@media (max-width:1023px) {
		display: none !important;
	}
}
.woo-sctr-shortcode-countdown-2 {
	justify-content: start !important;
	align-items: start !important; 
}
.woo-sctr-shortcode-wrap-wrap {
	text-align: left !important;
}
.woo-sctr-shortcode-countdown-text-before {
	display: none;
}
.woo-sctr-shortcode-countdown-value {
	font-family: Sen;
	font-weight: 500;
	line-height: 48px;
}
.woo-sctr-shortcode-countdown-text {
	font-family: Sen;
	text-transform: uppercase;
}
.woo-sctr-shortcode-countdown-unit {
	padding: 0;
}
@media (min-width:1134px) {
	.woo-sctr-shortcode-countdown-unit-wrap {
	    margin: 0 12px !important;
	}
}
.woo-sctr-shortcode-countdown-unit-wrap:first-child {
	margin-left: 0 !important;
}
.woo-sctr-shortcode-wrap-wrap-1 {
    margin-top: 8px !important;
}
.v2-best-seller {
	.woo-sctr-shortcode-countdown-value, .woo-sctr-shortcode-countdown-text {
		color:  var(--wp--preset--color--ebony) !important;
	} 
}
