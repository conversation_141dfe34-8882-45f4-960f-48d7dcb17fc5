{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Now displaying a preview of the Hand-picked Products block.": ["Şimdi Özel Olarak Seçilmiş Ürünler blokunun bir önizlemesi görüntüleniyor."], "An error has prevented the block from being updated.": ["<PERSON><PERSON> hata, blokun gü<PERSON><PERSON><PERSON><PERSON> engelledi."], "Edit selected products": ["Seçili ürünleri düzenle"], "Loading…": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…"], "The following error was returned": ["Aşağı<PERSON><PERSON> hata geri dö<PERSON>ü<PERSON>üldü"], "Align Buttons": ["<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>a"], "The following error was returned from the API": ["API’den aşağıdaki hata döndürüldü"], "Search results updated.": ["<PERSON>ma sonuçları güncellendi."], "%d item selected": ["%d <PERSON><PERSON><PERSON> seçildi", "%d <PERSON><PERSON><PERSON> seçildi"], "Search for items": ["Ögeleri ara"], "No results for %s": ["%s için sonuç bulu<PERSON>ı"], "No items found.": ["<PERSON>rün bulunamadı."], "Clear all selected items": ["Seçili tüm ögeleri temizle"], "Clear all": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle"], "Remove %s": ["%s kaldır"], "Display a selection of hand-picked products in a grid.": ["Özenle seçilmiş bir dizi ürünü ızgara düzeninde görüntüleyin."], "%d product selected": ["%d <PERSON><PERSON><PERSON><PERSON>", "%d <PERSON><PERSON><PERSON><PERSON>"], "Search for products to display": ["Görüntülenecek ürünleri ara"], "Clear all products": ["<PERSON>ü<PERSON> ürünleri temizle"], "Product search results updated.": ["Ürün arama sonuçları güncellendi."], "Your store doesn't have any products.": ["Mağazanızda herhangi bir ürün yok."], "Order By": ["S<PERSON>rala"], "Done": ["<PERSON><PERSON>"], "Layout": ["<PERSON><PERSON><PERSON><PERSON>"], "Buttons follow content.": ["Düğmeler içeriği takip eder."], "Buttons are aligned vertically.": ["<PERSON><PERSON><PERSON><PERSON><PERSON> dikey olarak hizalanmıştır."], "Columns": ["<PERSON><PERSON><PERSON><PERSON>"], "Add to Cart button": ["Sepete Ekle butonu"], "Hand-picked Products": ["<PERSON>zenle seçilmiş <PERSON>ü<PERSON>"], "Menu Order": ["<PERSON><PERSON> sırası"], "Title - alphabetical": ["Başlık - alfabetik sıraya göre"], "Sales - most first": ["Satış - en çok satanlar başta olacak şekilde"], "Rating - highest first": ["Puan - en yüksek puana sahip olanlar başta olacak şekilde"], "Price - high to low": ["Fiyat - yüksekten düşüğe"], "Price - low to high": ["Fiyat - düşükten yükseğe"], "Order products by": ["Ürünleri şuna göre sırala:"], "Product rating": ["<PERSON>rün puanı"], "Product price": ["<PERSON><PERSON><PERSON><PERSON>tı"], "Product title": ["<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı"], "Newness - newest first": ["<PERSON>ni olma du<PERSON>u - en yeni<PERSON> ba<PERSON><PERSON> olacak şekilde"], "Product image": ["<PERSON><PERSON><PERSON><PERSON>"], "Content": ["İçerik"], "Products": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/handpicked-products.js"}}