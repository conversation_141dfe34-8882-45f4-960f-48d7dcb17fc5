{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Upgrade all Filter blocks": ["Tüm Filtre bloklarını yükseltin"], "Upgrade all Filter blocks on this page for better performance and more customizability": ["Daha iyi performans ve daha fazla özelleştirilebilirlik için bu sayfadaki tüm Filtre bloklarını yükseltin"], "Apply stock filter": ["Stok filtresi uygula"], "Number of filters\u0004Single": ["Tek"], "Number of filters\u0004Multiple": ["<PERSON><PERSON>"], "Remove stock filter.": ["Stok filtresini kaldırın."], "Invalid stock filter.": ["Geçersiz stok filtresi."], "Stock filter added.": ["Stok filtresi eklendi."], "Stock filter removed.": ["Stok filtresi kaldırıldı."], "Select stock status": ["Stok durumunu seçin"], "Product Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "Display product count": ["<PERSON><PERSON><PERSON><PERSON>ö<PERSON>ü<PERSON>ü<PERSON>"], "Reset stock filter": ["Stok filtresini sıfırla"], "Show 'Apply filters' button": ["\"Filtreleri uygula\" d<PERSON>ğ<PERSON><PERSON> gö<PERSON>"], "Reset filter": ["Filtreleri sıfırla"], "Allow selecting multiple options?": ["Birden çok seçime izin verilsin mi?"], "Block title": ["Blok başlığı"], "%s filter removed.": ["%s filtre kaldırıldı."], "%s filter added.": ["%s filtre eklendi."], "Apply filter": ["Filtre uygula"], "Products will update when the button is clicked.": ["Ürünler düğmeye tıklandığında güncellenecektir."], "Display Style": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stili"], "Filter by stock status": ["Stok durumuna göre filtrele"], "Display Settings": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "%s product": ["%s ür<PERSON>n", "%s ür<PERSON>n"], "List": ["Liste"], "Dropdown": ["Açılır <PERSON>ü"], "Reset": ["Sıfırla"], "Apply": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/stock-filter.js"}}