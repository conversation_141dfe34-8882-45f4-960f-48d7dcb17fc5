{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Upgrade to Product Collection": ["Ürün Koleksiyonuna Yükseltin"], "Upgrade all Products (Beta) blocks on this page to <strongText /> for more features!": ["Daha fazla özellikten faydalanmak için bu sayfadaki tüm <PERSON>ü<PERSON>ler (Beta) bloklarını şuna yükseltin: <strongText />!"], "Product Collection": ["<PERSON><PERSON><PERSON><PERSON>"], "Pick some products": ["Bazı ürünleri seçin"], "Manage attributes": ["Nitelik<PERSON>i <PERSON>"], "Related Products Controls": ["Benzer Ürünler Kontrolleri"], "Display related products.": ["Benzer ürünleri görüntüleyin."], "Contains the block elements used to render a product, like its name, featured image, rating, and more.": ["Bir ürünü açıklamak için kullanılan ad, öne çıkan <PERSON>, puanlama ve daha fazlası gibi blok öğelerini içerir."], "Product template": ["<PERSON><PERSON><PERSON><PERSON>"], "A block that displays a selection of products in your store.": ["Mağazanızdaki çeşitli ürünleri gösteren bir blok."], "Toggle to use the global query context that is set with the current template, such as variations of the product catalog or search. Disable to customize the filtering independently.": ["Ürün kataloğu veya arama varyasyonları gibi geçerli şablonla ayarlanan genel sorgu bağlamını kullanmak için düğmeye basın. Filtrelemeyi bağımsız olarak özelleştirmek için devre dışı bırakın."], "Inherit query from template": ["<PERSON><PERSON><PERSON><PERSON> te<PERSON>n"], "Top Rated": ["En Çok Puan Alan"], "Best Selling": ["En İyi Satış"], "Newest": ["<PERSON>"], "Sorted by title": ["Başlığa göre sıralı"], "Products (Beta)": ["<PERSON><PERSON><PERSON><PERSON><PERSON> (Beta)"], "Arrange products by popular pre-sets.": ["Ürünleri popüler ön ayarlara göre dü<PERSON>."], "Choose among these pre-sets": ["Bu ön ayarlar arasından seçim yapın"], "Popular Filters": ["<PERSON><PERSON><PERSON> filt<PERSON>"], "Show only products on sale": ["Sadece indirimdeki ürünleri göster"], "Sale status": ["<PERSON><PERSON><PERSON> durumu"], "An error has prevented the block from being updated.": ["<PERSON><PERSON> hata, blokun gü<PERSON><PERSON><PERSON><PERSON> engelledi."], "%d term": ["%d terim", "%d terim"], "%1$s, has %2$d term": ["%1$s, %2$d terime sahip", "%1$s, %2$d terime sahip"], "%1$s, has %2$d product": ["%1$s, %2$d <PERSON><PERSON><PERSON><PERSON> sahip", "%1$s, %2$d <PERSON><PERSON><PERSON><PERSON> sahip"], "Loading…": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…"], "Advanced Filters": ["<PERSON><PERSON>ş<PERSON>ş filtreler"], "Product Summary": ["<PERSON><PERSON><PERSON><PERSON>"], "Display a short description about a product.": ["Bir ürün hakkında kısa bir açıklama görüntüleyin."], "The following error was returned": ["Aşağı<PERSON><PERSON> hata geri dö<PERSON>ü<PERSON>üldü"], "The following error was returned from the API": ["API’den aşağıdaki hata döndürüldü"], "Search results updated.": ["<PERSON>ma sonuçları güncellendi."], "%d item selected": ["%d <PERSON><PERSON><PERSON> seçildi", "%d <PERSON><PERSON><PERSON> seçildi"], "Search for items": ["Ögeleri ara"], "No results for %s": ["%s için sonuç bulu<PERSON>ı"], "No items found.": ["<PERSON>rün bulunamadı."], "Clear all selected items": ["Seçili tüm ögeleri temizle"], "Clear all": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle"], "Remove %s": ["%s kaldır"], "All selected attributes": ["<PERSON><PERSON><PERSON> se<PERSON>"], "Any selected attributes": ["<PERSON><PERSON><PERSON> se<PERSON>"], "Pick at least two attributes to use this setting.": ["Bu ayarı kullanmak için en az iki nitelik seçin."], "Product attribute search results updated.": ["<PERSON><PERSON>ün niteliği arama sonuçları güncellendi."], "%d attribute selected": ["%d nitelik seçildi", "%d nitelik seçildi"], "Search for product attributes": ["<PERSON><PERSON><PERSON><PERSON> nitelik<PERSON> ara"], "Your store doesn't have any product attributes.": ["Mağazanızda herhangi bir ürün niteliği yok."], "Clear all product attributes": ["<PERSON>ü<PERSON> ürün niteliklerini temizle"], "Hand-picked Products": ["<PERSON>zenle seçilmiş <PERSON>ü<PERSON>"], "Display products matching": ["Eşleşen ürünleri görüntüle"], "Product Attributes": ["<PERSON><PERSON><PERSON><PERSON>"], "Related products": ["İlgili <PERSON>"], "%d product": ["%d <PERSON><PERSON><PERSON><PERSON>", "%d <PERSON><PERSON><PERSON><PERSON>"], "Stock status": ["Stok durumu"], "Attributes": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/product-query.js"}}