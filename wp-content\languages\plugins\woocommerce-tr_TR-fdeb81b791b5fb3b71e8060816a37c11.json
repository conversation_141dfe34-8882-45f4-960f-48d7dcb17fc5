{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "%1$s was successfully activated.": ["%1$s başarıyla etkinleştirildi."], "A plugin was successfully installed and activated.": ["Eklenti başarıyla yüklendi ve etkinleştirildi."], "%1$s (%2$s) was successfully installed and activated.": ["%1$s (%2$s) başarıyla yüklendi ve etkinleştirildi."], "A plugin was successfully activated.": ["Eklenti başarıyla etkinleştirildi."], "Google for WooCommerce": ["WooCommerce İçin Google"], "WooPayments": ["WooPayments"], "Omnichannel for WooCommerce": ["WooCommerce için <PERSON>"], "TikTok for WooCommerce": ["WooCommerce için <PERSON>"], "Pinterest for WooCommerce": ["WooCommerce iç<PERSON>"], "Mercado Pago payments for WooCommerce": ["WooCommerce için <PERSON>"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["Eklenti %(actionType)s %(pluginName)s yap<PERSON>lamadı, %(error)s", "Şu eklentiler %(actionType)s yapılamadı: %(pluginName)s, şu hataları verdi: %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["WooCommerce için Creative Mail"], "WooCommerce Shipping & Tax": ["WooCommerce Gönderim ve Vergi"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce Payfast": ["WooCommerce Payfast"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation ağ geçidi"], "There was a problem updating your settings.": ["Ayarlarınız güncellenirken bir sorun yaşandı."], "Plugins were successfully installed and activated.": ["Eklentiler başarıyla yüklendi ve etkinleştirildi"], "MM/DD/YYYY": ["MM/DD/YYYY"], "Facebook for WooCommerce": ["WooCommerce için Facebook"], "Mailchimp for WooCommerce": ["WooCommerce için <PERSON>"], "Klarna Checkout for WooCommerce": ["WooCommerce için <PERSON>"], "Klarna Payments for WooCommerce": ["WooCommerce için <PERSON>"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}