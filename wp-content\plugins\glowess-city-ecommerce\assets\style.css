/**
 * Glowess City Based E-Commerce - Frontend Styles
 * Compatible with Glowess Theme & WooCommerce
 */

/* ===== CITY SELECTOR MODAL ===== */
.city-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.city-modal {
    background: white;
    border-radius: 0; /* Glowess theme - sharp corners */
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 50px rgba(0, 0, 0, 0.3);
}

.city-modal-header {
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.city-modal-header h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
    color: #333;
}

.city-modal-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

.city-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 30px;
}

.city-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #eee;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    border-radius: 0; /* Sharp corners for Glowess */
}

.city-option:hover {
    border-color: #333;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.city-option img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-bottom: 15px;
    border-radius: 50%;
}

.city-option span {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

/* ===== HEADER CITY SELECTOR ===== */
.glowess-city-selector {
    position: relative;
    display: inline-block;
}

.current-city-display {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 15px;
    border: 1px solid #ddd;
    background: white;
    transition: all 0.3s ease;
    border-radius: 0; /* Sharp corners */
}

.current-city-display:hover {
    border-color: #333;
}

.city-icon {
    margin-right: 8px;
    font-size: 16px;
}

.city-name {
    margin-right: 8px;
    font-weight: 500;
    color: #333;
}

.dropdown-arrow {
    font-size: 12px;
    color: #666;
    transition: transform 0.3s ease;
}

.glowess-city-selector.open .dropdown-arrow {
    transform: rotate(180deg);
}

.city-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border-radius: 0; /* Sharp corners */
}

.city-dropdown-item {
    padding: 12px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
    color: #333;
    border-bottom: 1px solid #f5f5f5;
}

.city-dropdown-item:hover {
    background: #f8f8f8;
}

.city-dropdown-item:last-child {
    border-bottom: none;
}

/* ===== CITY PRODUCTS GRID ===== */
.glowess-city-products {
    display: grid;
    gap: 30px;
    margin: 30px 0;
}

.glowess-city-products.columns-1 {
    grid-template-columns: 1fr;
}

.glowess-city-products.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.glowess-city-products.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.glowess-city-products.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* ===== PRODUCT CARDS ===== */
.glowess-city-product-card {
    background: white;
    border: 1px solid #eee;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-product-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.glowess-city-product-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.glowess-city-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.glowess-city-product-card:hover .glowess-city-product-image img {
    transform: scale(1.05);
}

.glowess-city-discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-product-info {
    padding: 20px;
}

.glowess-city-product-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.4;
}

.glowess-city-product-title a {
    color: inherit;
    text-decoration: none;
}

.glowess-city-product-title a:hover {
    color: #333;
}

.glowess-city-product-rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
}

.glowess-city-product-rating .stars {
    color: #ffc107;
    margin-right: 8px;
}

.glowess-city-product-rating .count {
    color: #666;
}

.glowess-city-product-price {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 8px;
}

.glowess-city-old-price {
    color: #999;
    text-decoration: line-through;
    font-size: 14px;
}

.glowess-city-new-price {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.glowess-city-add-to-cart {
    width: 100%;
    background: #333;
    color: white;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0; /* Sharp corners */
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.glowess-city-add-to-cart:hover {
    background: #000;
    transform: translateY(-2px);
}

.glowess-city-add-to-cart:active {
    transform: translateY(0);
}

/* ===== CATEGORY SLIDER ===== */
.glowess-city-categories {
    margin: 40px 0;
    overflow: hidden;
}

.glowess-city-category-slider {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    gap: 20px;
    padding: 20px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.glowess-city-category-slider::-webkit-scrollbar {
    display: none;
}

.glowess-city-category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #eee;
    background: white;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-category-item:hover {
    border-color: #333;
    transform: translateY(-5px);
}

.glowess-city-category-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 24px;
}

.glowess-city-category-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

/* ===== HERO SECTION ===== */
.glowess-city-hero {
    position: relative;
    height: 60vh;
    min-height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.glowess-city-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.glowess-city-hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    max-width: 600px;
    padding: 0 20px;
}

.glowess-city-hero h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.glowess-city-hero p {
    font-size: 18px;
    opacity: 0.9;
    margin: 0;
}

/* ===== DELIVERY INFO ===== */
.glowess-city-delivery-info {
    background: #f8f8f8;
    padding: 30px;
    margin: 40px 0;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-delivery-toggle {
    background: #333;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    text-align: center;
    transition: all 0.3s ease;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-delivery-toggle:hover {
    background: #000;
}

.glowess-city-delivery-content {
    padding: 20px 0 0 0;
    display: none;
}

.glowess-city-delivery-content.active {
    display: block;
}

.glowess-city-delivery-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.glowess-city-delivery-area {
    background: white;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 0; /* Sharp corners */
}

.glowess-city-delivery-area strong {
    color: #333;
    display: block;
    margin-bottom: 5px;
}

.glowess-city-delivery-area span {
    color: #666;
    font-size: 14px;
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 768px) {
    .city-modal {
        width: 95%;
        max-height: 90vh;
    }
    
    .city-modal-header {
        padding: 20px;
    }
    
    .city-modal-header h2 {
        font-size: 24px;
    }
    
    .city-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        padding: 20px;
    }
    
    .city-option {
        padding: 15px;
    }
    
    .city-option img {
        width: 60px;
        height: 60px;
    }
    
    .city-option span {
        font-size: 16px;
    }
    
    .glowess-city-products.columns-2,
    .glowess-city-products.columns-3,
    .glowess-city-products.columns-4 {
        grid-template-columns: 1fr;
    }
    
    .glowess-city-hero {
        height: 50vh;
        min-height: 300px;
    }
    
    .glowess-city-hero h1 {
        font-size: 32px;
    }
    
    .glowess-city-hero p {
        font-size: 16px;
    }
    
    .glowess-city-category-slider {
        gap: 15px;
        padding: 15px 0;
    }
    
    .glowess-city-category-item {
        min-width: 100px;
        padding: 15px;
    }
    
    .glowess-city-category-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .glowess-city-delivery-areas {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .city-modal-header h2 {
        font-size: 20px;
    }
    
    .city-modal-header p {
        font-size: 14px;
    }
    
    .city-grid {
        grid-template-columns: 1fr;
    }
    
    .glowess-city-hero h1 {
        font-size: 28px;
    }
    
    .current-city-display {
        padding: 6px 12px;
    }
    
    .city-name {
        font-size: 14px;
    }
}

/* ===== LOADING STATES ===== */
.glowess-city-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.glowess-city-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #333;
    border-radius: 50%;
    animation: glowess-spin 1s linear infinite;
}

@keyframes glowess-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ANIMATIONS ===== */
.glowess-fade-in {
    animation: glowess-fade-in 0.5s ease-in-out;
}

@keyframes glowess-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.glowess-slide-in {
    animation: glowess-slide-in 0.3s ease-out;
}

@keyframes glowess-slide-in {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* ===== CART INTEGRATION ===== */
.glowess-city-cart-threshold {
    background: #e8f5e8;
    border: 1px solid #4caf50;
    color: #2e7d32;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0; /* Sharp corners */
    text-align: center;
    font-weight: 500;
}

.glowess-city-cart-warning {
    background: #fff3cd;
    border: 1px solid #ffc107;
    color: #856404;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0; /* Sharp corners */
    text-align: center;
}

/* ===== GLOWESS THEME INTEGRATION ===== */
body.glowess-theme .glowess-city-product-card,
body.glowess-theme .city-option,
body.glowess-theme .current-city-display,
body.glowess-theme .glowess-city-add-to-cart,
body.glowess-theme .glowess-city-delivery-toggle {
    border-radius: 0 !important; /* Force sharp corners for Glowess */
}

/* Override any rounded corners from theme */
.glowess-city-selector *,
.city-modal *,
.glowess-city-products * {
    border-radius: 0 !important;
}

/* ===== SUCCESS MESSAGES ===== */
.glowess-city-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px 15px;
    margin: 10px 0;
    border-radius: 0;
}

.glowess-city-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px 15px;
    margin: 10px 0;
    border-radius: 0;
}