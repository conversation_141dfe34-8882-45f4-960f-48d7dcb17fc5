{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["<a>Belgelerimizde</a> bloklara geçmenin <PERSON>, eklentilerle uyumluluk ve kısa kodlara nasıl geri dönüleceği hakkında daha fazla bilgi edinebilirsiniz."], "Classic Shortcode Placeholder": ["Klasik Kısa Kod Yer <PERSON>u"], "Classic shortcode transformed to blocks.": ["Klasik kısa kod, bloklara dönüştürüldü."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["<PERSON><PERSON> blo<PERSON>, klasik sepet kısa kodunu işler. İsteğe bağlı olarak sepet deneyimi üzerinde daha fazla kontrol sahibi olmak için bloklara dönüştürebilirsiniz."], "Classic Cart": ["Klasik Sepet"], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["<PERSON><PERSON> blo<PERSON>, klasik ödeme kısa kodunu işler. İsteğe bağlı olarak ödeme deneyimi üzerinde daha fazla kontrol sahibi olmak için bloklara dönüştürebilirsiniz."], "Classic Checkout": ["Klasik Ödeme"], "Renders the classic checkout shortcode.": ["Klasik ödeme kısa kodunu i<PERSON>."], "Checkout Cart": ["Ödeme Sepeti"], "Renders the classic cart shortcode.": ["Klasik sepet kısa kodunu işler."], "Cart Shortcode": ["Sepet Kısa Kodu"], "Transform into blocks": ["Bloklara dönüştür"], "Undo": ["<PERSON><PERSON> al"], "Learn more": ["<PERSON>ha fazla bilgi edin"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}