jQuery(document).ready(function($) {
    
    // Şehir değiştiğinde çerezi güncelle
    function updateCityCookie(citySlug) {
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'update_city_cookie',
                city_slug: citySlug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('Şehir çerezi güncellendi: ' + citySlug);
                }
            }
        });
    }

    // URL değişikliğini izle (pushState/popstate ile)
    var currentUrl = window.location.href;
    
    // Her 1 saniyede bir URL kontrolü (basit çözüm)
    setInterval(function() {
        if (window.location.href !== currentUrl) {
            currentUrl = window.location.href;
            checkUrlForCity();
        }
    }, 1000);

    // URL'den şehir slug'ını kontrol et
    function checkUrlForCity() {
        var urlParams = new URLSearchParams(window.location.search);
        var cityFromParam = urlParams.get('city');
        
        if (cityFromParam) {
            if (cityFromParam !== glowess_city_ajax.current_city) {
                updateCityCookie(cityFromParam);
                glowess_city_ajax.current_city = cityFromParam;
            }
            return;
        }

        // URL path'inden şehir slug'ını al
        var path = window.location.pathname;
        var pathParts = path.split('/').filter(function(part) {
            return part.length > 0;
        });

        if (pathParts.length > 0) {
            var firstPart = pathParts[0];
            if (firstPart !== glowess_city_ajax.current_city) {
                // Bu bir şehir slug'ı mı AJAX ile kontrol et
                checkIfValidCitySlug(firstPart);
            }
        }
    }

    // Şehir slug'ının geçerli olup olmadığını kontrol et
    function checkIfValidCitySlug(slug) {
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'check_city_slug',
                city_slug: slug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateCityCookie(slug);
                    glowess_city_ajax.current_city = slug;
                }
            }
        });
    }

    // Sayfa yüklendiğinde mevcut şehri kontrol et
    checkUrlForCity();

    // Manuel şehir değiştirme butonları için (eğer varsa)
    $(document).on('click', '.city-selector', function(e) {
        e.preventDefault();
        var citySlug = $(this).data('city-slug');
        
        if (citySlug) {
            updateCityCookie(citySlug);
            
            // URL'yi güncelle (sayfa yenilenmeden)
            var newUrl = '/' + citySlug + '/';
            history.pushState({city: citySlug}, '', newUrl);
            
            // Sayfayı yenile
            location.reload();
        }
    });
});