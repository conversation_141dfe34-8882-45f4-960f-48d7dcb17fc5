<?php
/**
 * Title: Home v3 Collection
 * Slug: glowess/home-v3-collection
 * Categories: featured
 * Keywords: Home v3 Deal Collection
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v3-collection"},"align":"full","className":"v3-collection","style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}}},"backgroundColor":"bg-5","layout":{"type":"constrained","contentSize":"1400px"}} -->
<div class="wp-block-group alignfull v3-collection has-bg-5-background-color has-background" style="padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50)"><!-- wp:group {"style":{"spacing":{"padding":{"right":"0px","left":"0px","top":"5px","bottom":"17px"},"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="margin-top:0px;margin-bottom:0px;padding-top:5px;padding-right:0px;padding-bottom:17px;padding-left:0px"><!-- wp:columns {"style":{"spacing":{"blockGap":{"top":"28px","left":"28px"},"padding":{"top":"var:preset|spacing|30","bottom":"var:preset|spacing|30"}}}} -->
<div class="wp-block-columns" style="padding-top:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--30)"><!-- wp:column -->
<div class="wp-block-column"><!-- wp:group {"style":{"spacing":{"padding":{"top":"11px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:11px"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v3-cat-image-1.png","id":1129,"dimRatio":10,"overlayColor":"secondary","isUserOverlayColor":true,"focalPoint":{"x":0.16,"y":0.67},"minHeight":550,"contentPosition":"bottom center","className":"cat-img-gradient cover-img relative","style":{"border":{"radius":"16px"},"spacing":{"blockGap":"0px","padding":{"top":"25px","bottom":"25px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover has-custom-content-position is-position-bottom-center cat-img-gradient cover-img relative" style="border-radius:16px;padding-top:25px;padding-bottom:25px;min-height:550px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-10 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-1129" alt="" src="https://transvelo.github.io/glowess/assets/images/v3-cat-image-1.png" style="object-position:16% 67%" data-object-fit="cover" data-object-position="16% 67%"/><div class="wp-block-cover__inner-container"><!-- wp:heading {"textAlign":"center","level":3,"className":"stretched-link","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"}},"textColor":"base"} -->
<h3 class="wp-block-heading has-text-align-center stretched-link has-base-color has-text-color has-link-color" style="font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><a href="#"><?php echo esc_html__( 'Make Up', 'glowess' ); ?></a></h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"15px","lineHeight":"1.5","fontStyle":"normal","fontWeight":"400"}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( '2 Items', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","style":{"spacing":{"padding":{"top":"7px"}}}} -->
<div class="wp-block-column is-vertically-aligned-center" style="padding-top:7px"><!-- wp:image {"id":1177,"width":"auto","height":"108px","sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full is-resized"><img src="https://transvelo.github.io/glowess/assets/images/image-26.png" alt="" class="wp-image-1177" style="width:auto;height:108px"/></figure>
<!-- /wp:image -->

<!-- wp:group {"style":{"spacing":{"padding":{"top":"10px"}}},"layout":{"type":"constrained","contentSize":"429px"}} -->
<div class="wp-block-group" style="padding-top:10px"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Yes Girl, You’re Unique', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:group -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'This text helps you provide some more detail to the', 'glowess' ); ?><br><?php echo esc_html__( 'title above.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"31px"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:31px"><!-- wp:button {"backgroundColor":"secondary","textColor":"base","className":"inline-img","style":{"border":{"radius":"12px"},"spacing":{"padding":{"top":"12px","bottom":"12px"}},"typography":{"textTransform":"uppercase","fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"borderColor":"secondary"} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5;text-transform:uppercase"><a class="wp-block-button__link has-base-color has-secondary-background-color has-text-color has-background has-link-color has-border-color has-secondary-border-color wp-element-button" href="#" style="border-radius:12px;padding-top:12px;padding-bottom:12px">SHOP NEW COLLECTION<img class="wp-image-198" style="width: 15px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:group {"style":{"spacing":{"padding":{"top":"11px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:11px"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v3-cat-image-2.png","id":1187,"dimRatio":10,"overlayColor":"secondary","isUserOverlayColor":true,"minHeight":550,"contentPosition":"bottom center","isDark":false,"className":"cat-img-gradient cover-img relative","style":{"border":{"radius":"16px"},"spacing":{"blockGap":"0px","padding":{"top":"25px","bottom":"25px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover is-light has-custom-content-position is-position-bottom-center cat-img-gradient cover-img relative" style="border-radius:16px;padding-top:25px;padding-bottom:25px;min-height:550px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-10 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-1187" alt="" src="https://transvelo.github.io/glowess/assets/images/v3-cat-image-2.png" data-object-fit="cover"/><div class="wp-block-cover__inner-container"><!-- wp:heading {"textAlign":"center","level":3,"className":"stretched-link","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"}},"textColor":"base"} -->
<h3 class="wp-block-heading has-text-align-center stretched-link has-base-color has-text-color has-link-color" style="font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><a href="#"><?php echo esc_html__( 'Serum', 'glowess' ); ?></a></h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"15px","lineHeight":"1.5","fontStyle":"normal","fontWeight":"400"}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( '2 Items', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
