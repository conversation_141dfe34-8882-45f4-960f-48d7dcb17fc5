<?php
/* ==================== city-selector-dropdown.php ==================== */
$selected_city = glowess_get_selected_city();
$current_city_name = 'Şehir Seç';

if ($selected_city) {
    $current_city = get_post($selected_city);
    $current_city_name = $current_city ? $current_city->post_title : 'Şehir Seç';
}

$cities = get_posts(array(
    'post_type' => 'cities',
    'numberposts' => -1,
    'post_status' => 'publish',
    'meta_query' => array(
        array(
            'key' => '_city_is_active',
            'value' => '1',
            'compare' => '='
        )
    )
));
?>

<div class="glowess-city-selector">
    <div class="current-city-display">
        <span class="city-icon">📍</span>
        <span class="city-name"><?php echo esc_html($current_city_name); ?></span>
        <span class="dropdown-arrow">▼</span>
    </div>
    
    <div class="city-dropdown" style="display: none;">
        <div class="city-dropdown-header">
            <input type="text" placeholder="Şehir ara..." class="city-search" id="city-search">
        </div>
        <?php foreach ($cities as $city): 
            $city_slug = get_post_meta($city->ID, '_city_slug', true);
            $is_current = ($selected_city == $city->ID);
        ?>
            <div class="city-dropdown-item <?php echo $is_current ? 'current' : ''; ?>" 
                 data-city-id="<?php echo $city->ID; ?>" 
                 data-city-slug="<?php echo esc_attr($city_slug); ?>">
                <span class="city-dropdown-name"><?php echo esc_html($city->post_title); ?></span>
                <?php if ($is_current): ?>
                    <span class="current-indicator">✓</span>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
        
        <?php if (empty($cities)): ?>
            <div class="city-dropdown-empty">
                Henüz aktif şehir bulunmamaktadır.
            </div>
        <?php endif; ?>
    </div>
</div>