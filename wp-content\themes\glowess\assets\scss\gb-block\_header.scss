/*
################
* === Header STYLE  ===
################
*/

.search-toggle {
    a {display: flex;}

    &.active {
        a {
            &::after {
                font-family: "bootstrap-icons";
                content: "\F62A";
                font-size: 22px;
                position: relative;
                top: -2px;
                left: -3px;
                line-height: 1;
                width: 17px;
                height: 17px;
            }

            img {display: none;}
        }
    }
}

.primary-menu {
    > .wp-block-navigation-item {
        &:is(:hover){
            > .wp-block-navigation-item__content {
                text-decoration: underline;
                text-underline-offset: 9px;
            }
        }

        > .wp-block-navigation-item__content {
            text-transform: uppercase;
        }
    }
}

body:not(.editor-styles-wrapper) {
    .mobile-header {
        @media (min-width:1200px) {
            display: none;
        }
    }

    .desktop-header {
        @media (max-width:1199px) {
            display: none;
        }
    }
}

body:not(.scrolling-active) {
    .wp-block-template-part.transparent {
        @media (min-width:1200px) {
            position: absolute;
            width: 100%;
            z-index: 3;
        }

        > .header-transparent:not(.semi) {
            .desktop-header {
                .wp-block-site-title,
                .primary-menu {
                    color: var(--wp--preset--color--base);
                }
    
                .brand-logo {
                    img {
                        filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);
                    }
                }
            }

            &.header-v1 {
                .desktop-header {
                    .search-toggle.active a::after {
                        color: var(--wp--preset--color--base);
                    }

                    ul.catalog-menu > .wp-block-navigation-item:is(:hover) > .wp-block-navigation-item__content {
                        color: var(--wp--preset--color--base) !important;
                    }

                    .wp-block-site-title,
                    .wp-block-site-title a,
                    .primary-menu,
                    .catalog-menu,
                    .currency {
                        color: var(--wp--preset--color--base) !important;
                    }

                    > .wp-block-group:last-child {
                        border-bottom-color: rgba(from var(--wp--preset--color--base)  r g b / .3) !important;
                    }

                    .search-toggle img {
                        filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);
                    }

                    .wp-block-woocommerce-customer-account[data-display-style=icon_only] a::after {
                        background-image: url('data:image/svg+xml,<svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.99984 1.94855C5.45584 1.94855 4.20414 3.19221 4.20414 4.72634C4.20414 6.26045 5.45584 7.50409 6.99984 7.50409C8.54384 7.50409 9.79551 6.26045 9.79551 4.72634C9.79551 3.19221 8.54384 1.94855 6.99984 1.94855ZM2.91382 4.72634C2.91382 2.48415 4.74319 0.666504 6.99984 0.666504C9.25651 0.666504 11.0858 2.48415 11.0858 4.72634C11.0858 6.96851 9.25651 8.78617 6.99984 8.78617C4.74319 8.78617 2.91382 6.96851 2.91382 4.72634Z" fill="%23fff"/><path fill-rule="evenodd" clip-rule="evenodd" d="M4.8884 11.7776C3.08529 11.7776 1.62358 13.2299 1.62358 15.0215C1.62358 15.1156 1.64088 15.182 1.65902 15.2207C1.67433 15.2533 1.68747 15.2626 1.69843 15.2685C2.19982 15.5396 3.6056 16.0511 6.99992 16.0511C10.3943 16.0511 11.8 15.5396 12.3014 15.2685C12.3123 15.2626 12.3255 15.2533 12.3408 15.2207C12.359 15.182 12.3763 15.1156 12.3763 15.0215C12.3763 13.2299 10.9146 11.7776 9.11142 11.7776H4.8884ZM0.333252 15.0215C0.333252 12.5219 2.37266 10.4956 4.8884 10.4956H9.11142C11.6272 10.4956 13.6666 12.5219 13.6666 15.0215C13.6666 15.4804 13.4994 16.0804 12.9182 16.3946C12.1482 16.8109 10.4965 17.3332 6.99992 17.3332C3.50337 17.3332 1.85164 16.8109 1.08169 16.3946C0.500444 16.0804 0.333252 15.4804 0.333252 15.0215Z" fill="%23fff"/></svg>');
                    }

                    .wc-block-mini-cart:not([data-mini-cart-icon=bag], [data-mini-cart-icon=bag-alt]) .wc-block-mini-cart__quantity-badge::before {
                        background-image: url('data:image/svg+xml,<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.833252 1.29941C0.833252 0.949871 1.12217 0.666504 1.47856 0.666504H2.11048C3.30377 0.666504 4.31032 1.538 4.45833 2.69934L4.62945 4.04203H16.8273C17.7478 4.04203 18.4528 4.84502 18.3163 5.7379L17.3938 11.7703C17.22 12.9063 16.225 13.7467 15.0538 13.7467H6.83234C5.66112 13.7467 4.66614 12.9063 4.4924 11.7703L3.42162 4.76883L3.41946 4.75345L3.17768 2.85634C3.1104 2.32846 2.65288 1.93233 2.11048 1.93233H1.47856C1.12217 1.93233 0.833252 1.64896 0.833252 1.29941ZM4.80911 5.30785L5.76874 11.5825C5.8477 12.0988 6.29997 12.4808 6.83234 12.4808H15.0538C15.5862 12.4808 16.0384 12.0988 16.1174 11.5825L17.0399 5.55012C17.0594 5.42256 16.9588 5.30785 16.8273 5.30785H4.80911Z" fill="%23fff"/><path d="M16.1056 16.4893C16.1056 16.9554 15.7203 17.3333 15.2452 17.3333C14.77 17.3333 14.3848 16.9554 14.3848 16.4893C14.3848 16.0233 14.77 15.6455 15.2452 15.6455C15.7203 15.6455 16.1056 16.0233 16.1056 16.4893Z" fill="%23fff"/><path d="M7.50133 16.4893C7.50133 16.9554 7.11612 17.3333 6.64093 17.3333C6.16573 17.3333 5.78052 16.9554 5.78052 16.4893C5.78052 16.0233 6.16573 15.6455 6.64093 15.6455C7.11612 15.6455 7.50133 16.0233 7.50133 16.4893Z" fill="%23fff"/></svg>');
                    }

                    .wc-block-mini-cart__badge {
                        background-color: var(--wp--preset--color--base);
                        color: var(--wp--preset--color--secondary);
                    }

                    ul.catalog-menu > .wp-block-navigation-item:first-child > .wp-block-navigation-submenu__toggle {            
                        &::after {
                            background-color: rgba(from var(--wp--preset--color--base)  r g b / .3);
                        }
                    }

                    ul.currency > .wp-block-navigation-item > .wp-block-navigation-item__content::before {
                        background-image: url('data:image/svg+xml,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_43_361)"><path d="M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z" fill="white"/></g><defs><clipPath id="clip0_43_361"><rect width="18" height="18" fill="white"/></clipPath></defs></svg>');
                    }

                    ul.catalog-menu {
                        > .wp-block-navigation-item:first-child {
                            > .wp-block-navigation-item__content {                    
                                &::before {
                                    background-image: url('data:image/svg+xml,<svg width="19" height="10" viewBox="0 0 19 10" fill="none" xmlns="http://www.w3.org/2000/svg"><rect y="0.25" width="19" height="1" fill="white"/><rect y="8.75" width="14" height="1" fill="white"/></svg>');
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


#myModal {
    position: absolute;
    right: 0;
    left: 0;
    z-index: 999;
    margin: 0;
}

.search-cat-btn {
    > .wp-block-group:not(:hover) {
        border-color: rgb(from var(--wp--preset--color--base) r g b / .2) !important;
    }

    > .wp-block-group {
        position: relative;
        transition: all .15s ease-in;

        a {
            &::before {
                position: absolute;
                content: "";
                inset: 0;
            }
        }
    }
}

.search-box {
    input[type=search]{
        border-width: 0;
        padding: 13px 20px !important;

        &, &::placeholder {
            color:rgba(from var(--wp--preset--color--secondary)  r g b / .75);
        }

        &, &:is(:focus, :focus:invalid) {
            background-color: transparent;
            border-width: 0;
            box-shadow: none;
            color: var(--wp--preset--color--base);
        }
    }

    > .wp-block-group {
        @media (max-width:767px) {
            padding-top: 20px !important;
            padding-bottom: 40px !important;

            .search-cat-btn {
                margin-top: 30px !important;
            }
        }
    }

    :where(.wp-element-button, .wp-block-button__link) {
        border-width: 0;
        min-width: auto;

        svg {display: none;}

        &::after {
            background-image: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_117_4490)"><path d="M0 9.52393H18.8417" stroke="white"/><path d="M14.8755 13.9863C15.9834 11.5479 16.8612 10.5242 19.0481 9.53047C16.8124 8.43004 15.9446 7.40261 14.8755 5.06128" stroke="white"/></g><defs><clipPath id="clip0_117_4490"><rect width="20" height="19.0476" fill="white"/></clipPath></defs></svg>');
            content: " ";
            width: 20px;
            height: 20px;
            display: block;
        }
    }
}

body:not(.editor-styles-wrapper) {
    .search-box {
        transition: all .15s ease-in;
        transform: translate(0, -10px);
        padding: 0 clamp(0px, 4vw, 60px);
    
        &:not(.flex) {
            visibility: hidden;
            opacity: 0;
        }
    
        &.flex {
            //display: flex !important;
            transform: translate(0, 0);
        }
    
        > .wp-block-group {
            width: 100%;
        }
    }
}

ul.catalog-menu {
    > .wp-block-navigation-item:first-child {
        > .wp-block-navigation-item__content {
            display: flex;
            align-items: center;
            position: relative;

            &::before {
                content: " ";
                display: block;
                width: 20px;
                height: 10px;
                margin-right: 10px;
                background-image: url('data:image/svg+xml,<svg width="19" height="10" viewBox="0 0 19 10" fill="none" xmlns="http://www.w3.org/2000/svg"><rect y="0.25" width="19" height="1" fill="%23131C19"/><rect y="8.75" width="14" height="1" fill="%23131C19"/></svg>');
                //background-image: url('data:image/svg+xml,<svg width="19" height="10" viewBox="0 0 19 10" fill="none" xmlns="http://www.w3.org/2000/svg"><rect y="0.25" width="19" height="1" fill="white"/><rect y="8.75" width="14" height="1" fill="white"/></svg>');
                background-repeat: no-repeat;
            }
        }

        > .wp-block-navigation-submenu__toggle {
            font-size: 0;
            position: relative;
            pointer-events: none;

            &::after {
                content: " ";
                display: block;
                width: 1px;
                height: 30px;
                //background-color: rgba(from var(--wp--preset--color--base)  r g b / .3);
                background-color:var(--wp--preset--color--gray-100) ;
                position: absolute;
                right: -31px;
                transform: translateY(-14px);

                @media (min-width:1200px) and (max-width:1599px) {
                    right: -16px;
                }
            }
        }
    }

    .cat-img {
        > .wp-block-navigation-item__content {display: none !important;}
    }

    ul.cat-img {
        flex-direction: row !important;
        gap: 30px;

        > * {
            flex-grow: 1;
            width: 50%;

            a {
                width: 366px;
                padding: 0 !important;

                span {
                    display: flex;
                }
            }
        }
    }

    > .wp-block-navigation-item:is(:hover) > .wp-block-navigation-item__content {
        color: var(--wp--preset--color--secondary) !important;
    }

    .nav-title {
        &:hover > .wp-block-navigation-item__content,
        > .wp-block-navigation-item__content {
            color: rgb(from var(--wp--preset--color--base) r g b / .5) !important;
        }

        > .wp-block-navigation-item__content {
            padding-top: 0 !important;
        }

        ul.wp-block-navigation__submenu-container {
            display: block;
            column-count: 2;
            padding-right: 30px !important;
            max-width: 330px;
            min-width: 330px !important;
        }
    }

    > .wp-block-navigation-item {
        .wp-block-navigation-item__content {
            padding: 10px 0;
        }

        > .wp-block-navigation__submenu-container {
            right: calc(0px + 10px);
            flex-direction: row;
            align-items: flex-start;

            @media (min-width:1700px) {
                right: calc(0px + 406px);
            }

            > .wp-block-navigation-item {
                display: block;

                .wp-block-navigation__submenu-icon {display: none;}
            }

            > * {
                flex-grow: 1;

                &:first-child {
                    width: 40%;
                }

                &:last-child {
                    width: 60%;
                    overflow: hidden;
                }
            }

            .wp-block-navigation__submenu-container {
                position: relative;
                opacity: 1;
                visibility: visible;
                height: auto;
                width: auto;
                left: 0;
                padding: 0 !important;
                box-shadow: none !important;
                min-width: 100%;
                border-width: 0 !important;
                top: 0;

                .wp-block-navigation-item__content {
                    padding: 0 0 10px 0;
                    font-family: var(--wp--preset--font-family--body);
                }
            }
        }
    }
}

.catalog-menu {
    &,
    .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open),
    .wp-block-navigation__responsive-dialog,
    > .wp-block-navigation-item {
        position: static;
    }
}

ul.currency {
    > .wp-block-navigation-item {
        > .wp-block-navigation-item__content {
            display: flex;
            align-items: center;

            &::before {
                content: " ";
                display: block;
                width: 18px;
                height: 18px;
                margin-right: 10px;
                background-repeat: no-repeat;
                background-image: url('data:image/svg+xml,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_43_361)"><path d="M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z" fill="%23131C19"/></g><defs><clipPath id="clip0_43_361"><rect width="18" height="18" fill="%23131C19"/></clipPath></defs></svg>');
                //background-image: url('data:image/svg+xml,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_43_361)"><path d="M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z" fill="white"/></g><defs><clipPath id="clip0_43_361"><rect width="18" height="18" fill="white"/></clipPath></defs></svg>');
            }
        }

        .wp-block-navigation__submenu-icon {
            font-size: 16px;
            margin-left: 10px;
        }
    }
}

.header-v1,
.header-v5 {
    .desktop-header {
        ul.primary-menu {
            @media (min-width:1200px) and (max-width:1599px) {
                gap: 10px !important;
            }
        }

        .cat-pri-nav {
            @media (min-width:1200px) and (max-width:1599px) {
                gap: 30px !important;

                nav.catalog-menu::after {
                    right: -15px;
                }
            }
        }
    }
}

.header-default,
.header-v2 {
    .desktop-header ul.wp-block-navigation > .wp-block-navigation-item.has-mega-menu > .wp-block-navigation__submenu-container {
        top: calc(100% - 3px);
    }
}

.header-v3 {
    .desktop-header ul.wp-block-navigation > .wp-block-navigation-item.has-mega-menu > .wp-block-navigation__submenu-container {
        top: calc(100% + 20px);
    }
}

.header-v4 {
    nav.catalog-menu::after {
        display: none;
    }

    .desktop-header {
        ul.primary-menu {
            @media (min-width:1200px) and (max-width:1365px) {
                gap: 15px !important;
            }
        }
    }
}

.desktop-header {
    ul.catalog-menu {
        > .wp-block-navigation-item:is(:hover) {
            > .wp-block-navigation__submenu-container {
                padding: 58px 60px !important;
            }
        }
    }

    nav.primary-menu {
        &, .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open),
        .wp-block-navigation__responsive-dialog,
        ul.wp-block-navigation.primary-menu,
        .wp-block-navigation .wp-block-navigation-item.has-mega-menu {
            position: static;
        }
    }

    .has-mega-menu.wp-block-navigation__submenu-container {
        flex-direction: row;
        left: 0;
        right: 0;
        align-items: flex-start;

        > * {
            flex-grow: 1;
            width: 100%;
        }

        .menu-img {
            .wp-block-navigation-item__content {
                width: 266px;
                padding: 0;

                span {
                    display: flex;
                }

                @media (min-width:1500px) {
                    width: 366px;
                }
            }
        }

        .wp-block-navigation-item__content {
            padding: 0 0 10px 0;
        }

        .nav-title {
            &:hover > .wp-block-navigation-item__content, > .wp-block-navigation-item__content {
                color: rgb(from var(--wp--preset--color--base) r g b / .5);
                cursor: unset;
                line-height: 1.2;
            }

            > .wp-block-navigation-item {
                .wp-block-navigation-item__content {
                    font-family: var(--wp--preset--font-family--body);
                }
            }

            li.nav-title {
                margin-top: 40px;
            }
        }
    }

    ul.wp-block-navigation {
        .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-icon {
            margin-right: 30px;
        }

        .wp-block-navigation__submenu-container {
            .wp-block-navigation-item:not(.nav-title) {
                &:is(:hover) {
                    > .wp-block-navigation-item__content,
                    > .wp-block-navigation__submenu-icon {
                        color: var(--wp--preset--color--primary);
                    }
                }
            }
        }

        > .wp-block-navigation-item {
            > .wp-block-navigation__submenu-container {
                top: calc(100% + 20px);   
            }

            &.has-mega-menu {
                > .wp-block-navigation__submenu-container {
                    top: calc(100% - 14px);   
                }

                .wp-block-navigation__submenu-container {
                    >.wp-block-navigation-item {
                        >.wp-block-navigation-item__content {
                            flex-grow: 0;
                        }

                        .wp-block-navigation__submenu-icon {
                            display: none;
                        }
                    }
                }

                .has-child:not(.open-on-click):hover > .wp-block-navigation__submenu-container {
                    min-width: 100%;
                }

                &:is(:hover) {
                    .has-mega-menu {
                        padding: 30px;

                        @media (min-width:1500px) {
                            padding: 58px 60px 60px 60px;
                        }
                    }
                }
                
                .wp-block-navigation-item {
                    border-width: 0;
                    flex-direction: column;
                    align-items: flex-start;
    
                    .wp-block-navigation__submenu-container {
                        position: relative;
                        opacity: 1;
                        visibility: visible;
                        height: auto;
                        width: auto;
                        left: 0;
                        padding: 0 !important;
                        box-shadow: none !important;
                        min-width: 100%;
                        border-width: 0;
                    }
                }
            }

            .wp-block-navigation__submenu-container {
                padding: 12px 0;
                border-top: 2px solid var(--wp--preset--color--primary);
                border-width: 2px 0 0 0;
            }

            &:is(:hover) {
                &.has-child {
                    > .wp-block-navigation-item__content {
                        position: relative;
    
                        &::after {
                            position: absolute;
                            content: " ";
                            top: 100%;
                            height: 30px;
                            width: calc(100% + 20px);
                            left: 0;
                        }
                    }
                }
            }
        }
    }

    ul.catalog-menu {
        > .wp-block-navigation-item {
            > .wp-block-navigation__submenu-container {
                top: calc(100% - 14px);   
            }
        }
    }

    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-item a:not(.wp-element-button)), 
    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu a:not(.wp-element-button)), 
    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu button.wp-block-navigation-item__content), 
    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-pages-list__item button.wp-block-navigation-item__content) {
        padding: 10px 29px;
    }

    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-item.menu-img a:not(.wp-element-button)) {
        padding: 0;
    }

    .wp-block-navigation .has-child .wp-block-navigation-submenu__toggle[aria-expanded=true]~.wp-block-navigation__submenu-container, 
    .wp-block-navigation .has-child:not(.open-on-click):hover>.wp-block-navigation__submenu-container, 
    .wp-block-navigation .has-child:not(.open-on-click):not(.open-on-hover-click):focus-within>.wp-block-navigation__submenu-container {
        min-width: 300px;
    }
}

.header-v3 {
    .wp-block-site-title a {
        color: var(--wp--preset--color--secondary) !important;
    }
}

.header-v5 {
    .wp-block-site-title a {
        color: var(--wp--preset--color--primary) !important;
    }
}