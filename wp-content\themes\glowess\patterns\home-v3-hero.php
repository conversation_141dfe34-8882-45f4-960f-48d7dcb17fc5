<?php
/**
 * Title: Home v3 Hero
 * Slug: glowess/home-v3-hero
 * Categories: featured
 * Keywords: Home v3 Hero
 *
 * @package glowess
 */

?>
<!-- wp:group {"metadata":{"name":"Home-v3-hero"},"align":"full","className":"v3-hero","style":{"spacing":{"padding":{"bottom":"23px","top":"var:preset|spacing|50"}}},"backgroundColor":"bg-5","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull v3-hero has-bg-5-background-color has-background" style="padding-top:var(--wp--preset--spacing--50);padding-bottom:23px"><!-- wp:group {"style":{"spacing":{"padding":{"right":"0px","left":"0px","top":"var:preset|spacing|30"},"margin":{"top":"1px"}}},"layout":{"type":"constrained","contentSize":"1803px"}} -->
<div class="wp-block-group" style="margin-top:1px;padding-top:var(--wp--preset--spacing--30);padding-right:0px;padding-left:0px"><!-- wp:columns {"verticalAlignment":"top","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"0"},"margin":{"top":"7px"},"blockGap":{"left":"0px"}}}} -->
<div class="wp-block-columns are-vertically-aligned-top" style="margin-top:7px;padding-top:var(--wp--preset--spacing--30);padding-bottom:0"><!-- wp:column {"verticalAlignment":"top","style":{"spacing":{"padding":{"top":"0px"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:0px"><!-- wp:group {"className":"grid relative","style":{"spacing":{"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group grid relative" style="margin-top:0px;margin-bottom:0px"><!-- wp:group {"className":"grid-span-full position-image-left d-none d-xl-block","style":{"spacing":{"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group grid-span-full position-image-left d-none d-xl-block" style="margin-top:0px;margin-bottom:0px"><!-- wp:image {"id":1240,"width":"auto","height":"220px","sizeSlug":"full","linkDestination":"none","align":"center","className":"h-150","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image aligncenter size-full is-resized has-custom-border h-150"><img src="https://transvelo.github.io/glowess/assets/images/v3-hero-2.png" alt="" class="wp-image-1240" style="border-radius:16px;width:auto;height:220px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"grid-span-full","style":{"spacing":{"margin":{"top":"12px","bottom":"0px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group grid-span-full" style="margin-top:12px;margin-bottom:0px"><!-- wp:image {"id":1220,"width":"auto","height":"490px","sizeSlug":"full","linkDestination":"none","align":"center","className":"height-auto","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image aligncenter size-full is-resized has-custom-border height-auto"><img src="https://transvelo.github.io/glowess/assets/images/v3-hero-1.png" alt="" class="wp-image-1220" style="border-radius:16px;width:auto;height:490px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"d-none d-xl-block","style":{"spacing":{"padding":{"left":"0"},"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group d-none d-xl-block" style="margin-top:0px;margin-bottom:0px;padding-left:0"><!-- wp:image {"id":1223,"width":"auto","height":"196px","sizeSlug":"full","linkDestination":"none","align":"center","className":"h-130"} -->
<figure class="wp-block-image aligncenter size-full is-resized h-130"><img src="https://transvelo.github.io/glowess/assets/images/v3-hero-3.png" alt="" class="wp-image-1223" style="width:auto;height:196px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"top","width":"54%","style":{"spacing":{"padding":{"top":"var:preset|spacing|40"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:var(--wp--preset--spacing--40);flex-basis:54%"><!-- wp:group {"style":{"spacing":{"padding":{"top":"21px"},"blockGap":"var:preset|spacing|20"}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:21px"><!-- wp:heading {"textAlign":"center","level":6,"style":{"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"1.7","textTransform":"uppercase"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"default"} -->
<h6 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color has-default-font-size" style="font-style:normal;font-weight:400;line-height:1.7;text-transform:uppercase"><?php echo esc_html__( 'WHAT’S NEW', 'glowess' ); ?></h6>
<!-- /wp:heading -->

<!-- wp:group {"style":{"spacing":{"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"constrained","contentSize":"520px"}} -->
<div class="wp-block-group" style="margin-top:0px;margin-bottom:0px"><!-- wp:heading {"textAlign":"center","level":1,"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"spacing":{"margin":{"top":"9px"}}},"textColor":"secondary","fontSize":"xx-large"} -->
<h1 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color has-xx-large-font-size" style="margin-top:9px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'New Skincare Collection', 'glowess' ); ?></h1>
<!-- /wp:heading --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"padding":{"top":"4px","bottom":"10px"}}},"layout":{"type":"constrained","contentSize":"355px"}} -->
<div class="wp-block-group" style="padding-top:4px;padding-bottom:10px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"2"}},"textColor":"contrast","fontSize":"medium"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color has-medium-font-size" style="font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Discover our new collection and click all the items you like in your shopping basket.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"var:preset|spacing|30"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:var(--wp--preset--spacing--30)"><!-- wp:button {"backgroundColor":"secondary","textColor":"base","className":"inline-img","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"1.2"},"border":{"radius":"12px"},"spacing":{"padding":{"left":"49px","right":"49px","top":"12px","bottom":"12px"}}},"fontSize":"medium","borderColor":"secondary"} -->
<div class="wp-block-button has-custom-font-size inline-img has-medium-font-size" style="font-style:normal;font-weight:400;line-height:1.2"><a class="wp-block-button__link has-base-color has-secondary-background-color has-text-color has-background has-link-color has-border-color has-secondary-border-color wp-element-button" href="#" style="border-radius:12px;padding-top:12px;padding-right:49px;padding-bottom:12px;padding-left:49px"><?php echo esc_html__( 'SHOP NOW', 'glowess' ); ?><img class="wp-image-198" style="width: 15px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"top","width":"23.5%","style":{"spacing":{"padding":{"top":"var:preset|spacing|20"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:var(--wp--preset--spacing--20);flex-basis:23.5%"><!-- wp:group {"className":"grid","style":{"spacing":{"padding":{"top":"0px","bottom":"0px"},"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group grid" style="margin-top:0px;margin-bottom:0px;padding-top:0px;padding-bottom:0px"><!-- wp:group {"className":"grid-span-full position-image-right d-none d-xl-block","style":{"spacing":{"blockGap":"0"}},"layout":{"type":"default"}} -->
<div class="wp-block-group grid-span-full position-image-right d-none d-xl-block"><!-- wp:group {"layout":{"type":"constrained"}} -->
<div class="wp-block-group"><!-- wp:image {"id":1298,"width":"auto","height":"300px","sizeSlug":"full","linkDestination":"none","align":"left","className":"h-150","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image alignleft size-full is-resized has-custom-border h-150"><img src="https://transvelo.github.io/glowess/assets/images/v3-hero-4.png" alt="" class="wp-image-1298" style="border-radius:16px;width:auto;height:300px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"d-none d-lg-block","layout":{"type":"constrained"}} -->
<div class="wp-block-group d-none d-lg-block"><!-- wp:spacer {"height":"var:preset|spacing|50"} -->
<div style="height:var(--wp--preset--spacing--50)" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"d-none d-xl-block","layout":{"type":"constrained"}} -->
<div class="wp-block-group d-none d-xl-block"><!-- wp:spacer {"height":"45px"} -->
<div style="height:45px" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"padding":{"left":"0px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="padding-left:0px"><!-- wp:image {"id":1300,"width":"auto","height":"104px","sizeSlug":"full","linkDestination":"none","align":"center","className":"ml-22 h-130"} -->
<figure class="wp-block-image aligncenter size-full is-resized ml-22 h-130"><img src="https://transvelo.github.io/glowess/assets/images/v3-hero-5.png" alt="" class="wp-image-1300" style="width:auto;height:104px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"grid-span-full","style":{"spacing":{"margin":{"top":"var:preset|spacing|50"},"padding":{"top":"40px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group grid-span-full" style="margin-top:var(--wp--preset--spacing--50);padding-top:40px"><!-- wp:image {"id":1299,"width":"auto","height":"491px","sizeSlug":"full","linkDestination":"none","align":"right","className":"height-auto","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image alignright size-full is-resized has-custom-border height-auto"><img src="https://transvelo.github.io/glowess/assets/images/v2-single.png" alt="" class="wp-image-1299" style="border-radius:16px;width:auto;height:491px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
