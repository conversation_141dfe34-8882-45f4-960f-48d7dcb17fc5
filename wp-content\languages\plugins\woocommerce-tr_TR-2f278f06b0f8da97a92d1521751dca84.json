{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Revenue is now reported from paid orders ✅": ["<PERSON><PERSON>r artık ücretli siparişlerden bildiriliyor ✅"], "Analytics date settings": ["<PERSON><PERSON><PERSON> ve<PERSON>"], "We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.": ["Artık bu tabloda siparişleri ne zaman verildiklerine göre de<PERSON>, ödemenin ne zaman yapıldığına göre topluyoruz. Bunu <link>ayarlardan</link> değiştirebilirsiniz."], "Full refunds are not deducted from tax or net sales totals": ["Tam para iadeleri vergiden veya net satış toplamlarından düşülmez"], "Previous year:": ["Geçen yıl:"], "Previous period:": ["<PERSON>nce<PERSON> d<PERSON>"], "All Revenue": ["<PERSON><PERSON><PERSON> gelir"], "A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Revenue Matches <select/> Filters": ["<PERSON><PERSON><PERSON> <select/> Filtre ile Eşleşiyor"], "Got it": ["<PERSON><PERSON><PERSON><PERSON>"], "day": ["g<PERSON>n", "g<PERSON>n"], "order": ["siparş", "siparş"], "No data for the selected date range": ["Seçilen tarih aralığı için veri yok"], "No data for the current search": ["Mevcut arama için veri yok"], "Advanced Filters": ["<PERSON><PERSON>ş<PERSON>ş filtreler"], "Gross sales": ["<PERSON><PERSON><PERSON><PERSON>"], "Returns": ["<PERSON><PERSON><PERSON>"], "Net sales": ["Net satışlar"], "Revenue": ["Kazanç"], "Show": ["<PERSON><PERSON><PERSON>"], "Total sales": ["Toplam satışlar"], "Shipping": ["<PERSON><PERSON><PERSON><PERSON>"], "Date": ["<PERSON><PERSON><PERSON>"], "Taxes": ["<PERSON>er<PERSON><PERSON>"], "Orders": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Coupons": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-revenue.js"}}