{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Search product categories": ["<PERSON><PERSON><PERSON><PERSON> ka<PERSON> ara"], "Select a product category filter match": ["Bir ürün kategorisi filtresi eşleşmesi seç"], "Remove product category filter": ["Ürün kategorisi filtresini kaldır"], "Product category": ["<PERSON><PERSON><PERSON><PERSON> ka<PERSON>"], "Select product attributes": ["<PERSON><PERSON><PERSON><PERSON> nitelik<PERSON> seç"], "Remove product attribute filter": ["<PERSON><PERSON>ün niteliği filtresini kaldır"], "Search product attributes": ["<PERSON><PERSON><PERSON><PERSON> nitelik<PERSON> ara"], "Product attribute": ["<PERSON><PERSON><PERSON><PERSON>"], "<title>Product category</title> <rule/> <filter/>": ["<title><PERSON><PERSON><PERSON><PERSON></title> <rule/> <filter/>"], "<title>Product attribute</title> <rule/> <filter/>": ["<title><PERSON><PERSON><PERSON><PERSON></title> <rule/> <filter/>"], "<title>Product</title> <rule/> <filter/>": ["<title><PERSON><PERSON><PERSON><PERSON></title> <rule/> <filter/>"], "Advanced filters": ["<PERSON><PERSON>ş<PERSON>ş filtreler"], "categories\u0004Excludes": ["<PERSON><PERSON><PERSON>"], "categories\u0004Includes": ["<PERSON><PERSON><PERSON>"], "A sentence describing filters for Variations. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Variations match <select/> filters": ["Varyasyonlar <select/> filtre ile eşleşiyor"], "product attribute\u0004Is": ["<PERSON><PERSON>"], "product attribute\u0004Is Not": ["<PERSON><PERSON><PERSON>"], "Type to search for a variation": ["Bir varyasyon aramak için yazın"], "Select a product attribute filter match": ["Bir ürün özelliği filtre eşleşmesi seçin"], "Single variation": ["Tek <PERSON>yon"], "Check at least two variations below to compare": ["Karşılaştırmak için aşağıdan en az iki varyasyon işaretleyin"], "Remove product filter": ["<PERSON>rün filtresini kaldır"], "Indication of a low quantity\u0004Low": ["Düşük"], "%d variations": ["%d varyasyon"], "Search by variation name or SKU": ["Varyasyon adına veya SKU'ya göre ara"], "variation sold": ["<PERSON><PERSON><PERSON> varyasyon", "<PERSON><PERSON><PERSON>"], "Search for variations to compare": ["Karşılaştırmak için varyasyon ara"], "Comparison": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "products\u0004Excludes": ["<PERSON><PERSON><PERSON>"], "products\u0004Includes": ["<PERSON><PERSON><PERSON>"], "Select products": ["<PERSON><PERSON><PERSON><PERSON>"], "net sales": ["net satış"], "Select a product filter match": ["Bir ürün filtresi eşleşmesi seç"], "All variations": ["<PERSON><PERSON><PERSON>"], "Compare Variations": ["Varyasyonları karşılaştır"], "item sold": ["<PERSON><PERSON><PERSON> ürün", "<PERSON><PERSON><PERSON> ürün"], "(Deleted)": ["(<PERSON><PERSON><PERSON>)"], "Product / Variation title": ["Ürün/Varyasyon başlığı"], "Items sold": ["Satılan ürünler"], "Net sales": ["Net satışlar"], "Compare": ["Karş<PERSON>laştır"], "Select product categories": ["<PERSON><PERSON><PERSON><PERSON> ka<PERSON> seç"], "Search products": ["Ürünleri ara"], "Show": ["<PERSON><PERSON><PERSON>"], "orders": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "Variations": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Stock": ["Stok"], "Status": ["Durum"], "Orders": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "SKU": ["Stok kodu (SKU)"], "Product": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-variations.js"}}