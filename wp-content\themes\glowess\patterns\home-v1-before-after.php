<?php
/**
 * Title: Home v1 Before After
 * Slug: glowess/home-v1-before-after
 * Categories: featured
 * Keywords: Home v1 Before After
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Before-after Block"},"align":"wide","className":"v1-before-after","style":{"spacing":{"padding":{"top":"38px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignwide v1-before-after" style="padding-top:38px"><!-- wp:columns {"verticalAlignment":null,"style":{"spacing":{"margin":{"top":"16px"}}}} -->
<div class="wp-block-columns" style="margin-top:16px"><!-- wp:column {"verticalAlignment":"top","width":"31.5%","className":"pt-0","style":{"spacing":{"padding":{"top":"var:preset|spacing|50"}}}} -->
<div class="wp-block-column is-vertically-aligned-top pt-0" style="padding-top:var(--wp--preset--spacing--50);flex-basis:31.5%"><!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"spacing":{"padding":{"top":"13px","bottom":"2px"}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="has-secondary-color has-text-color has-link-color has-heading-font-family" style="padding-top:13px;padding-bottom:2px;font-size:16px;font-style:normal;font-weight:400;line-height:1.7"><?php echo esc_html__( 'BIG SALE', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Radiant Results Guaranteed', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"16px"}}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="margin-top:16px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'See the stunning before and after transformations that our satisfied customers have achieved with our products.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px"}}}} -->
<div class="wp-block-buttons" style="margin-top:30px"><!-- wp:button {"style":{"spacing":{"padding":{"left":"49px","right":"49px","top":"12px","bottom":"12px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","textTransform":"uppercase"},"border":{"width":"0px","style":"none"}},"className":"inline-img"} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;text-transform:uppercase"><a class="wp-block-button__link wp-element-button" style="border-style:none;border-width:0px;padding-top:12px;padding-right:49px;padding-bottom:12px;padding-left:49px"><?php echo esc_html__( 'SHOP NOW', 'glowess' ); ?><img class="wp-image-273" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/right-up-arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"68.5%"} -->
<div class="wp-block-column" style="flex-basis:68.5%"><!-- wp:image {"id":310,"width":"auto","height":"550px","aspectRatio":"16/9","scale":"cover","sizeSlug":"full","linkDestination":"none","className":"height-auto"} -->
<figure class="wp-block-image size-full is-resized height-auto"><img src="https://transvelo.github.io/glowess/assets/images/before-after.png" alt="" class="wp-image-310" style="aspect-ratio:16/9;object-fit:cover;width:auto;height:550px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
