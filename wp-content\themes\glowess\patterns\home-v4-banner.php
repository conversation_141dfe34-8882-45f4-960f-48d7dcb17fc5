<?php
/**
 * Title: Home v4 Banner
 * Slug: glowess/home-v4-banner
 * Categories: featured
 * Keywords: Home v4 Banner
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v4-Banner","categories":["featured"],"patternName":"glowess/home-v4-banner"},"align":"full","className":"home-v4-banner","style":{"spacing":{"padding":{"top":"var:preset|spacing|30"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull home-v4-banner" style="padding-top:var(--wp--preset--spacing--30)"><!-- wp:group {"align":"full","style":{"spacing":{"padding":{"right":"var:preset|spacing|40","left":"var:preset|spacing|40"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="padding-right:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--40)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"30px"}}}} -->
<div class="wp-block-columns"><!-- wp:column -->
<div class="wp-block-column"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v4-banner-1.png","id":17,"dimRatio":30,"overlayColor":"secondary","isUserOverlayColor":true,"minHeight":580,"style":{"border":{"radius":"16px"}},"layout":{"type":"default"}} -->
<div class="wp-block-cover" style="border-radius:16px;min-height:580px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-30 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-17" alt="" src="https://transvelo.github.io/glowess/assets/images/v4-banner-1.png" data-object-fit="cover"/><div class="wp-block-cover__inner-container"><!-- wp:paragraph {"align":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.75"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:16px;font-style:normal;font-weight:400;line-height:1.75;text-transform:uppercase"><?php echo esc_html__( 'clean beauty', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"textTransform":"capitalize","fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"margin":{"top":"20px","bottom":"0"}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:20px;margin-bottom:0;font-size:40px;font-style:normal;font-weight:500;line-height:1.2;text-transform:capitalize"><?php echo esc_html__( 'Orange', 'glowess' ); ?><br><?php echo esc_html__( 'Cleaning', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"24px","bottom":"0"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:24px;margin-bottom:0"><!-- wp:button {"textColor":"base","className":"flex before-inline-img","style":{"color":{"background":"#00000000"},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","width":"1px"},"left":{"width":"0px","style":"none"}},"spacing":{"padding":{"left":"0","right":"0","top":"0","bottom":"0"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}}} -->
<div class="wp-block-button flex before-inline-img"><a class="wp-block-button__link has-base-color has-text-color has-background has-link-color wp-element-button" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-width:1px;border-left-style:none;border-left-width:0px;background-color:#00000000;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""><?php echo esc_html__( 'shop now', 'glowess' ); ?></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v4-banner-2.png","id":18,"dimRatio":30,"overlayColor":"secondary","isUserOverlayColor":true,"minHeight":580,"style":{"border":{"radius":"16px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover" style="border-radius:16px;min-height:580px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-30 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-18" alt="" src="https://transvelo.github.io/glowess/assets/images/v4-banner-2.png" data-object-fit="cover"/><div class="wp-block-cover__inner-container"><!-- wp:paragraph {"align":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.75"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:16px;font-style:normal;font-weight:400;line-height:1.75;text-transform:uppercase"><?php echo esc_html__( 'new collection', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"margin":{"top":"20px","bottom":"0"}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:20px;margin-bottom:0;font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Dryness &', 'glowess' ); ?><br><?php echo esc_html__( 'Dehydration', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"24px","bottom":"0"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:24px;margin-bottom:0"><!-- wp:button {"textColor":"base","className":"flex before-inline-img","style":{"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","width":"1px"},"left":{"width":"0px","style":"none"}},"spacing":{"padding":{"left":"0","right":"0","top":"0","bottom":"0"}},"color":{"background":"#00000000"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}}} -->
<div class="wp-block-button flex before-inline-img"><a class="wp-block-button__link has-base-color has-text-color has-background has-link-color wp-element-button" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-width:1px;border-left-style:none;border-left-width:0px;background-color:#00000000;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""><?php echo esc_html__( 'shop now', 'glowess' ); ?></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v4-banner-3.png","id":19,"dimRatio":30,"overlayColor":"secondary","isUserOverlayColor":true,"minHeight":580,"style":{"border":{"radius":"16px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover" style="border-radius:16px;min-height:580px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-30 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-19" alt="" src="https://transvelo.github.io/glowess/assets/images/v4-banner-3.png" data-object-fit="cover"/><div class="wp-block-cover__inner-container"><!-- wp:paragraph {"align":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.75"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:16px;font-style:normal;font-weight:400;line-height:1.75;text-transform:uppercase"><?php echo esc_html__( 'skincare', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"margin":{"top":"20px","bottom":"0"}}},"textColor":"base"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:20px;margin-bottom:0;font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Intensive Glow', 'glowess' ); ?><br><?php echo esc_html__( 'C+ Serum', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"24px","bottom":"0"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:24px;margin-bottom:0"><!-- wp:button {"textColor":"base","className":"flex before-inline-img","style":{"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","width":"1px"},"left":{"width":"0px","style":"none"}},"spacing":{"padding":{"left":"0","right":"0","top":"0","bottom":"0"}},"color":{"background":"#00000000"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}}} -->
<div class="wp-block-button flex before-inline-img"><a class="wp-block-button__link has-base-color has-text-color has-background has-link-color wp-element-button" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-width:1px;border-left-style:none;border-left-width:0px;background-color:#00000000;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""><?php echo esc_html__( 'shop now', 'glowess' ); ?></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

