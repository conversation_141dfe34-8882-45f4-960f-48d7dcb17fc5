/*
################
* === UTILITY STYLE  ===
################
*/

.flex {
    display: flex;
}

.list-none {
    list-style: none;
    padding-left: 0;
}

.overflow-hidden {
    overflow: hidden;
}

.clear-after {
    &::after {
        content: "";
        display: block;
        clear: both;
    }
}

.f-shrink-0 {
    flex-shrink: 0;
}

.object-cover img {
  object-fit: cover;
}

.d-none {
    display: none;
}

@media (min-width:1024px) {
    .d-lg-block {
        display: block !important;
    }

    .d-lg-none {
        display: none !important;
    }
}

.relative {
    position:relative;
}

.absolute {
    position: absolute;
}

.stretched-link,
.stretched-link a {
    display: block;
}

.stretched-link:before,
.stretched-link a::before {
    position: absolute;
    inset: 0;
    content: "";
}

.stretched-link {
    &,a {
        display: block;

        &::before {
            position: absolute;
            inset: 0;
            content: "";
        }
    }
}

.h-auto {
	height: auto;
}

.grid {
    display: grid;
}

.grid-span-full {
    grid-column: 1/-1;
    grid-row: 1/-1;
}

.mt-auto {
    margin-top: auto !important;
}

.z-1 {
    z-index: 1;
}

.z-2 {
    z-index: 2;
}

.align-self-stretch {
    align-self: stretch;
}

.mt-0 {
    margin-top: 0;
}

.ml-auto {
    margin-left: auto !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.mr-auto {
    margin-right: auto !important;
}

.ml-0 {
    margin-left: 0 !important;
}

.mb-auto {
    margin-bottom: auto;
}

.w-100 {
    width: 100% !important;
}

.swipe {
    flex-wrap: nowrap !important;

    @media (max-width:767px) {
        overflow-x: scroll;

        &.wp-block-columns:not(.is-not-stacked-on-mobile)>.wp-block-column {
            flex-basis: 80% !important;
            flex-shrink: 0;
        }
    }
}

.ms-auto {
    margin-left: auto !important;
}

@media (min-width: 1024px) {
    .d-lg-block {
        display: block !important;
    }
}

@media (min-width: 1200px) {
    .d-xl-block {
        display: block !important;
    }
}