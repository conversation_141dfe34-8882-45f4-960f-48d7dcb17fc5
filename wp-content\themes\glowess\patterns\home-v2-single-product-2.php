<?php
/**
 * Title: Home v2 Single Product-2
 * Slug: glowess/home-v2-single-product-2
 * Categories: featured
 * Keywords: Home v2 Single Product-2
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v2-single-product-2"},"align":"wide","className":"v2-single-product-2","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"var:preset|spacing|30"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignwide v2-single-product-2" style="padding-top:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--30)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"top":"35px","left":"18px"},"padding":{"top":"18px","bottom":"16px"}}}} -->
<div class="wp-block-columns" style="padding-top:18px;padding-bottom:16px"><!-- wp:column {"width":"68%"} -->
<div class="wp-block-column" style="flex-basis:68%"><!-- wp:columns {"verticalAlignment":"center","isStackedOnMobile":false,"className":"flex-sm-wrap","style":{"spacing":{"blockGap":{"top":"29px","left":"22px"}}}} -->
<div class="wp-block-columns are-vertically-aligned-center is-not-stacked-on-mobile flex-sm-wrap"><!-- wp:column {"verticalAlignment":"center","width":"30%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:30%"><!-- wp:image {"id":621,"width":"auto","height":"330px","sizeSlug":"full","linkDestination":"none","className":"height-auto"} -->
<figure class="wp-block-image size-full is-resized height-auto"><img src="https://transvelo.github.io/glowess/assets/images/single-6.png" alt="" class="wp-image-621" style="width:auto;height:330px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":""} -->
<div class="wp-block-column is-vertically-aligned-center"><!-- wp:image {"id":641,"width":"auto","height":"600px","sizeSlug":"full","linkDestination":"none","className":"height-auto"} -->
<figure class="wp-block-image size-full is-resized height-auto"><img src="https://transvelo.github.io/glowess/assets/images/single-5.png" alt="" class="wp-image-641" style="width:auto;height:600px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":"32.7%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:32.7%"><!-- wp:group {"layout":{"type":"constrained","contentSize":"360px","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:heading {"level":6,"style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary"} -->
<h6 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:16px;font-style:normal;font-weight:400;line-height:1.7"><?php echo esc_html__( 'SPECIAL OFFER ', 'glowess' ); ?><mark style="background-color:rgba(0, 0, 0, 0)" class="has-inline-color has-primary-color"><?php echo esc_html__( '-20%', 'glowess' ); ?></mark></h6>
<!-- /wp:heading -->

<!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Mountain Pine Bath Oil', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Made using clean, non-toxic ingredients, our products are designed for everyone.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:spacer {"height":"88px"} -->
<div style="height:88px" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"22px"}}}} -->
<div class="wp-block-buttons" style="margin-top:22px"><!-- wp:button {"className":"inline-img","style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"border":{"width":"0px","style":"none"},"spacing":{"padding":{"left":"0","right":"0","top":"12px","bottom":"12px"}}}} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.7"><a class="wp-block-button__link wp-element-button" href="#" style="border-style:none;border-width:0px;padding-top:12px;padding-right:0;padding-bottom:12px;padding-left:0">SHOP SALE<img class="wp-image-198" style="width: 14px;" src="https://glowess.madrasthemes.com/wp-content/themes/glowess/assets/images/arrow-white.svg" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
