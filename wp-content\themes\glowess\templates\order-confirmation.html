<!-- wp:template-part {"slug":"header","theme":"glowess"} /-->

<!-- wp:group {"tagName":"main","metadata":{"name":"Order complete"},"style":{"spacing":{"margin":{"top":"var:preset|spacing|40","bottom":"var:preset|spacing|50"},"padding":{"top":"15px"}}},"layout":{"type":"constrained","contentSize":"924px"}} -->
<main class="wp-block-group" style="margin-top:var(--wp--preset--spacing--40);margin-bottom:var(--wp--preset--spacing--50);padding-top:15px">

    <!-- wp:pattern {"slug":"glowess/order-confirmation-title"} /-->


    <!-- wp:woocommerce/order-confirmation-status {"align":"","fontSize":"medium","style":{"typography":{"lineHeight":"1.7"},"spacing":{"margin":{"top":"10px","bottom":"16px"}}}} /-->

    
    <!-- wp:woocommerce/order-confirmation-summary {"align":"","style":{"spacing":{"padding":{"top":"var:preset|spacing|10"},"margin":{"top":"0","bottom":"var:preset|spacing|30"}}}} /-->
    
    <!-- wp:woocommerce/order-confirmation-totals-wrapper -->
    <!-- wp:heading {"level":3,"style":{"typography":{"fontSize":"18px","fontStyle":"normal","fontWeight":"500","textTransform":"uppercase"},"spacing":{"margin":{"bottom":"9px","top":"34px"}}},"fontFamily":"body"} -->
    <h3 class="wp-block-heading has-body-font-family" style="margin-top:34px;margin-bottom:9px;font-size:18px;font-style:normal;font-weight:500;text-transform:uppercase">Your Order</h3>
    <!-- /wp:heading -->
    
    <!-- wp:woocommerce/order-confirmation-totals {"lock":{"remove":true},"style":{"spacing":{"margin":{"top":"0","bottom":"17px"}}}} /-->
    <!-- /wp:woocommerce/order-confirmation-totals-wrapper -->
    
    <!-- wp:woocommerce/order-confirmation-downloads-wrapper -->
    <!-- wp:heading {"level":3,"style":{"typography":{"fontSize":"24px"}}} -->
    <h3 class="wp-block-heading" style="font-size:24px">Downloads</h3>
    <!-- /wp:heading -->
    
    <!-- wp:woocommerce/order-confirmation-downloads {"lock":{"remove":true}} /-->
    <!-- /wp:woocommerce/order-confirmation-downloads-wrapper -->
    
    <!-- wp:columns {"className":"woocommerce-order-confirmation-address-wrapper","style":{"spacing":{"blockGap":{"top":"30px"}}}} -->
    <div class="wp-block-columns woocommerce-order-confirmation-address-wrapper"><!-- wp:column -->
    <div class="wp-block-column"><!-- wp:woocommerce/order-confirmation-shipping-wrapper {"align":"wide"} -->
    <!-- wp:heading {"level":3,"style":{"typography":{"fontSize":"24px"}}} -->
    <h3 class="wp-block-heading" style="font-size:24px">Shipping address</h3>
    <!-- /wp:heading -->
    
    <!-- wp:woocommerce/order-confirmation-shipping-address {"lock":{"remove":true}} /-->
    <!-- /wp:woocommerce/order-confirmation-shipping-wrapper --></div>
    <!-- /wp:column -->
    
    <!-- wp:column -->
    <div class="wp-block-column"><!-- wp:woocommerce/order-confirmation-billing-wrapper {"align":"wide"} -->
    <!-- wp:heading {"level":3,"style":{"typography":{"fontSize":"24px"}}} -->
    <h3 class="wp-block-heading" style="font-size:24px">Billing address</h3>
    <!-- /wp:heading -->
    
    <!-- wp:woocommerce/order-confirmation-billing-address {"lock":{"remove":true}} /-->
    <!-- /wp:woocommerce/order-confirmation-billing-wrapper --></div>
    <!-- /wp:column --></div>
    <!-- /wp:columns -->
    
    <!-- wp:woocommerce/order-confirmation-additional-fields-wrapper -->
    <!-- wp:pattern {"slug":"woocommerce/order-confirmation-additional-fields-heading"} /-->
    
    <!-- wp:woocommerce/order-confirmation-additional-fields /-->
    <!-- /wp:woocommerce/order-confirmation-additional-fields-wrapper -->
    
    <!-- wp:woocommerce/order-confirmation-additional-information {"align":""} /--></main>
    <!-- /wp:group -->
    
    <!-- wp:template-part {"slug":"footer","theme":"glowess"} /-->