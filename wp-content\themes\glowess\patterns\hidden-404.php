<?php
/**
 * Title: Hidden 404
 * Slug: glowess/hidden-404
 * Inserter: no
 *
 * @package glowess
 */

?>
<!-- wp:group {"tagName":"main","metadata":{"name":"404"},"layout":{"type":"constrained"}} -->
<main class="wp-block-group"><!-- wp:group {"tagName":"main","layout":{"type":"constrained"}} -->
<main class="wp-block-group"><!-- wp:group {"tagName":"main","style":{"spacing":{"padding":{"top":"0"}}},"layout":{"type":"constrained"}} -->
<main class="wp-block-group" style="padding-top:0"><!-- wp:spacer {"height":"120px"} -->
<div style="height:120px" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer -->

<!-- wp:group {"tagName":"main","align":"wide","style":{"spacing":{"margin":{"top":"0","bottom":"0"},"padding":{"top":"var:preset|spacing|80"}},"dimensions":{"minHeight":"px"}},"layout":{"type":"constrained"}} -->
<main class="wp-block-group alignwide" style="min-height:px;margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--80)"><!-- wp:group {"align":"wide","style":{"spacing":{"margin":{"top":"0","bottom":"0px"}}},"layout":{"type":"constrained","contentSize":""}} -->
<div class="wp-block-group alignwide" style="margin-top:0;margin-bottom:0px"><!-- wp:group {"align":"wide","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide"><!-- wp:heading {"textAlign":"center","level":1,"align":"wide","style":{"typography":{"lineHeight":"1","fontStyle":"normal","fontWeight":"700","fontSize":"23rem"}},"textColor":"secondary","fontFamily":"body"} -->
<h1 class="wp-block-heading alignwide has-text-align-center has-secondary-color has-text-color has-body-font-family" style="font-size:23rem;font-style:normal;font-weight:700;line-height:1"><?php echo esc_html__( '404', 'glowess' ); ?></h1>
<!-- /wp:heading --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"align":"wide","style":{"spacing":{"margin":{"top":"8px"},"padding":{"top":"24px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide" style="margin-top:8px;padding-top:24px"><!-- wp:heading {"textAlign":"center","style":{"spacing":{"margin":{"bottom":"16px"}},"typography":{"fontSize":"48px","fontStyle":"normal","fontWeight":"600","lineHeight":"1.5","textTransform":"capitalize"}},"fontFamily":"body"} -->
<h2 class="wp-block-heading has-text-align-center has-body-font-family" style="margin-bottom:16px;font-size:48px;font-style:normal;font-weight:600;line-height:1.5;text-transform:capitalize"><?php echo esc_html__( 'Page not found', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"spacing":{"margin":{"top":"0px","bottom":"var:preset|spacing|40"}},"typography":{"fontSize":"18px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"}}} -->
<p class="has-text-align-center" style="margin-top:0px;margin-bottom:var(--wp--preset--spacing--40);font-size:18px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( 'The page you are looking for was moved, removed or might never existed.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons"><!-- wp:button {"textAlign":"center","style":{"spacing":{"padding":{"left":"32px","right":"32px","top":"16px","bottom":"16px"}},"border":{"radius":"50px"}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-text-align-center wp-element-button" href="<?php echo esc_url( home_url( '/' ) ); ?>" style="border-radius:50px;padding-top:16px;padding-right:32px;padding-bottom:16px;padding-left:32px"><?php echo esc_html__( 'Go to homepage', 'glowess' ); ?></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></main>
<!-- /wp:group -->

<!-- wp:spacer {"height":"120px"} -->
<div style="height:120px" aria-hidden="true" class="wp-block-spacer"></div>
<!-- /wp:spacer --></main>
<!-- /wp:group --></main>
<!-- /wp:group --></main>
<!-- /wp:group -->
