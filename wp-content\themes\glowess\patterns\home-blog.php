<?php
/**
 * Title: Home Blog
 * Slug: glowess/home-blog
 * Categories: posts
 * Keywords: Blog
 * Block Types: core/blog
 *
 * @package  glowess
 */

?>
<!-- wp:group {"metadata":{"name":"Blog Grid"},"align":"wide","className":"glowess-blog-grid","style":{"spacing":{"margin":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|60"},"padding":{"top":"var:preset|spacing|30"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide glowess-blog-grid" style="margin-top:var(--wp--preset--spacing--20);margin-bottom:var(--wp--preset--spacing--60);padding-top:var(--wp--preset--spacing--30)"><!-- wp:group {"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|40"},"padding":{"top":"5px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-bottom:var(--wp--preset--spacing--40);padding-top:5px"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"spacing":{"margin":{"bottom":"10px"}}},"textColor":"secondary","fontSize":"x-large","fontFamily":"heading"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color has-heading-font-family has-x-large-font-size" style="margin-bottom:10px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Blog List', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"0","bottom":"0"}}},"textColor":"contrast","fontSize":"medium","fontFamily":"body"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color has-body-font-family has-medium-font-size" style="margin-top:0;margin-bottom:0;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Stay up to date, get inspired, read tips and watch success stories with our blog.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:tag-cloud {"numberOfTags":4,"smallestFontSize":"15px","largestFontSize":"15px","align":"center","className":"grid-tags","style":{"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"bottom":"20px","top":"10px"},"padding":{"top":"7px"}}}} /-->

<!-- wp:group {"align":"wide","style":{"spacing":{"margin":{"bottom":"0"},"padding":{"bottom":"var:preset|spacing|10"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide" style="margin-bottom:0;padding-bottom:var(--wp--preset--spacing--10)"><!-- wp:query {"queryId":22,"query":{"perPage":"9","pages":0,"offset":0,"postType":"post","order":"desc","orderBy":"date","author":"","search":"","exclude":[],"sticky":"exclude","inherit":false},"align":"wide","layout":{"type":"constrained"}} -->
<div class="wp-block-query alignwide"><!-- wp:post-template {"align":"wide","style":{"spacing":{"blockGap":"28px"}},"layout":{"type":"grid","columnCount":3}} -->
<!-- wp:group {"style":{"spacing":{"blockGap":"0","margin":{"bottom":"0"},"padding":{"top":"0","bottom":"0"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="margin-bottom:0;padding-top:0;padding-bottom:0"><!-- wp:post-featured-image {"isLink":true,"style":{"border":{"radius":"0px","width":"0px","style":"none"},"spacing":{"margin":{"bottom":"20px"}}}} /-->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"370px","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:post-title {"isLink":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"spacing":{"margin":{"bottom":"12px"}}},"fontSize":"grande"} /--></div>
<!-- /wp:group -->

<!-- wp:post-date {"style":{"spacing":{"margin":{"bottom":"0"}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"1.9"}},"fontSize":"medium"} /--></div>
<!-- /wp:group -->
<!-- /wp:post-template -->

<!-- wp:query-pagination -->
<!-- wp:query-pagination-previous /-->

<!-- wp:query-pagination-numbers /-->

<!-- wp:query-pagination-next /-->
<!-- /wp:query-pagination -->

<!-- wp:query-no-results -->
<!-- wp:paragraph {"placeholder":"Add text or blocks that will display when a query returns no results."} -->
<p></p>
<!-- /wp:paragraph -->
<!-- /wp:query-no-results --></div>
<!-- /wp:query --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
