<?php
/**
 * Title: Home v2 Best Skin Banner
 * Slug: glowess/home-v2-best-skin-banner
 * Categories: featured
 * Keywords: Home v2 Best Skin Banner
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v2-best-skin-banner"},"align":"full","className":"v2-best-skin-banner","style":{"spacing":{"padding":{"right":"0px","left":"0px","top":"46px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull v2-best-skin-banner" style="padding-top:46px;padding-right:0px;padding-left:0px"><!-- wp:columns {"style":{"spacing":{"padding":{"top":"0px","bottom":"0px","left":"0px","right":"0px"},"blockGap":{"top":"0px","left":"0px"},"margin":{"top":"0px"}}},"backgroundColor":"bg-2"} -->
<div class="wp-block-columns has-bg-2-background-color has-background" style="margin-top:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><!-- wp:column {"width":"61.5%"} -->
<div class="wp-block-column" style="flex-basis:61.5%"><!-- wp:image {"id":26,"width":"auto","height":"750px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/best-skin-banner-1.png" alt="" class="wp-image-26" style="width:auto;height:750px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"top","style":{"spacing":{"padding":{"top":"8px"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:8px"><!-- wp:group {"style":{"spacing":{"padding":{"top":"25px","left":"25px","right":"25px","bottom":"25px"},"margin":{"top":"0"}}},"layout":{"type":"constrained","contentSize":"350px","wideSize":"350px"}} -->
<div class="wp-block-group" style="margin-top:0;padding-top:25px;padding-right:25px;padding-bottom:25px;padding-left:25px"><!-- wp:spacer {"height":"var:preset|spacing|40","className":"d-none d-xl-block","style":{"spacing":{"margin":{"top":"28px","bottom":"28px"}}}} -->
<div style="margin-top:28px;margin-bottom:28px;height:var(--wp--preset--spacing--40)" aria-hidden="true" class="wp-block-spacer d-none d-xl-block"></div>
<!-- /wp:spacer -->

<!-- wp:image {"id":262,"width":"auto","height":"108px","sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full is-resized"><img src="https://transvelo.github.io/glowess/assets/images/image-26.png" alt="" class="wp-image-262" style="width:auto;height:108px"/></figure>
<!-- /wp:image -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"spacing":{"padding":{"top":"13px","bottom":"2px"}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color has-heading-font-family" style="padding-top:13px;padding-bottom:2px;font-size:16px;font-style:normal;font-weight:400;line-height:1.7"><?php echo esc_html__( 'RISE AND SHINE', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Morning Routine Essentials', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"16px"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:16px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'This text helps you provide some more detail to the title above.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px","bottom":"30px"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:30px;margin-bottom:30px"><!-- wp:button {"textAlign":"center","className":"inline-img","style":{"spacing":{"padding":{"left":"49px","right":"49px","top":"12px","bottom":"12px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","textTransform":"uppercase"},"border":{"width":"0px","style":"none"}}} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;text-transform:uppercase"><a class="wp-block-button__link has-text-align-center wp-element-button" href="#" style="border-style:none;border-width:0px;padding-top:12px;padding-right:49px;padding-bottom:12px;padding-left:49px"><?php echo esc_html__( 'SHOP NOW', 'glowess' ); ?><img class="wp-image-273" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/right-up-arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
