<?php
/**
 * Title: Home v3 banner
 * Slug: glowess/home-v3-banner
 * Categories: featured
 * Keywords: Home v3 banner
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v3-banner"},"align":"wide","className":"v3-banner","style":{"spacing":{"padding":{"top":"var:preset|spacing|30"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignwide v3-banner" style="padding-top:var(--wp--preset--spacing--30)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"28px"},"padding":{"top":"13px"}}}} -->
<div class="wp-block-columns" style="padding-top:13px"><!-- wp:column {"width":"32%"} -->
<div class="wp-block-column" style="flex-basis:32%"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v3-banner.png","id":325,"dimRatio":20,"overlayColor":"secondary","isUserOverlayColor":true,"focalPoint":{"x":0.5,"y":0.34},"minHeight":350,"style":{"border":{"radius":"16px"},"spacing":{"blockGap":"var:preset|spacing|20"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover" style="border-radius:16px;min-height:350px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-20 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-325" alt="" src="https://transvelo.github.io/glowess/assets/images/v3-banner.png" style="object-position:50% 34%" data-object-fit="cover" data-object-position="50% 34%"/><div class="wp-block-cover__inner-container"><!-- wp:paragraph {"align":"center","placeholder":"Write title…","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"padding":{"bottom":"5px"}}},"textColor":"base","fontSize":"default","fontFamily":"heading"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color has-heading-font-family has-default-font-size" style="padding-bottom:5px;font-style:normal;font-weight:400"><?php echo esc_html__( 'IN-STORE AND ONLINE', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"style":{"spacing":{"blockGap":"15px"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"center","verticalAlignment":"bottom"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"padding":{"top":"0","bottom":"0"},"margin":{"top":"0","bottom":"0"}},"typography":{"lineHeight":"0.8"}},"textColor":"base","fontSize":"xx-large","fontFamily":"heading"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color has-heading-font-family has-xx-large-font-size" style="margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;line-height:0.8"><?php echo esc_html__( '20', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.17"}},"fontSize":"large","fontFamily":"heading"} -->
<p class="has-text-align-center has-heading-font-family has-large-font-size" style="font-style:normal;font-weight:500;line-height:1.17"><?php echo esc_html__( '% OFF', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"lineHeight":"2"}},"textColor":"base","fontSize":"medium","fontFamily":"body"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color has-body-font-family has-medium-font-size" style="line-height:2"><?php echo esc_html__( 'Save Extra on Sale Items', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"66.66%"} -->
<div class="wp-block-column" style="flex-basis:66.66%"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/v3-banner-2.png","id":326,"dimRatio":20,"overlayColor":"secondary","isUserOverlayColor":true,"focalPoint":{"x":0.5,"y":0.4},"minHeight":350,"style":{"border":{"radius":"16px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-cover" style="border-radius:16px;min-height:350px"><span aria-hidden="true" class="wp-block-cover__background has-secondary-background-color has-background-dim-20 has-background-dim"></span><img class="wp-block-cover__image-background wp-image-326" alt="" src="https://transvelo.github.io/glowess/assets/images/v3-banner-2.png" style="object-position:50% 40%" data-object-fit="cover" data-object-position="50% 40%"/><div class="wp-block-cover__inner-container"><!-- wp:paragraph {"align":"center","placeholder":"Write title…","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"1.75"}},"textColor":"base","fontSize":"default","fontFamily":"heading"} -->
<p class="has-text-align-center has-base-color has-text-color has-link-color has-heading-font-family has-default-font-size" style="font-style:normal;font-weight:400;line-height:1.75"><?php echo esc_html__( 'DERMATOLOGIST DEVELOPED', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"60px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"fontFamily":"heading"} -->
<p class="has-text-align-center has-heading-font-family" style="font-size:60px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Everyday Beauty', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"13px","bottom":"0"}}},"fontSize":"medium"} -->
<p class="has-text-align-center has-medium-font-size" style="margin-top:13px;margin-bottom:0;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Buy One Cleanser and get another one 50% off', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
