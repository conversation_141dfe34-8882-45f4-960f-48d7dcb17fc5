# Change Log for Post Meta Data Manager

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)

## 1.1.0 - 2022-09-12
### Fixed
* Fix Woocommerce Product terms add edit page not saving default fields data.

## 1.0.2 - 2021-08-31
### Added
- Move all html code into seprate file.
- Added meta box for user edit and listings of all user meta keys.
- Edit & delete user meta key's value.
- Searching, sorting, pagination for listings.

## 1.0.1 - 2021-08-26
### Fixed
* Added support for long string meta values from input type text to textarea tag.

## 1.0.0 - 2021-08-16 - Initial Release
### Added
- Show All meta data of posts, pages, Custom Post Type into backend details page.