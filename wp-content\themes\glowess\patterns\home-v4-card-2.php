<?php
/**
 * Title: Home v4 Card 2
 * Slug: glowess/home-v4-card-2
 * Categories: featured
 * Keywords: Home v4 Card
 *
 * @package glowess
 */

?>


<!-- wp:group {"metadata":{"name":"Home-v4-card-2","categories":["featured"],"patternName":"glowess/home-v4-card-2"},"align":"full","className":"home-v4-card-2","style":{"spacing":{"padding":{"right":"0","left":"0","top":"var:preset|spacing|10","bottom":"var:preset|spacing|30"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull home-v4-card-2" style="padding-top:var(--wp--preset--spacing--10);padding-right:0;padding-bottom:var(--wp--preset--spacing--30);padding-left:0"><!-- wp:group {"align":"full","style":{"spacing":{"padding":{"right":"var:preset|spacing|40","left":"var:preset|spacing|40"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="padding-right:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--40)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"28px"},"padding":{"right":"0","left":"0"}}}} -->
<div class="wp-block-columns" style="padding-right:0;padding-left:0"><!-- wp:column -->
<div class="wp-block-column"><!-- wp:image {"id":33,"sizeSlug":"full","linkDestination":"none","className":"card-banner-image","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image size-full has-custom-border card-banner-image"><img src="https://transvelo.github.io/glowess/assets/images/home-v4-card-banner-3.png" alt="" class="wp-image-33" style="border-radius:16px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:group {"style":{"border":{"radius":"16px"},"spacing":{"padding":{"top":"var:preset|spacing|60","bottom":"var:preset|spacing|60"}}},"backgroundColor":"bg-1","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-bg-1-background-color has-background" style="border-radius:16px;padding-top:var(--wp--preset--spacing--60);padding-bottom:var(--wp--preset--spacing--60)"><!-- wp:group {"style":{"spacing":{"margin":{"top":"0px","bottom":"15px"},"padding":{"top":"var:preset|spacing|40","bottom":"var:preset|spacing|40"}}},"layout":{"type":"constrained","contentSize":"500px"}} -->
<div class="wp-block-group" style="margin-top:0px;margin-bottom:15px;padding-top:var(--wp--preset--spacing--40);padding-bottom:var(--wp--preset--spacing--40)"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"60px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:60px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Face your skin with us', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"13px"},"padding":{"right":"20px","left":"20px"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:13px;padding-right:20px;padding-left:20px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Skincare made with the world’s finest plant oils and absolutes.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px","bottom":"0"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:30px;margin-bottom:0"><!-- wp:button {"className":"flex inline-img","style":{"border":{"radius":"8px"}}} -->
<div class="wp-block-button flex inline-img"><a class="wp-block-button__link wp-element-button" style="border-radius:8px"><?php echo esc_html__( 'explore now', 'glowess' ); ?><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
