{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Pickup": ["<PERSON><PERSON><PERSON> alma"], "Admin settings\u0004General": ["<PERSON><PERSON>"], "By enabling Local Pickup with more valuable features for your store, it's recommended that you remove the legacy Local Pickup option from your <a>shipping zones</a>.": ["Mağazanızdaki daha değerli özelliklerle Mağazadan Teslimi etkinleştirerek <a>gö<PERSON><PERSON> bölgelerinizden</a> eski Mağazadan Teslim seçeneğini kaldırmanız önerilir."], "If local pickup is enabled, the \"Hide shipping costs until an address is entered\" setting will be ignored.": ["Mağazadan te<PERSON>lim <PERSON>, \"<PERSON><PERSON> adres girilene kadar gönderim ücretini gizle\" ayarı göz ardı edilir."], "Add pickup location": ["<PERSON><PERSON><PERSON> alma konumu ekle"], "When you add a pickup location, it will appear here.": ["Bir teslim alma konumu eklediğinizde burada görünür."], "Define pickup locations for your customers to choose from during checkout.": ["Müşterilerinizin alışverişi tamamlama sırasında seçebileceği teslim alma konumları tanımlayın."], "Pickup locations": ["Teslim alma konumları"], "Edit pickup location": ["<PERSON><PERSON><PERSON> alma konumunu dü<PERSON>le"], "Pickup details": ["Teslim alma bilgileri"], "Location name": ["Konum adı"], "By default, the local pickup shipping method is free.": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazadan teslim gönderim yöntemi ücretsizdir."], "Add a price for customers who choose local pickup": ["Mağazadan teslimi seçen müşteriler için bir fiyat e<PERSON>in"], "Local pickup title is required": ["Mağazadan teslim başlığı gerekli"], "This is the shipping method title shown to customers.": ["<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>, müşterilere gösterilen gönderim yönte<PERSON> başlığıdır."], "When enabled, local pickup will appear as an option on the block based checkout.": ["Mağazadan teslim etkinleştirildiğinde blok tabanlı ödemede bir seçenek olarak görünür."], "Enable or disable local pickup on your store, and define costs. Local pickup is only available from the block checkout.": ["Mağazanızda mağazadan teslimi etkinleştirin veya devre dışı bırakın ve maliyetleri tanımlayın. Mağazadan teslim, yalnızca blok ödemede mevcuttur."], "Local Pickup settings have been saved.": ["Mağazadan teslim ayarları kaydedildi."], "Delete location": ["<PERSON><PERSON><PERSON> sil"], "A Location title is required": ["Konum başlığı gerekli"], "View checkout page": ["<PERSON><PERSON>me sayfasını gör<PERSON>üle"], "Not taxable": ["Vergiye tabi de<PERSON>"], "Optional cost to charge for local pickup.": ["Mağazadan teslimat için isteğe bağlı ücret maliyeti."], "If a cost is defined, this controls if taxes are applied to that cost.": ["Bir ücret tanımlanırsa bu, bu ücrete vergi uygulanıp uygulanmayacağını kontrol eder."], "Pickup location": ["<PERSON><PERSON><PERSON> alma konumu"], "Done": ["<PERSON><PERSON>"], "Country / State": ["Ülke / Şehir"], "Title": ["Başlık"], "Enable local pickup": ["Mağazadan teslimatı etkinleştir"], "Postcode / ZIP": ["Posta kodu"], "Save changes": ["Değişiklikleri kaydet"], "Free": ["Ücretsiz"], "Cost": ["Maliyet"], "Enabled": ["<PERSON><PERSON><PERSON>"], "Learn more": ["<PERSON>ha fazla bilgi edin"], "Taxable": ["Vergilendirilebilir"], "Address": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "Cancel": ["Vazgeç"], "Taxes": ["<PERSON>er<PERSON><PERSON>"], "City": ["İlçe / Semt"], "State": ["Şehir"], "Edit": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/wc-shipping-method-pickup-location.js"}}