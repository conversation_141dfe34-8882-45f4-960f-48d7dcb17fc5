<?php
/* ==================== city-products.php ==================== */
$selected_city = glowess_get_selected_city();
if ($selected_city):
    $categories = glowess_get_city_categories($selected_city);
    
    foreach ($categories as $category):
        $category_products = glowess_get_city_products($selected_city, array(
            'posts_per_page' => 12,
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category->term_id
                )
            )
        ));
        
        if ($category_products->have_posts()):
?>
<section id="category-<?php echo $category->term_id; ?>" class="glowess-city-product-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?php echo esc_html($category->name); ?></h2>
            <p class="section-description"><?php echo $category->description; ?></p>
            <a href="<?php echo get_term_link($category); ?>" class="view-all-link">
                <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <span class="arrow">→</span>
            </a>
        </div>
        
        <div class="glowess-city-products columns-4">
            <?php while ($category_products->have_posts()): 
                $category_products->the_post();
                global $product;
                
                $product_id = get_the_ID();
                $product_image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'woocommerce_thumbnail');
                $product_gallery = $product->get_gallery_image_ids();
                $second_image = !empty($product_gallery) ? wp_get_attachment_image_src($product_gallery[0], 'woocommerce_thumbnail') : null;
                $regular_price = $product->get_regular_price();
                $sale_price = $product->get_sale_price();
                $discount_percentage = $regular_price && $sale_price ? round((($regular_price - $sale_price) / $regular_price) * 100) : 0;
                $rating = $product->get_average_rating();
                $review_count = $product->get_review_count();
            ?>
                <div class="glowess-city-product-card">
                    <div class="glowess-city-product-image">
                        <a href="<?php the_permalink(); ?>">
                            <img src="<?php echo esc_url($product_image[0]); ?>" 
                                 alt="<?php the_title_attribute(); ?>"
                                 <?php if ($second_image): ?>
                                 data-hover-src="<?php echo esc_url($second_image[0]); ?>"
                                 <?php endif; ?>
                                 loading="lazy">
                        </a>
                        
                        <?php if ($discount_percentage > 0): ?>
                            <div class="glowess-city-discount-badge">
                                -%<?php echo $discount_percentage; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="product-actions">
                            <button class="glowess-city-wishlist" 
                                    data-product-id="<?php echo $product_id; ?>"
                                    title="Favorilere Ekle">
                                ♡
                            </button>
                            <button class="glowess-city-quick-view" 
                                    data-product-id="<?php echo $product_id; ?>"
                                    title="Hızlı İncele">
                                👁
                            </button>
                        </div>
                    </div>
                    
                    <div class="glowess-city-product-info">
                        <h3 class="glowess-city-product-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        
                        <?php if ($rating > 0): ?>
                            <div class="glowess-city-product-rating">
                                <div class="stars">
                                    <?php
                                    for ($i = 1; $i <= 5; $i++) {
                                        echo $i <= $rating ? '★' : '☆';
                                    }
                                    ?>
                                </div>
                                <span class="count"><?php echo $rating; ?> (<?php echo $review_count; ?>)</span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="glowess-city-product-price">
                            <?php if ($sale_price): ?>
                                <span class="glowess-city-old-price"><?php echo wc_price($regular_price); ?></span>
                                <span class="glowess-city-new-price"><?php echo wc_price($sale_price); ?></span>
                            <?php else: ?>
                                <span class="glowess-city-new-price"><?php echo $product->get_price_html(); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <button class="glowess-city-add-to-cart" 
                                data-product-id="<?php echo $product_id; ?>"
                                data-quantity="1">
                            Sepete Ekle
                        </button>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
        
        <?php if ($category_products->found_posts > 12): ?>
            <div class="section-footer">
                <a href="<?php echo get_term_link($category); ?>" class="glowess-city-button secondary">
                    Daha Fazla <?php echo esc_html($category->name); ?> Ürünü Gör
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php 
        endif;
        wp_reset_postdata();
    endforeach;
endif;
?>