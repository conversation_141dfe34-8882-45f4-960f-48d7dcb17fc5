# Post Meta Data Manager
Post Meta Data Manager

# Description
**Tired of changing values of hidden post meta or custom post meta through database or coding?**
**Then you have landed on Correct wordpress plugin.**

**Need some help to view your post meta or custom fields?**

Post Meta Data Manager plugin displays all post meta or custom fields keys and their values in a metabox at the bottom of post editing.

Awesome thing is there are no plugin settings.
You just need to install and activate the plugin by yourself.

**Boom! Added User Meta Manager**
* Now you can handle all user meta fields values from user edit page.

**Added Taxonomoies Meta Manager**
* Now you can handle all term or taxonomies meta fields values from term edit page.

# Stunning Features:

* You can search & replace that values from edit pages and posts.
* You can sort key column by ascending and descending orders.
* There is no long list of the meta keys. You will see in 10 items in a page.
* Works with any custom post types.

* Do you have an idea or want to some enhancement then just contact me.
* [Feature requests](http://www.wpexpertplugins.com/contact-us/)

# Contributing

* report bugs and suggest features on [GitHub](https://github.com/gandhihitesh9/post-meta-data-manager) or [WordPress Support Forum](https://wordpress.org/support/plugin/post-meta-data-manager)

# Installation

The plugin is simple to install:

 * Download post-meta-data-manager.zip
 * Unzip
 * Upload post-meta-data-manager directory to your /wp-content/plugins directory
 * Activate the "Post Meta Data Manager" list plugin through the 'Plugins' menu in WordPress.