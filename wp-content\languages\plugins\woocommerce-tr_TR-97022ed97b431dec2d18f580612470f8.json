{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "An error has prevented the block from being updated.": ["<PERSON><PERSON> hata, blokun gü<PERSON><PERSON><PERSON><PERSON> engelledi."], "Align the last block to the bottom.": ["Son bloku en alta hizalayın."], "Align the last block to the bottom": ["Son bloku en alta hizalayın"], "Stock status \"%s\" hidden.": ["Stok durumu \"%s\" gizlendi."], "Stock status \"%s\" visible.": ["Stok durumu \"%s\" görünüyor."], "Edit selected attribute": ["Seçili niteliği düzenle"], "%d term": ["%d terim", "%d terim"], "%1$s, has %2$d term": ["%1$s, %2$d terime sahip", "%1$s, %2$d terime sahip"], "%1$s, has %2$d product": ["%1$s, %2$d <PERSON><PERSON><PERSON><PERSON> sahip", "%1$s, %2$d <PERSON><PERSON><PERSON><PERSON> sahip"], "Loading…": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…"], "The last inner block will follow other content.": ["Son i<PERSON> blok diğer içeriği izler."], "The following error was returned": ["Aşağı<PERSON><PERSON> hata geri dö<PERSON>ü<PERSON>üldü"], "The following error was returned from the API": ["API’den aşağıdaki hata döndürüldü"], "Search results updated.": ["<PERSON>ma sonuçları güncellendi."], "%d item selected": ["%d <PERSON><PERSON><PERSON> seçildi", "%d <PERSON><PERSON><PERSON> seçildi"], "Search for items": ["Ögeleri ara"], "No results for %s": ["%s için sonuç bulu<PERSON>ı"], "No items found.": ["<PERSON>rün bulunamadı."], "Clear all selected items": ["Seçili tüm ögeleri temizle"], "Clear all": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle"], "Remove %s": ["%s kaldır"], "Display a grid of products from your selected attributes.": ["Seçtiğiniz niteliklerden oluşan bir ızgara görüntüleyin."], "All selected attributes": ["<PERSON><PERSON><PERSON> se<PERSON>"], "Any selected attributes": ["<PERSON><PERSON><PERSON> se<PERSON>"], "Pick at least two attributes to use this setting.": ["Bu ayarı kullanmak için en az iki nitelik seçin."], "Product attribute search results updated.": ["<PERSON><PERSON>ün niteliği arama sonuçları güncellendi."], "%d attribute selected": ["%d nitelik seçildi", "%d nitelik seçildi"], "Search for product attributes": ["<PERSON><PERSON><PERSON><PERSON> nitelik<PERSON> ara"], "Your store doesn't have any product attributes.": ["Mağazanızda herhangi bir ürün niteliği yok."], "Clear all product attributes": ["<PERSON>ü<PERSON> ürün niteliklerini temizle"], "Order By": ["S<PERSON>rala"], "Done": ["<PERSON><PERSON>"], "Layout": ["<PERSON><PERSON><PERSON><PERSON>"], "Rows": ["Satırlar"], "Columns": ["<PERSON><PERSON><PERSON><PERSON>"], "Add to Cart button": ["Sepete Ekle butonu"], "Filter by Product Attribute": ["<PERSON><PERSON><PERSON><PERSON> göre filtrele"], "Showing Products by Attribute block preview.": ["Niteliğe göre ürünler blok önizlemesini gösterir."], "Products by Attribute": ["Niteliğe göre ü<PERSON>ü<PERSON>ler"], "Menu Order": ["<PERSON><PERSON> sırası"], "Title - alphabetical": ["Başlık - alfabetik sıraya göre"], "Sales - most first": ["Satış - en çok satanlar başta olacak şekilde"], "Rating - highest first": ["Puan - en yüksek puana sahip olanlar başta olacak şekilde"], "Price - high to low": ["Fiyat - yüksekten düşüğe"], "Price - low to high": ["Fiyat - düşükten yükseğe"], "Order products by": ["Ürünleri şuna göre sırala:"], "Display products matching": ["Eşleşen ürünleri görüntüle"], "Product rating": ["<PERSON>rün puanı"], "Product price": ["<PERSON><PERSON><PERSON><PERSON>tı"], "Product title": ["<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı"], "Newness - newest first": ["<PERSON>ni olma du<PERSON>u - en yeni<PERSON> ba<PERSON><PERSON> olacak şekilde"], "Filter by stock status": ["Stok durumuna göre filtrele"], "Product image": ["<PERSON><PERSON><PERSON><PERSON>"], "%d product": ["%d <PERSON><PERSON><PERSON><PERSON>", "%d <PERSON><PERSON><PERSON><PERSON>"], "Content": ["İçerik"]}}, "comment": {"reference": "assets/client/blocks/products-by-attribute.js"}}