<?php
/**
 * Glowess OCDI Class
 *
 * @package glowess
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'Glowess_OCDI' ) ) :
	/**
	 * The one click demo import class.
	 */
	class Glowess_OCDI {

		/**
		 * Stores the assets URL.
		 *
		 * @var string
		 */
		public $assets_url;

		/**
		 * Stores the demo URL.
		 *
		 * @var string
		 */
		public $demo_url;

		/**
		 * Instantiate the class.
		 */
		public function __construct() {

			$this->assets_url = 'https://transvelo.github.io/glowess/assets/';
			$this->demo_url   = 'https://glowess.madrasthemes.com/';

			add_filter( 'pt-ocdi/confirmation_dialog_options', array( $this, 'confirmation_dialog_options' ), 10, 1 );

			add_action( 'pt-ocdi/import_files', array( $this, 'import_files' ) );

			add_action( 'pt-ocdi/after_import', array( $this, 'import_wpforms' ) );
			add_action( 'pt-ocdi/enable_wp_customize_save_hooks', '__return_true' );
			add_filter( 'pt-ocdi/disable_pt_branding', '__return_true' );

			add_filter( 'ocdi/register_plugins', array( $this, 'register_plugins' ) );
			add_action( 'pt-ocdi/after_import', array( $this, 'set_site_options' ) );
		}

		/**
		 * Set site options.
		 *
		 * @param array $selected_import The import that just ran.
		 */
		public function set_site_options( $selected_import ) {
			if ( isset( $selected_import['set_pages'] ) && $selected_import['set_pages'] ) {
				$front_page_title  = isset( $selected_import['front_page_title'] ) ? $selected_import['front_page_title'] : 'Home';
				$front_page_id     = get_page_by_title( $front_page_title );
				$blog_page_id      = get_page_by_title( 'Blog' );
				$shop_page_id      = get_page_by_title( 'Shop' );
				$cart_page_id      = get_page_by_title( 'Cart' );
				$checkout_page_id  = get_page_by_title( 'Checkout' );
				$myaccount_page_id = get_page_by_title( 'My account' );
				$wishlist_page_id  = get_page_by_title( 'Wishlist' );

				update_option( 'show_on_front', 'page' );
				update_option( 'page_on_front', $front_page_id->ID );
				update_option( 'page_for_posts', $blog_page_id->ID );
				update_option( 'woocommerce_shop_page_id', $shop_page_id->ID );
				update_option( 'woocommerce_cart_page_id', $cart_page_id->ID );
				update_option( 'woocommerce_checkout_page_id', $checkout_page_id->ID );
				update_option( 'woocommerce_myaccount_page_id', $myaccount_page_id->ID );
				update_option( 'yith_wcwl_wishlist_page_id', $wishlist_page_id->ID );
			}
		}

		/**
		 * Register plugins in OCDI.
		 *
		 * @param array $plugins List of plugins.
		 */
		public function register_plugins( $plugins ) {
			global $glowess;

			$profile = 'default';

			if ( isset( $_GET['import'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				$demo_id = absint( $_GET['import'] ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				switch ( $demo_id ) {
					case 0:
						$profile = 'default';
						break;
				}
			}

			return $glowess->plugin_install->get_demo_plugins( $profile );
		}

		/**
		 * Confirmation dialog box options.
		 *
		 * @param  array $options The dialog options.
		 * @return array
		 */
		public function confirmation_dialog_options( $options ) {
			return array_merge(
				$options,
				array(
					'width' => 410,
				)
			);
		}

		/**
		 * Import WPForms.
		 */
		public function import_wpforms() {
			$ocdi_host = 'https://transvelo.github.io/glowess';
			$dd_url    = $ocdi_host . '/assets/wpforms/';

			if ( ! function_exists( 'wpforms' ) || get_option( 'glowess_wpforms_imported', false ) ) {
				return;
			}

			$wpform_file_url = $dd_url . 'wpforms.json';
			$response        = wp_remote_get( $wpform_file_url );

			if ( is_wp_error( $response ) || 200 !== $response['response']['code'] ) {
				return;
			}

			$form_json = wp_remote_retrieve_body( $response );

			if ( is_wp_error( $form_json ) ) {
				return;
			}

			$forms = json_decode( $form_json, true );

			foreach ( $forms as $form_data ) {
				$form_title = $form_data['settings']['form_title'];

				if ( ! empty( $form_data['id'] ) ) {
					$form_content = array(
						'field_id' => '0',
						'settings' => array(
							'form_title' => sanitize_text_field( $form_title ),
							'form_desc'  => '',
						),
					);

					// Merge args and create the form.
					$form = array(
						'import_id'    => (int) $form_data['id'],
						'post_title'   => esc_html( $form_title ),
						'post_status'  => 'publish',
						'post_type'    => 'wpforms',
						'post_content' => wpforms_encode( $form_content ),
					);

					$form_id = wp_insert_post( $form );
				} else {
					// Create initial form to get the form ID.
					$form_id = wpforms()->form->add( $form_title );
				}

				if ( empty( $form_id ) ) {
					continue;
				}

				$form_data['id'] = $form_id;
				// Save the form data to the new form.
				wpforms()->form->update( $form_id, $form_data );
			}

			update_option( 'glowess_wpforms_imported', true );
		}

		/**
		 * Import Files from each demo.
		 *
		 * @return array $imports List of imports.
		 */
		public function import_files() {
			$ocdi_host   = 'https://transvelo.github.io/glowess';
			$dd_url      = $ocdi_host . '/assets/xml/';
			$widget_url  = $ocdi_host . '/assets/widgets/';
			$preview_url = $ocdi_host . '/assets/images/';
			/* translators: %1$s - The demo name. %2$s - Minutes. */
			$notice  = esc_html__( 'This demo will import %1$s. It may take %2$s', 'glowess' );
			$notice .= '<br><br>' . esc_html__( 'Menus, Widgets and Settings will not be imported. Content imported already will not be imported.', 'glowess' );
			$notice .= '<br><br>' . esc_html__( 'Alternatively, you can import this template directly into your page via Edit with Elementor.', 'glowess' );

			/**
			 * Hook to override imports
			 *
			 * @since 1.0.0
			 */
			return apply_filters(
				'glowess_ocdi_files_args',
				array(
					array(
						'import_file_name'         => 'Glowess',
						'import_file_url'          => $dd_url . 'full.xml',
						'import_preview_image_url' => $preview_url . 'preview.png',
						'preview_url'              => 'https://glowess.madrasthemes.com/',
						'plugin_profile'           => 'default',
						'uploads_dir'              => 'https://glowess.madrasthemes.com/wp-content/uploads',
						'set_pages'                => true,
						'front_page_title'         => 'Home v1',
					),
				)
			);
		}
	}

endif;


return new Glowess_OCDI();
