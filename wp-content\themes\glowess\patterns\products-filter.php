<?php
/**
 * Title: Products Filter
 * Slug: glowess/products-filter
 * Categories: query
 * Keywords: filter
 * Block Types: core/template-part/products-filter
 *
 * @package glowess
 */

?>


<!-- wp:group {"align":"wide","className":" filter-content","style":{"spacing":{"blockGap":"34px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide filter-content"><!-- wp:group {"style":{"border":{"radius":"20px"},"spacing":{"blockGap":"47px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="border-radius:20px"><!-- wp:details {"showContent":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"grande"} -->
<details class="wp-block-details has-secondary-color has-text-color has-link-color has-grande-font-size" style="font-style:normal;font-weight:500;line-height:1.2" open><summary>Product Type</summary><!-- wp:group {"style":{"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"20px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:20px;font-style:normal;font-weight:400"><!-- wp:woocommerce/filter-wrapper {"filterType":"attribute-filter","heading":"Filter by attribute"} -->
<div class="wp-block-woocommerce-filter-wrapper"><!-- wp:woocommerce/attribute-filter {"attributeId":3,"heading":"","lock":{"remove":true}} -->
<div class="wp-block-woocommerce-attribute-filter is-loading"></div>
<!-- /wp:woocommerce/attribute-filter --></div>
<!-- /wp:woocommerce/filter-wrapper --></div>
<!-- /wp:group --></details>
<!-- /wp:details -->

<!-- wp:details {"showContent":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"grande"} -->
<details class="wp-block-details has-secondary-color has-text-color has-link-color has-grande-font-size" style="font-style:normal;font-weight:500;line-height:1.2" open><summary>Availablity</summary><!-- wp:group {"style":{"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"20px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:20px;font-style:normal;font-weight:400"><!-- wp:woocommerce/filter-wrapper {"filterType":"attribute-filter","heading":"Filter by attribute"} -->
<div class="wp-block-woocommerce-filter-wrapper"><!-- wp:woocommerce/attribute-filter {"attributeId":4,"showCounts":true,"heading":"","lock":{"remove":true}} -->
<div class="wp-block-woocommerce-attribute-filter is-loading"></div>
<!-- /wp:woocommerce/attribute-filter --></div>
<!-- /wp:woocommerce/filter-wrapper --></div>
<!-- /wp:group --></details>
<!-- /wp:details -->

<!-- wp:details {"showContent":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"grande"} -->
<details class="wp-block-details has-secondary-color has-text-color has-link-color has-grande-font-size" style="font-style:normal;font-weight:500;line-height:1.2" open><summary>Price</summary><!-- wp:group {"style":{"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"20px"},"padding":{"right":"2px","left":"2px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:20px;padding-right:2px;padding-left:2px;font-style:normal;font-weight:400"><!-- wp:woocommerce/filter-wrapper {"filterType":"price-filter","heading":"Filter by price"} -->
<div class="wp-block-woocommerce-filter-wrapper"><!-- wp:woocommerce/price-filter {"showInputFields":false,"showFilterButton":true,"heading":"","lock":{"remove":true}} -->
<div class="wp-block-woocommerce-price-filter is-loading"><span aria-hidden="true" class="wc-block-product-categories__placeholder"></span></div>
<!-- /wp:woocommerce/price-filter --></div>
<!-- /wp:woocommerce/filter-wrapper --></div>
<!-- /wp:group --></details>
<!-- /wp:details -->

<!-- wp:details {"showContent":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"grande"} -->
<details class="wp-block-details has-secondary-color has-text-color has-link-color has-grande-font-size" style="font-style:normal;font-weight:500;line-height:1.2" open><summary>Color</summary><!-- wp:group {"style":{"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"25px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:25px;font-style:normal;font-weight:400"><!-- wp:woocommerce/filter-wrapper {"filterType":"attribute-filter","heading":"Filter by attribute","className":"filter-color"} -->
<div class="wp-block-woocommerce-filter-wrapper filter-color"><!-- wp:woocommerce/attribute-filter {"attributeId":1,"heading":"","lock":{"remove":true}} -->
<div class="wp-block-woocommerce-attribute-filter is-loading"></div>
<!-- /wp:woocommerce/attribute-filter --></div>
<!-- /wp:woocommerce/filter-wrapper --></div>
<!-- /wp:group --></details>
<!-- /wp:details -->

<!-- wp:details {"showContent":true,"style":{"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"grande"} -->
<details class="wp-block-details has-secondary-color has-text-color has-link-color has-grande-font-size" style="font-style:normal;font-weight:500;line-height:1.2" open><summary>Size</summary><!-- wp:group {"style":{"typography":{"fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"20px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:20px;font-style:normal;font-weight:400"><!-- wp:woocommerce/filter-wrapper {"filterType":"attribute-filter","heading":"Filter by attribute"} -->
<div class="wp-block-woocommerce-filter-wrapper"><!-- wp:woocommerce/attribute-filter {"attributeId":2,"selectType":"single","heading":"","lock":{"remove":true}} -->
<div class="wp-block-woocommerce-attribute-filter is-loading"></div>
<!-- /wp:woocommerce/attribute-filter --></div>
<!-- /wp:woocommerce/filter-wrapper --></div>
<!-- /wp:group --></details>
<!-- /wp:details --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
