<?php
/**
 * Title: Shop Best Skin Banner
 * Slug: glowess/shop-best-skin-banner
 * Categories: featured
 * Keywords: Shop Best Skin Banner
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Shop-best-skin-banners"},"align":"wide","className":"shop-best-skin","style":{"spacing":{"padding":{"top":"144px","bottom":"62px","left":"0px","right":"0px"},"margin":{"top":"0px","bottom":"0px"}}},"backgroundColor":"base","layout":{"type":"default"}} -->
<div class="wp-block-group alignwide shop-best-skin has-base-background-color has-background" style="margin-top:0px;margin-bottom:0px;padding-top:144px;padding-right:0px;padding-bottom:62px;padding-left:0px"><!-- wp:columns {"style":{"color":{"background":"#f2ebe3"},"spacing":{"padding":{"top":"0px","bottom":"0px","left":"0px","right":"0px"},"margin":{"top":"0px","bottom":"0px"},"blockGap":{"top":"0px","left":"0px"}}}} -->
<div class="wp-block-columns has-background" style="background-color:#f2ebe3;margin-top:0px;margin-bottom:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:group {"className":"content-wrap","style":{"spacing":{"padding":{"top":"109px","left":"25px","right":"25px"},"margin":{"top":"var:preset|spacing|30"}}},"layout":{"type":"constrained","contentSize":"430px"}} -->
<div class="wp-block-group content-wrap" style="margin-top:var(--wp--preset--spacing--30);padding-top:109px;padding-right:25px;padding-left:25px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"spacing":{"padding":{"top":"13px","bottom":"2px"}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color has-heading-font-family" style="padding-top:13px;padding-bottom:2px;font-size:16px;font-style:normal;font-weight:400;line-height:1.7"><?php echo esc_html__( 'POWDER HIGHLIGHTER', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"450px","justifyContent":"center"}} -->
<div class="wp-block-group"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Ingredients you will love', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"16px"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:16px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Apply a small amount of toner onto a cotton pad after cleansing. Gently swipe the pad across your face and neck, avoiding the eye area. Let the toner absorb into your skin before moisturizing. Use daily for best results.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px","bottom":"30px"}}},"layout":{"type":"flex","justifyContent":"center","verticalAlignment":"stretch"}} -->
<div class="wp-block-buttons" style="margin-top:30px;margin-bottom:30px"><!-- wp:button {"textAlign":"center","className":"inline-img","style":{"spacing":{"padding":{"left":"49px","right":"49px","top":"12px","bottom":"12px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","textTransform":"uppercase","lineHeight":"1.5"},"border":{"width":"0px","style":"none"}}} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5;text-transform:uppercase"><a class="wp-block-button__link has-text-align-center wp-element-button" href="#" style="border-style:none;border-width:0px;padding-top:12px;padding-right:49px;padding-bottom:12px;padding-left:49px"><?php echo esc_html__( 'Show More', 'glowess' ); ?>   <img class="wp-image-273" style="width: 14px; margin-left:10px;margin-top:2px" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":46,"width":"auto","height":"700px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/single-1.png" alt="" class="wp-image-46" style="width:auto;height:700px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns -->

<!-- wp:columns {"style":{"color":{"background":"#f2ebe3"},"spacing":{"padding":{"top":"0px","bottom":"0px","left":"0px","right":"0px"},"margin":{"top":"0px","bottom":"0px"},"blockGap":{"top":"0px","left":"0px"}}}} -->
<div class="wp-block-columns has-background" style="background-color:#f2ebe3;margin-top:0px;margin-bottom:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":47,"width":"auto","height":"700px","sizeSlug":"full","linkDestination":"none","className":"height-img"} -->
<figure class="wp-block-image size-full is-resized height-img"><img src="https://transvelo.github.io/glowess/assets/images/single-2.png" alt="" class="wp-image-47" style="width:auto;height:700px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:group {"className":"content-wrap","style":{"spacing":{"padding":{"top":"140px","left":"25px","right":"25px"},"margin":{"top":"var:preset|spacing|30"}}},"layout":{"type":"constrained","contentSize":"450px"}} -->
<div class="wp-block-group content-wrap" style="margin-top:var(--wp--preset--spacing--30);padding-top:140px;padding-right:25px;padding-left:25px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.7"},"spacing":{"padding":{"top":"13px","bottom":"2px"}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color has-heading-font-family" style="padding-top:13px;padding-bottom:2px;font-size:16px;font-style:normal;font-weight:400;line-height:1.7"><?php echo esc_html__( 'OUR STORY', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"450px","justifyContent":"center"}} -->
<div class="wp-block-group"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Clean beauty that works', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"19px"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:19px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Skincare and makeup that go hand in hand to protect, nourish and heal your skin while highlighting your natural beauty.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px","bottom":"30px"}}},"layout":{"type":"flex","justifyContent":"center","verticalAlignment":"stretch"}} -->
<div class="wp-block-buttons" style="margin-top:30px;margin-bottom:30px"><!-- wp:button {"textAlign":"center","className":"inline-img","style":{"spacing":{"padding":{"left":"49px","right":"49px","top":"12px","bottom":"12px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","textTransform":"uppercase","lineHeight":"1.5"},"border":{"width":"0px","style":"none"}}} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5;text-transform:uppercase"><a class="wp-block-button__link has-text-align-center wp-element-button" href="#" style="border-style:none;border-width:0px;padding-top:12px;padding-right:49px;padding-bottom:12px;padding-left:49px"><?php echo esc_html__( 'Show More', 'glowess' ); ?>   <img class="wp-image-273" style="width: 14px; margin-left:10px;margin-top:2px" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->