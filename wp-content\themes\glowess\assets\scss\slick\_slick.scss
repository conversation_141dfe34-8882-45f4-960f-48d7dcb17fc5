/*
################
* === Slick Slider Style ===
################
*/
.slick-slider {
    position: relative;
    display: block !important;
    box-sizing: border-box;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list {
    position: relative;
    overflow: hidden;
    display: block;
    margin: 0;
    padding: 0;

    @media (min-width:600px) {
        margin: 0 -20px;
    }

    &:focus {
        outline: none;
    }

    &.dragging {
        cursor: pointer;
        cursor: hand;
    }
}
.slick-slider .slick-track,
.slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.slick-track {
    position: relative;
    left: 0;
    top: 0;
    display: block;
    margin-left: auto;
    margin-right: auto;

    &:before,
    &:after {
        content: "";
        display: table;
    }

    &:after {
        clear: both;
    }

    .slick-loading & {
        visibility: hidden;
    }
}

.slick-slide {
    float: left;
    height: auto;
    min-height: 1px;

    [dir="rtl"] & {
        float: right;
    }

    img {
        display: block;
    }

    &.slick-loading img {
        display: none;
    }

    &.dragging img {
        pointer-events: none;
    }

    .slick-initialized & {
        display: block;
    }

    .slick-loading & {
        visibility: hidden;
    }

    .slick-vertical & {
        display: block;
        height: auto;
        border: 1px solid transparent;
    }
}

.slick-arrow.slick-hidden {
    display: none;
}

.home-v1-hero,
.v2-hero,
.home-v4-hero {
    .slick-prev, .slick-next {
        top: auto;
        transform: none;
        margin: 0;
        bottom: 60px;

        &::before {
            width: 42px;
            height: 40px;
        }
    }

    .slick-list {
        margin: 0;

        .slick-slide > div {
            padding: 0;
        }
    }

    .slick-next {
        right: 60px;

        &::before {
            background-image: url('data:image/svg+xml,<svg width="42" height="40" viewBox="0 0 42 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 20H39.5675" stroke="white"/><path d="M31.2383 29.3714C33.565 24.2507 35.4083 22.101 40.0008 20.0142C35.3058 17.7033 33.4834 15.5457 31.2383 10.6289" stroke="white"/></svg>');
        }
    }

    .slick-prev {
        right: 122px;
        left: auto;

        &::before {
            background-image: url('data:image/svg+xml,<svg width="42" height="40" viewBox="0 0 42 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42 20H2.4325" stroke="white"/><path d="M10.7617 29.3714C8.43502 24.2507 6.59172 22.101 1.99922 20.0142C6.69422 17.7033 8.51662 15.5457 10.7617 10.6289" stroke="white"/></svg>');
        }
    }
}


// Default Variables

// Slick icon entity codes outputs the following
// "\2190" outputs ascii character "←"
// "\2192" outputs ascii character "→"
// "\2022" outputs ascii character "•"

$slick-font-path: "./assets/fonts/bootstrap-icons/" !default;
$slick-font-family: "bootstrap-icons" !default;
$slick-loader-path: "./assets/images/" !default;
$slick-arrow-color: var(--wp--preset--color--gray-300) !default;
$slick-dot-color: black !default;
$slick-dot-color-active: $slick-dot-color !default;
$slick-prev-character: "\F284" !default;
$slick-next-character: "\F285" !default;
$slick-dot-character: "\F309" !default;
$slick-dot-size: 6px !default;
$slick-opacity-default: 1 !default;
$slick-opacity-on-hover: 1 !default;
$slick-opacity-not-active: 1 !default;

@function slick-image-url($url) {
    @if function-exists(image-url) {
        @return image-url($url);
    }
    @else {
        @return url($slick-loader-path + $url);
    }
}

@function slick-font-url($url) {
    @if function-exists(font-url) {
        @return font-url($url);
    }
    @else {
        @return url($slick-font-path + $url);
    }
}

/* Slider */

.slick-list {
    .slick-loading & {
        background: var(--wp--preset--color--base) slick-image-url("ajax-loader.gif") center center no-repeat;
    }
}

/* Icons */
@if $slick-font-family == "bootstrap-icons" {
    @font-face {
        font-family: "bootstrap-icons";
        src: url("assets/fonts/bootstrap-icons/bootstrap-icons.woff2?8d200481aa7f02a2d63a331fc782cfaf") format("woff2"), url("assets/fonts/bootstrap-icons/bootstrap-icons.woff?8d200481aa7f02a2d63a331fc782cfaf") format("woff");
        font-weight: normal;
        font-style: normal;
    }
}

/* Arrows */

.slick-prev,
.slick-next {
    position: absolute;
    display: block;
    line-height: 0px;
    font-size: 0px;
    cursor: pointer;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
    padding: 0;
    outline: none;
    z-index: 1;
    background-color: transparent;
    border-width: 0;
    
    &:hover, &:focus {
        outline: none;
    }

    &.slick-disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    
    &:before {
        line-height: 1;
        opacity: $slick-opacity-default;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        content: " ";
        width: 26px;
        height: 25px;
        display: block;
        background-repeat: no-repeat;
    }
}

.slick-prev {
    left: -25px;

    @media (min-width:1200px) {
        left: 25px;
    }

    @media (min-width:1400px) {
        left: -45px;
    }

    [dir="rtl"] & {
        left: auto;
        right: -25px;

        @media (min-width:1200px) {
            right: 25px;
        }
    
        @media (min-width:1400px) {
            right: -45px;
        }
    }
    
    &:before {
        //content: $slick-prev-character;
        background-image: url('data:image/svg+xml,<svg width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 11H0.505832" stroke="%23131C19"/><path d="M5.662 16.8012C4.22166 13.6313 3.08057 12.3005 0.237598 11.0087C3.14403 9.57812 4.27218 8.24246 5.662 5.19873" stroke="%23131C19"/></svg>');

        [dir="rtl"] & {
            background-image: url('data:image/svg+xml,<svg width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M-1 11H23.4942" stroke="%23131C19"/><path d="M18.338 16.8012C19.7783 13.6313 20.9194 12.3005 23.7624 11.0087C20.856 9.57812 19.7278 8.24246 18.338 5.19873" stroke="%23131C19"/></svg>');
        }
    }
}

.slick-next {
    right: -25px;

    @media (min-width:1200px) {
        right: 25px;
    }

    @media (min-width:1400px) {
        right: -45px;
    }

    [dir="rtl"] & {
        left: -25px;
        right: auto;

        @media (min-width:1200px) {
            left: 25px;
        }
    
        @media (min-width:1400px) {
            left: -45px;
        }
    }

    &:before {
        //content: $slick-next-character;
        background-image: url('data:image/svg+xml,<svg width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M-1 11H23.4942" stroke="%23131C19"/><path d="M18.338 16.8012C19.7783 13.6313 20.9194 12.3005 23.7624 11.0087C20.856 9.57812 19.7278 8.24246 18.338 5.19873" stroke="%23131C19"/></svg>');

        [dir="rtl"] & {
            background-image: url('data:image/svg+xml,<svg width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25 11H0.505832" stroke="%23131C19"/><path d="M5.662 16.8012C4.22166 13.6313 3.08057 12.3005 0.237598 11.0087C3.14403 9.57812 4.27218 8.24246 5.662 5.19873" stroke="%23131C19"/></svg>');
        }
    }
}

.slick-nav-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 23px;
    margin-top: clamp(1.25rem, 2.237vw - 0.185rem, 2rem);

    .slick-next {
        order: 1;
    }

    .slick-prev,
    .slick-next {
        position: relative;
        left: 0;
        right: 0;
        transform: none;
    }
}

.slick-nav-group {
    .slick-slider {
        > .slick-prev,
        > .slick-next,
        > .slick-dots {
            opacity: 0;
        }
    }
}

/* Dots */
.slick-dotted {
    text-align: center;
}

.slick-dots {
    list-style: none;
    display: inline-flex;
    text-align: center;
    padding: 0;
    margin: 0;
    background-color: var(--wp--preset--color--bg-3);
    border-radius: 75px;

    li {
        display: inline-block;
        cursor: pointer;

        button {
            border: 0;
            display: block;
            height: 4px;
            width: 25px;
            outline: none;
            line-height: 0px;
            font-size: 0px;
            cursor: pointer;
            padding: 0;  

            &:hover, &:focus {
                outline: none;
            }
        }

        &.slick-active {            
            button {
                background-color: var(--wp--preset--color--secondary);
                border-radius: 12px;
            }
        }
    }
}

@keyframes fillRotateBorder {
    0% {
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        transform: translate(-50%, -50%) rotate(0deg);
    }
    25% {
        border-top-color: var(--wp--preset--color--base);
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        transform: translate(-50%, -50%) rotate(90deg);
    }
    50% {
        border-top-color: var(--wp--preset--color--base);
        border-right-color: var(--wp--preset--color--base);
        border-bottom-color: transparent;
        border-left-color: transparent;
        transform: translate(-50%, -50%) rotate(180deg);
    }
    75% {
        border-top-color: var(--wp--preset--color--base);
        border-right-color: var(--wp--preset--color--base);
        border-bottom-color: var(--wp--preset--color--base);
        border-left-color: transparent;
        transform: translate(-50%, -50%) rotate(270deg);
    }
    100% {
        border-top-color: var(--wp--preset--color--base);
        border-right-color: var(--wp--preset--color--base);
        border-bottom-color: var(--wp--preset--color--base);
        border-left-color: var(--wp--preset--color--base);
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.dark-dot .slick-dots {
    li button {
        background-color: var(--wp--preset--color--contrast);
    }

    li.slick-active button:before {
        border:1px solid var(--wp--preset--color--contrast);
    }
}

.slick-dots-vertical {
    .slick-dots {
        bottom: 30px;
        
        @media (min-width:1024px) {
            display: flex;
            flex-direction: column;
            gap: 25px;
            width: auto;
            top: 50%;
            transform: translateY(-50%);
            bottom: auto;
            right: calc(100% - 66px);
        }
    }
}

.wc-block-grid__products {
    .slick-list {
        margin: 0 -10px;

        @media (min-width:600px) {
            margin: 0 -14px;
        }
    }
}

.slick-slide {
    > div {
        padding: 20px 10px;

        @media (min-width:600px) {
            padding: 20px 14px;
        }

        .wc-block-grid__product-image {
            img {
               width: 100%;
            }
        }
    }
}
