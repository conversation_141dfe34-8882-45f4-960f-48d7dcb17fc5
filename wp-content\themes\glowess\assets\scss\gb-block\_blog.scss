.glowess-blog-grid {
	.wp-block-post-template {
		row-gap: 19px;
	}

	.grid-tags a {
		padding: 4px 20px;
		border: 1px solid var(--wp--preset--color--primary);
		color: var(--wp--preset--color--primary);
		&:not(:last-child) {
			margin-right: 17px;
			margin-bottom: 10px;
		}

		&:hover {
			background-color: var(--wp--preset--color--bg-5);
		}
	}

	.wp-block-query-pagination {
        margin-top: 21px;
    }
}

.gl-single-post { 
	.single-tags a{
		padding: 5px 20px;
	}

	ul li {
		margin-bottom: 12px;
	}

	blockquote {
		cite {
			text-transform: capitalize;
		}
		p {
			margin-bottom: 0;
		}
	}
}

.wp-block-post-title:hover a {
	text-decoration: underline;
}


table.wp-block-calendar,
table.wp-calendar-table {
	th,
	td {
		text-align: center;
	}
}
