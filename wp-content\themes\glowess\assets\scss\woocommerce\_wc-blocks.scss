/*
################
* === WooCommerce - Blocks style  ===
################
*/

.wc-block-components-button:not(.is-link) {
    transition: .3s ease-in-out;
}

.wc_payment_methods {
    list-style: none;
    padding: 0;
}

.wc-block-components-payment-method-label,
.wc_payment_method label {
    font-weight: 500;
    font-size: 17px;
}

.wp-block-woocommerce-customer-account {
    &[data-display-style="icon_only"] {
        span,
        svg {
            display: none;
        }

        a {
            display: flex;

            &::after {
                width: 14px;
                height: 18px;
                content: " ";
                display: block;
                background-image: url('data:image/svg+xml,<svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.99984 1.94855C5.45584 1.94855 4.20414 3.19221 4.20414 4.72634C4.20414 6.26045 5.45584 7.50409 6.99984 7.50409C8.54384 7.50409 9.79551 6.26045 9.79551 4.72634C9.79551 3.19221 8.54384 1.94855 6.99984 1.94855ZM2.91382 4.72634C2.91382 2.48415 4.74319 0.666504 6.99984 0.666504C9.25651 0.666504 11.0858 2.48415 11.0858 4.72634C11.0858 6.96851 9.25651 8.78617 6.99984 8.78617C4.74319 8.78617 2.91382 6.96851 2.91382 4.72634Z" fill="%23131C19"/><path fill-rule="evenodd" clip-rule="evenodd" d="M4.8884 11.7776C3.08529 11.7776 1.62358 13.2299 1.62358 15.0215C1.62358 15.1156 1.64088 15.182 1.65902 15.2207C1.67433 15.2533 1.68747 15.2626 1.69843 15.2685C2.19982 15.5396 3.6056 16.0511 6.99992 16.0511C10.3943 16.0511 11.8 15.5396 12.3014 15.2685C12.3123 15.2626 12.3255 15.2533 12.3408 15.2207C12.359 15.182 12.3763 15.1156 12.3763 15.0215C12.3763 13.2299 10.9146 11.7776 9.11142 11.7776H4.8884ZM0.333252 15.0215C0.333252 12.5219 2.37266 10.4956 4.8884 10.4956H9.11142C11.6272 10.4956 13.6666 12.5219 13.6666 15.0215C13.6666 15.4804 13.4994 16.0804 12.9182 16.3946C12.1482 16.8109 10.4965 17.3332 6.99992 17.3332C3.50337 17.3332 1.85164 16.8109 1.08169 16.3946C0.500444 16.0804 0.333252 15.4804 0.333252 15.0215Z" fill="%23131C19"/></svg>');
                //filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);
            }
        }
    }
}

.woocommerce-breadcrumb {
    span {
        padding: 0 6px;
    }

    i {
        color: var(--wp--preset--color--contrast);
    }
}

.wp-block-woocommerce-product-meta {
    strong {
        font-weight: normal;
    }
}

.wc-block-components-quantity-selector:after {
    display: none;
}

.quantity {
    display: inline-block;
}

.quantity,
.wc-block-components-quantity-selector {
    margin: 0 !important;
    border: 1px solid var(--wp--preset--color--gray-100);
    border-radius: 0;
    align-items: center;

    .qty-container {
        display: flex;
        align-items: center;
    }

    button,
    .wc-block-components-quantity-selector__button {
        width: 30px;
        height: 30px;
        background-color: transparent;
        border-width: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin: 0;
        border-radius: 20px;
        cursor: pointer;
        opacity: 1;
        color: var(--wp--preset--color--secondary);
    }

    .qty-minus,
    .wc-block-components-quantity-selector__button--minus {
        order: -1;

    }

    .qty,
    .wc-block-components-quantity-selector__input {
        margin-right: 0;
        min-width: 48px;
        padding: 0 !important;
        min-height: 50px;
        border-width: 0;
        font-size: 16px;
        color: var(--wp--preset--color--secondary);
        box-shadow: none !important;
        flex-grow: 1;
        text-align: center;
        width: 48px !important;
        font-family: var(--wp--preset--font-family--heading);
    }

    .qty-minus,
    .wc-block-components-quantity-selector__button--minus {
        margin-left: 9px;
    }

    .qty-plus,
    .wc-block-components-quantity-selector__button--plus {
        margin-right: 9px;
    }
}

.wc-block-components-quantity-selector {
    input.wc-block-components-quantity-selector__input {
        order: 1;
        font-weight: normal;
    }
}

.wc-block-add-to-cart-form {
    .quantity {
        .qty {
            margin-right: 0;
        }
    }
}

.theme-glowess.woocommerce {
    .star-rating {
        font-size: 10px;
        width: 8.2em;
        letter-spacing: 8px;
        margin: 0 0 17px;
        span::before {
            content: '\F586\F586\F586\F586\F586';
            font-family: "bootstrap-icons";
        }
    }
    .star-rating::before {
         content: '\F588\F588\F588\F588\F588';
        font-family: "bootstrap-icons";
    }
}



.woocommerce-message,
.woocommerce-error,
.woocommerce-notice--success,
.woocommerce-info,
.wc-block-components-notice-banner,
.woocommerce-noreviews,
.wp-block-query-no-results {
    padding: 14px 24px;
    border: 1px solid transparent;
    border-radius: 0;
    display: flex;
    gap: 10px;
    font-size: 14px;
    list-style: none;
    margin-bottom: 16px;
    flex-direction: column;
    align-items: flex-start;
    
    @media (min-width:1024px) {
        align-items: center;
        flex-direction: row;
    }

    a {
        color: inherit;
        text-decoration: underline;
        text-underline-offset: 2px;
        padding: 0;
        background-color: transparent;
        border: 0;

        &:hover {
            background-color: transparent;
            color: inherit;
            border-color: transparent;
        }
    }
}

.woocommerce-notice--success,
.woocommerce-message,
.wc-block-components-notice-banner.is-success {
    background-color: var(--wp--preset--color--success);
    color: var(--wp--preset--color--base);
    border-color: var(--wp--preset--color--success);

    >.wc-block-components-notice-banner__content .wc-forward {
        color: var(--wp--preset--color--base) !important;
        opacity:1;
    }
}

.woocommerce-account {
    .woocommerce-MyAccount-content {
        .woocommerce-error,
        .woocommerce-info,
        .woocommerce-message {
            justify-content: space-between;
            
            .button {
                float: right;
                text-decoration: none;
                text-underline-offset: 0;
                padding: 14px 28px;
                background-color: var(--wp--preset--color--primary);
            }
        }
    }
}

.woocommerce-error,
.wp-block-query-no-results,
.wc-block-components-notice-banner.is-error,
.woocommerce-noreviews {
    background-color: var(--wp--preset--color--danger);
    color: var(--wp--preset--color--base);
    border-color: var(--wp--preset--color--danger);

    >.wc-block-components-notice-banner__content .wc-forward {
        color: var(--wp--preset--color--base) !important;
        opacity:1;
    }
}

.woocommerce-error {    
    @media (min-width:1024px) {
        flex-direction: column;
        align-items: flex-start;
    }
}

.woocommerce-info,
.wc-block-components-notice-banner.is-info {
    background-color: #ecf2fa;
    color: #3972b6;
    border-color: var(--wp--preset--color--info);
    
    >.wc-block-components-notice-banner__content .wc-forward {
        color: #3972b6 !important;
        opacity:1;

        &.wp-element-button {
            color: #3972b6 !important;
            text-transform: capitalize;
            text-decoration: underline !important;
            text-underline-offset: 2px;
            font-weight: 500;
        }
    }

    @media (max-width:1024px) {
        svg {
            display: none;
        }
    }
}

.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward.wp-element-button {
    color: var(--wp--preset--color--base) !important;
    float: none;
    opacity: 1;
}

.page-numbers,
.wc-block-pagination,
.wp-block-query-pagination-numbers,
.wp-block-query-pagination,
.wp-block-comments-pagination-numbers,
.post-nav-links {
    display: flex;
    list-style: none;
    padding: 0;
    justify-content: center;
    margin: 2.5rem 0;
    flex-wrap: wrap;
    gap: 0;
}

.wp-block-query-pagination-numbers {
    border: 1px solid var(--wp--preset--color--gray-100);
    padding: 0 20px;
}

.post-nav-links {
    align-items: center;
}

.page-numbers,
.wc-block-pagination,
.wp-block-query-pagination-numbers,
.wp-block-query-pagination,
.wp-block-comments-pagination-numbers,
.wp-block-comments-pagination,
.post-nav-links {
    .page-numbers,
    .wc-block-pagination-page,
    .post-page-numbers {
        width: 30px;
        color: rgb(from var(--wp--preset--color--secondary) r g b / .5);

        &:is(:hover, :focus) {
            box-shadow: 0px 1px 0px 0px var(--wp--preset--color--secondary);
        }
    }

    .wp-block-query-pagination-next,
    .wp-block-comments-pagination-next,
    .wp-block-query-pagination-previous,
    .wp-block-comments-pagination-previous {
        border-style: solid;
        border-color: var(--wp--preset--color--gray-100);
    }

    .wp-block-query-pagination-previous,
    .wp-block-comments-pagination-previous {
        border-width: 1px 0 1px 1px;
    }

    .wp-block-query-pagination-next,
    .wp-block-comments-pagination-next {
        border-width: 1px 1px 1px 0;
    }

    .page-numbers,
    .wc-block-pagination-page,
    .post-page-numbers,
    .wp-block-query-pagination-next,
    .wp-block-query-pagination-previous,
    .wp-block-comments-pagination-next,
    .wp-block-comments-pagination-previous {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        height: 50px;
        font-size: 15px;
        line-height: 1.6;

        &:not(.current),
        &:not(.wc-block-pagination-page--active),
        &:not(.wc-block-components-pagination__page--active) {
            color: rgb(from var(--wp--preset--color--secondary) r g b / 0.5);
        }

        &.current,
        &.wc-block-pagination-page--active {
            &,
            &:is(:hover, :focus) {
                color: rgb(from var(--wp--preset--color--secondary) r g b / 1);
                box-shadow: 0px 1px 0px 0px var(--wp--preset--color--secondary);
            }
        }

        &:is(:hover, :focus) {
            color: rgb(from var(--wp--preset--color--secondary) r g b / 1);
        }
    }

    .wp-block-query-pagination-next,
    .wp-block-comments-pagination-next,
    .wp-block-query-pagination-previous,
    .wp-block-comments-pagination-previous {
        height: 52px;

        &:not([aria-label="Next Page"],[aria-label="Previous Page"]) {
            padding: 0 20px; 

            .is-arrow-chevron,
            .is-arrow-arrow {
                width: 25px;
            }
        }

        &,
        &:is(:hover, :focus) {
            border-color: var(--wp--preset--color--gray-100);
        }
        
        .is-arrow-chevron,
        .is-arrow-arrow {
            margin-right: 0;
            margin-left: 0;
            width: 49px;
            justify-content: center;
            display: flex;
        }

        .is-arrow-arrow {
            font-size: 0;

            &::before {
                display: block;
                content: " ";
                height: 18px;
                width: 18px;
                background-repeat: no-repeat;
                background-position: center;
            }
        }

        .is-arrow-chevron {
            font-size: 20px;
        }
    }

    .wp-block-query-pagination-previous,
    .wp-block-comments-pagination-previous {
        .is-arrow-arrow::before {
            background-image: url('data:image/svg+xml,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_101_2154)"><path d="M18 9H1.0425" stroke="%23131C19"/><path d="M4.61215 13.2171C3.615 10.9128 2.82501 9.9454 0.856795 9.00634C2.86894 7.96644 3.64997 6.99552 4.61215 4.78296" stroke="%23131C19"/></g><defs><clipPath id="clip0_101_2154"><rect width="18" height="18" fill="white" transform="matrix(-1 0 0 1 18 0)"/></clipPath></defs></svg>');
        }
    }

    .wp-block-query-pagination-next,
    .wp-block-comments-pagination-next {
        .is-arrow-arrow::before {
            background-image: url('data:image/svg+xml,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_101_2158)"><path d="M0 9H16.9575" stroke="%23131C19"/><path d="M13.3878 13.2171C14.385 10.9128 15.175 9.9454 17.1432 9.00634C15.1311 7.96644 14.35 6.99552 13.3878 4.78296" stroke="%23131C19"/></g><defs><clipPath id="clip0_101_2158"><rect width="18" height="18" fill="white"/></clipPath></defs></svg>');
        }
    }
}

.wc-block-pagination-page {
    &.wc-block-components-pagination__page:not(.wc-block-pagination-page--active ) {
        cursor:pointer !important;
    }
}

.home-v2-single-product {
    .wc-block-components-product-rating__stars {
        margin-bottom: 0;
        color: var(--wp--preset--color--secondary);;
    }
    .wp-block-add-to-cart-form {
        margin-top: 0;
        .quantity {
            margin-right: 7px !important;
        }
        .single_add_to_cart_button {
            font-size: 15px;
            font-weight: 400;
            line-height: 26px;
            margin-top: 30px;
            @media (min-width:768px) {
                min-width: 308px !important;
            }
            &:after {
                margin-left: 10px;
                display: inline-block;
                vertical-align: middle;
                height: 23px;
                content: url('../../themes/glowess/assets/images/arrow-white.svg');
            }
        }
    }
}
