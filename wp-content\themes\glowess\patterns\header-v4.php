<?php
/**
 * Title: Header v4
 * Slug: glowess/header-v4
 * Categories: header
 * Block Types: core/template-part/header-v4
 *
 * @package  glowess
 */

?>
<!-- wp:group {"metadata":{"name":"Header v4"},"className":"header-v4","style":{"spacing":{"blockGap":"0","padding":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group header-v4" style="padding-top:0;padding-bottom:0"><!-- wp:group {"metadata":{"name":"Desktop Header"},"align":"full","className":"desktop-header","style":{"spacing":{"blockGap":"0","padding":{"top":"0px","bottom":"0px","left":"60px","right":"60px"}}},"layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group alignfull desktop-header" style="padding-top:0px;padding-right:60px;padding-bottom:0px;padding-left:60px"><!-- wp:group {"metadata":{"name":"Top bar"},"align":"full","style":{"spacing":{"padding":{"top":"10px","bottom":"10px","left":"0","right":"0"}},"dimensions":{"minHeight":"40px"}},"backgroundColor":"bg-1","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull has-bg-1-background-color has-background" style="min-height:40px;padding-top:10px;padding-right:0;padding-bottom:10px;padding-left:0"><!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"13px","fontStyle":"normal","fontWeight":"300"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:13px;font-style:normal;font-weight:300"><?php echo esc_html__( 'Free worldwide shipping for orders over $50', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"relative","style":{"spacing":{"padding":{"top":"19px","bottom":"21px","left":"0px","right":"0px"}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group relative" style="padding-top:19px;padding-right:0px;padding-bottom:21px;padding-left:0px"><!-- wp:group {"className":"cat-pri-nav","style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"blockGap":"32px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group cat-pri-nav"><!-- wp:site-title {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"layout":{"selfStretch":"fit","flexSize":null},"elements":{"link":{"color":{"text":"var:preset|color|primary"}}}},"textColor":"primary","fontSize":"x-large"} /-->

<!-- wp:navigation {"ref":163,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"catalog-menu","style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"blockGap":"30px"}},"fontFamily":"heading","layout":{"type":"flex","justifyContent":"left"}} /--></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"layout":{"selfStretch":"fill","flexSize":null},"spacing":{"blockGap":"30px"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"right"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":174,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"primary-menu","style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"blockGap":"30px"}},"fontFamily":"heading","layout":{"type":"flex","justifyContent":"left"}} /-->

<!-- wp:navigation {"ref":184,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"currency","style":{"typography":{"textTransform":"uppercase","fontSize":"13px"}}} /-->

<!-- wp:group {"style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"padding":{"right":"0","left":"0"},"blockGap":"34px"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"right"}} -->
<div class="wp-block-group" style="padding-right:0;padding-left:0"><!-- wp:image {"id":55,"width":"17px","sizeSlug":"full","linkDestination":"custom","className":"search-toggle","style":{"layout":{"selfStretch":"fit","flexSize":null}}} -->
<figure class="wp-block-image size-full is-resized search-toggle" id="showModalBtn"><a href="#"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/search.svg'; ?>" alt="" class="wp-image-55" style="width:17px"/></a></figure>
<!-- /wp:image -->

<!-- wp:woocommerce/customer-account {"displayStyle":"icon_only","iconClass":"wc-block-customer-account__account-icon"} /-->

<!-- wp:woocommerce/mini-cart {"style":{"typography":{"fontSize":"16px"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"metadata":{"name":"Mobile Header"},"align":"full","className":"mobile-header","style":{"spacing":{"padding":{"top":"var:preset|spacing|10","bottom":"var:preset|spacing|10","left":"16px","right":"16px"},"blockGap":"14px"},"border":{"bottom":{"color":"var:preset|color|gray-100","width":"1px"},"top":[],"right":[],"left":[]}},"layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group alignfull mobile-header" style="border-bottom-color:var(--wp--preset--color--gray-100);border-bottom-width:1px;padding-top:var(--wp--preset--spacing--10);padding-right:16px;padding-bottom:var(--wp--preset--spacing--10);padding-left:16px"><!-- wp:group {"align":"wide","layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide"><!-- wp:group {"style":{"spacing":{"blockGap":"12px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":174,"overlayMenu":"always","icon":"menu","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]}} /-->

<!-- wp:site-title {"style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"700","fontSize":"30px"}},"fontFamily":"heading"} /--></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"flex","flexWrap":"wrap"}} -->
<div class="wp-block-group"><!-- wp:image {"id":55,"width":"17px","sizeSlug":"full","linkDestination":"custom","className":"search-toggle","style":{"layout":{"selfStretch":"fit","flexSize":null}}} -->
<figure class="wp-block-image size-full is-resized search-toggle" id="showMModalBtn"><a href="#"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/search.svg'; ?>" alt="" class="wp-image-55" style="width:17px"/></a></figure>
<!-- /wp:image -->

<!-- wp:woocommerce/customer-account {"displayStyle":"icon_only","iconClass":"wc-block-customer-account__account-icon","style":{"typography":{"fontSize":"10px"},"layout":{"selfStretch":"fit","flexSize":null}}} /-->

<!-- wp:woocommerce/mini-cart {"style":{"typography":{"fontSize":"16px"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"metadata":{"name":"Search Block"},"align":"full","className":"search-box","style":{"spacing":{"padding":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group alignfull search-box" id="myModal" style="padding-top:0;padding-bottom:0"><!-- wp:group {"style":{"spacing":{"blockGap":"60px","padding":{"right":"var:preset|spacing|40","left":"var:preset|spacing|40","top":"59px","bottom":"60px"}},"layout":{"selfStretch":"fit","flexSize":null},"border":{"top":{"color":"var:preset|color|primary","width":"2px"},"right":[],"bottom":[],"left":[]}},"backgroundColor":"secondary","layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group has-secondary-background-color has-background" style="border-top-color:var(--wp--preset--color--primary);border-top-width:2px;padding-top:59px;padding-right:var(--wp--preset--spacing--40);padding-bottom:60px;padding-left:var(--wp--preset--spacing--40)"><!-- wp:search {"label":"Search","showLabel":false,"placeholder":"What are you looking for","widthUnit":"%","buttonText":"Search","buttonPosition":"button-inside","buttonUseIcon":true,"query":{"post_type":"product"},"style":{"border":{"radius":"0px","top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"#ffffff4d"},"left":{"width":"0px","style":"none"}},"color":{"background":"#ffffff00"}},"fontSize":"medium"} /-->

<!-- wp:group {"className":"search-cat-btn","style":{"spacing":{"blockGap":"10px"}},"layout":{"type":"flex","flexWrap":"wrap"}} -->
<div class="wp-block-group search-cat-btn"><!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Acne</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Acne &amp; Dark Spots</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Aging</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Best Sellers</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Bundles</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Cleansers</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Dryness</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Essential Oils</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Face</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Eyes</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Hand &amp; Body Care</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Lips</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"width":"1px"}},"borderColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-border-color has-base-border-color" style="border-width:1px"><!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"8px","bottom":"8px","left":"20px","right":"20px"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"jost"} -->
<p class="has-base-color has-text-color has-link-color has-jost-font-family" style="padding-top:8px;padding-right:20px;padding-bottom:8px;padding-left:20px"><a href="#">Makeup</a></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
