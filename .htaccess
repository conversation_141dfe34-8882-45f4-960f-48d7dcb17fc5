
# BEGIN WordPress
# "BEGIN WordPress" ve "END WordPress" arası<PERSON><PERSON> y<PERSON> (satırlar)
# dinamik olarak oluşturulmuştur ve yalnızca WordPress süzgeçleri ile düzenlenmelidir.
# Bu imler arasındaki yönergelerde yapılan değişikliklerin üzerine yazılır.
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /wordpress2/
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /wordpress2/index.php [L]
</IfModule>

# END WordPress