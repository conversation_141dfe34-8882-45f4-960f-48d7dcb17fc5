/*
################
* === Cart Style ===
################
*/
.woocommerce-cart,
.woocommerce-checkout,
.woocommerce-account {
    h1.wp-block-post-title {
        font-size: clamp(1.5rem, 0.3571rem + 1.7857vw, 2.5rem);
    }
}

// .woocommerce-cart {
//     .is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull):not(.wp-block-cover)) {
//         max-width: 1400px;
//     }
// }

.wc-block-cart-item__remove-link,
.wc-block-components-product-metadata__description p {
    color: var(--wp--preset--color--secondary);
}

table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__quantity .wc-block-cart-item__remove-link {
    font-size: 12px;
    margin-top: 10px;
}

body {
    &.woocommerce-cart  .woocommerce,
    .wp-block-woocommerce-cart:not(.block-editor-block-list__block) .wc-block-cart {
        row-gap: 40px;

        @media (min-width:1024px) {
            display: grid;
            column-gap: 30px;
            row-gap: 30px;
            grid-template-columns: repeat(12, minmax(0, 1fr));
            
            
            .woocommerce-notices-wrapper {
                grid-column: span 12 / span 12;
            }

            &.wc-block-breadcrumbs {
                display: block;
            }
    
            .woocommerce-cart-form,  
            .cross-sells,
            .wc-block-cart__main {
                grid-column: span 8 / span 8;
            }
    
            .cart-collaterals,
            .wc-block-cart__sidebar {

                grid-column: span 4 / span 4;
            }
        }

        @media (min-width:1200px) {
            column-gap: 76px;
        }

    }        

    &.woocommerce-cart  .woocommerce {
        @media (min-width:1024px) {
            grid-template-rows: 2fr;

            .cart-collaterals {
                grid-row: span 2/span 2;
            }
        }

        .cross-sells {
            margin-bottom: 50px;

            @media (min-width:1200px) {
                .columns-2 {
                    .product {
                        &:nth-child(2n) {
                            &::after {
                                content: " ";
                            }
                        }

                        &:nth-child(4n), &:last-child {
                            &::after {
                                content: none;
                            }
                        }
                    }
                }
            }
        }
    }

    .wp-block-woocommerce-cart {
        margin-top: 56px;
        margin-bottom: 60px;

        @media(min-width:1024px) {
            margin-bottom: 120px;
        }
        
        .wp-block-woocommerce-cart-cross-sells-block {
            margin-top: 30px;

            > div {
                display: grid;
                column-gap: 0;
                gap: 20px;
                grid-template-columns: repeat(2, minmax(0, 1fr));

                @media (min-width:768px) {
                    grid-template-columns: repeat(3, minmax(0, 1fr));
                }

                @media (min-width:1024px) {
                    grid-template-columns: repeat(4, minmax(0, 1fr));
                }
            }

            .cross-sells-product {
                margin-bottom: 0;
                width: 100%;
                position: relative;

                > .wp-block-button {
                    display: none;
                }

                .wp-block-button {
                    button {
                        width: 100%;
                    }
                }
            }

            > h2 {
                margin-bottom: 30px;
            }
        }
    }

    .cart-empty,
    .wc-block-checkout-empty,
    .wc-empty-cart-message,
    .return-to-shop {
        width: 100%;
        grid-column: span 12 / span 12;
    }
}

.wp-block-woocommerce-cart {
    .wp-block-woocommerce-cart-cross-sells-block .cross-sells-product div .wc-block-components-product-name {
        font-weight: 500;
    }
}

.return-to-shop {
    text-align: center;

    a {
        display: inline-block;

        br {
            display: none;
        }
        
    }
}

.wc-block-components-notice-banner {
    > p:not([class]) {
        display: none;
    }
}

.wp-block-woocommerce-cart {
    .wp-block-woocommerce-cart-cross-sells-block {  
        > div {
            gap:30px;
        }

        .cross-sells-product div .wp-block-cart-cross-sells-product__product-title {
            margin-bottom: 6px;
        }
        
        .cross-sells-product div .wp-block-cart-cross-sells-product__product-title,
        .wp-block-cart-cross-sells-product__product-price {
            font-size: 15px;
        }

        .cross-sells-product {
            display: grid;
            transition: .3s ease-in-out;
            position: relative;
            border-width: 0;
            padding-right: 0;

            .price,
            div .wc-block-components-product-name {
                text-align: left;
            }

            .wp-block-cart-cross-sells-product__product-title {
                font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.278), 15px);
                font-family: var(--wp--preset--font-family--body);
                text-transform: capitalize;
            }

            .price {
                display: flex !important;
            }

            .wp-block-cart-cross-sells-product__product-onsale {
                margin-bottom: 0 !important;
            }

            .wc-block-components-product-button__button {
                margin-top: 0;
            }

            .wc-block-components-product-image {
                margin-bottom: 0;
            }            

            .wc-block-components-product-rating {
                display: none;
            }

            .wp-block-button {
                align-items: flex-start;
            }
            
        }
    }
}


// Table Styles.
.wc-block-components-main,
.woocommerce-cart .woocommerce-cart-form {
    table {
        img {
            object-fit: contain;
            aspect-ratio: 1/1;
        }

        .product-thumbnail,
        .wc-block-cart-item__image {
            a img,
            img {
                max-width: 100px;
                object-fit: cover;

                @media (min-width:768px) {
                    height: 120px;
                    object-fit: cover;
                }
            }

            a {
                display:inline-flex;
            }
        }

        .product-thumbnail {
            padding-right:0px !important
        }

        .product-remove a {
            padding: 0px;
            font-size: 0px;
            font-weight: 300;
            text-decoration: none;
            &::before {
                content: '\F623';
                font-family: "bootstrap-icons";
                font-size: 15px;
                color: var(--wp--preset--color--primary) !important;
                
            }
        }

        .product-name a,
        .wc-block-components-product-name {
            line-height: 26px;
        }

        .product-name {
            a {
                display: flex;
                text-decoration: none;
            }

            .variation-Size {
                margin-left: 0;
            }
        }
        
    }
}

.woocommerce-cart {
    .woocommerce-cart-form {
        thead {
            background: var(--wp--preset--color--bg-1);

            th {
                border-top-width: 0;
                border-bottom-width: 0;
                text-transform: Capitalize;
                padding-top: 16.8px;
                padding-bottom: 16.8px;
                font-size: 15px;
                font-weight: 500;
                line-height: 26px;
                text-transform: uppercase;
                border-bottom: 1px solid transparent;

                &:first-child {
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                }

                &:last-child {
                    border-top-right-radius: 4px;
                    border-bottom-right-radius: 4px;
                }
            }
        }

        .actions {
            text-align: left;
            padding-right: 0;

            > .coupon {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-bottom: 20px;

                .input-text {
                    padding: 14px 19px !important;
                    font-size: 15px;
                    font-weight: 400;

                    @media (min-width:1300px) {
                        width: 300px !important;
                    }
                }

                @media (min-width:768px) {
                    float: left;
                    flex-direction: row;
                    margin-bottom: 0;
                }

                @media (min-width:1200px) {
                    gap: 20px;
                }

                .wp-element-button {
                    flex-shrink: 0;
                    align-self: flex-start;
                    display: flex;
                    align-items: center;
                }
            }

            @media (min-width:768px) {
                >.wp-element-button {
                    float: right;
                }
            }       
                
            button[name=update_cart] {
                background-color:var(--wp--preset--color--transparent) !important;
                border-color:var(--wp--preset--color--primary) !important;
                color: var(--wp--preset--color--primary) !important;
                padding: 12px 30px;

                &:hover {
                    color: var(--wp--preset--color--base) !important;
                    background-color: var(--wp--preset--color--secondary) !important;
                    border-color:var(--wp--preset--color--secondary) !important;
                }
            }

            .coupon {
                button {
                    padding: 12px 30px;
                    text-transform: uppercase;

                    &:not(:hover) {
                        background-color: var(--wp--preset--color--primary);
                        border-color: var(--wp--preset--color--primary);
                        color: var(--wp--preset--color--base);
                    }

                    &:hover,
                    &:focus {
                        border-color: var(--wp--preset--color--secondary);
                        color: var(--wp--preset--color--base);
                        background-color: var(--wp--preset--color--secondary);
                    }

                    &::after {
                        display: inline-block;
                        margin-left: 10px;
                        width: 16px;
                        font-family: "bootstrap-icons";
                    }
                }
                
            }
        }

        tbody {
            tr {
                td {
                    font-size: 15px;
                    font-weight: 400;
                    border-top: 1px solid var(--wp--preset--color--gray-100);
                    padding-top:20px;
                    padding-bottom: 13px;
                    @media (min-width:1200px) {
                        padding-right: 30px;
                        
                    }
                }
            }
            


            // td.product-subtotal {
            //     text-align: center;
            // } 

            td.product-name .variation {
                display: flex;
                margin: 0px !important;
                align-items: center;
                justify-content: start;
                gap:5px;

                dt {
                    font-weight: 700;
                }
            }
        }

        @media (max-width:767px) {
            thead {display: none;}

            tbody{
                tr {
                    &:not(:last-child) {
                        border-bottom: 1px solid var(--wp--preset--color--gray-100);
                    }

                    td {
                        text-align: right;
                        display: block;
                        border-width: 0;

                        &::before {
                            content: attr(data-title) ": ";
                            float: left;
                            font-weight: 700;
                        }

                        &:not(.actions ){
                            padding: 10px;
                        }
                    }
                }
                
                td.product-subtotal {
                    text-align: right;
                } 
            }

            .variation {
                text-align: left;
            }

            .coupon {
                input[type=text] {
                    width: auto;
                }
            }

            .product-remove::before,
            .actions::before,
            .product-thumbnail {
                display: none;

            }

            table .product-name a {
                display: block;

            }
        }  

    }
}

.is-large .wc-block-components-sidebar .wc-block-components-panel,
.is-large .wc-block-components-sidebar .wc-block-components-totals-coupon,
.is-large .wc-block-components-sidebar .wc-block-components-totals-item,
.is-large.wc-block-cart .wc-block-components-sidebar .wc-block-components-shipping-calculator,
.is-large.wc-block-cart .wc-block-components-sidebar .wc-block-components-shipping-rates-control__package:not(.wc-block-components-panel) {
    padding: 0 !important;
}

.cart-collaterals .cart_totals >h2, 
.wc-block-cart__totals-title,
.is-large.wc-block-cart .wc-block-cart__totals-title,
.cross-sells > h2,
.up-sells > h2, 
.wp-block-woocommerce-cart-cross-sells-block > h2 {
    font-size: var(--wp--preset--font-size--grande);
    position: relative;
    text-align: left;
    font-family: var(--wp--preset--font-family--heading);
    text-transform: capitalize;
    line-height: 30px;
    font-weight: 500 !important;
    margin-top: 0px;
    display: block;
}

.is-large.wc-block-cart .wc-block-cart__totals-title {
     margin-bottom: 6px !important;
}

.wp-block-woocommerce-cart-cross-sells-block > h2 {
     font-size: var(--wp--preset--font-size--grande) !important;
}

.cross-sells > h2 {
    margin-top: 0 !important;
}

.cart-collaterals {
    .shipping-calculator-form {
        margin: 20px 0;

        .form-row {
            display: flex;

            > span {
                width: 100%;
            }            
        }

        button[type=submit] {
            padding: 5px 18px;
        }
    }

    .cart_totals {
        th, td {
            font-weight: 400 !important;
            padding: 16px 0;
            border-top: 1px solid var(--wp--preset--color--gray-100);
        }

        td {
            text-align: right;
        }

        .woocommerce-shipping-destination {
            strong {
                font-weight: 500;
            }
        }

        .woocommerce-shipping-methods {
            list-style: none;
            padding: 0;
            margin-top: 0;

            li + li {
                margin-top: 10px;
            }
        }

        .order-total {
            td, th {
                font-size: 18px;
                font-weight: 500!important;
            }
        }

        .order-total {
            th {
                font-weight: 500 !important;
            }
        }

        .woocommerce-shipping-methods {
            label {
                font-weight: 400 !important;
                cursor:pointer;
            }
        }

        .woocommerce-Price-amount {
            font-weight: 500 !important;
        }

        .shipping-calculator-button {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            text-decoration:none;

            &,
            &:hover,
            &:focus {
                color: var(--wp--preset--color--primary);
            }            
        }

        .wc-proceed-to-checkout {
            margin-top: 15px;

            .checkout-button {
                display: flex;
                text-align: center;
                padding: 14px;
                background-color: var(--wp--preset--color--primary);
                color: var(--wp--preset--color--base);
                border-radius: 0;
                align-items: center;
                justify-content: center;

                &:hover,
                &:focus {
                    color: var(--wp--preset--color--base);
                    background-color: var(--wp--preset--color--secondary);
                    border-color: var(--wp--preset--color--secondary); 
                }
                &::after {
                    display: inline-block;
                    margin-left: 10px;
                    width: 16px;
                    font-family: "bootstrap-icons";
                }
            }
        }

        .woocommerce-shipping-totals {
            th {
                vertical-align: top;
            }
        }

        table tr:first-child th,table tr:first-child td {
            border-top-width: 0;
        }
        // .shipping-calculator-form {
        //     .input-text {
        //         padding: 8px 20px;
        //         font-size: 14px;
        //         font-weight: 400;
        //         line-height: 37.5px !important;
        //     }
        // }
    }

    @media(min-width: 600px) {
        ul.products {
            .product {
                padding: 0;
            }
        }
    }

}

// WC Cart Blocks
.wc-block-cart.wc-block-components-sidebar-layout {
    .wc-block-components-sidebar,
    .wc-block-components-main {
        width: 100%;
        padding: 0;
    }
}

.wp-block-woocommerce-cart-order-summary-coupon-form-block.wc-block-components-totals-wrapper {
    border-top: 0px !important;
    
}



.wc-block-cart__totals-title {
    textarea {
        padding: 0;
        font-size: clamp(16.834px, 1.052rem + ((1vw - 3.2px) * 0.849), 26px);
        border-width: 0;
        text-align: left;
        font-weight: 600;
        text-transform: capitalize;
        color: var(--wp--preset--color--primary);
    }
}

.is-large.wc-block-cart .wc-block-cart__totals-title {
    margin-bottom: 0;
    font-weight: 500;
    padding-top: 0;
    padding-bottom: 0;
}

.woocommerce-cart-form {
    .product-thumbnail {
        width: 100px;
    }

    .product-name {
        padding-left: 30px;
    }
}

.wp-block-woocommerce-cart-order-summary-coupon-form-block.wc-block-components-totals-wrapper:after {
    border-top-width: 0;
}

.wc-block-cart__submit-container {
    .wp-element-button {
        padding: 14px;

        &:not(:hover) {
            color: var(--wp--preset--color--base);
        }
    }
}

.wc-block-cart {
    font-size: 14px;
}

.is-large.wc-block-cart .wc-block-cart-items {
    tbody tr:first-child {
         td{
                border-top: 0px;
            }
        }

    td {
        padding-top: 20px;
        padding-bottom: 25px;

        &:last-child {
            padding-right: 30px;
        }
    }
}

.is-large.wc-block-cart .wc-block-cart-items td.wc-block-cart-item__image {
    padding-right: 0 !important;
}

.is-large.wc-block-cart .wc-block-cart-items td:after,
.is-large.wc-block-cart .wc-block-cart-items:after,
.wc-block-components-totals-wrapper:after {
    color: var(--wp--preset--color--gray-100);
    opacity: 1;
}

.wc-block-components-totals-coupon {
    font-size: 15px;
    line-height: 26px;  
}

.wc-block-cart-item__remove-link {
    cursor: pointer;
}

.wc-block-cart-item__wrap {
    .wc-block-components-product-badge {
        margin-bottom: 0;
        letter-spacing: 0;
        background-color: var(--wp--preset--color--secondary);
        padding: 3px 6px;
        color: var(--wp--preset--color--base);
        font-weight:400;
        border-width: 1;
        border-radius: 0px;
        position: relative;
        //top: -8px;
    }

    .wc-block-components-product-name {
        font-weight: 400;
        font-size: 15px;
        line-height: 26px;
        text-decoration: none;  
    }
}

.woocommerce-cart .woocommerce .cart-collaterals,
.woocommerce-cart .woocommerce .wc-block-cart__sidebar,
.wp-block-woocommerce-cart .wc-block-cart .cart-collaterals,
.wp-block-woocommerce-cart .wc-block-cart .wc-block-cart__sidebar {
    border: 1px solid var(--wp--preset--color--gray-100);
    padding: 30px 12px;
    border-radius: 0;
    height: fit-content;

    @media(min-width: 768px) {
        padding: 30px 39px 39px 39px;
    }
}

.wc-block-cart-item__total {
    text-align: left !important;

    .wc-block-components-sale-badge {
        background-color: var(--wp--preset--color--secondary);
        color: var(--wp--preset--color--base);
        text-transform: uppercase;
        border-radius: 0px;
        padding: 3px 6px;
        font-weight: 400;
    }

    .wc-block-cart-item__total-price-and-sale-badge-wrapper {
        align-items: flex-start;
    }

    .wc-block-components-product-price{
        font-size: 15px !important;
        font-weight: 400;
        line-height: 26px;
        color: var(--wp--preset--color--secondary);
        text-align: left;
    }
}

.is-large.wc-block-cart {
    .wc-block-cart-items thead {
        font-size: 14px ;
        text-transform: capitalize;
        background-color: var(--wp--preset--color--bg-1);
    }

    .wc-block-cart-items th {
        padding: 15px 30px ; /*10px 16px 10px 0;*/

        &.wc-block-cart-items__header-product {
            visibility: visible;
            background: var(--wp--preset--color--gray-100);
            text-align: left;
        }

        &:last-child {
            padding-left: 15px;
            padding-right: 30px;
            text-align: left;
        }

        .wc-block-cart-items__header-total {
            text-align: left;
        }
    }

    tbody {
        tr:first-child {
            td:after {
                content: none !important;
            }       
        }
    }
    
}

table.wc-block-cart-items {
    border-collapse: collapse;

    .wc-block-components-product-metadata {
        margin-bottom: 10px;
        display:none;
    }

    .wc-block-cart-item__prices {
        margin: 5px 0;
    }

    .wc-block-cart-items__header-product {
        padding-left: 16px !important;
    }

    .wc-block-cart-item__quantity {
         .wc-block-components-quantity-selector {
            .wc-block-components-quantity-selector__input {
                min-height: 30px;
                width: 40px !important;

                @media(min-width: 1024px) {
                    min-height: 44px;
                }
            }

            .wc-block-components-quantity-selector__button {
                min-width: 20px;
            }
        }
    }
}

.wc-block-cart-item__prices {
    .wc-block-components-product-price {
        display: flex;
        align-items: center;
        gap: 7px;
        flex-direction: row-reverse;
        justify-content: flex-end;
        color: var(--wp--preset--color--secondary) !important;
        font-size: 15px !important;
        font-weight: 400 !important;
        margin-bottom: 12px !important;
    }
}

.wc-block-cart__submit {
    margin-top: 14px;
    margin-bottom: 0;

    .wc-block-cart__submit-container {
        .wc-block-cart__submit-button {
            background: var(--wp--preset--color--primary);
            color: var(--wp--preset--color--base);

            &:hover,
            &:focus {
                border-color: var(--wp--preset--color--secondary);
                color: var(--wp--preset--color--base);
                background: var(--wp--preset--color--secondary);
                box-shadow: none !important;
                outline: 0 ! important;
            }
        }
    }
}

 .wc-block-components-totals-shipping__change-address__link {
    font-size: 14px;
    font-weight: 400 !important;
    margin-bottom: 10px;
    text-decoration:none !important;
    font-family: var(--wp--preset--font-family--body);
    padding-top: 20px !important;

    &,
    &:hover,
    &:focus {
        color: var(--wp--preset--color--primary);
    }
}

.wc-block-components-shipping-calculator-address {
    margin-top: 15px;
 }

.is-medium.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title,
.is-mobile.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title, 
.is-small.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title {
    display: block;
}

.wc-block-cart__empty-cart__title.with-empty-cart-icon:before {
    font-size: 20px;
    margin-top: 70px;
}

.wp-block-woocommerce-empty-cart-block {
    .wp-block-image {
        margin: auto auto 30px;
    }

    @media (min-width:1200px) {
        > .wp-block-product-new {
            margin: auto;
        }
    }

    .wc-block-cart__empty-cart__title {
        font-family: var(--wp--preset--color--base);
        font-size: var(--wp--preset--font-size--x-large);
        font-weight: 500;
    }

    .wc-block-grid__product-rating {display: none;}

    .yith-wcwl-add-to-wishlist {
        font-size: 0;
    }

    .wp-block-separator + h2 {
        margin: clamp(1.35rem, 2vw + 1rem, 4rem) 0 clamp(1.35rem, 2vw + 1rem, 2.25rem);
    }
}

.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link {
    font-size: 15px;
}

.wc-block-mini-cart__items {
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;

    &::-webkit-scrollbar {
        width: 2px;
        height: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--wp--preset--color--gray-700);
    }

    &::-webkit-scrollbar-track {
        background-color: var(--wp--preset--color--gray-200);
    }
}

.wc-block-cart-items__header .wc-block-cart-items__header-image, 
.wc-block-cart-items__header-total,
.wc-block-cart-items__header-product {
    font-size: 15px !important;
    font-weight: 400 !important;
    line-height: 30px;
    text-transform: uppercase;
     
}

.wc-block-components-totals-coupon__content {
    .wc-block-components-totals-coupon__form input[type=text],
    .wc-block-components-totals-coupon__form button[type=submit] {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
        height: 50px;
        min-width: auto;
    }
}

.wp-block-woocommerce-cart-order-summary-block {
    font-weight: 500;
    
    #wc-block-components-totals-coupon__input-0 {
        padding-left: 17px;
        text-indent: 0;
    }

    .wc-block-components-totals-item__value {
        font-weight: 500; 
    }

    .wc-block-components-totals-item .wc-block-components-totals-item__label {
        font-size: 15px;
        font-weight: 400;
        line-height: 26px;
    }

    .wc-block-components-radio-control__option-layout {
        display: flex;
        justify-content: space-between;
    }
}

.wc-block-components-totals-wrapper:not(.wp-block-woocommerce-cart-order-summary-subtotal-block):not(.wp-block-woocommerce-cart-order-summary-shipping-block):not(.wp-block-woocommerce-cart-order-summary-discount-block) {
    .wc-block-components-totals-footer-item .wc-block-components-totals-item__label,
    .wc-block-components-totals-item__value {
        font-size: 18px;
        font-weight: 500;
        line-height: 30px;
    }
}

.wp-block-woocommerce-cart-order-summary-subtotal-block {
    border-bottom: 1px solid hsla(0,0%,7%,.11) !important;
    padding-bottom: 12px !important;
}

.wc-block-components-totals-wrapper{
     padding: 12px 0;
}

.wc-block-components-text-input.is-active input[type=text] {
    padding: 14px 20px;
}

.wc-block-components-totals-item__description {
     text-align:right;

    .wc-block-components-shipping-address, a {
        display:block; 
        text-align:right;
        font-size:15px !important;
        font-weight:400; 

    }
    a {
        margin-left:7px;
        margin-top:25px;
    }

    .wc-block-components-shipping-address {
        margin-top:0px;
    }
    
}

.wc-block-components-radio-control .wc-block-components-radio-control__option-layout {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    margin-left: 5px;
    color: var(--wp--preset--color--secondary) !important;
    font-weight: 600;
}

 .wc-block-components-shipping-rates-control__package .wc-block-components-radio-control__label-group,
 .wc-block-checkout__payment-method .wc-block-components-radio-control__label-group{
    font-weight: 500;
    font-size: 15px;
    line-height: 26px;
    color: var(--wp--preset--color--secondary) !important;
 }

.wc-block-components-shipping-rates-control__package .wc-block-components-radio-control__description-group,
.wc-block-components-radio-control__description, .wc-block-components-radio-control__secondary-description {
    font-weight: 500;
    font-size: 15px;
    line-height: 26px;
    color: var(--wp--preset--color--secondary) !important;
}

.wc-block-components-radio-control__option {
    padding-left: 22px;
}

.wc-block-components-radio-control .wc-block-components-radio-control__input {
    min-height: 14px;
    min-width: 14px;
    height: 14px;
    width: 14px;
    border-width: 1px;
}

.wc-block-components-radio-control .wc-block-components-radio-control__input:checked {
    border-color: var(--wp--preset--color--primary);
    background-color: transparent !important;
}

.wc-block-components-radio-control .wc-block-components-radio-control__input:checked::before {
    background-color: var(--wp--preset--color--secondary);
    min-height: 9px;
    min-width: 9px;
    height: 9px;
    width: 9px;
}

.wc-block-components-totals-item__description .wc-block-components-totals-shipping__via {
    text-align: right;
    font-size: 15px !important;
    font-weight: 400;
}

.wc-block-cart-item__prices .wc-block-components-product-price__value.is-discounted {
    margin-left: 0px;
}

.wc-block-components-product-price__regular {
    color:var(--wp--preset--color--gray-300);
}

.wc-block-cart-item__image {
    padding-left:0px !important;
}

.woocommerce-cart-form__cart-item .product-remove {
    padding-right: 3px !important;
}

.wp-block-woocommerce-cart {
    .wp-block-woocommerce-cart-cross-sells-block {
        .cross-sells-product {
            text-align: left;
        }
    }
}

.woocommerce-cart {
    .product-quantity {
        .qty-container {
            .qty-container {
                .qty-minus, 
                .qty-plus {
                    display: none;
                }
            }
        }
    }
}

.wp-block-woocommerce-cart {
    .wc-block-components-button:not(.is-link):disabled .wc-block-components-button__text {
        opacity: 1;
    }
    .wc-block-components-form .wc-block-components-text-input:not(.is-active) label, .wc-block-components-text-input:not(.is-active) label {
        transform: translateY(-8px) !important;
    }
    .wc-block-components-totals-shipping .wc-block-components-totals-shipping__change-address__link {
        color: var(--wp--preset--color--primary);
    }

    .wc-blocks-components-select {
        .wc-blocks-components-select__select {
            border: 1px solid var(--wp--preset--color--gray-100);
            padding: 12.42px 40px 12.42px 20px;
            border-radius: 0;
            &:focus {
                border: 1px solid var(--wp--preset--color--contrast);
                box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
            }
        }
        
        .wc-blocks-components-select__container{
            height: 54px;
        }
        .wc-blocks-components-select__label {
            top: -14px;
            font-size: 20px;
            background-color: var(--wp--preset--color--base);
            padding: 2px 11px;
            left: 14px;
        }
    }
}

.woocommerce-cart main .woocommerce {
	max-width: var(--wp--style--global--wide-size);
    @media (max-width: 768px) {
        table.shop_table_responsive tr:nth-child(2n) td, table.shop_table_responsive tr:nth-child(2n) td {
            background-color: transparent;
        }
    }
    table.shop_table{
		border: none;
	}
    .woocommerce-cart-form {
		tbody tr td {
			padding-left: 1px;
		}
		#coupon_code {
			margin-right: 0;
			height: 54px;
		}
		button.button:disabled[disabled]{
			opacity: 1;
		}
		@media (max-width: 768px) {
			table.shop_table_responsive tr:nth-child(2n) td, table.shop_table_responsive tr:nth-child(2n) td{
				background-color: transparent;
			}
			#coupon_code {
				width: 100%;
			}
			table.cart td.actions .coupon .button{
				width: auto;
			}
			table .product-remove a{
				width: auto;
				text-align: right;
				margin-bottom: 10px;
			}
			table .product-name a{
				justify-content: end;
			}
		}
		@media (min-width: 1200px) {
			.product-name {
				padding-left: 30px !important;
			}
			
			table.shop_table th{
				padding: 16.8px 1px;
			}
			.product-remove {
				width: 1rem;
			}
		}
    }

	.cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals {
		width: 100%;
        table th {
            padding: 16px 0;
            width: auto;
        }
        .shipping-calculator-form {
            margin: 20px 0;
        }
        table td {
            padding: 16px 0;
        }
        .select2-container .select2-selection {
            height: 54px;
            padding: 8px;
        }
        form .form-row {
            padding: 0;
        }
        .input-text {
            padding: 14px 20px;
            height: 54px;
        }
        .wc-proceed-to-checkout {
            padding: 0;
            margin-top: 15px;
            a.checkout-button {
                margin-bottom: 0;
                font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.093), 15px);
            }
        }
	}
}


