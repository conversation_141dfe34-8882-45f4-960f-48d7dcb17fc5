/*
################
* === Single Product - Blocks style  ===
################
*/

.single-product {
    form.cart {
        &,.variations_button {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

        }

        .variations_button {
            flex-wrap: wrap;
            @media(min-width: 768px) {
                flex-wrap: nowrap;
            }
        }

        .single_variation_wrap {
            width: 100%;
        }

        &.grouped_form {
            gap: 30px;
            //flex-wrap: wrap !important;
        }

        .reset_variations {
            position: absolute;
            right: 0;
            top: -28px;
        }

        .woocommerce-variation-description {
            font-size: 15px;
            line-height: 1.9;

            p {
                margin-top: 0;
            }
        }

        .woocommerce-variation-price {
            font-size: 18px;
            padding: 10px 0 20px;
            font-weight: 500;
        }

        .woocommerce-variation-availability {
            padding-bottom: 4px;
        }

        .variations {
            position: relative;
            margin-bottom: 20px;

            th.label {
                font-weight: 500;
                text-transform: uppercase;
            }

            tr {
                &:not(:first-child):not(:last-child) td,
                &:not(:first-child):not(:last-child) th {
                    padding: 0 0 15px;
                }

                &:first-child td,
                &:first-child th {
                    padding-bottom: 16px;
                }
            }
        }

        button.wp-element-button {
            line-height: 1.1;
            flex: 1;

            // @media (max-width:767px) {
            //     width: 100%;
            // }

            // @media (min-width:768px) {
            //     min-width: 300px;
            // }
        }

        .woocommerce-grouped-product-list-item {
            &:not(:first-child):not(:last-child) td {
                padding: 0 0 19px;
            }

            &:first-child td {
                padding-bottom: 20px;
            }
        }

        .woocommerce-grouped-product-list-item__quantity {
            width: 110px;
            @media(min-width: 1200px) {
                width: 120px; 
            }

            .qty-container {
                width: 100px;

                .qty {
                    min-width: 40px;
                    width: 40px !important;
                }
            }
        }
    }

    .single-product-content {
        .wp-block-post-title + .wp-block-woocommerce-product-price {
            margin-top: 11px;
        }

        .wc-block-components-product-rating__container {
            display: flex;
            align-items: center;
            gap: 7px;

           
            .wc-block-grid__product-rating__stars {
                margin: 0;
            }
            
        }
    }


    &.product-type-external {
        .single-product-content {
            form.cart {
                .wp-element-button {
                    padding: 18px clamp(1.125rem, 0.7778rem + 1.8519vw, 3rem);
                }
            }
        }
    }

    &.woocommerce {
        .wp-block-woocommerce-product-image-gallery {
            max-width: 100%;
        }

        .wp-block-woocommerce-product-image-gallery {
            span.onsale {
                position: absolute;
                right: 0;
                top: 0;
                left: auto;
                color: var(--wp--preset--color--base);
                background-color: var(--wp--preset--color--ebony);
                padding: 2px 10px;
            }
        }
    }

    .single-product-gallery {
        .wp-block-woocommerce-product-image-gallery  > .woocommerce-product-gallery {
            @media (min-width:1200px) {
                display: flex;
                flex-direction: row-reverse;
                gap: 10px;
            }
        }
    }

    .flex-viewport {
        position: relative;
    }

    .single-product-main {
        @media (max-width:1199px) {
            gap: 30px;
        }
    }

    .flex-control-nav {
        list-style: none;
        margin: 0;
        padding: 0;
        flex-shrink: 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 10px;

        @media (min-width:1200px) {
            width: 100px;
            margin-top: 0;
            flex-direction: column;
        }

        li {
            &,img {
                height: 60px;
                width: 60px;

                @media (min-width:1200px) {
                    width: 100px;
                    height: 120px;
                    object-fit: cover;
                }
            }

            img {
                aspect-ratio: 1/1;
                cursor:pointer;
            }

            img:not(.flex-active) {
                opacity: 0.5;
            }

            // .flex-active {
            //     box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
            // }
        }
    }

    .woocommerce-product-gallery__image {
        > a {
            display: flex;

            img {
                object-fit: cover;
                aspect-ratio: 1/1;

                @media (max-width:1023px) {
                    width: 100%;
                }
            
                @media (min-width:1200px) {
                    width: 694px;
                    max-width: 694px;
                }
            }
        }
    }

    .woocommerce-product-gallery {
        position: relative;

        > .woocommerce-product-gallery__wrapper {
            @media (min-width:1200px) {
                width: 100%;
            }

            .woocommerce-product-gallery__image {
                > a {        
                    img {                    
                        @media (min-width:1200px) {
                            width: 100%;
                            max-width: 100%;
                        }
                    }
                }
            }
        }
    }

    .woocommerce-product-gallery__trigger {
        position: absolute;
        right: 30px;
        top: 30px;
        z-index: 1;
        font-size: 0;

        &::before {
            content: "";
            font-size: 14px;
            display: block;
            background-repeat: no-repeat;
            width: 50px;
            height: 50px;

            background-image: url('data:image/svg+xml,<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="50" height="50" rx="25" fill="white"/><path d="M24.2 17H25V33H24.2V17Z" fill="%23131C19"/><path d="M17 25L17 24.2L33 24.2V25L17 25Z" fill="%23131C19"/></svg>');
        }
    }

    .single-product-content {
        @media (max-width:1199px) {
            > div {
                padding-left: 0 !important;
            }
        }

        .no-stock {display: none;}

        .stock {
            display: block;
            min-width: clamp(1.25rem, -1.9907rem + 17.284vw, 18.75rem);
            text-transform: uppercase;
            text-align: center;
            padding: 15.5px 1rem;
            margin: 0;

            &.out-of-stock,
            &.available-on-backorder {
                border: 1px solid var(--wp--preset--color--primary);
                color: var(--wp--preset--color--primary);
                font-family: var(--wp--preset--font-family--heading);
            }

            &.in-stock,
            &.available-on-backorder {
                margin-bottom: 20px;
            }

            &.in-stock {
                border: 1px solid var(--wp--preset--color--success);
                color: var(--wp--preset--color--success);
            }
        }

        .wp-block-woocommerce-product-price {
            .wc-block-grid__product-price {
                display: flex;
                gap: 8px;
                // flex-direction: row-reverse;
                // justify-content: flex-end;
                align-items: center;

                del {
                    font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.37), 18px);
                    color: var(--wp--preset--color--gray-300);
                    line-height: 1;
                }
            }
        }

        .quantity {
             .qty-container {
                 width: 130px;

                 .qty-plus {
                     padding-top: 3.5px;
                }

                button {
                    font-size: 16px;
                }
             }
        }
    }

    .wp-block-woocommerce-product-details {
        .wc-tabs {
            font-size: var(--wp--preset--font-size--x-large);
            font-family: var(--wp--preset--font-family--heading);
            text-align: center;
            font-weight: 500;
            line-height: 1.2;

            display: none;

            li {
                 font-weight: 500 !important;
                 a {
                    color: var(--wp--preset--color--secondary);
                }
            }
        }

        .woocommerce-tabs {
            padding-top: 5px;

            .wc-tab {
                > h2 {
                    font-size: var(--wp--preset--font-size--x-large);
                    line-height: 48px;
                    font-weight: 500;
                }
            }
            .woocommerce-product-attributes {
                th {
                
                    @media (min-width:768px) {
                        width: 35%;
                    }
                }


                tr {
                    th,td {
                        padding: 11px 0;
                        border-bottom: 1px solid var(--wp--preset--color--gray-100);
                        color: var(--wp--preset--color--secondary);
                        font-size: 15px;
                        font-weight: 400;
                        line-height: 30px;

                        p {
                            margin: 0;
                        }
                    }

                    td {
                        text-align: right;
                    }
                }

                
            }

            .woocommerce-Reviews-title {
                font-size: 24px;
                font-weight: 500;
                margin-bottom: 33px;
                line-height: 29px;
            }

            .panel-title {
                font-size: 18px;
                font-weight: 700;
                margin-bottom: 30px;

                @media (min-width:768px) {
                    font-size: 20px;
                }
            
                a {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    &::after {
                        font-family: "bootstrap-icons";
                        content:"\F64D";
                    }
                }

                &.active {
                    a {
                        &::after {
                            content:"\F63B";
                        }
                    }
                }
            }

            .panel {
                margin-bottom: 30px;
                display: block !important;

                @media (min-width:1024px) {
                    margin-bottom: 0;
                }

                & + .panel {
                    margin-top: var(--wp--preset--spacing--50);
                    padding-top: 1px;
                }
            }
        }

        .woocommerce-Reviews {
            .woocommerce-noreviews,
            #review_form_wrapper {
                font-size: 16px;
                font-family: var(--wp--preset--font-family--heading);
            }
        }

        #tab-description {
            > p {
                line-height: 1.6;

                @media (min-width:768px) {
                    font-size: 15px;
                    line-height: 30px;
                }
            }

            ul {
                line-height: 1.7;
                padding-left: 0;

                > li {
                    display: flex;
                    align-items: center;

                    &::marker {
                        content: none;
                    }

                    &:before {
                        content: "\f309";
                        font-size: 18px;
                        font-family: bootstrap-icons;
                    }
                }
            }

            .has-x-large-font-size {
                @media (max-width:1199px) {
                    font-size: clamp(18px, 1rem + ((1vw - 3.2px) * 1.481), 36px) !important;
                }
            }
        }
    }

    .single_add_to_cart_button {
        padding-top: 18px;
        padding-bottom: 18px;
    }


}

html body.single-product .wp-block-woocommerce-product-details.is-style-minimal ul.tabs.wc-tabs {
    li {
        border-bottom-width: 1px;
        padding: 0;
        opacity: 1;

        a {
            padding: 9px 0;
        }
    
        &:not(:first-child):not(:last-child) {
            margin: 0 30px;
        }

        &:first-child:nth-last-child(2) {
            margin: 0 30px 0 0;
        }
    
        &:not(.active) {
            color: color-mix(in srgb, var(--wp--preset--color--gray-200) 50%, var(--wp--preset--color--gray-200) 0%);
        }
    }

    li.active {
        border-bottom-width: 1px;
    }
}

.pswp--open[aria-hidden="true"] {
    display: none;
}

.wp-block-woocommerce-related-products {
    margin-bottom: var(--wp--preset--spacing--50) !important;

    @media (min-width: 600px) {
        .slick-slide > div {
            padding: 20px 13.5px;
        }
    }

    @media (min-width: 768px) {
        .products-block-post-template .product .has-medium-font-size {
            font-size: 15px !important;
            margin-bottom: 5px;
        }
    }
    
    
    .slick-list {
        margin: 0 -14px !important;

        @media (max-width:767px) {
            .slick-slide > div {
                padding: 0 10px;
            }
        }
    }
}

.woocommerce .woocommerce-Reviews {
    

    #review_form_wrapper {
        margin-top: 52px;
    }

    

    .clear {display: none;}

    .comment-form-comment,
    .comment-form-author,
    .comment-form-email {
        margin-bottom: 22px;
    }

    .comment-reply-title {
        font-size: 24px;
        font-weight: 500;
        margin-top: 0;
        margin-bottom: 33px;
        display: block;
    }

    .comment-notes {
        margin-top: 0;
        margin-bottom: 25px;
    }

    .comment-form {
       

        label {
            font-weight: 500;
            display: inline-block;
            margin-bottom: 13px;
        }

        .comment-form-comment {
            margin-top: 25px;
        }
    }

    p.stars.selected a.active::before,
    p.stars.selected a:not(.active)::before {
        content: '\F586';
        font-family: "bootstrap-icons";
    }

    p.stars.selected a.active~a::before,
    p.stars a::before {
        content: '\F588';
        font-family: "bootstrap-icons";
    }


    .commentlist {
        list-style: none;
        padding: 0;

        .avatar {
            border-radius: 50%;

            @media (min-width: 768px) {
                width: 70px;
                height: 70px;
            }
        }

        .comment_container {
            display: flex;
            gap: 26px;
            flex-wrap: wrap;
            align-items: center;
        }

        .review {
            padding: clamp(1.25rem, 1.0185rem + 1.2346vw, 2.5rem) 0 clamp(1rem, 0.9074rem + 0.4938vw, 1.5rem);
            border-top: 1px solid var(--wp--preset--color--gray-100);
            display: flex;
            flex-direction: column;
            gap: 30px;

            &:last-child {
                border-bottom: 1px solid var(--wp--preset--color--gray-100);
            }

            .children {
                list-style: none;
                order: 2;

                > .comment {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;
                }
            }

            .description {
                line-height: 30px;
                font-size: 15px;
                width: 100%;
    
                p {
                    margin: 0;
                }
            }
        }
    }

    .woocommerce-review__author {
        text-transform: capitalize;
        font-size: 20px;
        font-weight: 500;
        font-family: var(--wp--preset--font-family--heading);
    }

    .comment-text {
        display: flex;
        flex-wrap: wrap;
        flex-grow: 1;
        justify-content: space-between;
        align-items: center;

        .star-rating {
        
            &:before {
                left: 0;
            }
        }
        
        .meta {
            margin: 0;
            line-height: 1.4;
            display: flex;
            order: -1;
            flex-direction: column;
            gap: 5px;

            .woocommerce-review__dash {display: none;}

            time {
                color: var(--wp--preset--color--secondary);
                font-size: 15px;
                font-weight: 300;
                font-family: var(--wp--preset--font-family--body);
            }
        }
    }
}

#commentform {
    .comment-form-comment,
    .comment-form-author,
    .comment-form-email,
    .comment-form-url {
        display: flex;
        flex-direction: column;
        gap:5px;
    }

    input[type=submit] {
        width: 100%;
    }

    input[type=text],
    input[type=password],
    input[type=email],
    input[type=number],
    input[type=url],
    input[type=search],
    input[type=tel],
    textarea {
        width: auto;
    }

    .comment-form-cookies-consent {
        margin-bottom: 26px;
        display: flex;
        gap: 10px !important;

        input[type=checkbox] {
            flex-shrink: 0;
            margin-right: 0;
        }

        label {
            margin-bottom: 0;
            font-size: 15px;
            font-weight: 400;
            font-family: var(--wp--preset--font-family--body);
        }

        #wp-comment-cookies-consent {
            margin-top: 4px;
        }
    }
}

.woocommerce div.product .wc-block-add-to-cart-form form.cart .quantity, 
.woocommerce div.product .wc-block-add-to-cart-form form.cart button.single_add_to_cart_button {
    margin-bottom: 0;
}

.single-product-details .wp-block-details[open] > summary::after,
.single-product-details .wp-block-details summary::after{
    font-size: 8px;
}

.single-product-details summary {
    display: flex;
    align-items: center;
}