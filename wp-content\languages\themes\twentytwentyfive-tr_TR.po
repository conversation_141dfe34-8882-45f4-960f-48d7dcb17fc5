# Translation of Themes - Twenty Twenty-Five in Turkish
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-06 09:29:12+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: tr\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Üst kısım"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Alt kısım"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Çok büyük"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Large"
msgstr "Büyük"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Orta"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Base"
msgstr "Temel"

#: functions.php:76
msgid "Checkmark"
msgstr "Onay işareti"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Kenar çubuğu"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Small"
msgstr "Küçük"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Kenar çubuğu"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Ara"

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Yazı dolaşımı"

#: patterns/comments.php:18
msgid "Comments"
msgstr "Yorumlar"

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Yorumlar"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "Hakkında"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Başlıksız sayfa"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Ekstra ekstra büyük"

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Yazı listesi, 1 sütun"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "Tam sayfa düzenlerinden oluşan bir koleksiyon."

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress ekibi"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://wordpress.org"
