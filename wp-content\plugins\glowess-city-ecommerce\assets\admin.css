/**
 * Glowess City Based E-Commerce - Admin Panel Styles
 * WordPress Admin Panel i<PERSON><PERSON> stiller
 */

/* ===== GENEL ADMIN STILLERI ===== */
.glowess-city-admin-page {
    max-width: 1200px;
    margin: 20px 0;
}

.glowess-city-admin-header {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.glowess-city-admin-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1d2327;
}

.glowess-city-admin-header p {
    margin: 0;
    color: #646970;
    font-size: 14px;
}

/* ===== FORM STİLLERİ ===== */
.glowess-city-admin-form {
    background: white;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.glowess-city-form-table {
    width: 100%;
}

.glowess-city-form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
    font-weight: 600;
    color: #1d2327;
}

.glowess-city-form-table td {
    padding: 20px 10px;
    vertical-align: top;
}

.glowess-city-form-table input[type="text"],
.glowess-city-form-table input[type="number"],
.glowess-city-form-table input[type="url"],
.glowess-city-form-table textarea,
.glowess-city-form-table select {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
}

.glowess-city-form-table input[type="text"]:focus,
.glowess-city-form-table input[type="number"]:focus,
.glowess-city-form-table input[type="url"]:focus,
.glowess-city-form-table textarea:focus,
.glowess-city-form-table select:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.glowess-city-form-table .description {
    margin-top: 5px;
    color: #646970;
    font-size: 13px;
    font-style: italic;
}

/* ===== CHECKBOX STİLLERİ ===== */
.glowess-city-checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.glowess-city-checkbox-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    transition: all 0.2s ease;
}

.glowess-city-checkbox-item:hover {
    background: #f0f0f1;
    border-color: #2271b1;
}

.glowess-city-checkbox-item input[type="checkbox"] {
    margin-right: 8px;
}

.glowess-city-checkbox-item label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
    user-select: none;
}

/* ===== BUTON STİLLERİ ===== */
.glowess-city-button {
    display: inline-block;
    padding: 10px 20px;
    background: #2271b1;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #2271b1;
    cursor: pointer;
}

.glowess-city-button:hover {
    background: #135e96;
    border-color: #135e96;
    color: white;
}

.glowess-city-button.secondary {
    background: white;
    color: #2271b1;
    border: 1px solid #2271b1;
}

.glowess-city-button.secondary:hover {
    background: #f6f7f7;
}

.glowess-city-button.danger {
    background: #d63638;
    border-color: #d63638;
}

.glowess-city-button.danger:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

/* ===== TABLO STİLLERİ ===== */
.glowess-city-admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.glowess-city-admin-table th,
.glowess-city-admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f1;
}

.glowess-city-admin-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.glowess-city-admin-table tbody tr:hover {
    background: #f6f7f7;
}

.glowess-city-admin-table .status-active {
    color: #00a32a;
    font-weight: 600;
}

.glowess-city-admin-table .status-inactive {
    color: #d63638;
    font-weight: 600;
}

.glowess-city-admin-table .actions {
    white-space: nowrap;
}

.glowess-city-admin-table .actions a {
    margin-right: 10px;
    text-decoration: none;
    color: #2271b1;
}

.glowess-city-admin-table .actions a:hover {
    color: #135e96;
}

/* ===== DASHBOARD KARTLARI ===== */
.glowess-city-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.glowess-city-dashboard-card {
    background: white;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.glowess-city-dashboard-card h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.glowess-city-dashboard-number {
    font-size: 32px;
    font-weight: 700;
    color: #2271b1;
    line-height: 1;
    margin-bottom: 5px;
}

.glowess-city-dashboard-label {
    color: #646970;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== AYAR SEKMELERI ===== */
.glowess-city-tabs {
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
}

.glowess-city-tab-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.glowess-city-tab-nav li {
    margin-right: 20px;
}

.glowess-city-tab-nav a {
    display: block;
    padding: 15px 20px;
    text-decoration: none;
    color: #646970;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.glowess-city-tab-nav a:hover,
.glowess-city-tab-nav a.active {
    color: #2271b1;
    border-bottom-color: #2271b1;
}

.glowess-city-tab-content {
    display: none;
}

.glowess-city-tab-content.active {
    display: block;
}

/* ===== BİLDİRİMLER ===== */
.glowess-city-notice {
    margin: 5px 0 15px 0;
    padding: 12px;
    border-left: 4px solid #00a32a;
    background: white;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.glowess-city-notice.error {
    border-left-color: #d63638;
}

.glowess-city-notice.warning {
    border-left-color: #dba617;
}

.glowess-city-notice.info {
    border-left-color: #2271b1;
}

.glowess-city-notice p {
    margin: 0;
    font-size: 14px;
}

/* ===== ŞEHIR META BOX ===== */
.glowess-city-meta-box {
    background: white;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.glowess-city-meta-box .inside {
    padding: 12px;
}

.glowess-city-meta-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 15px;
    align-items: start;
    margin-bottom: 15px;
}

.glowess-city-meta-row:last-child {
    margin-bottom: 0;
}

.glowess-city-meta-label {
    font-weight: 600;
    color: #1d2327;
    padding-top: 8px;
}

.glowess-city-meta-field input,
.glowess-city-meta-field textarea,
.glowess-city-meta-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
}

.glowess-city-meta-field .description {
    margin-top: 5px;
    color: #646970;
    font-size: 12px;
    font-style: italic;
}

/* ===== ÜRÜN EDİT SAYFASI ===== */
.product_city_fields {
    padding: 12px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 12px 0;
}

.product_city_fields p.form-field {
    margin: 8px 0;
}

.product_city_fields label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.product_city_fields input[type="checkbox"] {
    margin-right: 8px;
}

/* ===== YARDIM METİNLERİ ===== */
.glowess-city-help-text {
    background: #f0f6fc;
    border: 1px solid #c3e6ff;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
}

.glowess-city-help-text h4 {
    margin: 0 0 10px 0;
    color: #0c4a6e;
    font-size: 16px;
}

.glowess-city-help-text p {
    margin: 0;
    color: #0c4a6e;
    font-size: 14px;
}

.glowess-city-help-text ul {
    margin: 10px 0 0 20px;
    color: #0c4a6e;
}

/* ===== LOADING STİLLERİ ===== */
.glowess-city-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2271b1;
    border-radius: 50%;
    animation: glowess-admin-spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes glowess-admin-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ISTATISTIK GRAFIKLERI ===== */
.glowess-city-chart-container {
    background: white;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
}

.glowess-city-chart-title {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

/* ===== MODAL STİLLERİ ===== */
.glowess-city-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 160000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.glowess-city-modal {
    background: white;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border-radius: 4px;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25);
}

.glowess-city-modal-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.glowess-city-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.glowess-city-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    color: #646970;
    line-height: 1;
}

.glowess-city-modal-close:hover {
    color: #1d2327;
}

.glowess-city-modal-body {
    padding: 20px;
}

.glowess-city-modal-footer {
    padding: 20px;
    border-top: 1px solid #f0f0f1;
    text-align: right;
}

/* ===== RESPONSIVE TASARIM ===== */
@media screen and (max-width: 782px) {
    .glowess-city-dashboard {
        grid-template-columns: 1fr;
    }
    
    .glowess-city-meta-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .glowess-city-meta-label {
        padding-top: 0;
    }
    
    .glowess-city-form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .glowess-city-form-table td {
        display: block;
        padding-top: 0;
    }
    
    .glowess-city-checkbox-group {
        grid-template-columns: 1fr;
    }
    
    .glowess-city-tab-nav {
        flex-direction: column;
    }
    
    .glowess-city-tab-nav li {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .glowess-city-modal {
        width: 95%;
        max-height: 95vh;
    }
}

/* ===== DARK MODE DESTEĞI ===== */
@media (prefers-color-scheme: dark) {
    .glowess-city-admin-page {
        color: #f0f0f1;
    }
    
    .glowess-city-admin-header,
    .glowess-city-admin-form,
    .glowess-city-dashboard-card,
    .glowess-city-admin-table {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .glowess-city-admin-header h1,
    .glowess-city-dashboard-card h3,
    .glowess-city-admin-table th {
        color: #f0f0f1;
    }
    
    .glowess-city-form-table input[type="text"],
    .glowess-city-form-table input[type="number"],
    .glowess-city-form-table input[type="url"],
    .glowess-city-form-table textarea,
    .glowess-city-form-table select {
        background: #1d2327;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .glowess-city-checkbox-item {
        background: #2c3338;
        border-color: #3c434a;
    }
    
    .glowess-city-checkbox-item:hover {
        background: #32373c;
    }
}

/* ===== ÖZEL ICONLAR ===== */
.glowess-city-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 8px;
}

.glowess-city-icon-city {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232271b1' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
}

.glowess-city-icon-product {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232271b1' viewBox='0 0 24 24'%3E%3Cpath d='M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z'/%3E%3C/svg%3E");
}

.glowess-city-icon-settings {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232271b1' viewBox='0 0 24 24'%3E%3Cpath d='M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z'/%3E%3C/svg%3E");
}

/* ===== PROGRESS BAR ===== */
.glowess-city-progress {
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.glowess-city-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #2271b1, #135e96);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.glowess-city-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.1) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.1) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: glowess-progress-stripes 1s linear infinite;
}

@keyframes glowess-progress-stripes {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 0;
    }
}

/* ===== TOOLTIP STİLLERİ ===== */
.glowess-city-tooltip {
    position: relative;
    cursor: help;
}

.glowess-city-tooltip::before,
.glowess-city-tooltip::after {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000000;
}

.glowess-city-tooltip::before {
    content: attr(data-tooltip);
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #1d2327;
    color: #f0f0f1;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    max-width: 200px;
    white-space: normal;
    text-align: center;
}

.glowess-city-tooltip::after {
    content: '';
    bottom: 117%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1d2327;
}

.glowess-city-tooltip:hover::before,
.glowess-city-tooltip:hover::after {
    visibility: visible;
    opacity: 1;
}

/* ===== COLOR PICKER ===== */
.glowess-city-color-picker {
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.glowess-city-color-preview {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

/* ===== BADGE STİLLERİ ===== */
.glowess-city-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.glowess-city-badge.success {
    background: #d1e7dd;
    color: #0a3622;
}

.glowess-city-badge.warning {
    background: #fff3cd;
    color: #664d03;
}

.glowess-city-badge.error {
    background: #f8d7da;
    color: #58151c;
}

.glowess-city-badge.info {
    background: #cff4fc;
    color: #055160;
}

/* ===== ACCORDION STİLLERİ ===== */
.glowess-city-accordion {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.glowess-city-accordion-item {
    border-bottom: 1px solid #ccd0d4;
}

.glowess-city-accordion-item:last-child {
    border-bottom: none;
}

.glowess-city-accordion-header {
    background: #f6f7f7;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #1d2327;
    transition: background 0.2s ease;
}

.glowess-city-accordion-header:hover {
    background: #f0f0f1;
}

.glowess-city-accordion-icon {
    transition: transform 0.2s ease;
}

.glowess-city-accordion-item.active .glowess-city-accordion-icon {
    transform: rotate(180deg);
}

.glowess-city-accordion-content {
    padding: 20px;
    background: white;
    display: none;
}

.glowess-city-accordion-item.active .glowess-city-accordion-content {
    display: block;
}

/* ===== PRINT STİLLERİ ===== */
@media print {
    .glowess-city-admin-page {
        box-shadow: none;
        border: none;
    }
    
    .glowess-city-button,
    .glowess-city-modal,
    .glowess-city-tooltip::before,
    .glowess-city-tooltip::after {
        display: none !important;
    }
    
    .glowess-city-admin-table,
    .glowess-city-admin-table th,
    .glowess-city-admin-table td {
        border: 1px solid #000 !important;
    }
}