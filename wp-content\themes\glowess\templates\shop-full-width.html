<!-- wp:template-part {"slug":"header-v5","area":"header","tagName":"header"} /-->

<!-- wp:group {"metadata":{"name":"Shop Full Width"},"style":{"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|60"}}},"layout":{"inherit":true,"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:var(--wp--preset--spacing--60)"><!-- wp:template-part {"slug":"page-title","theme":"glowess","align":"full"} /-->
    <!-- wp:group {"align":"wide","className":"shop-v1","style":{"spacing":{"blockGap":"0","margin":{"top":"0"}}},"layout":{"type":"constrained"}} -->
    <div class="wp-block-group alignwide shop-v1" id="shop-filter-toggle" style="margin-top:0"><!-- wp:group {"align":"wide","className":"filter-block ","layout":{"type":"constrained"}} -->
    <div class="wp-block-group alignwide filter-block"><!-- wp:group {"align":"wide","backgroundColor":"base","layout":{"type":"constrained"}} -->
    <div class="wp-block-group alignwide has-base-background-color has-background"><!-- wp:template-part {"slug":"products-filter","theme":"glowess","align":"wide"} /--></div>
    <!-- /wp:group --></div>
    <!-- /wp:group -->
    
    <!-- wp:group {"align":"wide","className":"archive-content","style":{"spacing":{"padding":{"top":"0px"},"blockGap":"10px"}},"layout":{"type":"constrained"}} -->
    <div class="wp-block-group alignwide archive-content" style="padding-top:0px"><!-- wp:group {"className":"alignwide","layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"left"}} -->
    <div class="wp-block-group alignwide"><!-- wp:buttons {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"700"},"spacing":{"margin":{"bottom":"var:preset|spacing|20"}}}} -->
    <div class="wp-block-buttons has-custom-font-size" id="openFilter" style="margin-bottom:var(--wp--preset--spacing--20);font-size:15px;font-style:normal;font-weight:700"><!-- wp:button {"backgroundColor":"base","className":"is-style-outline","style":{"typography":{"textTransform":"capitalize","fontStyle":"normal","fontWeight":"600"},"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"10px","bottom":"10px"}},"border":{"width":"1px"}},"fontSize":"small","borderColor":"gray-100"} -->
    <div class="wp-block-button has-custom-font-size is-style-outline has-small-font-size" style="font-style:normal;font-weight:600;text-transform:capitalize"><a class="wp-block-button__link has-base-background-color has-background has-border-color has-gray-100-border-color wp-element-button" style="border-width:1px;padding-top:10px;padding-right:var(--wp--preset--spacing--30);padding-bottom:10px;padding-left:var(--wp--preset--spacing--30)">Filters</a></div>
    <!-- /wp:button --></div>
    <!-- /wp:buttons -->

    <!-- wp:buttons {"style":{"typography":{"fontSize":"15px"}}} -->
<div class="wp-block-buttons has-custom-font-size" id="showFilter" style="font-size:15px"><!-- wp:button {"backgroundColor":"base","textColor":"contrast","style":{"typography":{"textTransform":"capitalize","fontStyle":"normal","fontWeight":"500"},"spacing":{"padding":{"left":"var:preset|spacing|50","right":"var:preset|spacing|50","top":"10px","bottom":"10px"}},"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"border":{"width":"1px"}},"borderColor":"secondary","className":"is-style-outline","fontSize":"small"} -->
    <div class="wp-block-button has-custom-font-size is-style-outline has-small-font-size" style="font-style:normal;font-weight:500;text-transform:capitalize"><a class="wp-block-button__link has-contrast-color has-base-background-color has-text-color has-background has-link-color has-border-color has-secondary-border-color wp-element-button" style="border-width:1px;padding-top:10px;padding-right:var(--wp--preset--spacing--50);padding-bottom:10px;padding-left:var(--wp--preset--spacing--50)">Filters</a></div>
    <!-- /wp:button --></div>
    <!-- /wp:buttons -->
    
    <!-- wp:group {"style":{"layout":{"selfStretch":"fill","flexSize":null}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
    <div class="wp-block-group"><!-- wp:woocommerce/product-results-count /-->
    
    <!-- wp:woocommerce/catalog-sorting /--></div>
    <!-- /wp:group --></div>
    <!-- /wp:group -->
    
    <!-- wp:query {"queryId":1,"query":{"perPage":"12","pages":0,"offset":0,"postType":"product","order":"asc","orderBy":"title","author":"","search":"","exclude":[],"sticky":"","inherit":false,"__woocommerceAttributes":[],"__woocommerceStockStatus":["instock","outofstock","onbackorder"]},"namespace":"woocommerce/product-query","align":"wide"} -->
    <div class="wp-block-query alignwide"><!-- wp:post-template {"className":"products-block-post-template","layout":{"type":"grid","columnCount":"4","minimumColumnWidth":null},"__woocommerceNamespace":"woocommerce/product-query/product-template"} -->
    <!-- wp:woocommerce/product-image {"imageSizing":"thumbnail","isDescendentOfQueryLoop":true,"height":"","scale":"contain"} /-->
    
    <!-- wp:post-title {"textAlign":"left","level":3,"isLink":true,"style":{"spacing":{"margin":{"bottom":"0.75rem","top":"0"}}},"fontSize":"medium","__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->
    
    <!-- wp:woocommerce/product-rating {"isDescendentOfQueryLoop":true} /-->
    
    <!-- wp:woocommerce/product-price {"isDescendentOfQueryLoop":true,"textAlign":"left"} /-->
    
    <!-- wp:woocommerce/product-button {"textAlign":"center","isDescendentOfQueryLoop":true,"fontSize":"small"} /-->
    <!-- /wp:post-template -->
    
    <!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|30"}}},"layout":{"type":"constrained"}} -->
    <div class="wp-block-group" style="padding-top:var(--wp--preset--spacing--30)"><!-- wp:query-pagination {"paginationArrow":"arrow","showLabel":false,"layout":{"type":"flex","justifyContent":"center"}} -->
    <!-- wp:query-pagination-previous /-->
    
    <!-- wp:query-pagination-numbers /-->
    
    <!-- wp:query-pagination-next /-->
    <!-- /wp:query-pagination --></div>
    <!-- /wp:group -->
    
    <!-- wp:query-no-results -->
    <!-- wp:paragraph {"placeholder":"Add text or blocks that will display when a query returns no results."} -->
    <p></p>
    <!-- /wp:paragraph -->
    <!-- /wp:query-no-results --></div>
    <!-- /wp:query --></div>
    <!-- /wp:group --></div>
    <!-- /wp:group --></div>
    <!-- /wp:group -->

<!-- wp:template-part {"slug":"footer","tagName":"footer"} /-->