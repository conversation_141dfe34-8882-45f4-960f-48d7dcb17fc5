<?php
/* ==================== cross-sell-products.php ==================== */
// Sepet sayfası için cross-sell ürünler
$cart = WC()->cart;
if (!$cart || $cart->is_empty()) return;

$selected_city = glowess_get_selected_city();
if (!$selected_city) return;

$cart_items = $cart->get_cart();
$product_ids = array();
$category_ids = array();

// Sepetteki ürünlerin kategori ID'lerini topla
foreach ($cart_items as $cart_item) {
    $product_id = $cart_item['product_id'];
    $product_ids[] = $product_id;
    
    $terms = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));
    $category_ids = array_merge($category_ids, $terms);
}

$category_ids = array_unique($category_ids);

if (empty($category_ids)) return;

// Benzer ürünleri getir
$related_products = glowess_get_city_products($selected_city, array(
    'posts_per_page' => 8,
    'post__not_in' => $product_ids,
    'tax_query' => array(
        array(
            'taxonomy' => 'product_cat',
            'field' => 'term_id',
            'terms' => $category_ids,
            'operator' => 'IN'
        )
    )
));

if (!$related_products->have_posts()) return;
?>

<div class="glowess-city-cross-sell">
    <h3 class="cross-sell-title">Bu Ürünler de İlginizi Çekebilir</h3>
    
    <div class="cross-sell-slider">
        <div class="cross-sell-products">
            <?php while ($related_products->have_posts()): 
                $related_products->the_post();
                global $product;
                include __DIR__ . '/product-card.php';
            endwhile; ?>
        </div>
        
        <div class="slider-navigation">
            <button class="slider-prev" aria-label="Önceki">‹</button>
            <button class="slider-next" aria-label="Sonraki">›</button>
        </div>
    </div>
</div>

<?php wp_reset_postdata(); ?>