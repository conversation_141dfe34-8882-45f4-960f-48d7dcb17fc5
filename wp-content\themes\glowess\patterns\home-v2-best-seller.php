<?php
/**
 * Title: Home v2 Best Seller
 * Slug: glowess/home-v2-best-seller
 * Categories: featured
 * Keywords: Home v2 Best Seller
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v2-best-seller"},"align":"full","className":"v2-best-seller","style":{"spacing":{"padding":{"top":"45px","bottom":"55px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull v2-best-seller" style="padding-top:45px;padding-bottom:55px"><!-- wp:group {"layout":{"type":"constrained","contentSize":"1400px"}} -->
<div class="wp-block-group"><!-- wp:group {"style":{"spacing":{"padding":{"bottom":"18px"}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between","verticalAlignment":"bottom"}} -->
<div class="wp-block-group" style="padding-bottom:18px"><!-- wp:group {"layout":{"type":"constrained","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:heading {"textAlign":"left","level":1,"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h1 class="wp-block-heading has-text-align-left has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Best Seller', 'glowess' ); ?></h1>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"left","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"8px"}}},"textColor":"contrast"} -->
<p class="has-text-align-left has-contrast-color has-text-color has-link-color" style="margin-top:8px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Our universally agreed, most-loved products.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"0","bottom":"10px"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:0;margin-bottom:10px"><!-- wp:button {"textColor":"secondary","className":"before-inline-img","style":{"color":{"background":"#00000000"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|secondary","width":"1px"},"left":{"width":"0px","style":"none"}},"spacing":{"padding":{"left":"0px","right":"0px","top":"0px","bottom":"0px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"}}} -->
<div class="wp-block-button has-custom-font-size before-inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.5"><a class="wp-block-button__link has-secondary-color has-text-color has-background has-link-color wp-element-button" href="#" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--secondary);border-bottom-width:1px;border-left-style:none;border-left-width:0px;background-color:#00000000;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><img class="wp-image-197" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-right.svg'; ?>" alt=""><?php echo esc_html__( 'View All', 'glowess' ); ?></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"padding":{"top":"13px"},"margin":{"top":"0px","bottom":"0px"}}},"layout":{"type":"constrained","contentSize":"1428px"}} -->
<div class="wp-block-group" style="margin-top:0px;margin-bottom:0px;padding-top:13px"><!-- wp:query {"queryId":20,"query":{"perPage":9,"pages":0,"offset":0,"postType":"product","order":"desc","orderBy":"popularity","author":"","search":"","exclude":[],"sticky":"","inherit":false,"__woocommerceAttributes":[],"__woocommerceStockStatus":["instock","outofstock","onbackorder"]},"namespace":"woocommerce/product-query","className":"gl-slick slick-nav-group"} -->
<div class="wp-block-query gl-slick slick-nav-group"><!-- wp:post-template {"className":"products-block-post-template","style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0px","bottom":"0px"}}},"textColor":"secondary","fontSize":"small","layout":{"type":"grid","columnCount":4},"__woocommerceNamespace":"woocommerce/product-query/product-template"} -->
<!-- wp:group {"className":"height-auto","layout":{"type":"default"}} -->
<div class="wp-block-group height-auto"><!-- wp:woocommerce/product-image {"imageSizing":"thumbnail","isDescendentOfQueryLoop":true,"width":"","height":"400px"} /--></div>
<!-- /wp:group -->

<!-- wp:post-title {"textAlign":"left","level":3,"isLink":true,"style":{"spacing":{"margin":{"bottom":"8px","top":"0"},"padding":{"top":"15px"}},"typography":{"fontSize":"16px"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->

<!-- wp:woocommerce/product-rating {"isDescendentOfQueryLoop":true,"textColor":"secondary","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"bottom":"8px"},"padding":{"top":"0px","bottom":"0px"}},"typography":{"fontSize":"5px"}}} /-->

<!-- wp:woocommerce/product-price {"isDescendentOfQueryLoop":true,"textAlign":"left","textColor":"secondary","style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0px","bottom":"0px"}}}} /-->

<!-- wp:woocommerce/product-button {"textAlign":"center","isDescendentOfQueryLoop":true,"fontSize":"small"} /-->
<!-- /wp:post-template -->

<!-- wp:query-no-results {"style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0px","bottom":"0px"}}},"textColor":"secondary","fontSize":"small"} -->
<!-- wp:paragraph {"placeholder":"Add text or blocks that will display when a query returns no results."} -->
<p></p>
<!-- /wp:paragraph -->
<!-- /wp:query-no-results --></div>
<!-- /wp:query --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
