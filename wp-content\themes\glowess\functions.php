<?php
/**
 * Glowess functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Glowess
 * @since Glowess 1.0.0
 */

/**
 * Register block styles.
 */

$theme          = wp_get_theme( 'glowess' );
$glowess_version = $theme['Version'];


$glowess = (object) array(
	'version' => $glowess_version,
);

if ( is_admin() ) {
	$glowess->admin = require get_template_directory() . '/inc/admin/class-glowess-admin.php';

	/**
	 * TGM Plugin Activation class.
	 */
	require get_template_directory() . '/inc/vendor/class-tgm-plugin-activation.php';
	$glowess->plugin_install = require get_template_directory() . '/inc/admin/class-glowess-plugin-install.php';

	if ( class_exists( 'OCDI_Plugin' ) ) {
		$glowess->ocdi = require get_template_directory() . '/inc/ocdi/class-glowess-ocdi.php';
	}
}


if ( ! function_exists( 'glowess_block_styles' ) ) :
	/**
	 * Register custom block styles
	 *
	 * @since glowess 1.0.0
	 * @return void
	 */
	function glowess_block_styles() {

		register_block_style(
			'core/details',
			array(
				'name'         => 'arrow-icon-details',
				'label'        => __( 'Arrow icon', 'glowess' ),

				/*
				 * Styles for the custom Arrow icon style of the Details block
				 */
				'inline_style' => '
				.is-style-arrow-icon-details {
					padding-top: var(--wp--preset--spacing--10);
					padding-bottom: var(--wp--preset--spacing--10);
				}

				.is-style-arrow-icon-details summary {
					list-style-type: "\2193\00a0\00a0\00a0";
				}

				.is-style-arrow-icon-details[open]>summary {
					list-style-type: "\2192\00a0\00a0\00a0";
				}',
			)
		);
		register_block_style(
			'core/post-terms',
			array(
				'name'         => 'pill',
				'label'        => __( 'Pill', 'glowess' ),

				/*
				 * Styles variation for post terms
				 * https://github.com/WordPress/gutenberg/issues/24956
				 */
				'inline_style' => '
				.is-style-pill a,
				.is-style-pill span:not([class], [data-rich-text-placeholder]) {
					display: inline-block;
					background-color: var(--wp--preset--color--base);
					padding: 0.375rem 0.875rem;
					border-radius: var(--wp--preset--spacing--20);
				}

				.is-style-pill a:hover {
					background-color: var(--wp--preset--color--contrast-3);
				}',
			)
		);
		register_block_style(
			'core/list',
			array(
				'name'         => 'checkmark-list',
				'label'        => __( 'Checkmark', 'glowess' ),

				/*
				 * Styles for the custom checkmark list block style
				 * https://github.com/WordPress/gutenberg/issues/51480
				 */
				'inline_style' => '
				ul.is-style-checkmark-list {
					list-style-type: "\2713";
				}

				ul.is-style-checkmark-list li {
					padding-inline-start: 1ch;
				}',
			)
		);
		register_block_style(
			'core/navigation-link',
			array(
				'name'         => 'arrow-link',
				'label'        => __( 'With arrow', 'glowess' ),

				/*
				 * Styles for the custom arrow nav link block style
				 */
				'inline_style' => '
				.is-style-arrow-link .wp-block-navigation-item__label:after {
					content: "\2197";
					padding-inline-start: 0.25rem;
					vertical-align: middle;
					text-decoration: none;
					display: inline-block;
				}',
			)
		);
		register_block_style(
			'core/heading',
			array(
				'name'         => 'asterisk',
				'label'        => __( 'With asterisk', 'glowess' ),
				'inline_style' => "
				.is-style-asterisk:before {
					content: '';
					width: 1.5rem;
					height: 3rem;
					background: var(--wp--preset--color--contrast-2, currentColor);
					clip-path: path('M11.93.684v8.039l5.633-5.633 1.216 1.23-5.66 5.66h8.04v1.737H13.2l5.701 5.701-1.23 1.23-5.742-5.742V21h-1.737v-8.094l-5.77 5.77-1.23-1.217 5.743-5.742H.842V9.98h8.162l-5.701-5.7 1.23-1.231 5.66 5.66V.684h1.737Z');
					display: block;
				}

				/* Hide the asterisk if the heading has no content, to avoid using empty headings to display the asterisk only, which is an A11Y issue */
				.is-style-asterisk:empty:before {
					content: none;
				}

				.is-style-asterisk:-moz-only-whitespace:before {
					content: none;
				}

				.is-style-asterisk.has-text-align-center:before {
					margin: 0 auto;
				}

				.is-style-asterisk.has-text-align-right:before {
					margin-left: auto;
				}

				.rtl .is-style-asterisk.has-text-align-left:before {
					margin-right: auto;
				}",
			)
		);
	}
endif;

add_action( 'init', 'glowess_block_styles' );

/**
 * Enqueue block stylesheets.
 */

if ( ! function_exists( 'glowess_block_stylesheets' ) ) :
	/**
	 * Enqueue custom block stylesheets
	 *
	 * @since glowess 1.0.0
	 * @return void
	 */
	function glowess_block_stylesheets() {
		/**
		 * The wp_enqueue_block_style() function allows us to enqueue a stylesheet
		 * for a specific block. These will only get loaded when the block is rendered
		 * (both in the editor and on the front end), improving performance
		 * and reducing the amount of data requested by visitors.
		 *
		 * See https://make.wordpress.org/core/2021/12/15/using-multiple-stylesheets-per-block/ for more info.
		 */
		wp_enqueue_block_style(
			'core/button',
			array(
				'handle' => 'glowess-button-style-outline',
				'src'    => get_parent_theme_file_uri( 'assets/css/button-outline.css' ),
				'ver'    => wp_get_theme( get_template() )->get( 'Version' ),
				'path'   => get_parent_theme_file_path( 'assets/css/button-outline.css' ),
			)
		);
	}
endif;

add_action( 'init', 'glowess_block_stylesheets' );

if ( ! function_exists( 'glowess_support' ) ) :

	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * @since glowess
	 *
	 * @return void
	 */
	function glowess_support() {

		// Add support for block styles.
		add_theme_support( 'wp-block-styles' );

		// Enqueue editor styles.
		add_editor_style( 'style.css' );
		add_editor_style( 'editor-style.css' );
	}

endif;

add_action( 'after_setup_theme', 'glowess_support' );


/**
 * Enqueue stylesheets.
 */

if ( ! function_exists( 'glowess_styles' ) ) :

	/**
	 * Enqueue styles.
	 *
	 * @since pizzeria 1.0.0
	 *
	 * @return void
	 */
	function glowess_styles() {

		// Register theme stylesheet.
		$theme_version = wp_get_theme()->get( 'Version' );

		$version_string = is_string( $theme_version ) ? $theme_version : false;
		wp_register_style(
			'glowess-style',
			get_template_directory_uri() . '/style.css',
			array(),
			$version_string
		);

		// Enqueue theme stylesheet.
		wp_enqueue_style( 'glowess-style' );
		wp_style_add_data( 'glowess-style', 'rtl', 'replace' );
	}

endif;

add_action( 'wp_enqueue_scripts', 'glowess_styles' );
add_action( 'wp_enqueue_scripts', 'glowess_scripts' );
add_action( 'customize_register', '__return_true' );

if ( ! function_exists( 'glowess_scripts' ) ) {

	/**
	 * Enqueue script.
	 *
	 * @since glowess 1.0.0
	 *
	 * @return void
	 */
	function glowess_scripts() {

		// Enqueueing JS.
		wp_enqueue_script( 'glowess-slick-script', get_template_directory_uri() . '/assets/js/slick.min.js', array( 'jquery' ), wp_get_theme()->get( 'Version' ), true );
		wp_enqueue_script( 'glowess-script', get_template_directory_uri() . '/assets/js/script.js', array( 'jquery' ), wp_get_theme()->get( 'Version' ), true );
	}
}

/**
 * Enable Gutenberg in WooCommerce
 *
 * @param boolean $can_edit  Check true or false.
 * @param string  $post_type The post type being checked.
 */
function activate_gutenberg_product( $can_edit, $post_type ) {
	if ( 'product' === $post_type ) {
		$can_edit = true;
	}
	return $can_edit;
}
add_filter( 'use_block_editor_for_post_type', 'activate_gutenberg_product', 10, 2 );

/**
 * Enable taxonomy fields for woocommerce with gutenberg on
 *
 * @param array $args Arguments.
 */
function enable_taxonomy_rest( $args ) {
	$args['show_in_rest'] = true;
	return $args;
}
add_filter( 'woocommerce_taxonomy_args_product_cat', 'enable_taxonomy_rest' );
add_filter( 'woocommerce_taxonomy_args_product_tag', 'enable_taxonomy_rest' );

/**
 * Register pattern categories.
 */

if ( ! function_exists( 'glowess_pattern_categories' ) ) :
	/**
	 * Register pattern categories
	 *
	 * @since glowess 1.0.0
	 * @return void
	 */
	function glowess_pattern_categories() {

		register_block_pattern_category(
			'page',
			array(
				'label'       => _x( 'Pages', 'Block pattern category', 'glowess' ),
				'description' => __( 'A collection of full page layouts.', 'glowess' ),
			)
		);
	}
endif;

add_action( 'init', 'glowess_pattern_categories' );
