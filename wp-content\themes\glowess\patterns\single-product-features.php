<?php
/**
 * Title: Single Product Features
 * Slug: glowess/single-product-features
 * Categories: featured
 * Keywords: Single Product Features
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Single Product Features"},"align":"wide","className":"about-yours","style":{"spacing":{"padding":{"top":"74px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide about-yours" style="padding-top:74px"><!-- wp:columns {"align":"wide","className":"yours-scroll","style":{"spacing":{"blockGap":{"left":"202px"},"margin":{"top":"var:preset|spacing|40"}}}} -->
<div class="wp-block-columns alignwide yours-scroll" style="margin-top:var(--wp--preset--spacing--40)"><!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":120,"sizeSlug":"large","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/icon-2.svg'; ?>" alt="" class="wp-image-120"/></figure>
<!-- /wp:image -->

<!-- wp:heading {"textAlign":"center","style":{"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"16px","bottom":"0"},"padding":{"top":"6px"}}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:16px;margin-bottom:0;padding-top:6px;font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><?php echo esc_html__( 'Made in Europe', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:group {"style":{"spacing":{"margin":{"top":"0px"}}},"layout":{"type":"constrained","contentSize":"350px"}} -->
<div class="wp-block-group" style="margin-top:0px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","lineHeight":"2","fontStyle":"normal","fontWeight":"400"},"spacing":{"margin":{"top":"12px","bottom":"0"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:12px;margin-bottom:0;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Clean and natural skincare with safe and transparent ingredients', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":121,"sizeSlug":"large","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/icon-5.svg'; ?>" alt="" class="wp-image-121"/></figure>
<!-- /wp:image -->

<!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"capitalize","fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"16px","bottom":"0"},"padding":{"top":"6px"}}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:16px;margin-bottom:0;padding-top:6px;font-size:20px;font-style:normal;font-weight:500;line-height:1.5;text-transform:capitalize"><?php echo esc_html__( 'Sulfate Free', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:group {"style":{"spacing":{"margin":{"top":"0px"}}},"layout":{"type":"constrained","contentSize":"350px"}} -->
<div class="wp-block-group" style="margin-top:0px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"12px","bottom":"0"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:12px;margin-bottom:0;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Clean and natural skincare with safe and transparent ingredients', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":122,"sizeSlug":"large","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/icon-6.svg'; ?>" alt="" class="wp-image-122"/></figure>
<!-- /wp:image -->

<!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"spacing":{"margin":{"top":"16px","bottom":"0"},"padding":{"top":"6px"}}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:16px;margin-bottom:0;padding-top:6px;font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><?php echo esc_html__( 'Cruelty Free', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:group {"style":{"spacing":{"margin":{"top":"0px"}}},"layout":{"type":"constrained","contentSize":"350px"}} -->
<div class="wp-block-group" style="margin-top:0px"><!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"12px","bottom":"0"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:12px;margin-bottom:0;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Clean and natural skincare with safe and transparent ingredients', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->
