<?php
/**
 * Plugin Name: Glowess City Based E-Commerce
 * Plugin URI: https://sewpos.com
 * Description: Şehir bazlı e-ticaret sistemi - her ürün ve kategoriye tek şehir seçimi
 * Version: 2.0.0
 * Author: <PERSON><PERSON>ez
 * License: GPL v2 or later
 */

if (!defined('ABSPATH')) {
    exit;
}

define('GLOWESS_CITY_VERSION', '2.0.0');
define('GLOWESS_CITY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GLOWESS_CITY_PLUGIN_PATH', plugin_dir_path(__FILE__));

class GlowessCityEcommerce {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }
    
    public function init() {
        // WooCommerce kontrolü
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Temel bileşenleri başlat
        new GC_CityManager();
        new GC_ProductFilter();
        new GC_Admin();
    }

    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><strong>Glowess City Based E-Commerce:</strong> Bu eklenti WooCommerce gerektirir.</p>
        </div>
        <?php
    }
    
    public function activate() {
        // Şehirler post type'ını oluştur
        GC_CityManager::create_cities_post_type();
        flush_rewrite_rules();
    }
}

/**
 * Şehir Yöneticisi - Cities post type ve şehir işlemleri
 */
class GC_CityManager {
    
    public function __construct() {
        add_action('init', array($this, 'create_cities_post_type'));
        add_action('add_meta_boxes', array($this, 'add_city_meta_boxes'));
        add_action('save_post', array($this, 'save_city_meta_boxes'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        add_action('wp_ajax_update_city_cookie', array($this, 'handle_city_cookie_update'));
        add_action('wp_ajax_nopriv_update_city_cookie', array($this, 'handle_city_cookie_update'));
        
        // Shortcode ekle
        add_shortcode('city_banner', array($this, 'city_banner_shortcode'));
    }

    public static function create_cities_post_type() {
        register_post_type('cities', array(
            'label' => 'Şehirler',
            'public' => true,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => true,
            'menu_position' => 25,
            'menu_icon' => 'dashicons-location-alt',
            'supports' => array('title', 'thumbnail'),
            'labels' => array(
                'name' => 'Şehirler',
                'singular_name' => 'Şehir',
                'add_new' => 'Yeni Şehir Ekle',
                'edit_item' => 'Şehir Düzenle',
            )
        ));
    }

    public function add_city_meta_boxes() {
        add_meta_box(
            'city_details',
            'Şehir Detayları',
            array($this, 'city_details_meta_box'),
            'cities',
            'normal',
            'high'
        );
    }

    public function city_details_meta_box($post) {
        wp_nonce_field('city_details_nonce', 'city_details_nonce');
        
        $city_slug = get_post_meta($post->ID, '_city_slug', true);
        $is_active = get_post_meta($post->ID, '_city_is_active', true);

        // Slider alanları
        $slider_title = get_post_meta($post->ID, '_city_slider_title', true);
        $slider_subtitle = get_post_meta($post->ID, '_city_slider_subtitle', true);
        $slider_button_text = get_post_meta($post->ID, '_city_slider_button_text', true);
        $slider_button_url = get_post_meta($post->ID, '_city_slider_button_url', true);
        $slider_images = get_post_meta($post->ID, '_city_slider_images', true);

        if (!is_array($slider_images)) {
            $slider_images = array('', '', ''); // 3 slider için boş array
        }

        ?>
        <table class="form-table">
            <tr>
                <th><label for="city_slug">Şehir Kodu (URL için)</label></th>
                <td>
                    <input type="text" id="city_slug" name="city_slug" value="<?php echo esc_attr($city_slug); ?>" class="regular-text" />
                    <p class="description">Örnek: istanbul, ankara, izmir</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_is_active">Durum</label></th>
                <td>
                    <label>
                        <input type="checkbox" id="city_is_active" name="city_is_active" value="1" <?php checked($is_active, '1'); ?> />
                        Aktif
                    </label>
                </td>
            </tr>
        </table>

        <h3>Ana Sayfa Banner/Slider Ayarları</h3>
        <table class="form-table">
            <tr>
                <th><label for="city_slider_title">Banner Başlık</label></th>
                <td>
                    <input type="text" id="city_slider_title" name="city_slider_title" value="<?php echo esc_attr($slider_title); ?>" class="regular-text" />
                    <p class="description">Ana sayfa banner başlığı</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_slider_subtitle">Banner Açıklama</label></th>
                <td>
                    <textarea id="city_slider_subtitle" name="city_slider_subtitle" rows="3" cols="50"><?php echo esc_textarea($slider_subtitle); ?></textarea>
                    <p class="description">Banner açıklama metni</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_slider_button_text">Buton Metni</label></th>
                <td>
                    <input type="text" id="city_slider_button_text" name="city_slider_button_text" value="<?php echo esc_attr($slider_button_text); ?>" class="regular-text" />
                    <p class="description">Banner buton yazısı (örn: Alışverişe Başla)</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_slider_button_url">Buton Link</label></th>
                <td>
                    <input type="url" id="city_slider_button_url" name="city_slider_button_url" value="<?php echo esc_attr($slider_button_url); ?>" class="regular-text" />
                    <p class="description">Buton tıklandığında gidilecek sayfa</p>
                </td>
            </tr>
            <tr>
                <th><label>Banner Görselleri</label></th>
                <td>
                    <?php for ($i = 0; $i < 3; $i++):
                        $image_id = isset($slider_images[$i]) ? $slider_images[$i] : '';
                        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'medium') : '';
                    ?>
                        <div class="city-image-upload" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd;">
                            <label><strong>Slider <?php echo ($i + 1); ?> Görseli:</strong></label><br>

                            <div class="image-preview" style="margin: 10px 0;">
                                <?php if ($image_url): ?>
                                    <img src="<?php echo esc_url($image_url); ?>" style="max-width: 200px; height: auto; border: 1px solid #ddd;" />
                                <?php else: ?>
                                    <div style="width: 200px; height: 150px; background: #f0f0f0; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                                        Görsel Seçilmedi
                                    </div>
                                <?php endif; ?>
                            </div>

                            <input type="hidden" id="city_slider_image_<?php echo $i; ?>" name="city_slider_images[<?php echo $i; ?>]" value="<?php echo esc_attr($image_id); ?>" />

                            <button type="button" class="button city-upload-button" data-target="city_slider_image_<?php echo $i; ?>">
                                <?php echo $image_id ? 'Görseli Değiştir' : 'Görsel Seç'; ?>
                            </button>

                            <?php if ($image_id): ?>
                                <button type="button" class="button city-remove-button" data-target="city_slider_image_<?php echo $i; ?>" style="margin-left: 10px;">
                                    Görseli Kaldır
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                    <p class="description">En fazla 3 slider görseli ekleyebilirsiniz. Önerilen boyut: 1920x700px</p>
                </td>
            </tr>
        </table>
        <?php
    }

    public function save_city_meta_boxes($post_id) {
        if (!isset($_POST['city_details_nonce']) || !wp_verify_nonce($_POST['city_details_nonce'], 'city_details_nonce')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Temel alanlar
        if (isset($_POST['city_slug'])) {
            update_post_meta($post_id, '_city_slug', sanitize_text_field($_POST['city_slug']));
        }

        if (isset($_POST['city_is_active'])) {
            update_post_meta($post_id, '_city_is_active', '1');
        } else {
            update_post_meta($post_id, '_city_is_active', '0');
        }

        // Slider alanları
        $slider_fields = array(
            'city_slider_title' => 'sanitize_text_field',
            'city_slider_subtitle' => 'sanitize_textarea_field',
            'city_slider_button_text' => 'sanitize_text_field',
            'city_slider_button_url' => 'esc_url_raw'
        );

        foreach ($slider_fields as $field => $sanitize_function) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, $sanitize_function($_POST[$field]));
            }
        }

        // Slider görselleri (attachment ID olarak)
        if (isset($_POST['city_slider_images']) && is_array($_POST['city_slider_images'])) {
            $slider_images = array_map('intval', $_POST['city_slider_images']);
            // Boş değerleri temizle
            $slider_images = array_filter($slider_images);
            update_post_meta($post_id, '_city_slider_images', $slider_images);
        }
    }

    public function enqueue_scripts() {
        wp_enqueue_script('glowess-city-js', GLOWESS_CITY_PLUGIN_URL . 'assets/city.js', array('jquery'), GLOWESS_CITY_VERSION, true);
        
        wp_localize_script('glowess-city-js', 'glowess_city_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('glowess_city_nonce'),
            'current_city' => self::get_current_city_from_url()
        ));
    }

    /**
     * URL'den şehir slug'ını al ve çerezi güncelle
     */
    public static function get_current_city_from_url() {
        $request_uri = $_SERVER['REQUEST_URI'];
        
        // GET parametresi kontrolü (?city=istanbul)
        if (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);
            self::update_city_cookie($city_slug);
            return $city_slug;
        }

        // URL path kontrolü (/istanbul/ veya /istanbul/kategori gibi)
        $path = parse_url($request_uri, PHP_URL_PATH);
        $path_parts = array_filter(explode('/', trim($path, '/')));

        if (!empty($path_parts)) {
            $first_part = $path_parts[0];
            
            // Bu bir şehir slug'ı mı kontrol et
            if (self::is_valid_city_slug($first_part)) {
                self::update_city_cookie($first_part);
                return $first_part;
            }
        }

        // URL'de şehir yoksa çerezden al
        return isset($_COOKIE['glowess_selected_city']) ? sanitize_text_field($_COOKIE['glowess_selected_city']) : '';
    }

    /**
     * Şehir slug'ının geçerli olup olmadığını kontrol et
     */
    public static function is_valid_city_slug($slug) {
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => 1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_slug',
                    'value' => $slug,
                    'compare' => '='
                ),
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        return !empty($cities);
    }

    /**
     * Şehir çerezini güncelle
     */
    public static function update_city_cookie($city_slug) {
        if (!headers_sent()) {
            setcookie('glowess_selected_city', $city_slug, time() + (86400 * 30), '/');
        }
    }

    /**
     * Şehir slug'ına göre şehir ID'sini bul
     */
    public static function get_city_id_by_slug($city_slug) {
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => 1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_slug',
                    'value' => $city_slug,
                    'compare' => '='
                ),
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        return !empty($cities) ? $cities[0]->ID : false;
    }

    /**
     * AJAX ile çerez güncelleme
     */
    public function handle_city_cookie_update() {
        check_ajax_referer('glowess_city_nonce', 'nonce');
        
        $city_slug = sanitize_text_field($_POST['city_slug']);
        
        if (self::is_valid_city_slug($city_slug)) {
            self::update_city_cookie($city_slug);
            wp_send_json_success(array('message' => 'Şehir güncellendi'));
        } else {
            wp_send_json_error('Geçersiz şehir');
        }
    }

    /**
     * Mevcut seçili şehir ID'sini döndür
     */
    public static function get_current_city_id() {
        $city_slug = self::get_current_city_from_url();
        return $city_slug ? self::get_city_id_by_slug($city_slug) : false;
    }

    /**
     * Admin script'lerini yükle
     */
    public function admin_enqueue_scripts($hook) {
        global $post_type;

        // Şehir düzenleme sayfalarında medya uploader'ı yükle
        if (($hook == 'post.php' || $hook == 'post-new.php') && $post_type == 'cities') {
            wp_enqueue_media();
            wp_enqueue_script('glowess-city-admin-js', GLOWESS_CITY_PLUGIN_URL . 'assets/admin.js', array('jquery'), GLOWESS_CITY_VERSION, true);
        }
    }

    /**
     * Şehir banner shortcode
     */
    public function city_banner_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_title' => 'true',
            'show_subtitle' => 'true',
            'show_button' => 'true',
            'show_images' => 'true'
        ), $atts);

        $current_city_id = self::get_current_city_id();

        if (!$current_city_id) {
            return '<div class="glowess-no-city-banner"><p>Şehir seçin ve özel içerikleri görün!</p></div>';
        }

        $city = get_post($current_city_id);
        $slider_title = get_post_meta($current_city_id, '_city_slider_title', true);
        $slider_subtitle = get_post_meta($current_city_id, '_city_slider_subtitle', true);
        $slider_button_text = get_post_meta($current_city_id, '_city_slider_button_text', true);
        $slider_button_url = get_post_meta($current_city_id, '_city_slider_button_url', true);
        $slider_images = get_post_meta($current_city_id, '_city_slider_images', true);

        if (!is_array($slider_images)) {
            $slider_images = array();
        }

        ob_start();
        ?>
        <div class="glowess-city-banner" data-city="<?php echo esc_attr($city->post_title); ?>">
            <?php if ($atts['show_images'] === 'true' && !empty($slider_images)): ?>
                <div class="city-slider">
                    <?php foreach ($slider_images as $image_id): 
                        $image_url = wp_get_attachment_image_url($image_id, 'full');
                        if ($image_url): ?>
                            <div class="slider-item">
                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($city->post_title); ?>" />
                            </div>
                        <?php endif;
                    endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="banner-content">
                <div class="city-info">
                    <span class="current-city"><?php echo esc_html($city->post_title); ?></span>
                </div>

                <?php if ($atts['show_title'] === 'true' && $slider_title): ?>
                    <h1 class="banner-title"><?php echo esc_html($slider_title); ?></h1>
                <?php endif; ?>

                <?php if ($atts['show_subtitle'] === 'true' && $slider_subtitle): ?>
                    <p class="banner-subtitle"><?php echo esc_html($slider_subtitle); ?></p>
                <?php endif; ?>

                <?php if ($atts['show_button'] === 'true' && $slider_button_text && $slider_button_url): ?>
                    <div class="banner-actions">
                        <a href="<?php echo esc_url($slider_button_url); ?>" class="banner-button">
                            <?php echo esc_html($slider_button_text); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <style>
        .glowess-city-banner {
            position: relative;
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .city-slider {
            position: relative;
            height: 400px;
            overflow: hidden;
        }

        .city-slider .slider-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .city-slider img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            background: rgba(0,0,0,0.4);
            padding: 40px 20px;
        }

        .current-city {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            margin-bottom: 20px;
            display: inline-block;
        }

        .banner-title {
            font-size: 3em;
            margin: 0 0 20px 0;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .banner-subtitle {
            font-size: 1.2em;
            margin: 0 0 30px 0;
            max-width: 600px;
            line-height: 1.6;
        }

        .banner-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .banner-button:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            color: white;
            text-decoration: none;
        }

        .glowess-no-city-banner {
            text-align: center;
            padding: 40px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .banner-title {
                font-size: 2em;
            }
            
            .banner-content {
                padding: 20px 15px;
            }
            
            .city-slider {
                height: 300px;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }
}

/**
 * Ürün ve Kategori Filtreleme
 */
class GC_ProductFilter {
    
    public function __construct() {
        // Ürün alanları
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_city_field_to_product'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_city_field'));

        // Kategori alanları
        add_action('product_cat_add_form_fields', array($this, 'add_city_field_to_category_add'));
        add_action('product_cat_edit_form_fields', array($this, 'add_city_field_to_category_edit'));
        add_action('created_product_cat', array($this, 'save_category_city_field'));
        add_action('edited_product_cat', array($this, 'save_category_city_field'));

        // Filtreleme
        add_action('pre_get_posts', array($this, 'filter_products_by_city'));
        add_filter('get_terms_args', array($this, 'filter_categories_by_city'), 10, 2);
    }

    /**
     * Ürüne şehir seçim alanı ekle
     */
    public function add_city_field_to_product() {
        global $post;
        
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        $selected_city = get_post_meta($post->ID, '_product_city', true);

        echo '<div class="product_city_field show_if_simple show_if_variable">';
        echo '<p class="form-field"><label><strong>Bu ürün hangi şehirde satılacak?</strong></label></p>';
        
        echo '<p class="form-field">';
        echo '<select name="product_city" style="width: 100%;">';
        echo '<option value="">Şehir Seçin</option>';
        
        foreach ($cities as $city) {
            $city_slug = get_post_meta($city->ID, '_city_slug', true);
            $selected = selected($selected_city, $city->ID, false);
            echo '<option value="' . $city->ID . '" ' . $selected . '>' . $city->post_title . '</option>';
        }
        
        echo '</select>';
        echo '</p></div>';
    }

    /**
     * Ürün şehir alanını kaydet
     */
    public function save_product_city_field($post_id) {
        $city_id = isset($_POST['product_city']) ? intval($_POST['product_city']) : '';
        update_post_meta($post_id, '_product_city', $city_id);
    }

    /**
     * Kategori ekleme formuna şehir alanı ekle
     */
    public function add_city_field_to_category_add() {
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        ?>
        <div class="form-field">
            <label for="category_city"><strong>Bu kategori hangi şehirde gösterilecek?</strong></label>
            <select name="category_city" id="category_city" style="width: 100%;">
                <option value="">Şehir Seçin</option>
                <?php foreach ($cities as $city): ?>
                    <option value="<?php echo $city->ID; ?>"><?php echo $city->post_title; ?></option>
                <?php endforeach; ?>
            </select>
            <p class="description">Kategorinin hangi şehirde görüneceğini seçin.</p>
        </div>
        <?php
    }

    /**
     * Kategori düzenleme formuna şehir alanı ekle
     */
    public function add_city_field_to_category_edit($term) {
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        $selected_city = get_term_meta($term->term_id, '_category_city', true);

        ?>
        <tr class="form-field">
            <th scope="row">
                <label for="category_city"><strong>Bu kategori hangi şehirde gösterilecek?</strong></label>
            </th>
            <td>
                <select name="category_city" id="category_city" style="width: 100%;">
                    <option value="">Şehir Seçin</option>
                    <?php foreach ($cities as $city): ?>
                        <option value="<?php echo $city->ID; ?>" <?php selected($selected_city, $city->ID); ?>>
                            <?php echo $city->post_title; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <p class="description">Kategorinin hangi şehirde görüneceğini seçin.</p>
            </td>
        </tr>
        <?php
    }

    /**
     * Kategori şehir alanını kaydet
     */
    public function save_category_city_field($term_id) {
        $city_id = isset($_POST['category_city']) ? intval($_POST['category_city']) : '';
        update_term_meta($term_id, '_category_city', $city_id);
    }

    /**
     * Ürünleri seçili şehre göre filtrele
     */
    public function filter_products_by_city($query) {
        if (is_admin() || !$query->is_main_query()) {
            return;
        }

        // Sadece ürün sorgularını filtrele
        if (!isset($query->query_vars['post_type']) || $query->query_vars['post_type'] !== 'product') {
            return;
        }

        $current_city_id = GC_CityManager::get_current_city_id();
        
        if (!$current_city_id) {
            return;
        }

        $meta_query = $query->get('meta_query');
        if (!is_array($meta_query)) {
            $meta_query = array();
        }

        $meta_query[] = array(
            'key' => '_product_city',
            'value' => $current_city_id,
            'compare' => '='
        );

        $query->set('meta_query', $meta_query);
    }

    /**
     * Kategorileri seçili şehre göre filtrele
     */
    public function filter_categories_by_city($args, $taxonomies) {
        if (is_admin()) {
            return $args;
        }

        if (!in_array('product_cat', $taxonomies)) {
            return $args;
        }

        $current_city_id = GC_CityManager::get_current_city_id();
        
        if (!$current_city_id) {
            return $args;
        }

        if (!isset($args['meta_query'])) {
            $args['meta_query'] = array();
        }

        $args['meta_query'][] = array(
            'relation' => 'OR',
            array(
                'key' => '_category_city',
                'value' => $current_city_id,
                'compare' => '='
            ),
            array(
                'key' => '_category_city',
                'compare' => 'NOT EXISTS'
            )
        );

        return $args;
    }
}

/**
 * Admin Panel
 */
class GC_Admin {
    
    public function __construct() {
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
        }
    }

    public function add_admin_menu() {
        add_menu_page(
            'Glowess Şehir Ayarları',
            'Şehir Yönetimi',
            'manage_options',
            'glowess-city-management',
            array($this, 'admin_page'),
            'dashicons-location-alt',
            30
        );
    }

    public function admin_page() {
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));

        ?>
        <div class="wrap">
            <h1>Glowess Şehir Yönetimi</h1>
            
            <div class="card">
                <h2>Şehirler</h2>
                <p><a href="<?php echo admin_url('post-new.php?post_type=cities'); ?>" class="button button-primary">Yeni Şehir Ekle</a></p>
                
                <?php if (!empty($cities)): ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>Şehir Adı</th>
                                <th>Slug</th>
                                <th>Durum</th>
                                <th>Ürün Sayısı</th>
                                <th>Kategori Sayısı</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cities as $city): 
                                $is_active = get_post_meta($city->ID, '_city_is_active', true);
                                $city_slug = get_post_meta($city->ID, '_city_slug', true);
                                $product_count = $this->get_city_product_count($city->ID);
                                $category_count = $this->get_city_category_count($city->ID);
                            ?>
                                <tr>
                                    <td><strong><?php echo esc_html($city->post_title); ?></strong></td>
                                    <td><code><?php echo esc_html($city_slug); ?></code></td>
                                    <td>
                                        <?php if ($is_active): ?>
                                            <span style="color: green;">✓ Aktif</span>
                                        <?php else: ?>
                                            <span style="color: red;">✗ Pasif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $product_count; ?> ürün</td>
                                    <td><?php echo $category_count; ?> kategori</td>
                                    <td>
                                        <a href="<?php echo get_edit_post_link($city->ID); ?>" class="button button-small">Düzenle</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>Henüz şehir eklenmemiş. <a href="<?php echo admin_url('post-new.php?post_type=cities'); ?>">İlk şehri ekle</a></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    private function get_city_product_count($city_id) {
        $products = get_posts(array(
            'post_type' => 'product',
            'numberposts' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_product_city',
                    'value' => $city_id,
                    'compare' => '='
                )
            ),
            'fields' => 'ids'
        ));

        return count($products);
    }

    private function get_city_category_count($city_id) {
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'meta_query' => array(
                array(
                    'key' => '_category_city',
                    'value' => $city_id,
                    'compare' => '='
                )
            )
        ));

        return is_array($categories) ? count($categories) : 0;
    }
}

// Eklentiyi başlat
new GlowessCityEcommerce();

// Yardımcı fonksiyonlar
function glowess_get_current_city_slug() {
    return GC_CityManager::get_current_city_from_url();
}

function glowess_get_current_city_id() {
    return GC_CityManager::get_current_city_id();
}

function glowess_get_current_city() {
    $city_id = GC_CityManager::get_current_city_id();
    return $city_id ? get_post($city_id) : false;
}
?>