/*
################
* === Mini Cart  ===
################
*/

.wc-block-mini-cart__button {
    padding: 0 11px 0 0;
    cursor: pointer;
}

.wc-block-mini-cart__icon.changed {
    margin: 0;
    width: 18px;
    height: 20px;
}

.wc-block-mini-cart {
    cursor: pointer;
}

body:not(.editor-styles-wrapper) {
    .wc-block-mini-cart:not([data-mini-cart-icon="bag"], [data-mini-cart-icon="bag-alt"]) {
        .wc-block-mini-cart__quantity-badge {
            svg {display: none;}
    
            &::before {
                width: 19px;
                height: 18px;
                content: " ";
                display: block;
                background-image: url('data:image/svg+xml,<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.833252 1.29941C0.833252 0.949871 1.12217 0.666504 1.47856 0.666504H2.11048C3.30377 0.666504 4.31032 1.538 4.45833 2.69934L4.62945 4.04203H16.8273C17.7478 4.04203 18.4528 4.84502 18.3163 5.7379L17.3938 11.7703C17.22 12.9063 16.225 13.7467 15.0538 13.7467H6.83234C5.66112 13.7467 4.66614 12.9063 4.4924 11.7703L3.42162 4.76883L3.41946 4.75345L3.17768 2.85634C3.1104 2.32846 2.65288 1.93233 2.11048 1.93233H1.47856C1.12217 1.93233 0.833252 1.64896 0.833252 1.29941ZM4.80911 5.30785L5.76874 11.5825C5.8477 12.0988 6.29997 12.4808 6.83234 12.4808H15.0538C15.5862 12.4808 16.0384 12.0988 16.1174 11.5825L17.0399 5.55012C17.0594 5.42256 16.9588 5.30785 16.8273 5.30785H4.80911Z" fill="%23131C19"/><path d="M16.1056 16.4893C16.1056 16.9554 15.7203 17.3333 15.2452 17.3333C14.77 17.3333 14.3848 16.9554 14.3848 16.4893C14.3848 16.0233 14.77 15.6455 15.2452 15.6455C15.7203 15.6455 16.1056 16.0233 16.1056 16.4893Z" fill="%23131C19"/><path d="M7.50133 16.4893C7.50133 16.9554 7.11612 17.3333 6.64093 17.3333C6.16573 17.3333 5.78052 16.9554 5.78052 16.4893C5.78052 16.0233 6.16573 15.6455 6.64093 15.6455C7.11612 15.6455 7.50133 16.0233 7.50133 16.4893Z" fill="%23131C19"/></svg>');
            }
        }
    }
}

.wc-block-mini-cart__button:hover:not([disabled]) {
    opacity: 1;
}

.wc-block-mini-cart__quantity-badge {
    gap: 6px;
}

.wc-block-mini-cart[style="visibility:hidden"] {
    display: none;
}

.wc-block-mini-cart__badge {
    width: 16px;
    height: 16px;
    background-color: var(--wp--preset--color--secondary);
    color: var(--wp--preset--color--base);
    font-size: 8px;
    font-weight: 400;
    margin-left: -5px;
    top: 4px;
}

.wc-block-components-button:not(.is-link).outlined:not(:focus) {
    box-shadow: none;
}

.wc-block-mini-cart__template-part {
    .wc-block-components-drawer__close {
        width: 30px;
        height: 30px;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 !important;
        color: var(--wp--preset--color--base);
        opacity: 1;
        top: 36px;
        right: 45px;
        border-color: var(--wp--preset--color--gray-100);
        z-index: 9999;
        min-width: auto;
        background-color: var(--wp--preset--color--bg-1) !important;

        @media (min-width:768px) {
            top: 40px;
        }

        svg {
            height: 14px;
            width: 14px;
        }
    }

    .wc-block-components-product-metadata__description {
        .has-global-padding {
            padding: 0;
        }

        p {
            font-size: 12px;
        }
    }
} 

.wc-block-components-quantity-selector {
    input.wc-block-components-quantity-selector__input:focus,
    .wc-block-components-quantity-selector__button:focus {
        box-shadow: none;
    }

    &::after {
        border-color: var(--wp--preset--color--base);
        opacity: 1;
    }
}

.is-medium table.wc-block-cart-items .wc-block-cart-items__row, 
.is-mobile table.wc-block-cart-items .wc-block-cart-items__row, 
.is-small table.wc-block-cart-items .wc-block-cart-items__row {
    grid-template-columns:50px 132px;

    @media (min-width:768px) {
        grid-template-columns:130px 132px;
    }
}

.is-medium table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image, 
.is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image, 
.is-small table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image {
    padding-right: 16px;

    @media (min-width:768px) {
        padding-right: 30px;
    }
}

.wc-block-components-quantity-selector {
    input.wc-block-components-quantity-selector__input {
        font-size: 14px;
        padding: 10px 0;
    }
}

.wc-block-components-drawer:after {
    border-color: var(--wp--preset--color--base);
}

.wc-block-cart-item__remove-link {
    cursor: pointer;
}

.wc-block-components-product-badge {
    border-width: 0;
    background-color: color-mix(in srgb, var(--wp--preset--color--info) 90%, var(--wp--preset--color--info) 0%);
    color: var(--wp--preset--color--base);
    font-size: 10px;
    border-radius: 0;
    padding: 4px 10px;
}

h2.wc-block-mini-cart__title {
    font-size: clamp(1rem, 0.9074rem + 0.4938vw, 1.5rem);
    margin: 0;
    padding: 25px 30px;
    border-bottom: 1px solid var(--wp--preset--color--gray-100);
    text-transform: uppercase;
    mask-image:none;
}

h2.wc-block-mini-cart__title span:first-child {
    margin-right: 0;
}

.wc-block-mini-cart__footer:after {
    border-color: var(--wp--preset--color--gray-100);
    opacity: 0;
}

.wc-block-mini-cart__items {
    padding: 16px;
    margin-right: 2px;

    @media (min-width:768px) {
        padding: 30px;
    }

    table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__quantity .wc-block-cart-item__remove-link {
        font-size: 15px;
        margin-top: 14px;
    }

    .wc-block-components-quantity-selector {
        min-width:98px;
        width: 92px;
        margin-top: 16px !important;

        .wc-block-components-quantity-selector__button--plus {
            margin-left: 14px;
        }

        .wc-block-components-quantity-selector__button--minus {
            margin-right: 14px;
        }

        .wc-block-components-quantity-selector__input {
            min-height: 24px;
        }

        button,
        .wc-block-components-quantity-selector__button {
            width: 20px;
            height: 20px;
            min-width: 20px;
            font-size: 11px;
            color: var(--wp--preset--color--secondary);
            opacity: 1;
        }

        input.wc-block-components-quantity-selector__input {
            min-width: 20px;
        }
    }
    

    .wc-block-mini-cart-items {
        tbody {
            .wc-block-cart-items__row {
                border-bottom: 1px solid var(--wp--preset--color--gray-100);
            }
        }

        .wc-block-components-product-price__value.is-discounted {
            margin-left: 0;
        }

        .wc-block-components-product-name {
            font-size: 15px;
            font-weight: 500;
        }

        .wc-block-cart-item__prices .wc-block-components-product-price {
            padding: 0;
            justify-content: flex-end;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            gap: 4px;
            font-size: 15px;
            
            del {
                font-size: 12px;
                color: var(--wp--preset--color--gray-300);
                font-weight: normal;
            }
        }

        .wc-block-cart-item__total .wc-block-components-product-price {
            padding: 0 0 0 30px;
        }
    }

    &::-webkit-scrollbar {
        width: 2px;
        height: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--wp--preset--color--contrast);
    }

    &::-webkit-scrollbar-track {
        background-color: var(--wp--preset--color--gray-100);
    }

    .wc-block-cart-item__total {
        .wc-block-components-product-price {
            justify-content: flex-end;
        }
    }

    .wc-block-cart-item__image {
        a {
            display: block;
        }
    }
}

.wc-block-mini-cart__drawer.is-medium table.wc-block-cart-items .wc-block-cart-items__row, 
.wc-block-mini-cart__drawer.is-mobile table.wc-block-cart-items .wc-block-cart-items__row, 
.wc-block-mini-cart__drawer.is-small table.wc-block-cart-items .wc-block-cart-items__row {
    padding: 0 0 30px 0;
    margin-bottom: 30px;
}

.wc-block-mini-cart__footer {
    padding: 20px 30px;

    .wc-block-mini-cart__footer-actions {
        column-gap: 27px;
        row-gap: 14px;
    }

    .wc-block-mini-cart__footer-actions{
        .components-button {
            min-height: 50px;
        }
    }

    .wc-block-components-totals-item__value,
    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal {
        font-weight: 500;
        font-size: 19px;
    }

    .wp-block-woocommerce-mini-cart-checkout-button-block {
        &:not(:hover) {
            //color: var(--wp--preset--color--contrast);
        }
    }

    .wp-block-woocommerce-mini-cart-cart-button-block {
        &:not(:hover) {
            border-color: var(--wp--preset--color--primary);
            color: var(--wp--preset--color--primary);
        }
    }

    .wp-element-button {
        padding: 14.22px;
        //border-radius: 50px;
        line-height: 1.572;
        color: var(--wp--preset--color--base);
        gap: 10px;
        transition: 0.3s ease-in-out;

        &::after{
            content: "\F144";
            font-family: bootstrap-icons;
        }
    }

    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal {
        padding-bottom: 6px;
        margin-bottom: 20px;
    }

    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal .wc-block-components-totals-item__description {
        font-size: 12px;
    }
}

.wc-block-mini-cart__empty-cart-wrapper {
    strong {
        font-weight: 500;
    }

    p {
        font-size: 20px;
        margin-bottom: 30px;
    }

    a {
        padding: 10px 30px;
        min-height: 50px;
    }
}