<?php
/**
 * Title: Footer v4
 * Slug: glowess/footer-v4
 * Categories: footer
 * Block Types: core/template-part/footer-v4
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Footer v4"},"align":"full","className":"light-mode footer-v4","style":{"spacing":{"padding":{"top":"0","bottom":"0","left":"0","right":"0"},"blockGap":"0"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull light-mode footer-v4 has-base-color has-text-color has-link-color" style="padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><!-- wp:group {"align":"full","style":{"spacing":{"padding":{"right":"var:preset|spacing|40","left":"var:preset|spacing|40"}},"border":{"radius":"50px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="border-radius:50px;padding-right:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--40)"><!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|20"},"margin":{"bottom":"var:preset|spacing|40"},"blockGap":"var:preset|spacing|40"},"border":{"radius":"50px"}},"backgroundColor":"secondary","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide has-secondary-background-color has-background" style="border-radius:50px;margin-bottom:var(--wp--preset--spacing--40);padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--20)"><!-- wp:group {"align":"wide","style":{"spacing":{"blockGap":"250px","padding":{"top":"5px"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"left"}} -->
<div class="wp-block-group alignwide" style="padding-top:5px"><!-- wp:group {"style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:site-title {"style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"x-large","fontFamily":"heading"} /-->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"380px","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"none","fontStyle":"normal","fontWeight":"300","lineHeight":"2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"medium"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-medium-font-size" style="font-style:normal;font-weight:300;line-height:2;text-transform:none"><?php echo esc_html__( 'Made using clean, non-toxic ingredients, our', 'glowess' ); ?><br><?php echo esc_html__( 'products are designed for everyone.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:social-links {"iconColor":"base","iconColorValue":"#ffffff","openInNewTab":true,"size":"has-normal-icon-size","className":"is-style-logos-only footer-social-icons"} -->
<ul class="wp-block-social-links has-normal-icon-size has-icon-color is-style-logos-only footer-social-icons"><!-- wp:social-link {"url":"#","service":"facebook"} /-->

<!-- wp:social-link {"url":"#","service":"x"} /-->

<!-- wp:social-link {"url":"#","service":"instagram"} /-->

<!-- wp:social-link {"url":"#","service":"youtube"} /--></ul>
<!-- /wp:social-links --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"footer-widgets","style":{"spacing":{"blockGap":"90px"}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between","verticalAlignment":"stretch"}} -->
<div class="wp-block-group footer-widgets"><!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"default","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family has-default-font-size" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Shop All', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":604,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Explore', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":606,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'More', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":15,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Community', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":21,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"var:preset|spacing|30"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide" style="padding-top:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--30)"><!-- wp:paragraph {"className":"copyright-text","style":{"typography":{"fontStyle":"normal","fontWeight":"300","fontSize":"13px"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base"} -->
<p class="copyright-text has-base-color has-text-color has-link-color" style="font-size:13px;font-style:normal;font-weight:300"><?php echo esc_html__( '© 2024 - All rights reserved. Made by ', 'glowess' ); ?><a href="#"><?php echo esc_html__( 'Glowess.', 'glowess' ); ?></a></p>
<!-- /wp:paragraph -->

<!-- wp:navigation {"ref":116,"textColor":"base","overlayMenu":"never","overlayTextColor":"secondary","className":"footer-language","style":{"typography":{"fontSize":"13px","fontStyle":"normal","fontWeight":"300"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
