<?php
/**
 * Title: Home v2 Testimonial
 * Slug: glowess/home-v2-testimonial
 * Categories: featured
 * Keywords: Home v2 Testimonial
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home-v2-testimonial"},"align":"full","className":"v2-testimonial","style":{"spacing":{"padding":{"top":"var:preset|spacing|40","bottom":"var:preset|spacing|40"}}},"backgroundColor":"bg-2","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull v2-testimonial has-bg-2-background-color has-background" style="padding-top:var(--wp--preset--spacing--40);padding-bottom:var(--wp--preset--spacing--40)"><!-- wp:group {"style":{"spacing":{"padding":{"left":"0px","right":"0px","top":"var:preset|spacing|40","bottom":"var:preset|spacing|40"},"margin":{"top":"13px","bottom":"31px"}}},"layout":{"type":"constrained","contentSize":"1390px"}} -->
<div class="wp-block-group" style="margin-top:13px;margin-bottom:31px;padding-top:var(--wp--preset--spacing--40);padding-right:0px;padding-bottom:var(--wp--preset--spacing--40);padding-left:0px"><!-- wp:group {"className":"gl-slick-single","layout":{"type":"default"}} -->
<div class="wp-block-group gl-slick-single"><!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"0px"}}}} -->
<div class="wp-block-columns"><!-- wp:column {"verticalAlignment":"top","width":"","style":{"spacing":{"blockGap":"17px","padding":{"top":"38px"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:38px"><!-- wp:group {"style":{"spacing":{"blockGap":"8px","padding":{"bottom":"32px"}}},"layout":{"type":"constrained","justifyContent":"left"}} -->
<div class="wp-block-group" style="padding-bottom:32px"><!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'What Clients Are Saying', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'At vero eos et accusamus.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"7px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group"><!-- wp:image {"id":742,"sizeSlug":"full","linkDestination":"none"} -->
<figure class="wp-block-image size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"scale":"cover","sizeSlug":"full","linkDestination":"none"} -->
<figure class="wp-block-image size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742" style="object-fit:cover"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"32px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"},"spacing":{"margin":{"top":"26px"}}},"textColor":"secondary","fontFamily":"body"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color has-body-font-family" style="margin-top:26px;font-size:32px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( '“I absolutely love the products I purchased from this boutique! The quality is exceptional, and my skin has never looked better. The packaging is also beautiful, making it a luxurious experience every time I use them. Highly recommend!”', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:heading {"level":5,"style":{"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary"} -->
<h5 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><?php echo esc_html__( 'Ali Tufan', 'glowess' ); ?></h5>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"spacing":{"margin":{"top":"0px"}}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="margin-top:0px;font-size:15px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( 'New York, USA', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":756,"width":"565px","height":"650px","scale":"cover","sizeSlug":"full","linkDestination":"none","align":"right","className":"image-height"} -->
<figure class="wp-block-image alignright size-full is-resized image-height"><img src="https://transvelo.github.io/glowess/assets/images/v2-testimonial-1.png" alt="" class="wp-image-756" style="object-fit:cover;width:565px;height:650px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"0px"}}}} -->
<div class="wp-block-columns"><!-- wp:column {"verticalAlignment":"top","style":{"spacing":{"blockGap":"17px","padding":{"top":"38px"}}}} -->
<div class="wp-block-column is-vertically-aligned-top" style="padding-top:38px"><!-- wp:group {"style":{"spacing":{"blockGap":"8px","padding":{"bottom":"32px"}}},"layout":{"type":"constrained","justifyContent":"left"}} -->
<div class="wp-block-group" style="padding-bottom:32px"><!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'What Clients Are Saying', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'At vero eos et accusamus.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"7px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group"><!-- wp:image {"id":742,"sizeSlug":"full","linkDestination":"none"} -->
<figure class="wp-block-image size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"scale":"cover","sizeSlug":"full","linkDestination":"none"} -->
<figure class="wp-block-image size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742" style="object-fit:cover"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image -->

<!-- wp:image {"id":742,"sizeSlug":"large","linkDestination":"none"} -->
<figure class="wp-block-image size-large"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/star.svg'; ?>" alt="" class="wp-image-742"/></figure>
<!-- /wp:image --></div>
<!-- /wp:group -->

<!-- wp:heading {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"32px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"},"spacing":{"margin":{"top":"26px"}}},"textColor":"secondary","fontFamily":"body"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color has-body-font-family" style="margin-top:26px;font-size:32px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( '“I absolutely love the products I purchased from this boutique! The quality is exceptional, and my skin has never looked better. The packaging is also beautiful, making it a luxurious experience every time I use them. Highly recommend!”', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:heading {"level":5,"style":{"typography":{"fontSize":"20px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary"} -->
<h5 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="font-size:20px;font-style:normal;font-weight:500;line-height:1.5"><?php echo esc_html__( 'Ali Tufan', 'glowess' ); ?></h5>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.5"},"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"spacing":{"margin":{"top":"0px"}}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="margin-top:0px;font-size:15px;font-style:normal;font-weight:400;line-height:1.5"><?php echo esc_html__( 'New York, USA', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:column -->

<!-- wp:column {"width":""} -->
<div class="wp-block-column"><!-- wp:image {"id":790,"width":"565px","height":"650px","scale":"cover","sizeSlug":"full","linkDestination":"none","align":"right","className":"image-height"} -->
<figure class="wp-block-image alignright size-full is-resized image-height"><img src="https://transvelo.github.io/glowess/assets/images/v2-testimonial-2.png" alt="" class="wp-image-790" style="object-fit:cover;width:565px;height:650px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
