=== Metadata Viewer ===
Contributors: pluginizelab, aminurislam01
Tags: metadata, post meta, user meta, custom post type meta, meta viewer
Requires at least: 6.0.0
Stable tag: 2.1.0
Tested up to: 6.6
Requires PHP: 7.4
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A plugin or theme developer can view metadata by this plugin easily. 

== Description ==

The Metadata Viewer plugin displays post, user (ie. posts, pages, and custom post types, user, WooCommerce products & orders) meta keys and their values at the bottom of the post & user editing page. There is also integrated realtime search feature.
Just install a single plugin to solve multiple purpose like to show posts, pages, custom post types & user meta.


= Features =
* Posts Metadata Viewer
* Custom Post Types Metadata Viewer
* Pages Metadata Viewer
* Users Metadata Viewer
* WooCommerce Products Metadata Viewer
* WooCommerce Orders Metadata Viewer


= Up-Comming Features =
* Comment Metadata Viewer
* Term Metadata Viewer

== Installation ==

= FOR STANDARD INSTALLATION: =
Installing this plugin is very easy just like any other WordPress plugin. Please follow these instructions:
1. In your WordPress admin panel, go to Plugins > New Plugin, search for "Metadata Viewer" and click on "Install Now"
2. Alternatively, download the plugin and upload the metadata-viewer.zip to your plugins directory, which usually is /wp-content/plugins/.
3. Activate the plugin from plugins page.