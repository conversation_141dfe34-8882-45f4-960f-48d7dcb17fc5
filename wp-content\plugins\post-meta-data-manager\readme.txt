=== Post Meta Data Manager ===

Contributors: gand<PERSON><PERSON>esh9
Donate link: https://paypal.me/h1994tesh
Plugin Name:  Post Meta Data Manager
Plugin URI: http://www.wpexpertplugins.com/
Tags: custom post meta editor, meta fiedls editor, meta, post meta, inspector, custom fields, debug, tools
Requires at least: 3.0
Tested up to: 6.8
Stable tag: 1.4.3
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
WC tested up to: 4.8.0
Author URI: http://www.wpexpertplugins.com/contact-us/


== Description ==
**Tired of changing values of hidden post meta or custom post meta through database or coding?**
**Then you have landed on Correct wordpress plugin.**

**Need some help to view your post meta or custom fields?**

Post Meta Data Manager plugin displays all post meta or custom fields keys and their values in a metabox at the bottom of post editing.

Awesome thing is there are no plugin settings.
You just need to install and activate the plugin by yourself.

**Boom! Added User Meta Manager**
* Now you can handle all user meta fields values from user edit page.

**Added Taxonomoies Meta Manager**
* Now you can handle all term or taxonomies meta fields values from term edit page.

**Enabling the display of post meta data manager box by configuring the selected post type.**

= Stunning Features: =

* You can search & replace that values from edit pages and posts.
* You can sort key column by ascending and descending orders.
* There is no long list of the meta keys. You will see in 10 items in a page.
* Works with any custom post types.

* Do you have an idea or want to some enhancement then just contact me.
* [Feature requests](http://www.wpexpertplugins.com/contact-us/)

= Contributing =

* report bugs and suggest features on [GitHub](https://github.com/gandhihitesh9/post-meta-data-manager) or [WordPress Support Forum]((https://wordpress.org/support/plugin/post-meta-data-manager))

== Installation ==

The plugin is simple to install:

 * Download post-meta-data-manager.zip
 * Unzip
 * Upload post-meta-data-manager directory to your /wp-content/plugins directory
 * Activate the "Post Meta Data Manager" list plugin through the 'Plugins' menu in WordPress.

== Screenshots ==
1. List of keys with search, pagination, limit per page, sorting by key column.
2. Edit single field value.
3. Edit multi fields value.

== Changelog ==
= 1.4.3 - 2025-04-25 =
* Resolved multisite security issue.

= 1.4.2 - 2024-07-23 = 
* Resolved a support ticket.
* https://wordpress.org/support/topic/cant-see-metadata-section-on-1-4-1/

= 1.4.1 - 2024-07-14 = 
* Resolved a support ticket.
* https://wordpress.org/support/topic/breaks-when-real3d-pdf-is-installed/

= 1.4.0 - 2024-07-10 = 
* Resolved all support tickets.
* https://wordpress.org/support/topic/fatal-error-on-coupons-because-of-wrong-data-type/
* https://wordpress.org/support/topic/incompatible-with-high-performance-order-storage-17/
* https://wordpress.org/support/topic/plugin-conflict-with-postx-pro-taxonomy-image-function/
* https://wordpress.org/support/topic/bug-woocommerce-price-not-update-if-sale-price-is-earlier-set-and-removed/

= 1.3.0 - 2024-06-29 = 
* Vulnerability fixes

= 1.2.3 - 2023-12-21
* Resolved the issue of storing unnecessary metadata.
* https://wordpress.org/support/topic/plugin-corrupted-post_meta-table/

= 1.2.2 - 2023-11-11 = 
* Vulnerability fixes

= 1.2.1 - 2023-10-20 = 
* Vulnerability fixes

= 1.2.0 - 2023-04-15 = 
* Selection of taxonomoies setting provided.


= 1.1.5 - 2023-04-11 = 
* General Settings page.
* Provide selection of post types.

= 1.1.4 - 2023-03-11 = 
* check is string value

= 1.1.3 - 2022-11-09 = 
* Applied max-height for bigger model.

= 1.1.2 - 2022-11-09 = Fixed issue
* Fixed issue when object stored in key - https://wordpress.org/support/topic/php-fatal-error-324/

= 1.1.1 - 2022-10-28 = Feature Requests
* Added meta box for profile.php - https://wordpress.org/support/topic/good-plugin-as-compare-to-others-but/


= 1.1.0 - 2022-09-12 =
* Fix Woocommerce Product terms add edit page not saving default fields data.

= 1.0.3 - 2022-03-13 =
* Added taxonomy metaboxes


= 1.0.2 - 2021-08-31 =
* Move all html code into seprate file.
* Added meta box for user edit and listings of all user meta keys.
* Edit & delete user meta key's value.
* Searching, sorting, pagination for listings.

= 1.0.1 - 2021-08-26 =
* Added support for long string meta values from input type text to textarea tag.

= 1.0 - 2021-08-16 =
* Initial release.