[2025-08-21 01:21:44] merlin-logger.DEBUG: The welcome step has been displayed [] []
[2025-08-21 01:21:46] merlin-logger.DEBUG: The child theme installation step has been displayed [] []
[2025-08-21 01:21:47] merlin-logger.DEBUG: The newly generated child theme was activated [] []
[2025-08-21 01:21:52] merlin-logger.DEBUG: The license activation step has been displayed [] []
[2025-08-21 01:21:55] merlin-logger.DEBUG: The plugin installation step has been displayed [] []
[2025-08-21 01:21:56] merlin-logger.DEBUG: A plugin with the following data will be processed {"plugin_slug":"groovy-functions","message":"Installing"} []
[2025-08-21 01:21:58] merlin-logger.DEBUG: A plugin with the following data was processed {"plugin_slug":"groovy-functions"} []
[2025-08-21 01:21:58] merlin-logger.DEBUG: A plugin with the following data will be processed {"plugin_slug":"contact-form-7","message":"Installing"} []
[2025-08-21 01:22:02] merlin-logger.DEBUG: A plugin with the following data was processed {"plugin_slug":"contact-form-7"} []
[2025-08-21 01:22:06] merlin-logger.DEBUG: The content import step has been displayed [] []
[2025-08-21 01:22:55] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"Content","data":"C:\\laragon\\www2\\wordpress/wp-content/themes/groovy/functions/import/demo-content.xml"} []
[2025-08-21 01:22:55] merlin-logger.INFO: Imported "Health" (category) [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Term 2 remapped to 2 [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 2 : main_color => #27cc81 ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 2 : _main_color => epcl_post_categories_main_color ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 2 : archives_image => 425 ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 2 : _archives_image => epcl_post_categories_archives_image ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 2 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.INFO: Imported "Lifestyle" (category) [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Term 3 remapped to 3 [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : bg_color =>  ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : _bg_color => epcl_post_categories_bg_color ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : text_color =>  ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : _text_color => epcl_post_categories_text_color ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : archives_image => 422 ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : _archives_image => epcl_post_categories_archives_image ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : main_color => #6860fa ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : _main_color => epcl_post_categories_main_color ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 3 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.INFO: Imported "Music" (category) [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Term 4 remapped to 4 [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 4 : main_color => #dd3333 ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 4 : _main_color => epcl_post_categories_main_color ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 4 : archives_image => 473 ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 4 : _archives_image => epcl_post_categories_archives_image ; successfully added! [] []
[2025-08-21 01:22:55] merlin-logger.DEBUG: Meta for term_id 4 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Technology" (category) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 5 remapped to 5 [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : bg_color =>  ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : _bg_color => epcl_post_categories_bg_color ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : text_color =>  ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : _text_color => epcl_post_categories_text_color ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : archives_image => 431 ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : _archives_image => epcl_post_categories_archives_image ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : main_color => #057af5 ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : _main_color => epcl_post_categories_main_color ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 5 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Travel" (category) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 6 remapped to 6 [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 6 : main_color => #8338ec ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 6 : _main_color => epcl_post_categories_main_color ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 6 : archives_image => 416 ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 6 : _archives_image => epcl_post_categories_archives_image ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 6 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Uncategorized" (category) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 1 remapped to 7 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Video" (category) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 7 remapped to 8 [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Meta for term_id 8 : epcl_post_categories => Array ; successfully added! [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Country" (post_tag) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 8 remapped to 9 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Image" (post_tag) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 9 remapped to 10 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Audio" (post_format) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 10 remapped to 11 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Gallery" (post_format) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 12 remapped to 12 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "groovy" (wp_theme) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 17 remapped to 13 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Header" (nav_menu) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 13 remapped to 14 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "twentynineteen" (wp_theme) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 15 remapped to 15 [] []
[2025-08-21 01:22:56] merlin-logger.INFO: Imported "Video" (post_format) [] []
[2025-08-21 01:22:56] merlin-logger.DEBUG: Term 16 remapped to 16 [] []
[2025-08-21 01:22:57] merlin-logger.DEBUG: The final step has been displayed [] []
[2025-08-21 01:22:57] merlin-logger.INFO: Imported "minimal-school-bell_360032-1067" (Ortam) [] []
[2025-08-21 01:22:57] merlin-logger.DEBUG: Post 859 remapped to 859 [] []
[2025-08-21 01:22:59] merlin-logger.INFO: Imported "layered-number-one-stylized-typography_53876-96387" (Ortam) [] []
[2025-08-21 01:22:59] merlin-logger.DEBUG: Post 865 remapped to 865 [] []
[2025-08-21 01:23:01] merlin-logger.INFO: Imported "hand-drawing-illustration-successful-concept_53876-36763" (Ortam) [] []
[2025-08-21 01:23:01] merlin-logger.DEBUG: Post 866 remapped to 866 [] []
[2025-08-21 01:23:03] merlin-logger.INFO: Imported "top-view-smartphone-with-keyboard-charger_23-2149404178" (Ortam) [] []
[2025-08-21 01:23:03] merlin-logger.DEBUG: Post 868 remapped to 868 [] []
[2025-08-21 01:23:04] merlin-logger.INFO: Imported "world-food-day-arrangement_23-2149043019" (Ortam) [] []
[2025-08-21 01:23:04] merlin-logger.DEBUG: Post 869 remapped to 869 [] []
[2025-08-21 01:23:05] merlin-logger.INFO: Imported "various-types-pumpkins-arranged-height-food-gravity-concept-autumn-halloween-theme_492182-203" (Ortam) [] []
[2025-08-21 01:23:05] merlin-logger.DEBUG: Post 871 remapped to 871 [] []
[2025-08-21 01:23:08] merlin-logger.INFO: Imported "Layered editable text effect template vector 3d typography" (Ortam) [] []
[2025-08-21 01:23:08] merlin-logger.DEBUG: Post 875 remapped to 875 [] []
[2025-08-21 01:23:10] merlin-logger.INFO: Imported "bright-pick-near-electric-guitar_23-2147781763" (Ortam) [] []
[2025-08-21 01:23:10] merlin-logger.DEBUG: Post 877 remapped to 877 [] []
[2025-08-21 01:23:10] merlin-logger.INFO: Imported "logo-groovy" (Ortam) [] []
[2025-08-21 01:23:10] merlin-logger.DEBUG: Post 880 remapped to 880 [] []
[2025-08-21 01:23:11] merlin-logger.INFO: Imported "banner-728x90" (Ortam) [] []
[2025-08-21 01:23:11] merlin-logger.DEBUG: Post 892 remapped to 892 [] []
[2025-08-21 01:23:12] merlin-logger.INFO: Imported "bulb-sign-idea-symbol-word_53876-125397" (Ortam) [] []
[2025-08-21 01:23:12] merlin-logger.DEBUG: Post 910 remapped to 910 [] []
[2025-08-21 01:23:14] merlin-logger.INFO: Imported "1920-love-to-drink-coffee-to-increase-energy" (Ortam) [] []
[2025-08-21 01:23:14] merlin-logger.DEBUG: Post 915 remapped to 915 [] []
[2025-08-21 01:23:16] merlin-logger.INFO: Imported "robot-hand-holding-light-bulb-ice-cream_53876-146268" (Ortam) [] []
[2025-08-21 01:23:16] merlin-logger.DEBUG: Post 917 remapped to 917 [] []
[2025-08-21 01:23:16] merlin-logger.INFO: New AJAX call! [] []
