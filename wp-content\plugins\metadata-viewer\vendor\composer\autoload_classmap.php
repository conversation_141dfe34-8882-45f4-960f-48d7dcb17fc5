<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'WeLabs\\MetadataViewer\\Assets' => $baseDir . '/includes/Assets.php',
    'WeLabs\\MetadataViewer\\Helpers' => $baseDir . '/includes/Helpers.php',
    'WeLabs\\MetadataViewer\\MetadataViewer' => $baseDir . '/includes/MetadataViewer.php',
    'WeLabs\\MetadataViewer\\OrderMetaData' => $baseDir . '/includes/OrderMetaData.php',
    'WeLabs\\MetadataViewer\\PostMetaData' => $baseDir . '/includes/PostMetaData.php',
    'WeLabs\\MetadataViewer\\UserMetaData' => $baseDir . '/includes/UserMetaData.php',
);
