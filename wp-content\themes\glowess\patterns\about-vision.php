<?php
/**
 * Title: About Vision
 * Slug: glowess/about-vision
 * Categories: featured
 * Keywords: About Vision
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"About - vision"},"align":"wide","style":{"spacing":{"margin":{"top":"var:preset|spacing|40"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignwide" style="margin-top:var(--wp--preset--spacing--40)"><!-- wp:group {"layout":{"type":"constrained","contentSize":"643px"}} -->
<div class="wp-block-group"><!-- wp:heading {"textAlign":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontSize":"40px","lineHeight":"1.21","fontStyle":"normal","fontWeight":"500"}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.21"><?php echo esc_html__( 'Our vision', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"9px"}}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:9px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( "Our vision is to give&nbsp;melanin&nbsp;rich&nbsp;skin the attention it deserves. We don't want to just nurture your skin—we want you to discover the beauty that lies within.", 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"var:preset|spacing|30"},"margin":{"top":"11px"}}},"layout":{"type":"constrained","contentSize":"1162px"}} -->
<div class="wp-block-group alignwide" style="margin-top:11px;padding-top:var(--wp--preset--spacing--30)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"var:preset|spacing|60"}}}} -->
<div class="wp-block-columns"><!-- wp:column {"width":"","className":"test"} -->
<div class="wp-block-column test"><!-- wp:cover {"url":"https://transvelo.github.io/glowess/assets/images/image-14.png","id":53,"dimRatio":20,"customOverlayColor":"#956b5f","isUserOverlayColor":true,"minHeight":600,"contentPosition":"center center","className":"about-vision","layout":{"type":"constrained","contentSize":""}} -->
<div class="wp-block-cover about-vision" style="min-height:600px"><span aria-hidden="true" class="wp-block-cover__background has-background-dim-20 has-background-dim" style="background-color:#956b5f"></span><img class="wp-block-cover__image-background wp-image-53" alt="" src="https://transvelo.github.io/glowess/assets/images/image-14.png" data-object-fit="cover"/><div class="wp-block-cover__inner-container"><!-- wp:group {"align":"wide","layout":{"type":"constrained","contentSize":"163px"}} -->
<div class="wp-block-group alignwide"><!-- wp:group {"align":"wide","layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group alignwide"><!-- wp:image {"lightbox":{"enabled":false},"id":55,"sizeSlug":"large","linkDestination":"custom"} -->
<figure class="wp-block-image size-large"><a href="#"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/icon.svg'; ?>" alt="" class="wp-image-55"/></a></figure>
<!-- /wp:image -->

<!-- wp:paragraph {"style":{"typography":{"textTransform":"uppercase","fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.88"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:1.88;text-transform:uppercase"><?php echo esc_html__( 'play video', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div></div>
<!-- /wp:cover --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:image {"id":57,"sizeSlug":"full","linkDestination":"none"} -->
<figure class="wp-block-image size-full"><img src="https://transvelo.github.io/glowess/assets/images/image-15.png" alt="" class="wp-image-57"/></figure>
<!-- /wp:image -->

<!-- wp:group {"style":{"spacing":{"margin":{"top":"var:preset|spacing|30"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:var(--wp--preset--spacing--30)"><!-- wp:heading {"style":{"typography":{"fontSize":"24px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.21"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0","bottom":"0"},"padding":{"top":"13px"}}},"textColor":"secondary"} -->
<h2 class="wp-block-heading has-secondary-color has-text-color has-link-color" style="margin-top:0;margin-bottom:0;padding-top:13px;font-size:24px;font-style:normal;font-weight:500;line-height:1.21"><?php echo esc_html__( 'Beauty reinvented', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"14px"}}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color has-link-color" style="margin-top:14px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'We sustainably and ethically source our ingredients, to create clean and sustainable products that are good for your skin and good for the planet.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
