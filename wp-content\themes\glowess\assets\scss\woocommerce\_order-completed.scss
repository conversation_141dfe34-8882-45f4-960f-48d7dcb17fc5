/*
################
* === Order Complete Style  ===
################
*/

.woocommerce-order-received {
    .wc-block-order-confirmation-status p {
		text-align: center;
	}

    img[src*="order-icon.svg"] {
        @media (max-width:767px) {
            max-width: 60px;
        }
    }

    .wc-block-order-confirmation-summary-list {
        list-style:none;
		padding: 40px 80px;
		border-radius: 0 ;
		border: 1px dashed var(--wp--preset--color--secondary);
        background-color: var(--wp--preset--color--bg-1);
       // background-color: color-mix(in srgb, var(--wp--preset--color--contrast) 5%, var(--wp--preset--color--base) 5%);

        @media (max-width:768px) { 
			padding: 30px 40px;
		}

        li {

            @media (max-width:768px) { 
                display: flex;
                flex-direction: column;
                flex-basis: 0;
                flex-grow: 1;
            }

           


            .wc-block-order-confirmation-summary-list-item__key {
                font-size: 15px;
                font-weight: 400;
            }

            .wc-block-order-confirmation-summary-list-item__value {
                font-size: 15px;
                font-weight: 500;
                margin-top: 8px;
            }
        }

        
    }

    .wc-block-order-confirmation-totals-wrapper {

        border: 1px solid var(--wp--preset--color--gray-100);
        padding: 0px 40px;
        border-radius: 0;

        h3.wp-block-heading {
            font-family: var(--wp--preset--font-family--heading) !important;
            text-transform: capitalize !important;
            font-size: 20px !important;
        }

        .wc-block-order-confirmation-totals table {
            border: 0;
        }

        .wc-block-order-confirmation-totals__table {
            thead {
                font-size: 15px;
                font-weight: 500;
                text-transform: uppercase;
            }

            tfoot {
                font-size: 17px;
                font-weight: 500;
                th{
                    font-weight: 500;
                }
               
            }

            .wc-block-order-confirmation-totals__product, .wc-block-order-confirmation-totals__total, .wc-block-order-confirmation-totals__label, .wc-block-order-confirmation-totals__total {
                padding-left: 0px;
                padding-right: 0px;
            }
        }

    }

    .woocommerce-order-confirmation-address-wrapper {
        margin-top: 30px;

        h3.wp-block-heading {
            margin-bottom: 10px;
        }
    }

    .wc-block-order-confirmation-billing-address,
    .wc-block-order-confirmation-shipping-address {
        line-height: 26px;
        border-color: var(--wp--preset--color--gray-100);
    }

    .product-quantity {
        font-weight: 400;
    }

    .wc-bacs-bank-details {
        border: 1px solid var(--wp--preset--color--gray-100);
        border-radius: 4px;
        padding: 16px 20px;
        line-height: 1.8;
        list-style: none;
    }

    .wc-bacs-bank-details-heading {
        text-align: center;
        margin-top: clamp(1.25rem, 0.787rem + 2.4691vw, 3.75rem);
    }
}