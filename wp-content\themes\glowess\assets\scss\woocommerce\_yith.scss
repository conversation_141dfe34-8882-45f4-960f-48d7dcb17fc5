/*
################
* === YITH - Compare, Whislist, Quick view  ===
################
*/

.products-block-post-template {
    .wp-block-post {
        &,.grid {
            div[style="text-align: center"],
            .yith-wccp-compare,
            .yith-wcwl-add-to-wishlist {
                grid-column: 1 / -1;
                grid-row: 1 / -1;
                margin-bottom: auto;
                margin-left: auto;
            }
        }
    }
}

.wc-block-grid__products {
    .yith-wcwl-add-to-wishlist {
        margin-bottom: auto;
        margin-left: auto !important;
    }
}

.wc-block-grid__product,
.wp-block-post {
    .wp-block-button {
        & + {
            div[style="text-align: center"],
            .yith-wccp-compare,
            .yith-wcwl-add-to-wishlist {
                margin-top: 20px;
            }
        }
    }

    .yith-wcwl-add-to-wishlist {
        margin-top: 70px;

        & + .yith-wccp-compare {
            margin-top: 70px;
        }
    }

    .yith-wcqv-button,
    .compare {
        &::before {
            font-family: "bootstrap-icons";
            font-size: 14px;
        }
    }

    .compare {
        &::before {
            content: "\F12B";
        }
    }

    .yith-wcqv-button {
        &::before {
            content: "\F341";
        }
    }

    div[style="text-align: center"] {
        & + .yith-wcwl-add-to-wishlist {
            margin-top: 70px;

            & + .yith-wccp-compare {
                margin-top: 120px;
            }
        }

        & + .yith-wccp-compare {
            margin-top: 70px;
        }
    }

    div[style="text-align: center"],
    .yith-wccp-compare,
    .yith-wcwl-add-to-wishlist {
        z-index: 1;
        margin-right: 20px !important;
        opacity: 0;
        visibility: hidden;
        transition: 0.3s ease-in-out;

        @media (max-width:1119px) {
            display: none;
        }

        a {
            font-size: 0;
            height: 40px;
            width: 40px;
            background-color: var(--wp--preset--color--base);
            color: var(--wp--preset--color--contrast);
            border-radius: 200px;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            &:is(:hover, :focus) {
                background-color: var(--wp--preset--color--contrast);
                color: var(--wp--preset--color--base);
            }

            i {
                font-size: 14px;
                margin: 0;
            }
        }
    }

    &:is(:hover, :focus) {
        div[style="text-align: center"],
        .yith-wccp-compare,
        .yith-wcwl-add-to-wishlist {
            opacity: 1;
            visibility: visible;
        }
    }

    .yith-wcwl-wishlistaddedbrowse,
    .yith-wcwl-wishlistexistsbrowse {
        position: relative;
        display: inline-grid;
        background-color: var(--wp--preset--color--base);
        color: var(--wp--preset--color--contrast);
        border-radius: 200px;
        transition: 0.3s ease-in-out;

        &:is(:hover, :focus) {
            background-color: var(--wp--preset--color--contrast);
            color: var(--wp--preset--color--base);
        }

        > * {
            grid-column: 1 / -1;
            grid-row: 1 / -1;
        }

        &,.feedback {
            font-size: 0;
            margin: auto;
            z-index: 1;

            i {
                font-size: 14px;
                margin: 0;
            }
        }

        > a {
            background-color: transparent;
            &::after {
                position: absolute;
                inset: 0;
                content: "";
                z-index: 1;
            }
        }
    }
}

#yith-quick-view-modal {
    .product {
        &, .product,.woocommerce-product-gallery,.woocommerce-product-gallery * {
            height: 100%;
        }

        .woocommerce-product-gallery__trigger {
            display: none;
        }

        img {
            object-fit: cover;
        }

        .summary {
            padding: 10px 40px;
        }

        .product_title {
            font-size: clamp(1.125rem, -0.4464rem + 2.4554vw, 2rem);
        }

        .cart {
            margin-bottom: 20px;

            .quantity {
                display: inline-block;
                max-width: 100px;
                margin-right: 14px;
            }

            .wp-element-button {
                padding: 13.5px 40px;
            }

            &.variations_form,
            &.grouped_form {
                .variations,
                .group_table {
                    margin-bottom: 16px;
                }
            }
        }

        .price {
            font-size: 18px;
        }
    }

    #yith-quick-view-close {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--wp--preset--color--danger);
        border-radius: 200px;
        opacity: 1;
        color: var(--wp--preset--color--base);
    }

    .onsale {
        color: var(--wp--preset--color--base);
        padding: 4px 10px;
        background-color: var(--wp--preset--color--success);
        position: relative;
        left: -51px;
        z-index: 1;
    }
}

.single-product {
    .yith-wcwl-add-to-wishlist,
    .yith-wccp-compare {
        display: none;
    }
}