/*
################
* === MY ACCOUNT STYLE  ===
################
*/


.woocommerce-account  {
    .woocommerce-notices-wrapper {
        margin-bottom: 24px;
    }

    .alignfull {
        padding-top: 0;
    }

    .wp-block-post-content {
        > .woocommerce {
            gap: 20px;
            display: grid;

            @media (min-width:768px) {
                grid-template-columns: repeat(12, minmax(0, 1fr));
            }

            > .woocommerce-MyAccount-navigation,
            > .woocommerce-MyAccount-content {
                padding: 38px 0;

                @media (min-width:768px) {
                    padding: 48px 0;
                }
            }

            > .woocommerce-MyAccount-navigation {
                border-bottom: 1px solid var(--wp--preset--color--gray-100);

                @media (min-width:768px) {
                    border-right: 1px solid var(--wp--preset--color--gray-100);
                    border-block-width: 0;
                }

                ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    > li:not(:last-child) {
                        margin-bottom: 8px;
                    }

                    @media (min-width:768px) {
                        padding: 0 30px 0 0;
                    }

                    @media (min-width:1200px) {
                        padding: 0 60px 0 0;
                    }

                    a {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        color: var(--wp--preset--color--secondary);
                        padding: 10px 16px;
                        border-radius: 4px;
                        transition: 0.3s ease-in-out;
                        font-weight: 500;
                    }

                    li:not(.is-active) a:hover {
                        background-color: var(--wp--preset--color--bg-2);
                    }

                    li.is-active a {
                        color: var(--wp--preset--color--contrast);
                        background-color: var(--wp--preset--color--bg-2);
                    }
                }

                @media (min-width:768px) {
                    grid-column: span 4 / span 4;
                }

                @media (min-width:1200px) {
                    grid-column: span 3 / span 3;
                }
            }

            > .woocommerce-MyAccount-content {
                padding-top: 14px;
                overflow: auto;

                @media (min-width:768px) {
                    grid-column: span 8 / span 8;
                    padding-top: 48px;
                    padding-left: 20px;
                    overflow: visible;
                }

                @media (min-width:1200px) {
                    grid-column: span 9 / span 9;
                    padding-left: 40px;
                }
            }
        }

        & + .wp-block-group {
            display: none;
        }
    }

    .wp-block-post-content .woocommerce {
        max-width: 1200px;
        font-size: 16px;
    }

    .woocommerce-customer-details {
        margin-top: 30px;
    }

    .woocommerce-orders-table__row {
        .woocommerce-orders-table__cell-order-status span {
            font-size: 12px;
            padding: 4px 7px;
            color: var(--wp--preset--color--base);
            font-weight: 700;
            border-radius: 4px;
            background-color: var(--wp--preset--color--gray-500);
        }
    }

    .woocommerce-orders-table__cell-order-actions {
        .view {
            display: inline-flex;
            font-size: 12px;
            align-items: center;
            gap:6px;
            padding: 4px 10px;
            border-radius: 4px;
            transition: 0.3s ease-in-out;
            min-width: auto;
            
            &::before {
                font-family: bootstrap-icons;
                content: "\F341";
            }
        }
    }

    .woocommerce-orders-table__row--status {
        &-on-hold {
            .woocommerce-orders-table__cell-order-status span {
                background-color: var(--wp--preset--color--warning);
            }
        }

        &-failed {
            .woocommerce-orders-table__cell-order-status span {
                background-color: var(--wp--preset--color--danger);
            }
        }

        &-processing {
            .woocommerce-orders-table__cell-order-status span {
                background-color: var(--wp--preset--color--info);
            }
        }

        &-completed {
            .woocommerce-orders-table__cell-order-status span {
                background-color: var(--wp--preset--color--success);
            }
        }
    }

    .woocommerce-MyAccount-content {
        @media (min-width:768px) {
            padding-right: 14px;
        }

        @media (min-width:1200px) {
            padding-right: 0;
        }

        h2 {
            font-size: var(--wp--preset--font-size--grande);
            margin-top: 30px;
        }

        .woocommerce-Address-title {
            display: flex;
            justify-content: space-between;

            h3 {
                margin-bottom: 12px;
            }
        }

        .woocommerce-PaymentMethods {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .woocommerce-address-fields__field-wrapper {
            gap:16px;

            &,.form-row {
                display: flex;
                flex-direction: column;
            }

            .form-row {
                gap:8px;
            }

            .woocommerce-input-wrapper {
                display: flex;
            }

            input {
                width: 100%;
            }
        }

        .woocommerce-EditAccountForm {
            gap:16px;

            .clear {display: none;}

            &,.woocommerce-form-row {
                display: flex;
                flex-direction: column;
            }

            .woocommerce-form-row {
                gap:8px;
                margin-bottom: 0;

                input {
                    width: auto;
                    font-size: 14px;
                }
            }

            fieldset {
                border: 1px solid var(--wp--preset--color--gray-100);
                padding: 20px;
                border-radius: 6px;

                legend {
                    color: var(--wp--preset--color--contrast);
                    font-weight: 600;
                }

                .password-input {
                    display: flex;

                    input {
                        width: 100%;
                    }
                }

                .woocommerce-form-row:not(:last-child) {
                    margin-bottom: 16px;
                }
            }
        }


        .order-again {
            text-align: center;

            a {
                display: inline-flex;
                margin: 20px 0;
                font-size: 14px;
                align-items: center;
                gap:6px;
                padding: 8px 20px;
                border-radius: 4px;
                color: var(--wp--preset--color--base);
                transition: 0.3s ease-in-out;
                background-color: var(--wp--preset--color--secondary);

                &::before {
                    font-family: bootstrap-icons;
                    content: "\F406";
                }

                &:hover {
                    background-color: var(--wp--preset--color--secondary-hover);
                }
            }
        }

        .woocommerce-Addresses.col2-set {
            margin-top: 20px;

            h3 {
                font-size: var(--wp--preset--font-size--grande);
            }
        }

        .woocommerce-Addresses.col2-set,
        .woocommerce-columns {
            border: 1px solid var(--wp--preset--color--gray-100);

            @media (min-width:1024px) {
                display: grid;
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }

            h2,h3 {
                margin-top: 0;
            }

            .woocommerce-column,
            .woocommerce-Address {
                padding: 26px 30px;
            }
        }

        strong {
            color: var(--wp--preset--color--contrast);
        }

        a:not(.button):not(:hover) {
            color: var(--wp--preset--color--secondary);   
        }

        a:not(.button):hover {
            text-decoration: underline;
            text-underline-offset: 4px;
        }

        .woocommerce-table--order-details {
            tfoot {
                th {
                    text-align: left;
                }
                th,td {
                    padding: 8px 12px;
                }
            }
        }

        .woocommerce-MyAccount-orders,
        .woocommerce-table {
            border-collapse: collapse;
            border: 1px solid var(--wp--preset--color--gray-100);

            @media (min-width:1024px) {
                min-width: 500px;
            }

            thead {
                th {
                    border-top-color: transparent;
                    color: var(--wp--preset--color--secondary);
                    background-color: var(--wp--preset--color--bg-2);
                    padding: 8px 12px;
                    text-align: left;
                }
        
                .product-name {
                    text-align: left;
                }
            }

            tr td.product-thumbnail a {
                display: flex;
        
                img {
                    height: auto;
                    width: 64px;
                }
            }
        
            tr td.product-remove {
                padding: 8px;
            }
        
            tbody {
                tr {
                    td {
                        border-bottom: 1px solid var(--wp--preset--color--gray-100);
                        text-align: left;
                        padding: 8px;

                        span {
                            white-space: nowrap;
                        }
                    }
            
                    &:first-child td {
                        border-top: 1px solid var(--wp--preset--color--gray-100);
                    }
                }
        
                .product-name {
                    a {
                        font-size: 14px;
                        font-weight: 600;
                        
                        &:not(:hover) {
                            color: var(--wp--preset--color--secondary);
                        }
                    }
                }
            }
        }
    }

    .woocommerce-MyAccount-navigation-link {
        a::before {
            font-family: bootstrap-icons;
            font-size: 16px;
        }

        &--dashboard {
            a::before {
                content: "\F2EE";
            }
        }

        &--orders {
            a::before {
                content: "\F180";
            }
        }

        &--downloads {
            a::before {
                content: "\F30A";
            }
        }

        &--edit-address {
            a::before {
                content: "\F3E8";
            }
        }

        &--payment-methods {
            a::before {
                content: "\F2DC";
            }
        }

        &--edit-account {
            a::before {
                content: "\F4D7";
            }
        }

        &--customer-logout {
            a::before {
                content: "\F1C3";
            }
        }
    }

    &.woocommerce-lost-password {
        .woocommerce {
            .woocommerce-message {
                order: -1;
                padding: 10px 16px;
                border-radius: 6px;
                background-color: #d1e7dd;
                color: var(--wp--preset--color--success);
                display: flex;
                align-items: center;
                gap: 10px;
            }
        }
    }

    &:not(.logged-in) {
        .wp-block-post-title {
            display: none;
        }

        header + main {
            padding: var(--wp--preset--spacing--20) 0 !important;
        }

        main > section.wp-block-template-part {display: none;}

        main > .wp-block-post-content {
            margin-block-start:0;
        }

        &.woocommerce-lost-password{
            .woocommerce {
                .woocommerce-message {
                    order: -1;
                    padding: 10px 16px;
                    border-radius: 6px;
                    background-color: #d1e7dd;
                    color: var(--wp--preset--color--success);
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
            }
        }

        #customer_login {
            > div:not(.active) {
                display: none;
            }

            > div {
                h2 {
                    display: none;
                }

                a {
                    color: var(--wp--preset--color--secondary);
                    transition: 0.3s ease-in-out;
                }
            }
        }

        .customer_login_toggle {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: space-between;
            background-color: var(--wp--preset--color--base);
            border-bottom: 1px solid var(--wp--preset--color--gray-100);
            border: none;

            li {
                padding: 10px 28px;
                position: relative;
                cursor: pointer;
                flex-grow: 1;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;

                @media(min-width: 768px) {
                     padding: 19px 28px;
                }

                h4 {
                    color: var(--wp--preset--color--secondary);
                    margin: 0;
                    font-size: 18px;
                    //text-transform: uppercase;
                }

                &.active {
                    //box-shadow: inset 0px -3px 0px 0px var(--wp--preset--color--secondary);
                    background-color: var(--wp--preset--color--secondary);
                    border-radius: 0;
                    
                    h4 {
                        color: var(--wp--preset--color--base);
                    }
                }
            }
        }

        .entry-content {
            margin-top: 0 !important;
        }

        .woocommerce {
            max-width: 443px;
            display: block;

            .woocommerce-form-login {
                p:last-child {
                    flex-direction: row;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    align-items: center;
                    gap: 14px;
                    
                    @media (min-width:768px) {
                        gap: 20px;
                    }

                    button {
                        width: 100%;
                        margin: 0;
                    }
                }
            }

            .woocommerce-form-login,
            .woocommerce-form-register,
            .woocommerce-ResetPassword {
                padding: 30px 0;
               // box-shadow: 4px 12px 15.48px 2.52px rgba(137, 137, 137, 0.1);
                justify-content: center;
                gap: 20px;
                // background-color: var(--wp--preset--color--base);
                // border-radius: 0 0 12px 12px;

                 @media(min-width: 768px) {
                    padding: 40px 0;
                 }

                .clear {display: none;}

                &,.form-row {
                    display: flex;
                    flex-direction: column;
                    margin-top: 0;
                }

                > p,
                .woocommerce-privacy-policy-text p {
                    margin-bottom: 0;
                }

                .form-row {
                    gap: 10px;
                    margin-bottom: 0;

                    .woocommerce-LostPassword {
                        margin-bottom: 0;
                        margin-top: 0;
                    }

                    .woocommerce-form__label-for-checkbox {
                        display: inline-flex;
                        align-items: center;

                        span {
                            font-weight: 400;
                        }

                        input[type=checkbox] {
                            width: 14px;
                            height: 14px;
                            margin: 0 10px 0 0;
                        }
                    }

                    > input,
                    .password-input input {
                        width: auto;
                    }

                    .password-input {
                        display: flex;

                        input {width: 100%;}
                    }

                    &:not(.woocommerce-form-row--wide) {
                        margin-top: 5px;
                    }
                }

                button.woocommerce-button,
                button.woocommerce-Button {
                    display: block;
                    font-size: 18px;
                    font-weight: 500;
                    transition: 0.3s ease-in-out;
                    text-transform: capitalize;
                }
            }

            > h2 {
                margin-bottom: 0;
                position: relative;
                margin-top: 0;
                //border-bottom: 1px solid var(--wp--preset--color--gray-100);
                color: var(--wp--preset--color--secondary);
                //padding: 21px 28px;
                text-align: center;
                font-size: var(--wp--preset--font-size--x-large);
                // text-transform: uppercase;
                // background-color: var(--wp--preset--color--secondary);
                // border-radius: 12px 12px 0 0;
                // box-shadow: inset 0px -3px 0px 0px var(--wp--preset--color--secondary);
            }
        }
    }

    wc-order-attribution-inputs {
        display: none;
    }

    .woocommerce {
        .woocommerce-MyAccount-navigation, .woocommerce-MyAccount-content{
            float: none;
            width: 100%;
        }
        .woocommerce-MyAccount-navigation li {
            padding: 0;
            a:hover{
                text-decoration: none !important;
            }
        }
    }
    .woocommerce::before, .woocommerce::after {
        display: none;
    }

    .woocommerce .col2-set .col-1,
    .woocommerce-page .col2-set .col-1,
    .woocommerce .col2-set .col-2,
    .woocommerce-page .col2-set .col-2 {
        width: 100%;
    }

    form .form-row .woocommerce-form-login__rememberme {
        margin-bottom: 0;
    }

    form.login,
    form.register {
        border: none;
    }

}

 .woocommerce-LostPassword {
    a {
        color: var(--wp--preset--color--primary) !important;
    }
}

form.woocommerce-form-track-order {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 20px;

    .clear {
        display: none;
    }

    > * {
        grid-column: span 12 / span 12;
        margin-bottom: 0;
    }

    .form-row-first,
    .form-row-last {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        @media (min-width:768px) {
            grid-column: span 6 / span 6;
        }

        input {
            width: auto;
        }
    }

    .wp-element-button {
        min-width: 100px;
    }
}

