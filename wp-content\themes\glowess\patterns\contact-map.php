<?php
/**
 * Title: Contact Map
 * Slug: glowess/contact-map
 * Categories: featured
 * Keywords: Contact Map
 * Block Types: core/buttons
 *
 * @package  glowess
 */

?>

<!-- wp:group {"metadata":{"name":"contact-map"},"align":"full","className":"contact-map","style":{"spacing":{"padding":{"right":"20px","left":"20px","bottom":"41px"}}},"layout":{"type":"constrained","contentSize":"1800px"}} -->
<div class="wp-block-group alignfull contact-map" style="padding-right:20px;padding-bottom:41px;padding-left:20px"><!-- wp:group {"className":"grid","layout":{"type":"default"}} -->
<div class="wp-block-group grid"><!-- wp:group {"className":"grid-span-full","layout":{"type":"default"}} -->
<div class="wp-block-group grid-span-full"><!-- wp:html -->
<p><iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2481.593303940039!2d-0.15470444843858283!3d51.53901886611164!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x48761ae62edd5771%3A0x27f2d823e2be0249!2sPrincess+Rd%2C+London+NW1+8JR%2C+UK!5e0!3m2!1sen!2s!4v1458827996435" width="100%" height="600" style="border: 0px; pointer-events: none;" allowfullscreen=""></iframe></p>
<!-- /wp:html --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"grid-span-full","style":{"spacing":{"padding":{"top":"0px","bottom":"0px","left":"0px","right":"0px"}}},"layout":{"type":"constrained","justifyContent":"left","contentSize":"400px"}} -->
<div class="wp-block-group grid-span-full" style="padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px"><!-- wp:group {"className":"map-inner-box","style":{"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"36px","bottom":"40px"},"margin":{"top":"0px","bottom":"0px"}}},"backgroundColor":"base","layout":{"type":"default"}} -->
<div class="wp-block-group map-inner-box has-base-background-color has-background" style="margin-top:0px;margin-bottom:0px;padding-top:36px;padding-right:var(--wp--preset--spacing--30);padding-bottom:40px;padding-left:var(--wp--preset--spacing--30)"><!-- wp:group {"style":{"spacing":{"padding":{"right":"0","left":"0","bottom":"0","top":"0"},"blockGap":"0","margin":{"top":"0","bottom":"0"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:0;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><!-- wp:group {"style":{"spacing":{"padding":{"top":"0","bottom":"0","left":"0","right":"0"},"margin":{"top":"0","bottom":"0"},"blockGap":"1px"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:0;padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><!-- wp:heading {"style":{"typography":{"fontStyle":"normal","fontWeight":"500","fontSize":"24px","lineHeight":"1.1"},"spacing":{"margin":{"top":"0px","bottom":"var:preset|spacing|10"}}}} -->
<h2 class="wp-block-heading" style="margin-top:0px;margin-bottom:var(--wp--preset--spacing--10);font-size:24px;font-style:normal;font-weight:500;line-height:1.1"><?php echo esc_html__( 'Find Our Store', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:heading {"style":{"typography":{"fontStyle":"normal","fontWeight":"400","fontSize":"15px","lineHeight":"1.74"}}} -->
<h2 class="wp-block-heading" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.74"><?php echo esc_html__( 'ADDRESS', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( '620 King Street West', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.8"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:1.8"><?php echo esc_html__( 'Toronto, ON M5V 1M7, Canada', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"1px","margin":{"top":"0","bottom":"0px"},"padding":{"top":"14px","bottom":"4px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:0px;padding-top:14px;padding-bottom:4px"><!-- wp:heading {"style":{"typography":{"fontStyle":"normal","fontWeight":"400","fontSize":"15px","lineHeight":"1.74"}}} -->
<h2 class="wp-block-heading" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.74"><?php echo esc_html__( 'OPENING HOURS', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:group {"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Mon - Fri', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( '8:30am - 10:30pm', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Saturday', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( '8:30am - 10:30pm', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Sunday', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}}} -->
<p style="font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( '8:30am - 10:30pm', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"var:preset|spacing|10"}}},"layout":{"type":"flex","justifyContent":"left","flexWrap":"wrap"}} -->
<div class="wp-block-buttons" style="margin-top:var(--wp--preset--spacing--10)"><!-- wp:button {"className":"inline-img","style":{"spacing":{"padding":{"left":"0px","right":"0px","top":"12px","bottom":"12px"}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400"}}} -->
<div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400"><a class="wp-block-button__link wp-element-button" style="padding-top:12px;padding-right:0px;padding-bottom:12px;padding-left:0px"><?php echo esc_html__( 'GET DIRECTIONS', 'glowess' ); ?><img class="wp-image-231" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/right-up-arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
