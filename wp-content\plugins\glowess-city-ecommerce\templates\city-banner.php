<?php
/**
 * Şehir bazlı dinamik banner şablonu
 * Glowess temasının home-v1-banner.php şablonunu kullanır
 */

// Güvenlik kontrolü
if (!defined('ABSPATH')) {
    exit;
}

// Seçili şehir bilgilerini al
$glowess_city = new GlowessCityEcommerce();
$selected_city_id = $glowess_city->get_selected_city();

if (!$selected_city_id) {
    return; // Şehir seçilmemişse banner gösterme
}

// Şehir verilerini al
$city = get_post($selected_city_id);
if (!$city) {
    return;
}

$slider_title = get_post_meta($selected_city_id, '_city_slider_title', true);
$slider_subtitle = get_post_meta($selected_city_id, '_city_slider_subtitle', true);
$slider_button_text = get_post_meta($selected_city_id, '_city_slider_button_text', true);
$slider_button_url = get_post_meta($selected_city_id, '_city_slider_button_url', true);
$slider_images = get_post_meta($selected_city_id, '_city_slider_images', true);

// Varsayılan değerler
if (empty($slider_title)) {
    $slider_title = $city->post_title . ' için özel ürünler';
}
if (empty($slider_subtitle)) {
    $slider_subtitle = $city->post_title . ' şehrindeki en kaliteli ürünleri keşfedin.<br>Hızlı teslimat ve güvenli alışveriş.';
}
if (empty($slider_button_text)) {
    $slider_button_text = 'Alışverişe Başla';
}
if (empty($slider_button_url)) {
    $slider_button_url = '/shop';
}

// İlk slider görselini al (birden fazla varsa ilkini kullan)
$slider_image = '';
if (is_array($slider_images) && !empty($slider_images[0])) {
    // Attachment ID'den URL'yi al
    $slider_image = wp_get_attachment_image_url($slider_images[0], 'full');
}

// Eğer slider görseli yoksa hero görselini kullan
if (!$slider_image) {
    $hero_image_id = get_post_meta($selected_city_id, '_city_hero_image', true);
    if ($hero_image_id) {
        $slider_image = wp_get_attachment_image_url($hero_image_id, 'full');
    }
}

// Hala görsel yoksa varsayılan görseli kullan
if (!$slider_image) {
    $slider_image = 'https://transvelo.github.io/glowess/assets/images/img-1.png';
}
?>

<!-- wp:group {"metadata":{"name":"<?php echo esc_attr($city->post_title); ?> Banner"},"align":"full","className":"sp-home-v1-banner light-mode glowess-city-banner","style":{"spacing":{"blockGap":"0","margin":{"top":"var:preset|spacing|50"},"padding":{"top":"44px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull sp-home-v1-banner light-mode glowess-city-banner" style="margin-top:var(--wp--preset--spacing--50);padding-top:44px">
    <!-- wp:group {"align":"full","layout":{"type":"constrained"}} -->
    <div class="wp-block-group alignfull">
        <!-- wp:cover {"url":"<?php echo esc_url($slider_image); ?>","dimRatio":20,"isUserOverlayColor":true,"focalPoint":{"x":0.5,"y":0.3},"minHeight":700,"align":"full","className":"cover-img","style":{"spacing":{"padding":{"top":"0","bottom":"0","left":"0","right":"0"}},"color":[]},"layout":{"type":"default"}} -->
        <div class="wp-block-cover alignfull cover-img" style="padding-top:0;padding-right:0;padding-bottom:0;padding-left:0;min-height:700px">
            <span aria-hidden="true" class="wp-block-cover__background has-background-dim-20 has-background-dim"></span>
            <img class="wp-block-cover__image-background" alt="<?php echo esc_attr($city->post_title); ?>" src="<?php echo esc_url($slider_image); ?>" style="object-position:50% 30%" data-object-fit="cover" data-object-position="50% 30%"/>
            <div class="wp-block-cover__inner-container">
                <!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}}},"layout":{"type":"default"}} -->
                <div class="wp-block-group" style="padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50)">
                    <!-- wp:group {"style":{"spacing":{"padding":{"bottom":"5px","top":"var:preset|spacing|20"}}},"layout":{"type":"default"}} -->
                    <div class="wp-block-group" style="padding-top:var(--wp--preset--spacing--20);padding-bottom:5px">
                        <!-- wp:group {"style":{"spacing":{"padding":{"bottom":"var:preset|spacing|30","top":"var:preset|spacing|40"}}},"layout":{"type":"default"}} -->
                        <div class="wp-block-group" style="padding-top:var(--wp--preset--spacing--40);padding-bottom:var(--wp--preset--spacing--30)">
                            <!-- wp:heading {"textAlign":"center","style":{"typography":{"fontSize":"60px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2","textTransform":"none"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"margin":{"top":"32px"}}},"textColor":"base"} -->
                            <h2 class="wp-block-heading has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:32px;font-size:60px;font-style:normal;font-weight:500;line-height:1.2;text-transform:none">
                                <?php echo esc_html($slider_title); ?>
                            </h2>
                            <!-- /wp:heading -->

                            <!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"16px","lineHeight":"1.9"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"spacing":{"margin":{"top":"var:preset|spacing|10"}}},"textColor":"base"} -->
                            <p class="has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:var(--wp--preset--spacing--10);font-size:16px;line-height:1.9">
                                <?php echo wp_kses_post($slider_subtitle); ?>
                            </p>
                            <!-- /wp:paragraph -->

                            <!-- /wp:buttons -->
                        </div>
                        <!-- /wp:group -->
                    </div>
                    <!-- /wp:group -->
                </div>
                <!-- /wp:group -->
            </div>
        </div>
        <!-- /wp:cover -->
    </div>
    <!-- /wp:group -->
</div>
<!-- /wp:group -->

<?php
// Şehir değiştirme butonu ekle
?>