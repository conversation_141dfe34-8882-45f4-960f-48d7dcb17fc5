/*Metadata plugin styles*/
.metadata-viewer-table td {
    border-bottom: 1px solid #e2e4e7;
    padding: 5px 10px;
}

.metadata-viewer-table tbody tr:last-child td{
    border-bottom: 0px;
}

.metadata-viewer-table {
    border-collapse: collapse;
    width: 100%;
}

.metadata-viewer-table pre {
    margin: 0;
}

.metadata-viewer-table thead tr th:first-child {
    width: 30%;
}
.metadata-viewer-table thead tr th {
    text-align: left;
    border-bottom: 1px solid #e2e4e7;
    background: #f8f9fa;
    padding: 10px;
}

#post-metadata-viewer .inside, #user-metadata-viewer-id .inside {
    padding-left: 0;
    padding-right: 0;
    margin-top: 0;
    padding-bottom: 0;
}
.metadata-viewer-table tr td {
    transition: .1s;
}

.metadata-viewer-table tr:hover td {
    background: #fafafa;
}
#post-metadata-viewer, #user-metadata-viewer-id{
  border: 0px;
  box-shadow: none;
}

#post-metadata-viewer .postbox-header, #user-metadata-viewer-id .postbox-header {
  border-bottom-color: #e2e4e7;
}
#post-metadata-viewer h2, #user-metadata-viewer-id h2 {
  font-size: 14px;
  padding: 8px 12px;
  margin: 0;
  line-height: 1.4;
}
.metadata-filter input {
  width: 100%;
  border: none;
  padding: 6px 15px 6px 10px!important;
  border-left: 0px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  margin-left: 0px;
  height: 42px;
}
.metadata-filter input:focus{
  outline: 0;
  border: 0;
  box-shadow: none;
}

.metadata-filter {
  padding: 8px 12px;
}
.metadata-search-group{
  display: flex;
  align-items: center;
  gap: 0px;
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  padding: 0;
}

.metadata-filter .icon {
  color: #ddd;
  height: 42px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  padding-left: 10px;
  border-right: 0px;
  display: flex;
  align-items: center;
}

.not-metadata-found h2 {
  font-size: 25px!important;
  padding: 0!important;
  text-align: center;
  margin-top: 25px!important;
}

.not-metadata-found svg {
  font-size: 45px;
  width: 50px;
  height: 50px;
  fill: #d73638;
}

.not-metadata-found {
  text-align: center;
  padding: 50px 20px;
}

.not-metadata-found p {
  margin-top: 8px;
  font-size: 15px;
  margin-bottom: 0;
}

.metadata-viewer-wrapper .hidden{
  display: none;
}

/*
Atom One Light by Daniel Gamage
Original One Light Syntax theme from https://github.com/atom/one-light-syntax
*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    color: #383a42;
    background: #fafafa;
  }
  
  .hljs-comment,
  .hljs-quote {
    color: #a0a1a7;
    font-style: italic;
  }
  
  .hljs-doctag,
  .hljs-keyword,
  .hljs-formula {
    color: #a626a4;
  }
  
  .hljs-section,
  .hljs-name,
  .hljs-selector-tag,
  .hljs-deletion,
  .hljs-subst {
    color: #e45649;
  }
  
  .hljs-literal {
    color: #0184bb;
  }
  
  .hljs-string,
  .hljs-regexp,
  .hljs-addition,
  .hljs-attribute,
  .hljs-meta-string {
    color: #50a14f;
  }
  
  .hljs-built_in,
  .hljs-class .hljs-title {
    color: #c18401;
  }
  
  .hljs-attr,
  .hljs-variable,
  .hljs-template-variable,
  .hljs-type,
  .hljs-selector-class,
  .hljs-selector-attr,
  .hljs-selector-pseudo,
  .hljs-number {
    color: #986801;
  }
  
  .hljs-symbol,
  .hljs-bullet,
  .hljs-link,
  .hljs-meta,
  .hljs-selector-id,
  .hljs-title {
    color: #4078f2;
  }
  
  .hljs-emphasis {
    font-style: italic;
  }
  
  .hljs-strong {
    font-weight: bold;
  }
  
  .hljs-link {
    text-decoration: underline;
  }