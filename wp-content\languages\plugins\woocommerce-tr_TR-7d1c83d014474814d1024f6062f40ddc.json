{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Upgrade all Filter blocks": ["Tüm Filtre bloklarını yükseltin"], "Upgrade all Filter blocks on this page for better performance and more customizability": ["Daha iyi performans ve daha fazla özelleştirilebilirlik için bu sayfadaki tüm Filtre bloklarını yükseltin"], "Max. Price": ["Maks. <PERSON>"], "Min. Price": ["<PERSON><PERSON>"], "Product Filters": ["<PERSON><PERSON><PERSON><PERSON>"], "To filter your products by price you first need to assign prices to your products.": ["Ürünlerinizi fiyata göre filtrelemek için öncelikle ürünlerinize fiyat atamanız gerekir."], "Show 'Apply filters' button": ["\"Filtreleri uygula\" d<PERSON>ğ<PERSON><PERSON> gö<PERSON>"], "Show input fields inline with the slider.": ["Slayt gösterisi aracı ile satır içi giriş alanlarını göster."], "Inline input fields": ["Sa<PERSON>ır içi giriş al<PERSON>ı"], "Price Range Selector": ["Fiyat Aralığı Seçici"], "Filter by Price": ["<PERSON>ya<PERSON>ö<PERSON>"], "Reset price filter": ["Fiyat filtresini sıfırla"], "Reset filter": ["Filtreleri sıfırla"], "Block title": ["Blok başlığı"], "Apply price filter": ["Fiyat filtresi uygula"], "Apply filter": ["Filtre uygula"], "Editable": ["Düzenlenebilir"], "Filter products by minimum price": ["Ürünleri asgari fiyata göre filtrele"], "Filter products by maximum price": ["Ürünleri azami fiyata göre filtrele"], "Products will update when the button is clicked.": ["Ürünler düğmeye tıklandığında güncellenecektir."], "Display a slider to filter products in your store by price.": ["Mağazanızdaki ürünleri fiyata göre filtrelemek için bir kaydırıcı gösterir."], "Text": ["<PERSON><PERSON>"], "Filter by price": ["<PERSON><PERSON><PERSON> g<PERSON>re filtrele"], "Add new product": ["<PERSON><PERSON>"], "Reset": ["Sıfırla"], "Learn more": ["<PERSON>ha fazla bilgi edin"], "Apply": ["<PERSON><PERSON><PERSON><PERSON>"], "Settings": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}