{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["Sepet simgesine tıklandığında Mini Sepet çekmecesinin açılmasını devre dışı bırakmak için düğmeyi kullanın ve bunun yerine ödeme sayfasına gidin."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["Mini Sepet'e tıklandığında çekmecenin açılması yerine ödeme sayfasına gidin."], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["Düzenleyici gerçek sayı değerini göstermez ancak ön uçta nasıl gözükeceğini belirtmek için yer tutucu gösterir."], "Only if cart has items": ["Yalnızca sepette öğeler varsa"], "Always (even if empty)": ["Her zaman (bo<PERSON><PERSON> bile)"], "Show Cart Item Count:": ["Sepet Öğe Sayısını Göster:"], "Product Count": ["<PERSON><PERSON><PERSON><PERSON>"], "Cart Icon": ["Sepet Simgesi"], "Icon": ["<PERSON>m<PERSON>"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Müşteri bir ürünü sepetine eklediğinde Mini Sepet çekmecesini açmak için düğmeye basın."], "Open drawer when adding": ["Eklerken çekmeceyi aç"], "Behavior": ["Davranış"], "Edit Mini-Cart Drawer template": ["Mini Sepet Çekmecesi şablonunu düzenle"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mini Sepet çekmecesi, müşterilere seçili ürünlerini görüntülemek ve ödeme yapmak için hızlı erişim verir."], "Cart Drawer": ["Sepet Çekmecesi"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Alışveriş sepetindeki ürünlerin toplam fiyatını görüntülemek için düğmeye basın. Sepete hiç ürün eklenmemişse, fiyat görüntülenmez."], "Display total price": ["Toplam fiyatı görüntüle"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["Sepet ve Ödeme sayfalarında Mini Sepetin nasıl davranacağını seçin. Bu, üst bilgiler düzenini etkileyebilir."], "Mini-Cart in cart and checkout pages": ["Sepet ve ödeme sayfalarındaki Mini Sepet"], "Hide": ["<PERSON><PERSON><PERSON>"], "Display": ["G<PERSON>rü<PERSON><PERSON><PERSON>"], "Never": ["<PERSON><PERSON>"], "Price": ["<PERSON><PERSON><PERSON>"], "Remove": ["Kaldır"], "Settings": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}