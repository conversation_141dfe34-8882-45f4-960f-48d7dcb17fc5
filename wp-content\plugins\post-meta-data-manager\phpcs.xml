<?xml version="1.0"?>
<ruleset name="WordPressExtended-PMDM" namespace="PMDM\CS\Standard">
    <description>WordPress Extended but with modifications.</description>
    <arg name="tab-width" value="4"/>
    <rule ref="PSR2">
        <exclude name="Generic.WhiteSpace.DisallowTabIndent"/>
    </rule>
    <!-- <rule ref="Generic.WhiteSpace.DisallowSpaceIndent"/>
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="indent" value="4"/>
            <property name="tabIndent" value="true"/>
        </properties>
    </rule> -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="1024"/>
            <property name="absoluteLineLimit" value="2048"/>
        </properties>
     </rule>
</ruleset>