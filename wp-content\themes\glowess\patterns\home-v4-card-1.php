<?php
/**
 * Title: Home v4 Card 1
 * Slug: glowess/home-v4-card-1
 * Categories: featured
 * Keywords: Home v4 Card
 *
 * @package glowess
 */

?>


<!-- wp:group {"metadata":{"name":"Home-v4-Card-1","categories":["featured"],"patternName":"glowess/home-v4-card-1"},"align":"full","className":"home-v4-card-1","style":{"spacing":{"padding":{"top":"7px","bottom":"5px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull home-v4-card-1" style="padding-top:7px;padding-bottom:5px"><!-- wp:group {"align":"full","style":{"spacing":{"padding":{"top":"0","bottom":"0","left":"var:preset|spacing|40","right":"var:preset|spacing|40"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="padding-top:0;padding-right:var(--wp--preset--spacing--40);padding-bottom:0;padding-left:var(--wp--preset--spacing--40)"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"28px"}}}} -->
<div class="wp-block-columns"><!-- wp:column -->
<div class="wp-block-column"><!-- wp:group {"style":{"border":{"radius":"16px"},"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}}},"backgroundColor":"bg-1","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-bg-1-background-color has-background" style="border-radius:16px;padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50)"><!-- wp:group {"style":{"spacing":{"margin":{"top":"0","bottom":"0"},"padding":{"top":"var:preset|spacing|10","bottom":"48px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--10);padding-bottom:48px"><!-- wp:image {"id":12,"sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full"><img src="https://transvelo.github.io/glowess/assets/images/home-v4-card-banner-2.png" alt="" class="wp-image-12"/></figure>
<!-- /wp:image -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"16px","lineHeight":"1.75","fontStyle":"normal","fontWeight":"400"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"31px","bottom":"0px"}}},"textColor":"secondary"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color" style="margin-top:31px;margin-bottom:0px;font-size:16px;font-style:normal;font-weight:400;line-height:1.75;text-transform:uppercase"><?php echo esc_html__( 'new collection', 'glowess' ); ?></p>
<!-- /wp:paragraph -->


<!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"60px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2","textTransform":"capitalize"},"spacing":{"margin":{"top":"14px","bottom":"0"}},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="has-text-align-center has-secondary-color has-text-color has-link-color has-heading-font-family" style="margin-top:14px;margin-bottom:0;font-size:60px;font-style:normal;font-weight:500;line-height:1.2;text-transform:capitalize"><?php echo esc_html__( 'Let\'s make it', 'glowess' ); ?><br><?php echo esc_html__( 'routine', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"align":"center","style":{"spacing":{"margin":{"top":"16px","bottom":"0"}},"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"}},"textColor":"contrast"} -->
<p class="has-text-align-center has-contrast-color has-text-color has-link-color" style="margin-top:16px;margin-bottom:0;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'This text helps you provide some more detail to the', 'glowess' ); ?><br><?php echo esc_html__( 'title above.', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"30px","bottom":"0"}}},"layout":{"type":"flex","justifyContent":"center"}} -->
<div class="wp-block-buttons" style="margin-top:30px;margin-bottom:0"><!-- wp:button {"className":"flex inline-img","style":{"border":{"radius":"8px","width":"0px","style":"none"},"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"1.74"}}} -->
<div class="wp-block-button has-custom-font-size flex inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.74"><a class="wp-block-button__link wp-element-button" style="border-style:none;border-width:0px;border-radius:8px"><?php echo esc_html__( 'shop cosmetics', 'glowess' ); ?><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"stretch"} -->
<div class="wp-block-column is-vertically-aligned-stretch"><!-- wp:image {"id":13,"scale":"cover","sizeSlug":"full","linkDestination":"none","className":"card-banner-image","style":{"border":{"radius":"16px"}}} -->
<figure class="wp-block-image size-full has-custom-border card-banner-image"><img src="https://transvelo.github.io/glowess/assets/images/home-v4-card-banner-1.png" alt="" class="wp-image-13" style="border-radius:16px;object-fit:cover"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
