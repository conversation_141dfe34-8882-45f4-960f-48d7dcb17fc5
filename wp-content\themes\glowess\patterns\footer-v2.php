<?php
/**
 * Title: Footer v2
 * Slug: glowess/footer-v2
 * Categories: footer
 * Block Types: core/template-part/footer-v2
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Footer v2"},"align":"full","className":"dark-mode footer-v2","style":{"spacing":{"padding":{"top":"0","bottom":"0","left":"0","right":"0"},"blockGap":"0"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"backgroundColor":"contrast","textColor":"base","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull dark-mode footer-v2 has-base-color has-contrast-background-color has-text-color has-background has-link-color" style="padding-top:0;padding-right:0;padding-bottom:0;padding-left:0"><!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|40"},"margin":{"bottom":"0px"}}},"layout":{"type":"constrained","contentSize":""}} -->
<div class="wp-block-group" style="margin-bottom:0px;padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--40)"><!-- wp:group {"align":"wide","style":{"spacing":{"blockGap":"20px","padding":{"top":"5px"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide" style="padding-top:5px"><!-- wp:group {"style":{"spacing":{"blockGap":"18px","padding":{"top":"12px","bottom":"12px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:12px;padding-bottom:12px"><!-- wp:site-title {"style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"x-large","fontFamily":"heading"} /-->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"380px","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"none","fontStyle":"normal","fontWeight":"300","lineHeight":"2"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"medium"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-medium-font-size" style="font-style:normal;font-weight:300;line-height:2;text-transform:none"><?php echo esc_html__( 'Add short newsletter content for your customers to encourage them to sign up for newsletter.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"dimensions":{"minHeight":"59px"},"spacing":{"margin":{"top":"20px"}},"border":{"color":"#36454000"}},"layout":{"type":"default"}} -->
<div class="wp-block-group has-border-color" style="border-color:#36454000;min-height:59px;margin-top:20px"><!-- wp:wpforms/form-selector {"clientId":"ad795457-11bb-4798-98d8-bc185132268d","formId":"100","copyPasteJsonValue":"{\u0022displayTitle\u0022:false,\u0022displayDesc\u0022:false,\u0022theme\u0022:\u0022default\u0022,\u0022themeName\u0022:\u0022\u0022,\u0022fieldSize\u0022:\u0022medium\u0022,\u0022backgroundImage\u0022:\u0022none\u0022,\u0022backgroundPosition\u0022:\u0022center center\u0022,\u0022backgroundRepeat\u0022:\u0022no-repeat\u0022,\u0022backgroundSizeMode\u0022:\u0022cover\u0022,\u0022backgroundSize\u0022:\u0022cover\u0022,\u0022backgroundWidth\u0022:\u0022100px\u0022,\u0022backgroundHeight\u0022:\u0022100px\u0022,\u0022backgroundUrl\u0022:\u0022url()\u0022,\u0022backgroundColor\u0022:\u0022rgba( 0, 0, 0, 0 )\u0022,\u0022fieldBorderRadius\u0022:\u00223px\u0022,\u0022fieldBorderStyle\u0022:\u0022solid\u0022,\u0022fieldBorderSize\u0022:\u00221px\u0022,\u0022fieldBackgroundColor\u0022:\u0022#ffffff\u0022,\u0022fieldBorderColor\u0022:\u0022rgba( 0, 0, 0, 0.25 )\u0022,\u0022fieldTextColor\u0022:\u0022rgba( 0, 0, 0, 0.7 )\u0022,\u0022fieldMenuColor\u0022:\u0022#ffffff\u0022,\u0022labelSize\u0022:\u0022medium\u0022,\u0022labelColor\u0022:\u0022rgba( 0, 0, 0, 0.85 )\u0022,\u0022labelSublabelColor\u0022:\u0022rgba( 0, 0, 0, 0.55 )\u0022,\u0022labelErrorColor\u0022:\u0022#d63637\u0022,\u0022buttonSize\u0022:\u0022medium\u0022,\u0022buttonBorderStyle\u0022:\u0022none\u0022,\u0022buttonBorderSize\u0022:\u00221px\u0022,\u0022buttonBorderRadius\u0022:\u00223px\u0022,\u0022buttonBackgroundColor\u0022:\u0022#066aab\u0022,\u0022buttonTextColor\u0022:\u0022#ffffff\u0022,\u0022buttonBorderColor\u0022:\u0022#066aab\u0022,\u0022pageBreakColor\u0022:\u0022#066aab\u0022,\u0022containerPadding\u0022:\u00220px\u0022,\u0022containerBorderStyle\u0022:\u0022none\u0022,\u0022containerBorderWidth\u0022:\u00221px\u0022,\u0022containerBorderColor\u0022:\u0022#000000\u0022,\u0022containerBorderRadius\u0022:\u00223px\u0022,\u0022containerShadowSize\u0022:\u0022none\u0022,\u0022customCss\u0022:\u0022\u0022}"} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"footer-widgets","style":{"spacing":{"blockGap":"var:preset|spacing|50","padding":{"left":"0"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between","verticalAlignment":"stretch"}} -->
<div class="wp-block-group footer-widgets" style="padding-left:0"><!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontSize":"default","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family has-default-font-size" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Shop All', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":604,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Explore', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":606,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'More', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":15,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base","fontFamily":"heading"} -->
<p class="widgets-title has-base-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Connect', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":21,"textColor":"base","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"padding":{"bottom":"15px"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group" style="padding-bottom:15px"><!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"var:preset|spacing|30"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide" style="padding-top:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--30)"><!-- wp:paragraph {"className":"copyright-text","style":{"typography":{"fontStyle":"normal","fontWeight":"300","fontSize":"13px"},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"textColor":"base"} -->
<p class="copyright-text has-base-color has-text-color has-link-color" style="font-size:13px;font-style:normal;font-weight:300"><?php echo esc_html__( '© 2024 - All rights reserved. Made by ', 'glowess' ); ?><a href="#"><?php echo esc_html__( 'Glowess.', 'glowess' ); ?></a></p>
<!-- /wp:paragraph -->

<!-- wp:navigation {"ref":116,"textColor":"base","overlayMenu":"never","overlayTextColor":"secondary","className":"footer-language","style":{"typography":{"fontSize":"13px","fontStyle":"normal","fontWeight":"300"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
