"use strict";

(function ($) {
    'use strict';

    $(
        function () {
            $("#order_review_heading").prependTo("#order_review");

            // // Check if the WooCommerce blocks are present
            // if ($('.wc-block-grid__product').length) {
            //     // Loop through each product
            //     $('.wc-block-grid__product').each(function() {
            //         // Find the rating and price elements
            //         var $rating = $(this).find('.wc-block-grid__product-rating');
            //         var $price = $(this).find('.wc-block-grid__product-price');

            //         // Move the rating above the price
            //         if ($rating.length && $price.length) {
            //             $rating.insertBefore($price);
            //         }
            //     });
            // }

            
            // Set a delay for the next block of code (e.g., 1 second = 1000 milliseconds)
            setTimeout(function() {

                $('.slick-nav-group').each(function(){
                    // Find the elements to be grouped
                    var slickPrev = $(this).find('.slick-prev');
                    var slickNext = $(this).find('.slick-next');
                    var slickDots = $(this).find('.slick-dots');
                    // If all elements are found, group them
                    if (slickPrev.length && slickNext.length && slickDots.length) {
                        $('<div class="slick-nav-wrap"></div>')
                            .insertAfter($(this).find('.slick-list'))
                            .append(slickPrev)
                            .append(slickNext)
                            .append(slickDots);
                    }
                });

                // Testimoial home-v1 Add img class for before and after current slide.
                function updateClasses() {
                    // Remove current-before and current-after classes from all slides
                    $('.v1-testimonial .slick-slide').removeClass('current-before current-after');
              
                    // Get the current slide
                    var $currentSlide = $('.v1-testimonial .slick-current');
                    
                    // Get total number of slides
                    var $slides = $('.v1-testimonial .slick-slide');
                    var slideCount = $slides.length;
              
                    // Get current index
                    var currentIndex = $slides.index($currentSlide);
              
                    // Calculate indexes for the previous and next slides
                    var prevIndex = (currentIndex - 1 + slideCount) % slideCount;
                    var nextIndex = (currentIndex + 1) % slideCount;
              
                    // Add classes to the respective slides
                    $slides.eq(prevIndex).addClass('current-before');
                    $slides.eq(nextIndex).addClass('current-after');
                  }
              
                  // Initial update
                  updateClasses();
              
                  // Update classes on afterChange event
                  $('.v1-testimonial').on('afterChange', function(event, slick, currentSlide){
                    updateClasses();
                  });

            }, 1000); // 1000 milliseconds = 1 Second

            

            /*===================================================================================*/
            /*  WC Breadcrumb add arrow icon for divider
            /*===================================================================================*/

            var $breadcrumb = $('.woocommerce-breadcrumb');
    
            function wrapGreaterThans(html) {
                // Remove &nbsp; entities
                html = html.replace(/&nbsp;/g, ' ');

                // Temporarily replace content of <a> tags to avoid modification
                var placeholders = [];
                var placeholderHtml = html.replace(/<a(.*?)<\/a>/g, function(match) {
                    placeholders.push(match);
                    return '\0'; // Unique placeholder character
                });

                // Replace / with > outside <a> tags
                placeholderHtml = placeholderHtml.replace(/(?<!<\/a>)\//g, '>');

                // Wrap > characters with <span>
                placeholderHtml = placeholderHtml.replace(/>/g, '<span><i class="bi bi-chevron-right" style="font-size: 10px;"></i></span>');

                // Restore the <a> tags from placeholders
                placeholders.forEach(function(placeholder) {
                    placeholderHtml = placeholderHtml.replace('\0', placeholder);
                });

                return placeholderHtml;
            }

            // Get the current HTML, process it, and update the breadcrumb content

            if ( $breadcrumb.length ) {
                var currentHtml = $breadcrumb.html();
                var newHtml = wrapGreaterThans(currentHtml);
                $breadcrumb.html(newHtml);
            }

            /*===================================================================================*/
            /*  Product Items add ID name "added" after click add to cart button
            /*===================================================================================*/

            // Move the inner element to the div for each li
            $(".products-block-post-template .wp-block-post").each(function() {
                $(this).find('button[data-wc-on--click="actions.addToCart"]').click(function() {
                    $(this).attr("id","added");
                });
            });

            $(document).ready(function(){
                /*===================================================================================*/
                /*  Single Product review comment description moved to parent of the block
                /*===================================================================================*/ 

                // Select each <li> element
                $('.single-product .review,.single-product .review .children .comment').each(function() {
                    // Get the content of <p> tag inside the <div>
                    var $pContent = $(this).find('> .comment_container .description');
                    
                    // Move the <p> tag and its content to the root of <li> element
                    $(this).append($pContent);
                });

                /*===================================================================================*/
                /*  Single product woo gallery trigger icon changed to bootstrap icon
                /*===================================================================================*/

                // Select the <a> tag to move
                var $link = $('.single-product .woocommerce-product-gallery .woocommerce-product-gallery__trigger');
                
                // Move the <a> tag to the new container
                $link.appendTo('.single-product .flex-viewport, .single-product .woocommerce-product-gallery > .woocommerce-product-gallery__wrapper .woocommerce-product-gallery__image');

                // Select the <a> tag within '.content' div
                var $a = $('.single-product .woocommerce-product-gallery__trigger');
                
                // Create a new <span> element with custom content
                var $span = $('<i class="bi bi-search"></i>');
                
                // Remove the content of <a> tag
                $a.empty();
                
                // Append the <span> to the <a> tag
                $a.append($span);


                /*===================================================================================*/
                /*  Product Item Combain yith wishlist into prodcut image div.
                /*===================================================================================*/

                // Move the inner element to the div for each li
                $(".wc-block-grid__products .wc-block-grid__product, .wp-block-woocommerce-cart-cross-sells-block").each(function() {
                    var innerElement = $(this).find('.wp-block-button, .yith-wcwl-add-to-wishlist');
                    var targetDiv = $(this).find('.wc-block-grid__product-image, .cross-sells-product .wc-block-components-product-image');
        
                    // Move the inner element to the target div
                    $(innerElement).appendTo(targetDiv);
                });

                /*===================================================================================*/
                /*  Product Item (ALL product blocks) Combain yith wishlist into prodcut image div.
                /*===================================================================================*/

                // Immediately execute code after page load
                console.log('This code runs after the page is fully loaded.');

                // Set a delay for the next block of code (e.g., 2 minutes = 120000 milliseconds)
                setTimeout(function() {
                    // Code to execute after the delay
                    console.log('This code runs 2 minutes after the page has loaded.');
                    
                    // Example action: Change the text color
                    $(".wc-block-all-products .wc-block-grid__products .wc-block-grid__product, .wp-block-woocommerce-cart-cross-sells-block").each(function() {
                        var innerElement = $(this).find('.wp-block-button');
                        var targetDiv = $(this).find('.wc-block-grid__product-image, .cross-sells-product .wc-block-components-product-image');
            
                        // Move the inner element to the target div
                        $(innerElement).appendTo(targetDiv);
                    });
                }, 1000); // 1000 milliseconds = 1 Second
            });

            /*===================================================================================*/
            /*  On click Product Item (ALL product blocks) Combain yith wishlist into prodcut image div.
            /*===================================================================================*/

            $(document).on('click', function(event) {
                // Check if the click happened below the specified div
                if ($(event.target).closest('.wc-block-all-products').length) {
                    // Action to run when the click happens below myDiv
                    // Immediately execute code after page load
                    console.log('This code runs after the page is fully loaded.');

                    // Set a delay for the next block of code (e.g., 2 minutes = 120000 milliseconds)
                    setTimeout(function() {
                        // Code to execute after the delay
                        console.log('This code runs 2 minutes after the page has loaded.');
                        
                        // Example action: Change the text color
                        $(".wc-block-all-products .wc-block-grid__products .wc-block-grid__product").each(function() {
                            var innerElement = $(this).find('.wp-block-button');
                            var targetDiv = $(this).find('.wc-block-grid__product-image');
                
                            // Move the inner element to the target div
                            $(innerElement).appendTo(targetDiv);
                        });
                    }, 1000); // 1000 milliseconds = 1 Second
                }
            });

            /*===================================================================================*/
            /*  Product Item (WC Product blocks) Combain yith wishlist into prodcut image div.
            /*===================================================================================*/

            // Move the inner element to the div for each li
            $(".products-block-post-template .wp-block-post").each(function() {
                var innerElement = $(this).find('.wp-block-button, .yith-wcwl-add-to-wishlist, .yith-wccp-compare, div[style="text-align: center"]');
                var targetDiv = $(this).find('.wc-block-grid__product-image');
    
                // Move the inner element to the target div
                $(innerElement).appendTo(targetDiv);
            });

            /*===================================================================================*/
            /*  Header scroll active class add to body tag
            /*===================================================================================*/
            
            if ($('.wp-block-group').hasClass('header-transparent')) {
                $('header.wp-block-template-part').addClass("transparent");
            }

            var header = $('header');
            var sticky = header.offset().top;
            var lastScrollTop = 0;
            // Add class to body when scrolling to add white background
            var targetDiv = $('body');

            $(window).scroll(function() {
                var scrollTop = $(this).scrollTop();
                var windowpos = $(window).scrollTop();

                // change amount here to choose distance from top to add class
                if( windowpos >= 50 ) {
                        //targetDiv.addClass('scrolling-active');
                } else {
                        targetDiv.removeClass('scrolling-active');
                }

                // Check if scrolling up
                if (scrollTop < lastScrollTop) {
                    // Scrolling up, make header sticky
                    header.addClass('sticky');
                } else {
                    // Scrolling down, remove sticky class
                    header.removeClass('sticky');
                }

                // Update last scroll position
                lastScrollTop = scrollTop;
            });

            // $('.v1-testimonial .slick-track .slick-current').each(function() {
            //     var currentClass = $(this).attr('class');
            //     var newClass = 'current-before ' + currentClass + ' current-after';
            //     $(this).attr('class', newClass);
            //   });
            
              

            /*===================================================================================*/
            /*  Slick Carousel Setup
            /*===================================================================================*/

            $('.gl-slick-single').slick({
                dots: false,
                autoplay: true,
                arrows: true,
                responsive: [
                    {
                      breakpoint: 600,
                      settings: {
                        arrows: false
                      }
                    }
                ]
            });

            $('.vertical-slider').slick({
                dots: true,
                vertical: true,
                slidesToShow: 1,
                slidesToScroll: 1,
                verticalSwiping: true,
            });


            $('.slider-for').slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                arrows: false,
                draggable: false,
                fade: true,
                cssEase: 'linear',
                asNavFor: '.slider-nav'
            });

            $('.slider-nav').slick({
                slidesToShow: 5,
                slidesToScroll: 1,
                asNavFor: '.slider-for',
                dots: false,
                centerMode: true,
                centerPadding: '50px',
                arrows: true,
                focusOnSelect: true,
                responsive: [
                    {
                      breakpoint: 600,
                      settings: {
                        arrows: false,
                        slidesToShow: 3
                      }
                    }
                ]
            });

            $(".gl-slick.has-2-columns .wc-block-grid__products, .gl-slick .columns-2").slick({
                slidesToShow: 2,
                slidesToScroll: 1,
                dots: false,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        dots: true
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        arrows: false
                      }
                    }
                ]
            });

            $(".gl-slick.has-3-columns .wc-block-grid__products, .gl-slick .columns-3").slick({
                slidesToShow: 3,
                slidesToScroll: 1,
                dots: true,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        dots: false,
                        arrows: true
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        dots: false,
                        arrows: true
                      }
                    }
                ]
            });

            $(".gl-slick.has-4-columns .wc-block-grid__products, .gl-slick .columns-4").slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                dots: true,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        dots: false,
                        arrows: true
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        dots: false,
                        arrows: true
                      }
                    }
                ]
            });

            $('.glowess-cat .wc-block-product-categories-list').slick({
                slidesToShow: 5,
                slidesToScroll: 5,
                dots: true,
                arrows: true,
                responsive: [
                    {
                        breakpoint: 1024,
                        settings: {
                          slidesToShow: 3,
                          slidesToScroll: 1,
                        }
                      },
                      {
                        breakpoint: 600,
                        settings: {
                          slidesToShow: 1,
                          slidesToScroll: 1,
                          arrows: false,
                            dots: false
                        }
                      }
                ]
            });


            $(".gl-slick.has-5-columns .wc-block-grid__products, .gl-slick .columns-5").slick({
                slidesToShow: 5,
                slidesToScroll: 1,
                dots: true,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        dots: false
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                      }
                    }
                ]
            });

            $(".gl-slick.has-6-columns .wc-block-grid__products").slick({
                slidesToShow: 6,
                slidesToScroll: 1,
                dots: false,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        dots: true
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        arrows: false
                      }
                    }
                ]
            });


            $(".gl-shop-slick.shop-activity .columns-4").slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                dots: false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 4,
                        slidesToScroll: 1,
                        dots: true
                      }
                    },
                    {
                      breakpoint: 600,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        arrows: false
                      }
                    }
                ]
            });

            /**************** slick-testimonial*********************/
            $(".gl-slick-testimonial").slick({
                slidesToShow: 2,
                slidesToScroll: 1,
                dots: true,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        dots: true
                      }
                    },
                    {
                      breakpoint: 768,
                      settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        arrows: false
                      }
                    }
                ]
            });
            /****************       END        *********************/
            /**************** slick-feature*********************/
            $(".gl-slick-feature").slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                dots: false,
            });
            
            $('.slick-v1-hero').slick({
                slidesToShow: 1,
                fade: true,
                dots: true,
                arrows: false,
            });
            
            //************************ About Testimonial ****************************//

            $(".gl-slick-testimonial-about").slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                dots: true,
                arrows: false,
                infinite:false,
                responsive: [
                    {
                      breakpoint: 1024,
                      settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        arrows: false,
                        dots: true
                      }
                    },
                    {
                      breakpoint: 768,
                      settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        arrows: false,
                        dots: true
                      }
                    }
                ]
            });

            $('.gl-slick-v2').slick({
                dots: true,
                speed: 1000,
                responsive: [
                    {
                        breakpoint: 600,
                        settings: {
                            breakpoint: 600,
                            slidesToShow: 1,
                            slidesToScroll: 1,
                            arrows: false
                        }
                    }
                ]
            });

            $('.gs-slick-single').slick({
                dots: false,
                autoplay: true,
                responsive: [
                    {
                      breakpoint: 600,
                      settings: {
                        arrows: false
                      }
                    }
                ]
            });
            /****************       END        *********************/
            
            /*===================================================================================*/
            /*  Shop page Fillter toggle
            /*===================================================================================*/
            
            // Check if the screen width is below a certain threshold (e.g., 1023 pixels)
            var isMobileScreen = window.matchMedia('(max-width: 1023px)').matches;

            // Your jQuery code for mobile screens
            if (isMobileScreen) {
                // Show Filter
                $('#showFilter .wp-block-button__link').click(function() {
                    $('#shop-filter-toggle').toggleClass("active");
                    $('body').toggleClass("toggle-overflow-hidden");
                });
                // Close Filter
                $('#CloseFilter').click(function() {
                    $('#shop-filter-toggle').removeClass("active");
                    $('body').removeClass("toggle-overflow-hidden");
                });
            } else {
                // Show Filter
                $('#CloseFilter').click(function() {
                    $('#shop-filter-toggle').toggleClass("active");
                    $('body').removeClass("toggle-overflow-hidden");
                });

                // Close Filter
                $('#showFilter .wp-block-button__link').click(function() {
                    $('#shop-filter-toggle').removeClass("active");
                    $('body').addClass("toggle-overflow-hidden");
                });
            }

            // Show the modal when the "Show Modal" button is clicked
            $('#openFilter a').click(function() {
                $('#shop-filter-toggle').toggleClass("open");
            });

            // Show the modal when the "Show Modal" button is clicked
            $('#showModalBtn,#showMModalBtn').click(function() {
                $('#myModal').toggleClass("flex");
                $('#showModalBtn,#showMModalBtn').toggleClass("active");
            });

            // Close the modal when the "Close" button or overlay is clicked
            $('#closeModalBtn').click(function() {
                $('#myModal,#myModal1').removeClass("flex");
                $('#myModal,#myModal1').hide();
            });

            /*===================================================================================*/
            /*  Search product no found class add in body.
            /*===================================================================================*/
            if ($('p').is(".woocommerce-no-products-found")) {
                $('body').addClass("woocommerce-no-cat-products-found");
            }
            
            /*===================================================================================*/
            /*  My Account page move lost password feild.
            /*===================================================================================*/
            $(".woocommerce-account .woocommerce-LostPassword").insertAfter(".woocommerce-account .form-row .woocommerce-form__label-for-checkbox");

            /*===================================================================================*/
            /*  Single Product add no-stock class
            /*===================================================================================*/
            if ($('.single-product form.cart .quantity input').is("input[type='hidden']")) {
                $('.single-product form.cart .quantity').addClass("no-stock");
            }
            
            /*===================================================================================*/
            /*  My account add span tag for order status
            /*===================================================================================*/
            $(".woocommerce-account .woocommerce-orders-table__cell-order-status").wrapInner(document.createElement("span"));
            
            /*===================================================================================*/
            /*  Shop page Fillter toggle
            /*===================================================================================*/

            $(document.body).on(
                'click', '.filter-toggle', function () {
                    if ($('.filter-content').children('.overlay').length == 0) {
                        var filter_overlay = document.createElement('div');
                        $(filter_overlay).addClass('overlay');
                        $('.filter-content').append(filter_overlay);
                    }

                    if ($('.filter-content').children('.filter-close').length == 0) {
                        var filter_close = document.createElement('div');
                        $(filter_close).addClass('filter-close');
                        $('.filter-content').append(filter_close);
                    }

                    $('.filter-content').hasClass('active') ? $('.filter-content').removeClass('active') : $('.filter-content').addClass('active');
                }
            );
            $(document.body).on(
                'click', '.filter-content .overlay, .filter-content .filter-close', function () {
                    $('.filter-content').removeClass('active');
                }
            );

            /*===================================================================================*/
            /*  My Account login style
            /*===================================================================================*/

            $('#customer_login').each(
                function () {
                    var titles = $(this).find('h2').get();
                    var tab_list_html = document.createElement('ul');
                    $(tab_list_html).addClass('customer_login_toggle');
                    titles.forEach(
                        function (title) {
                            var $tab_title = document.createElement('li');
                            $($tab_title).html('<h4>' + $(title).text() + '</h4>');
                            $(tab_list_html).append($tab_title);
                        }
                    );
                    $(this).prepend(tab_list_html);
                }
            );
            $('#customer_login').find('li').each(
                function (index) {
                    var tab_list = $('#customer_login').find('li');
                    var columns = $('#customer_login').find('>div');
                    $(tab_list[0]).add($(columns[0])).addClass('active');
                    $(this).on(
                        'click', function () {
                            columns.removeClass('active');
                            $(columns[index]).addClass('active');
                            tab_list.removeClass('active');
                            $(this).addClass('active');
                        }
                    );
                }
            );
            
            /*===================================================================================*/
            /*  Quantity style
            /*===================================================================================*/

            $.fn.insertQtyButtons = function () {
                $(this).each(
                    function () {
                        $(this).wrap('<div class="qty-container"></div>');
                        $(this).parent('.qty-container').append('<button class="qty-minus">-</button><button class="qty-plus">+</button>');
                    }
                );
            };

            $('form .qty').insertQtyButtons();
            $(document.body).on(
                'updated_cart_totals', function () {
                    $('.woocommerce-cart-form .qty').insertQtyButtons();
                }
            );

            /*===================================================================================*/
            /*  Cart Page update coupon code
            /*===================================================================================*/

            $(document.body).on(
                'updated_checkout', function () {
                    if ($('input[name="coupon_code"]').length == 0) {
                        $.ajax(
                            {
                                type: 'POST',
                                url: wc_checkout_params.wc_ajax_url.toString().replace('%%endpoint%%', 'checkout_coupon'),
                                data: {},
                                success: function success(res)
                                {
                                    console.log("res", res);
                                    $('<tr class="mdt_checkout_coupon_field"><td>' + res + '</td></tr>').insertBefore('.woocommerce-checkout-review-order-table .order-total');
                                },
                                dataType: 'html'
                            }
                        );
                    }
                }
            );
            $(document.body).on(
                'click', ".coupon_submit", function (e) {
                    e.preventDefault();
                    var $coupon = $(this).closest('.mdt_checkout_coupon');
                    var data = {
                        security: wc_checkout_params.apply_coupon_nonce,
                        coupon_code: $(this).siblings('input[name="coupon_code"]').val()
                    };
                    $.ajax(
                        {
                            type: 'POST',
                            url: wc_checkout_params.wc_ajax_url.toString().replace('%%endpoint%%', 'apply_coupon'),
                            data: data,
                            success: function success(code)
                            {
                                if (code) {
                                      $coupon.before(code);
                                    $(document.body).trigger(
                                        'update_checkout', {
                                            update_shipping_method: false
                                        }
                                    );
                                }
                            },
                            dataType: 'html'
                        }
                    );
                }
            );

            /*===================================================================================*/
            /*  Quantity input function
            /*===================================================================================*/

            $(document.body).on(
                'click', '.qty-plus, .qty-minus', function (e) {
                    e.preventDefault();
                    var $qty = $(this).closest('.qty-container').find('.qty'),
                    currentVal = parseFloat($qty.val()),
                    max = parseFloat($qty.attr('max')),
                    min = parseFloat($qty.attr('min')),
                    step = $qty.attr('step');
                    if (!currentVal || currentVal === '' || currentVal === 'NaN') { currentVal = 0;
                    }
                    if (max === '' || max === 'NaN') { max = '';
                    }
                    if (min === '' || min === 'NaN') { min = 0;
                    }
                    if (step === 'any' || step === '' || step === undefined || parseFloat(step) === 'NaN') { step = 1;
                    }

                    if ($(this).is('.qty-plus')) {
                        if (max && (max == currentVal || currentVal > max)) {
                            $qty.val(max);
                        } else {
                            $qty.val(currentVal + parseFloat(step));
                        }
                    } else {
                        if (min && (min == currentVal || currentVal < min)) {
                            $qty.val(min);
                        } else if (currentVal > 0) {
                            $qty.val(currentVal - parseFloat(step));
                        }
                    }

                    $qty.trigger('change');
                }
            );

            
        }
    );
})(jQuery);
