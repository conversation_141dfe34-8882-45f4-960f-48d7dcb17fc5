/*
################
* === Prodcut Filter Style  ===
################
*/

body:not(.editor-styles-wrapper) {
    @media (min-width:1024px) {
        #shop-filter-toggle:not(.active) {
            #showFilter {
                display: none;
            }
        }
    }   

    @media (min-width:1200px) {
        #shop-filter-toggle.active {
            #showFilter {
                display: none;
            }
        }
    }
}

@media (max-width: 1023px) {
    .filter-content .filter-close {
        position: absolute;
        right: 30px !important;
        top:20px !important;
        padding: 12px;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath d='M8 1.2A6.74 6.74 0 0 0 1.2 8 6.74 6.74 0 0 0 8 14.8 6.74 6.74 0 0 0 14.8 8 6.74 6.74 0 0 0 8 1.2zM0 8c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8zm10.6-2.6a.61.61 0 0 1 0 .8L8.8 8l1.9 1.9a.61.61 0 0 1 0 .8.61.61 0 0 1-.8 0L8 8.8l-1.9 1.9a.61.61 0 0 1-.8 0 .61.61 0 0 1 0-.8L7.2 8 5.4 6.1a.61.61 0 0 1 0-.8.61.61 0 0 1 .8 0l1.9 1.9L10 5.3c.1-.1.4-.1.6.1z' fill-rule='evenodd' fill='%23000'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: center;
        margin-top: 0;
        opacity: .55;
    }
}

.filter-content {
    details {
        summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: var(--wp--preset--font-family--heading);

            &::after {
                font-family: "bootstrap-icons";
                content:"\F285";
                font-size: 13px;
                font-weight: 700;
            }
        }

        &[open] {
            summary {
                &::after {
                    content:"\F282";
                }
            }
        }
    }

    .wp-block-woocommerce-product-categories .wc-block-product-categories-list-item:not(:last-child) {
        margin-bottom: 14px;
    }
}

#shop-filter-toggle {
    transition: .3s ease-in-out;

    @media (min-width:1024px) {
        &.active {
            gap: 0;

            .filter-block {
                flex-basis: 0% !important;
                visibility: hidden;
                opacity: 0;
                max-height: 200px;
            }
    
            .archive-content {
                flex-basis: 100% !important;
            }
        }
    }

    .archive-content,
    .filter-block {
        transition: .3s ease-in-out;
    }

    @media (max-width:1023px) {
        &::after {
            content: "";
            background-color: color-mix(in srgb, var(--wp--preset--color--black) 70%, var(--wp--preset--color--white) 10%);
            position: fixed;
            inset: 0;
            z-index: 2;
            transition: .3s ease-in-out;
            opacity: 0;
            visibility: hidden;
        }

        &.active  {
            .filter-block {
                opacity: 1;
                visibility: visible;
                left: 0;
            }

            &::after{
                opacity: 1;
                visibility: visible;
            }
        }

        .filter-block {
            left: -100%;
            z-index: 9;
            background-color: var(--wp--preset--color--white);
            top: 0;
            bottom: 90px;
            padding: 30px;
            max-width: 400px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            overflow-y: auto;
            margin-top: 0;
            position: fixed;
        }

        #showFilter {
            @media (min-width:1200px) {
                display: none;
            }

            .wp-block-button__link {
                &::before {
                    content: '\F3E1';
                    font-family: "bootstrap-icons";
                }
            }
        }

        #showFilter,
        #CloseFilter {
            position: fixed;
            bottom: 0;
            right: 0;
            left: 0;
            z-index: 9;
            width: 100%;
            background-color: rgb(from var(--wp--preset--color--secondary) r g b / .86);
            justify-content: center;
            padding: 25px 0;

            .wp-block-button__link {
                background-color: var(--wp--preset--color--white);
                display: flex;
                gap: 10px;
                border-color: transparent !important;
                font-weight: 700;
            }
        }
    }
}

@media (max-width:1023px) {
    .toggle-overflow-hidden {
        #shop-filter-toggle #showFilter .wp-block-button__link::before {
            content: "\F659";
        }
    }
}

.wc-block-product-categories.wc-block-product-categories ul {
    margin-left: 0;
}

@media (min-width:1024px) and (max-width:1199px) {
    .filter-block {
        flex-basis: 30%  !important;
    }

    .archive-content {
        flex-basis: 70%  !important;
    }
}

.archive-content {
    .wc-block-catalog-sorting {
        select {
            border-width: 0;
            padding: 0;
        }
    }

    .woocommerce.wc-block-catalog-sorting select.orderby {
        font-size: 15px;
        text-transform: capitalize;
        background-size: 18px 10px;
        background-position: right -4px center;

        &:focus {
            border-width: 0;
            box-shadow: none;
        }
    }
}

@media (max-width:1023px) {
    .admin-bar #shop-filter-toggle .filter-block {
        margin-top: 46px;
    }

    body.toggle-overflow-hidden {
        overflow: hidden;
    }
}

.wc-block-components-price-slider__actions {
    justify-content: flex-start !important;  
    margin-top: 10px;
}

.wc-block-product-categories-list {
    list-style: none;
    padding: 0;
    margin: 0;

    ul {
        padding-left: 16px;
        list-style: none;
    }
}

.wc-block-components-product-rating__stars {
    color: var(--wp--preset--color--warning);
    font-family: "bootstrap-icons";
    font-size: 12px;
    width: 6.2em;
    letter-spacing: 2.8px;
    margin: 0 0 8px;
    font-weight: 400;
    height: 1.618em;
    line-height: 1.618;
    overflow: hidden;
    position: relative;
    text-align: left;
    display: inline-block;


    &::before {
        content: '\F586\F586\F586\F586\F586';
        font-family: "bootstrap-icons";
        color: var(--wp--preset--color--warning);
        opacity: 1;
        position: absolute;
        right: 0;
        top: 0;
        white-space: nowrap;
    }

    span:before,
    strong:before {
        font-family: "bootstrap-icons";
        content: '\F586\F586\F586\F586\F586';
    }

    > span {
        display: block;
        overflow: hidden;
    }
}

.wc-block-attribute-filter__actions {
    justify-content: flex-start;

    .wc-block-components-filter-reset-button {
        margin-bottom: 20px;
        margin-top: 20px;
        color: var(--wp--preset--color--danger);
    }
}

body:not(.editor-styles-wrapper) .filter-block input[type=checkbox],
body:not(.editor-styles-wrapper) .filter-block .wc-block-components-checkbox input[type=checkbox], 
.filter-block .wc-block-checkbox-list .wc-block-components-checkbox input[type=checkbox],
.filter-block div.wc-block-components-checkbox input[type=checkbox] {
    margin: 0 11px 0 0;
}

.filter-block {
    .wc-block-components-checkbox {
        label {
            align-items: center;
            font-size: 16px;
        }
    }
}

.wc-block-components-price-slider__range-input-wrapper:before {
    color: var(--wp--preset--color--gray-100);
    opacity: 1;
}

.wc-block-components-price-slider__range-input-progress {
    color: var(--wp--preset--color--secondary);   
}

.wc-block-components-price-slider__range-input-wrapper,
.wc-block-components-price-slider__range-input-progress {
    height: 3px;
}

body:not(.editor-styles-wrapper) {
    .filter-color {
        #blue::before {
            background-color: #0016F5;
        }
    
        #gray::before {
            background-color: #808080;
        }
    
        #green::before {
            background-color: #3A7E21;
        }
    
        #red::before {
            background-color: #F5513A;
        }
    
        #yellow::before {
            background-color: #f2de2c;
        }

        #brown::before {
            background-color: #973530;
        }

        #orange::before {
            background-color: #F2A93B;
        }

        #pink::before {
            background-color: #FAE5E2;
        }
    
        #black::before {
            background-color: #000;
        }
    
        .wc-block-attribute-filter-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px 20px;
        }
    
        &.wp-block-woocommerce-filter-wrapper .wc-block-components-checkbox {
            margin-top: 0;
        }
    
        .wc-block-checkbox-list {
            .wc-block-components-checkbox {
                position: relative;
    
                label {
                    position: static;
                }
    
                input:checked[type=checkbox],
                .wc-block-components-checkbox__input[type=checkbox]:checked {
                    background-image: none;
                    background-color: var(--wp--preset--color--base);
                    border-width: 0;
                    
                    &::after {
                        border: 1px solid var(--wp--preset--color--secondary);
                    }                
                }
        
                input[type=checkbox],
                .wc-block-components-checkbox__input[type=checkbox] {
                    border-width: 0;
                    display: grid;
                    overflow: visible;
                    min-width: 30px;
                    width: 30px;
                    height: 30px;

                    &::before,
                    &::after {
                        grid-column: 1 / -1;
                        grid-row: 1 / -1;
                        margin: auto;
                    }
    
                    &::before {
                        width: 20px;
                        height: 20px;
                        border-radius: 100px;
                        display: block;
                    }
    
                    &::after {
                        border: 1px solid transparent;
                        border-radius: 50px;
                        height: 30px;
                        width: 30px;
                    }
                }
            }
        }
    }
}

.wc-block-components-price-slider__range-input--min::-webkit-slider-thumb, 
.wc-block-components-price-slider__range-input--max::-webkit-slider-thumb {
    box-shadow: 0;
    border: 2px solid var(--wp--preset--color--secondary) !important;
    background: var(--wp--preset--color--base) !important;
    height: 25px;
    width: 25px;
    
    &:is(:hover,:active,:focus,:focus-visible,:focus-within) {
        background: var(--wp--preset--color--base);
        box-shadow: 0 0 0 0.236em rgba(0,0,0,.1);
    }
}

.wc-block-components-price-slider__range-input {
    top: -7px;
}

.wc-block-components-price-slider__range-input--min:focus 
.wc-block-components-price-slider__range-input--max:focus{
    background-color: var(--wp--preset--color--base);
}

.wc-block-components-price-slider__range-input--min:hover, 
.wc-block-components-price-slider__range-input--max:hover {
    &,
    &::-webkit-slider-thumb {
        box-shadow: 0 2px 8px rgba(0, 0, 0, .09);
        background: var(--wp--preset--color--base);
    }
}

.wc-block-components-product-rating__stars {
    margin: 0;
}

.wc-block-components-product-rating {
    display: flex;
}

.wp-block-woocommerce-filter-wrapper .wc-block-components-checkbox {
    margin-top: 11px;
}

.wp-block-woocommerce-filter-wrapper .wc-block-rating-filter .wc-block-components-checkbox {
    margin-top: 17px;
}

.wc-block-components-price-slider__range-text {
    justify-content: flex-start;
    gap: 6px;
    align-items: center;
    margin: 21px 0 0 0;

    span:first-child {
        order: -1;
    }

    &::before {
        content: "__";
        display: block;
        line-height: 1;
        position: relative;
        top: -6px;
    }
}

.wc-block-components-price-slider {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.wc-block-components-price-slider__range-input-wrapper {
    width: 100%;
}

.wc-block-components-price-slider__actions button[type=submit]:not(.wp-block-search__button).wc-block-components-filter-submit-button {
    font-size: 15px;
    font-weight: 500;
    text-transform: capitalize;
    background-color: transparent;
    border-width: 0;
    padding: 0;
    color: var(--wp--preset--color--contrast);
    text-align: right;
}

.wc-block-components-filter-reset-button {
    color: var(--wp--preset--color--secondary);
}

.wc-block-components-price-slider__actions {
    margin-top: 20px;
}

#openFilter {
    a {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 700;

        &::after {
            content: '\F3E1';
            font-family: "bootstrap-icons";
        }
    }
}

.open {
    #openFilter a::after {
        content: "\F659";
    }
}

body:not(.editor-styles-wrapper) {
    #openFilter {
        display: none;

        @media (min-width:1024px) {
            display: block;
        }
    }
}

@media (min-width:1024px) {
    body:not(.editor-styles-wrapper) {    
        .shop-v1 {
            display: grid;
            margin-bottom: 0;
        
            > div {
                grid-column: 1/-1;
                grid-row: 1/-1;
            }
        
            &:not(.open) .filter-block {
                display: none;
            }
        
            .filter-block {
                width: 100%;
                z-index: 2;
                margin-top: 66px;
        
                > div {
                    padding: 40px;
                    border: 1px solid var(--wp--preset--color--contrast);
                }
            }
        
            .filter-content {
                > div {
                    max-width: var(--wp--style--global--wide-size);
                    display: flex;
                    gap: 40px;
        
                    > * {
                        margin-top: 0 !important;
                        flex: 0 100%;
                    }
        
                    .wp-block-heading {
                        margin-top: 0;
                        padding-top: 0 !important;
                    }
                }
            }
        
            #openFilter {
                .wp-block-button__link {
                    img:last-child {
                        display: none;
                    }
                }
            }
        
            &.open {
                #openFilter {
                    .wp-block-button__link {
                        img:first-child {
                            display: none;
                        }
        
                        img:last-child {
                            display: block;
                        }
                    }
                }
            }
        }
    }
}


