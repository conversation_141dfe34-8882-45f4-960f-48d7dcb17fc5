# Translation of Plugins - Contact Form 7 - Stable (latest release) in Turkish
# This file is distributed under the same license as the Plugins - Contact Form 7 - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-22 17:28:11+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: tr\n"
"Project-Id-Version: Plugins - Contact Form 7 - Stable (latest release)\n"

#: modules/checkbox.php:219
msgid "Too many items are selected."
msgstr "Çok fazla öğe seçildi."

#: includes/contact-form.php:572
msgid "Invalid action URL is detected."
msgstr "Geçersiz eylem URL'si tespit edildi."

#: modules/constant-contact/constant-contact.php:135
msgid "https://contactform7.com/2024/02/02/we-end-the-constant-contact-integration/"
msgstr "https://contactform7.com/2024/02/02/we-end-the-constant-contact-integration/"

#: modules/checkbox.php:300 modules/select.php:217
msgid "Undefined value was submitted through this field."
msgstr "Bu alan üzerinden tanımlanmamış değer gönderildi."

#: includes/rest-api.php:364
msgid "There is no valid unit tag."
msgstr "Geçerli bir birim etiketi yok."

#: modules/sendinblue/contact-form-properties.php:98
#: modules/sendinblue/service.php:177
msgid "Brevo integration"
msgstr "Brevo entegrasyonu"

#. translators: 1: blog name, 2: blog URL
#: includes/contact-form-template.php:111
msgid "This email is a receipt for your contact form submission on our website (%1$s %2$s) in which your email address was used. If that was not you, please ignore this message."
msgstr "Bu e-posta, e-posta adresinizin kullanıldığı web sitemizdeki (%1$s %2$s) iletişim formu gönderiminizin makbuzudur. Eğer bu kişi siz değilseniz, lütfen bu mesajı dikkate almayın."

#. translators: 1: blog name, 2: blog URL
#: includes/contact-form-template.php:77
msgid "This is a notification that a contact form was submitted on your website (%1$s %2$s)."
msgstr "Bu, web sitenizde bir iletişim formunun gönderildiğine dair bir bildirimdir (%1$s %2$s)."

#: includes/config-validator/mail.php:234
#: includes/config-validator/mail.php:324
msgid "Unsafe email config is used without sufficient protection."
msgstr "Güvenli kabul edilmeyen bir e-posta yapılanması yeterli koruma düzeyi olmadan kullanılıyor."

#: includes/config-validator/validator.php:239
msgid "Configuration error is detected."
msgstr "Konfigürasyon hatası tespit edildi."

#: modules/sendinblue/service.php:189
msgid "Brevo is active on this site."
msgstr "Brevo bu sitede aktif."

#: admin/includes/welcome-panel.php:157
#: modules/sendinblue/contact-form-properties.php:110
#: modules/sendinblue/contact-form-properties.php:405
#: modules/sendinblue/service.php:30
msgid "Brevo"
msgstr "Brevo"

#: includes/contact-form-functions.php:271
msgid "Contact form not found."
msgstr "İletişim formu bulunamadı."

#: includes/contact-form-functions.php:270 includes/contact-form.php:571
#: includes/js/index.js:1
msgid "Error:"
msgstr "Hata:"

#: includes/block-editor/block.json
msgctxt "block keyword"
msgid "form"
msgstr "form"

#: includes/block-editor/block.json
msgctxt "block description"
msgid "Insert a contact form you have created with Contact Form 7."
msgstr "Contact Form 7 ile oluşturduğunuz bir iletişim formu ekleyin."

#: includes/block-editor/block.json
msgctxt "block title"
msgid "Contact Form 7"
msgstr "Contact Form 7"

#: modules/select.php:88
msgid "&#8212;Please choose an option&#8212;"
msgstr "&#8212;Lütfen bir seçenek seçin&#8212;"

#: modules/akismet/service.php:77
msgid "Akismet is active on this site."
msgstr "Akismet bu sitede aktiftir."

#: modules/akismet/service.php:54
msgid "CAPTCHAs are designed to distinguish spambots from humans, and are therefore helpless against human spammers. In contrast to CAPTCHAs, Akismet checks form submissions against the global database of spam; this means Akismet is a comprehensive solution against spam. This is why we consider Akismet to be the centerpiece of the spam prevention strategy."
msgstr "CAPTCHA'lar spambotları insanlardan ayırmak için tasarlanmıştır ve bu nedenle insan spam göndericilere karşı çaresizdir. CAPTCHA'ların aksine, Akismet form gönderimlerini küresel spam veritabanına karşı kontrol eder; bu da Akismet'in spam'e karşı kapsamlı bir çözüm olduğu anlamına gelir. Bu nedenle Akismet'i spam önleme stratejisinin en önemli parçası olarak görüyoruz."

#: modules/akismet/akismet.php:305
msgid "Learn how your data is processed."
msgstr "Verilerinizin nasıl işlendiğini öğrenin."

#: modules/akismet/akismet.php:302
msgid "This form uses Akismet to reduce spam."
msgstr "Bu form, spam'i azaltmak için Akismet'i kullanır."

#: includes/contact-form.php:645
msgid "Contact form"
msgstr "İletişim Formu"

#: includes/config-validator/form.php:69
msgid "Colons are used in form-tag names."
msgstr "Form etiketi adlarında iki nokta üst üste kullanılır."

#: modules/text.php:197
msgid "Please enter a telephone number."
msgstr "Lütfen bir telefon numarası girin."

#: modules/text.php:190
msgid "Please enter a URL."
msgstr "Lütfen bir URL girin."

#: modules/text.php:183
msgid "Please enter an email address."
msgstr "Lütfen bir e-posta adresi giriniz."

#: modules/number.php:191
msgid "This field has a too large number."
msgstr "Bu alan çok büyük bir sayıya sahiptir."

#: modules/number.php:186
msgid "This field has a too small number."
msgstr "Bu alan çok küçük bir sayıya sahiptir."

#: modules/number.php:181
msgid "Please enter a number."
msgstr "Lütfen bir sayı giriniz."

#: modules/date.php:172
msgid "This field has a too late date."
msgstr "Bu alan çok geç bir tarihe sahiptir."

#: modules/date.php:167
msgid "This field has a too early date."
msgstr "Bu alan çok erken bir tarihe sahiptir."

#: modules/date.php:162
msgid "Please enter a date in YYYY-MM-DD format."
msgstr "Lütfen YYYY-AA-GG formatında bir tarih girin."

#: includes/rest-api.php:338
msgid "The request payload format is not supported."
msgstr "İstek yükü biçimi desteklenmiyor."

#. translators: 1: WordPress hook name, 2: version number
#: includes/functions.php:537
msgid "Hook %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s with no alternative available."
msgstr "%1$s kancası Contact Form 7'nin %2$s sürümünden beri <strong>öncelikli değil</strong> ve alternatifi yok."

#: includes/file.php:116
msgid "The uploaded file is too large."
msgstr "Yüklenen dosya çok büyük."

#: includes/contact-form-template.php:199
msgid "This field has a too short input."
msgstr "Bu alan çok kısa bir girişe sahiptir."

#: includes/contact-form-template.php:194
msgid "This field has a too long input."
msgstr "Bu alan çok uzun bir girdiye sahiptir."

#: includes/contact-form-template.php:189
msgid "Please fill out this field."
msgstr "Lütfen bu alanı doldurunuz."

#. translators: 1: PHP function name, 2: version number, 3: alternative
#. function name
#: includes/functions.php:493
msgid "Function %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s! Use %3$s instead."
msgstr "%1$s işlevi, İletişim Formu 7 sürüm %2$s'den beri <strong>kullanımdan kaldırılmıştır</strong>! Bunun yerine %3$s kullanın."

#: includes/integration.php:25
msgid "Spam protection"
msgstr "Spam koruması"

#. translators: Developer debugging message. 1: PHP function name, 2:
#. Explanatory message, 3: Contact Form 7 version number.
#: includes/functions.php:577
msgid "Function %1$s was called incorrectly. %2$s %3$s"
msgstr "%1$s işlevi yanlış çağrıldı. %2$s %3$s"

#. translators: 1: blog name, 2: confirmation link
#: modules/sendinblue/doi.php:84
msgid ""
"Hello,\n"
"\n"
"This is a confirmation email sent from %1$s.\n"
"\n"
"We have received your submission to our web form, according to which you have allowed us to add you to our contact list. But, the process has not yet been completed. To complete it, please click the following link.\n"
"\n"
"%2$s\n"
"\n"
"If it was not your intention, or if you have no idea why you received this message, please do not click on the link, and ignore this message. We will never collect or use your personal data without your clear consent.\n"
"\n"
"Sincerely,\n"
"%1$s"
msgstr ""
"Merhaba,\n"
"\n"
"Bu, %1$s tarafından gönderilen bir onay e-postasıdır.\n"
"\n"
"Sizi iletişim listemize eklememize izin verdiğiniz web formumuza yaptığınız başvuruyu aldık. Ancak süreç henüz tamamlanmadı. Tamamlamak için lütfen aşağıdaki bağlantıyı tıklayın.\n"
"\n"
"%2$s\n"
"\n"
"Niyetiniz bu değilse veya bu mesajı neden aldığınızı bilmiyorsanız, lütfen bağlantıya tıklamayın ve bu mesajı dikkate almayın. Açık onayınız olmadan kişisel verilerinizi asla toplamayacağız veya kullanmayacağız.\n"
"\n"
"Saygılarımızla,\n"
"%1$s"

#. translators: %s: blog name
#: modules/sendinblue/doi.php:78
msgid "Opt-in confirmation from %s"
msgstr "%s'den katılım onayı"

#: modules/stripe/stripe.php:380
msgid "Complete payment"
msgstr "Ödemeyi tamamla"

#: modules/stripe/stripe.php:365
msgid "Proceed to checkout"
msgstr "Ödemeye Git"

#: modules/stripe/stripe.php:249
msgid "Payment is required. Please pay by credit card."
msgstr "Ödeme gereklidir. Lütfen kredi kartı ile ödeme yapınız."

#: includes/integration.php:27
msgid "Payments"
msgstr "Ödemeler"

#: modules/stripe/service.php:252
msgid "Publishable Key"
msgstr "Yayınlanabilir Anahtar"

#: modules/stripe/service.php:225
msgid "Stripe is not available on this site. It requires an HTTPS-enabled site."
msgstr "Stripe bu sitede mevcut değil. HTTPS özellikli bir site gerektirir."

#: modules/stripe/service.php:196
msgid "Stripe is active on this site."
msgstr "Stripe bu sitede aktif."

#: modules/stripe/service.php:184
msgid "Stripe integration"
msgstr "Stripe entegrasyonu"

#. translators: 1: first name, 2: last name
#: modules/sendinblue/sendinblue.php:114
msgctxt "personal name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: includes/rest-api.php:517
msgid "Unique identifier for the contact form."
msgstr "İletişim formu için benzersiz tanımlayıcı."

#: includes/config-validator/form.php:57
msgid "Dots are used in form-tag names."
msgstr "Form etiketi adlarında noktalar kullanılır."

#: admin/includes/welcome-panel.php:305
msgid "Show welcome panel"
msgstr "Karşılama panelini göster"

#: admin/includes/welcome-panel.php:302
msgid "Welcome panel"
msgstr "Hoş geldiniz paneli"

#: admin/includes/welcome-panel.php:169 modules/stripe/service.php:38
msgid "Stripe"
msgstr "Stripe"

#: admin/includes/welcome-panel.php:168 modules/stripe/service.php:183
msgid "https://contactform7.com/stripe-integration/"
msgstr "https://contactform7.com/stripe-integration/"

#. translators: 1: link labeled 'Cloudflare Turnstile', 2: link labeled
#. 'Stripe'
#: admin/includes/welcome-panel.php:162
msgid "With help from cloud-based machine learning, anti-spam services will protect your forms (%1$s). Even payment services are natively supported (%2$s)."
msgstr "Bulut tabanlı makine öğreniminin yardımıyla istenmeyen e-posta önleme hizmetleri formlarınızı (%1$s) koruyacaktır. Ödeme hizmetleri bile yerel olarak desteklenmektedir (%2$s)."

#. translators: 1: link labeled 'Brevo'
#: admin/includes/welcome-panel.php:154
msgid "Your contact forms will become more powerful and versatile by integrating them with external APIs. With CRM and email marketing services, you can build your own contact lists (%1$s)."
msgstr "İletişim formlarınız harici API'lerle entegre edilerek daha güçlü ve çok yönlü hale gelecektir. CRM ve e-posta pazarlama hizmetleri ile kendi kişi listelerinizi oluşturabilirsiniz (%1$s)."

#: admin/includes/welcome-panel.php:146
msgid "You have strong allies to back you up."
msgstr "Sizi destekleyecek güçlü müttefikleriniz var."

#: admin/admin.php:589
msgid "Integration with external APIs"
msgstr "Harici API'ler ile entegrasyon"

#: admin/admin.php:588
msgid "https://contactform7.com/integration-with-external-apis/"
msgstr "https://contactform7.com/integration-with-external-apis/"

#. translators: %s: link labeled 'Integration with external APIs'
#: admin/admin.php:586
msgid "You can expand the possibilities of your contact forms by integrating them with external services. For details, see %s."
msgstr "İletişim formlarınızın olanaklarını harici servislerle entegre ederek genişletebilirsiniz. Ayrıntılar için bkz. %s."

#: admin/admin.php:65 admin/admin.php:576
msgid "Integration with External API"
msgstr "Harici API ile Entegrasyon"

#: modules/sendinblue/contact-form-properties.php:374
msgid "Manage your email templates"
msgstr "E-posta şablonlarınızı yönetin"

#: modules/sendinblue/contact-form-properties.php:357
msgid "You have no active email template yet."
msgstr "Henüz aktif bir e-posta şablonunuz yok."

#: modules/sendinblue/contact-form-properties.php:335
msgid "&mdash; Select &mdash;"
msgstr "&mdash; Seç &mdash;"

#: modules/sendinblue/contact-form-properties.php:320
msgid "Select an email template:"
msgstr "Bir e-posta şablonu seçin:"

#: modules/sendinblue/contact-form-properties.php:299
msgid "Send a welcome email to new contacts"
msgstr "Yeni kişilere hoş geldiniz e-postası gönderin"

#: modules/sendinblue/contact-form-properties.php:269
#: modules/sendinblue/contact-form-properties.php:281
msgid "Welcome email"
msgstr "Hoşgeldin e-postası"

#: modules/sendinblue/contact-form-properties.php:246
#: modules/sendinblue/contact-form-properties.php:384
msgid "(opens in a new tab)"
msgstr "(yeni bir sekmede açılır)"

#: modules/sendinblue/contact-form-properties.php:236
msgid "Manage your contact lists"
msgstr "Kişi listelerinizi yönetin"

#: modules/sendinblue/contact-form-properties.php:219
msgid "You have no contact list yet."
msgstr "Henüz bir iletişim listeniz yok."

#: modules/sendinblue/contact-form-properties.php:188
msgid "Select lists to which contacts are added:"
msgstr "Kişilerin ekleneceği listeleri seçin:"

#: modules/sendinblue/contact-form-properties.php:169
msgid "Add form submitters to your contact lists"
msgstr "Form gönderenleri kişi listelerinize ekleyin"

#: modules/sendinblue/contact-form-properties.php:139
#: modules/sendinblue/contact-form-properties.php:151
msgid "Contact lists"
msgstr "İletişim listeleri"

#. translators: %s: link labeled 'Brevo integration'
#: modules/sendinblue/contact-form-properties.php:94
msgid "You can set up the Brevo integration here. For details, see %s."
msgstr "Brevo entegrasyonunu buradan ayarlayabilirsiniz. Ayrıntılar için %s bölümüne bakın."

#: modules/sendinblue/service.php:251
msgid "Save changes"
msgstr "Değişiklikleri kaydet"

#: modules/sendinblue/service.php:247
msgctxt "API keys"
msgid "Remove key"
msgstr "Anahtarı kaldır"

#: modules/sendinblue/service.php:226
msgid "API key"
msgstr "API anahtarı"

#: modules/recaptcha/service.php:331 modules/sendinblue/service.php:208
#: modules/stripe/service.php:215 modules/turnstile/service.php:311
msgid "Setup integration"
msgstr "Entegrasyon Kurulumu"

#: admin/includes/welcome-panel.php:156
#: modules/sendinblue/contact-form-properties.php:97
#: modules/sendinblue/service.php:176
msgid "https://contactform7.com/sendinblue-integration/"
msgstr "https://contactform7.com/sendinblue-integration/"

#: modules/sendinblue/service.php:166
msgid "Store and organize your contacts while protecting user privacy on Brevo, the leading CRM & email marketing platform in Europe. Brevo offers unlimited contacts and advanced marketing features."
msgstr "Avrupa'nın lider CRM ve e-posta pazarlama platformu Brevo'da kullanıcı gizliliğini korurken kişilerinizi saklayın ve düzenleyin. Brevo sınırsız kişi ve gelişmiş pazarlama özellikleri sunar."

#: modules/sendinblue/service.php:128
msgid "You have not been authenticated. Make sure the provided API key is correct."
msgstr "Kimliğiniz doğrulanmadı. Sağlanan API anahtarının doğru olduğundan emin olun."

#: includes/mail.php:311
msgid "Failed to attach a file. The total file size exceeds the limit of 25 megabytes."
msgstr "Dosya eklenemedi. Toplam dosya boyutu 25 megabayt sınırını aşıyor."

#. translators: %s: Attachment file path.
#: includes/mail.php:291
msgid "Failed to attach a file. %s is not a readable file."
msgstr "Dosya eklenemedi. %s okunabilir bir dosya değil."

#. translators: %s: Attachment file path.
#: includes/mail.php:277
msgid "Failed to attach a file. %s is not in the allowed directory."
msgstr "Dosya eklenemedi. %s, izin verilen dizinde değil."

#: includes/contact-form-template.php:42
msgid "Submit"
msgstr "Gönder"

#: includes/contact-form-template.php:37
msgid "(optional)"
msgstr "(tercihe bağlı)"

#: admin/includes/welcome-panel.php:74
msgid "disallowed list"
msgstr "izin verilmeyenler listesi"

#. translators: %s: comma separated list of disallowed words
#: modules/disallowed-list.php:36
msgid "Disallowed words (%s) are used."
msgstr "İzin verilmeyen kelimeler (%s) kullanılmış."

#: modules/disallowed-list.php:32
msgid "Disallowed words are used."
msgstr "İzin verilmeyen kelimeler kullanılmış."

#. translators: %s: Contact Form 7 version number.
#: includes/functions.php:568
msgid "(This message was added in Contact Form 7 version %s.)"
msgstr "(Bu mesaj Contact Form 7 sürüm %s ile eklendi.)"

#: includes/special-mail-tags.php:22 includes/special-mail-tags.php:104
#: includes/special-mail-tags.php:170 includes/special-mail-tags.php:239
#: modules/flamingo.php:310
msgid "The fourth parameter ($mail_tag) must be an instance of the WPCF7_MailTag class."
msgstr "Dördüncü parametre ($mail_tag), WPCF7_MailTag sıfının bir örneği olmalı."

#. translators: time format, see https://www.php.net/date
#: admin/includes/class-contact-forms-list-table.php:231
msgid "g:i a"
msgstr "H:i"

#. translators: 1: date, 2: time
#: admin/includes/class-contact-forms-list-table.php:227
msgid "%1$s at %2$s"
msgstr "%1$s - %2$s"

#: admin/edit-contact-form.php:373
msgid "docs"
msgstr "belgeler"

#. translators: 1: FAQ, 2: Docs ("FAQ & Docs")
#: admin/edit-contact-form.php:366
msgid "%1$s and %2$s"
msgstr "%1$s ve %2$s"

#: admin/includes/editor.php:527
msgid "Additional settings"
msgstr "Ek ayarlar"

#: modules/sendinblue/service.php:127 modules/sendinblue/service.php:138
msgid "Error"
msgstr "Hata"

#: includes/config-validator/mail.php:437
msgid "The total size of attachment files is too large."
msgstr "Ek dosyalarının toplam boyutu çok büyük."

#: includes/config-validator/form.php:45
msgid "Unavailable HTML elements are used in the form template."
msgstr "Şablon formunda uygun olmayan HTML öğeleri kullanılıyor"

#: modules/recaptcha/service.php:312
msgid "reCAPTCHA is active on this site."
msgstr "reCAPTCHA bu sitede aktif."

#: modules/recaptcha/recaptcha.php:148
msgid "reCAPTCHA response token is empty."
msgstr "reCAPTCHA cevap anahtarı boş."

#: includes/submission.php:660
msgid "Submitted nonce is invalid."
msgstr "Geçici olarak gönderilen geçersiz."

#: includes/submission.php:651
msgid "User-Agent string is unnaturally short."
msgstr "Kullanıcı aracısı metin öngörülenden daha kısa."

#. translators: 1: value of reCAPTCHA score 2: value of reCAPTCHA threshold
#: modules/recaptcha/recaptcha.php:155
msgid "reCAPTCHA score (%1$.2f) is lower than the threshold (%2$.2f)."
msgstr "(%1$.2f) reCAPTCHA puanı (%2$.2f) eşikten daha düşük."

#: modules/akismet/akismet.php:84
msgid "Akismet returns a spam response."
msgstr "Akismet tarafından istenmeyen olarak döndü."

#. translators: 1: response code, 2: message, 3: body, 4: URL
#: includes/functions.php:621
msgid "HTTP Response: %1$s %2$s %3$s from %4$s"
msgstr "HTTP yanıtı: %1$s %2$s %3$s %4$s"

#: modules/recaptcha/service.php:397 modules/stripe/service.php:294
#: modules/turnstile/service.php:376
msgid "Save Changes"
msgstr "Değişiklikleri kaydet"

#: modules/recaptcha/service.php:289
msgid "reCAPTCHA protects you against spam and other types of automated abuse. With Contact Form 7&#8217;s reCAPTCHA integration module, you can block abusive form submissions by spam bots."
msgstr "reCAPTCHA, spam ve diğer otomatik kötüye kullanımlara karşı sizi korur. Contact Form 7 nin reCAPTCHA entegrasyon modülü sayesinde spam vb. botları formunuzdan korumuş olursunuz."

#: modules/recaptcha/recaptcha.php:255 modules/recaptcha/service.php:300
msgid "reCAPTCHA (v3)"
msgstr "reCAPTCHA V3"

#. translators: %s: link labeled 'reCAPTCHA (v3)'
#: modules/recaptcha/recaptcha.php:252
msgid "API keys for reCAPTCHA v3 are different from those for v2; keys for v2 do not work with the v3 API. You need to register your sites again to get new keys for v3. For details, see %s."
msgstr "ReCAPTCHA v3 için API anahtarları, v2 için olanlardan farklıdır; v2 için olan anahtarlar v3 API ile çalışmaz. v3 için yeni anahtarlar almak için sitelerinizi tekrar kaydetmeniz gerekmektedir. Detaylar için %s adresine bakabilirsiniz."

#: modules/constant-contact/constant-contact.php:206
#: modules/recaptcha/service.php:392 modules/stripe/service.php:290
#: modules/turnstile/service.php:372
msgctxt "API keys"
msgid "Remove Keys"
msgstr "Anahtarları kaldır"

#: modules/constant-contact/constant-contact.php:191
msgid "App Secret"
msgstr "App güvenlik kodu"

#: modules/constant-contact/constant-contact.php:169
msgid "API Key"
msgstr "API anahtarı"

#: modules/constant-contact/constant-contact.php:56
msgid "Constant Contact"
msgstr "Constant Contact"

#: includes/integration.php:26
msgid "Email marketing"
msgstr "E-posta pazarlama"

#: includes/config-validator/mail.php:423
msgid "It is not allowed to use files outside the wp-content directory."
msgstr "Wp-content dizini dışındaki dosyaları kullanmak izin verilmez."

#: admin/edit-contact-form.php:392
msgid "Professional services"
msgstr "Profesyonel hizmetler"

#: admin/edit-contact-form.php:391
msgid "https://contactform7.com/custom-development/"
msgstr "https://contactform7.com/custom-development/"

#: admin/edit-contact-form.php:383
msgid "Support forums"
msgstr "Destek forumları"

#: admin/edit-contact-form.php:382
msgid "https://wordpress.org/support/plugin/contact-form-7/"
msgstr "https://wordpress.org/support/plugin/contact-form-7/"

#: admin/edit-contact-form.php:354
msgid "Here are some available options to help solve your problems."
msgstr "Burada sorunlarınızı çözmeye yardımcı olabilecek bazı seçenekler mevcut."

#: admin/edit-contact-form.php:342
msgid "Do you need help?"
msgstr "Yardıma ihtiyacın var mı?"

#: modules/acceptance.php:320
msgid "Condition"
msgstr "Koşul"

#. translators: 1: 'Consented' or 'Not consented', 2: conditions
#: modules/acceptance.php:240
msgctxt "mail output for acceptance checkboxes"
msgid "%1$s: %2$s"
msgstr "%1$s: %2$s"

#: modules/acceptance.php:224
msgid "Not consented"
msgstr "Kabul edilmedi"

#: modules/acceptance.php:222
msgid "Consented"
msgstr "Kabul edildi"

#: includes/submission.php:120
msgid "Sending mail has been aborted."
msgstr "E-posta gönderimi iptal edildi."

#. translators: %s: link labeled 'Editing messages'
#: admin/includes/editor.php:460
msgid "You can edit messages used in various situations here. For details, see %s."
msgstr "Burada çeşitli durumlarda kullanılan mesajları düzenleyebilirsiniz. Ayrıntılar için %s bölümüne bakın."

#: admin/includes/editor.php:463
msgid "Editing messages"
msgstr "Mesajları düzenleme"

#: admin/includes/editor.php:462
msgid "https://contactform7.com/editing-messages/"
msgstr "https://contactform7.com/editing-messages/"

#. translators: %s: link labeled 'Setting up mail'
#: admin/includes/editor.php:205
msgid "You can edit the mail template here. For details, see %s."
msgstr "Posta şablonunu buradan düzenleyebilirsiniz. Ayrıntılar için %s bölümüne bakın."

#. translators: %s: link labeled 'Editing form template'
#: admin/includes/editor.php:81
msgid "You can edit the form template here. For details, see %s."
msgstr "Form şablonunu buradan düzenleyebilirsiniz. Ayrıntılar için %s bölümüne bakın."

#: admin/includes/editor.php:84
msgid "Editing form template"
msgstr "Form şablonunu düzenleme"

#: admin/includes/editor.php:83
msgid "https://contactform7.com/editing-form-template/"
msgstr "https://contactform7.com/editing-form-template/"

#: includes/contact-form.php:546 includes/contact-form.php:1069
msgid "This contact form is available only for logged in users."
msgstr "Bu iletişim formu yalnızca oturum açmış kullanıcılar tarafından kullanılabilir."

#: includes/config-validator/additional-settings.php:19
msgid "Deprecated settings are used."
msgstr "Kullanım dışı olan ayarlar kullanıldı."

#. translators: %s: link labeled 'Really Simple CAPTCHA'
#: modules/really-simple-captcha.php:36
msgid "To use CAPTCHA, you need %s plugin installed."
msgstr "CAPTCHA kullanabilmeniz için %s eklentisi yüklü olmalıdır."

#: includes/rest-api.php:323
msgid "There was an error deleting the contact form."
msgstr "İletişim formu silinirken bir hata oluştu."

#: includes/rest-api.php:66 includes/rest-api.php:82 includes/rest-api.php:98
msgid "You are not allowed to access the requested contact form."
msgstr "Talep edilen iletişim formuna erişmenize izin verilmiyor."

#: includes/rest-api.php:245 includes/rest-api.php:267
#: includes/rest-api.php:314 includes/rest-api.php:353
#: includes/rest-api.php:425 includes/rest-api.php:444
msgid "The requested contact form was not found."
msgstr "Talep edilen iletişim formu bulunamadı."

#: includes/rest-api.php:44
msgid "You are not allowed to create a contact form."
msgstr "İletişim formu oluşturmanıza izin verilmiyor."

#: includes/rest-api.php:197
msgid "Cannot create existing contact form."
msgstr "Mevcut iletişim formu tekrar oluşturulamaz."

#: includes/rest-api.php:30
msgid "You are not allowed to access contact forms."
msgstr "İletişim formlarına erişmenize izin verilmiyor."

#: includes/config-validator/mail.php:409
msgid "Attachment file does not exist at %path%."
msgstr "Ek dosya %path% konumunda mevcut değil."

#: includes/config-validator/mail.php:310
msgid "Invalid mailbox syntax is used in the %name% field."
msgstr "%name% alanında geçersiz e-posta kutusu söz dizimi kullanıldı."

#. translators: %names%: a list of form control names
#: includes/config-validator/form.php:32
msgid "Unavailable names (%names%) are used for form controls."
msgstr "Kullanılamayan isimler (%names%) form denetimleri için kullanıldı."

#: includes/config-validator/mail.php:297
msgid "There are invalid mail header fields."
msgstr "Geçersiz posta başlığı alanları var."

#: includes/config-validator/messages.php:29
msgid "HTML tags are used in a message."
msgstr "Mesaj içerisinde HTML etiketleri kullanıldı."

#: includes/config-validator/mail.php:198
msgid "Sender email address does not belong to the site domain."
msgstr "Gönderen e-posta adresi site alan adına ait değil."

#: includes/config-validator/mail.php:180
#: includes/config-validator/mail.php:217
msgid "Invalid mailbox syntax is used."
msgstr "Geçersiz posta kutusu kullanıldı."

#: includes/config-validator/mail.php:160
#: includes/config-validator/mail.php:344
msgid "There is a possible empty field."
msgstr "Olası bir boş alan var."

#. translators: %s: number of errors detected
#: admin/includes/class-contact-forms-list-table.php:131
msgid "%s configuration error detected"
msgid_plural "%s configuration errors detected"
msgstr[0] "%s yapılandırma hatası tespit edildi"
msgstr[1] "%s yapılandırma hatası tespit edildi"

#: admin/admin.php:646 includes/rest-api.php:209 includes/rest-api.php:278
msgid "There was an error saving the contact form."
msgstr "İletişim formu kaydedilirken hata oluştu."

#. translators: 1: WordPress hook name, 2: version number, 3: alternative hook
#. name
#: includes/functions.php:525
msgid "Hook %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s! Use %3$s instead."
msgstr "%1$s kancası Contact Form 7 sürüm %2$s'den beri <strong>kullanılmıyor</strong>! Bunun yerine %3$s kullanın."

#: admin/includes/welcome-panel.php:130
msgid "Flamingo"
msgstr "Flamingo"

#. translators: %s: link labeled 'Flamingo'
#: admin/includes/welcome-panel.php:127
msgid "Install a message storage plugin before this happens to you. %s saves all messages through contact forms into the database. Flamingo is a free WordPress plugin created by the same author as Contact Form 7."
msgstr "Bu size gerçekleşmeden önce bir mesaj depolama eklentisi yükleyin. %s tüm mesajları iletişim formları üzerinden veritabanına kaydeder. Flamingo, Contact Form 7 ile aynı yazar tarafından oluşturulan ücretsiz bir WordPress eklentisidir."

#: admin/includes/welcome-panel.php:124
msgid "Contact Form 7 does not store submitted messages anywhere. Therefore, you may lose important messages forever if your mail server has issues or you make a mistake in mail configuration."
msgstr "Contact Form 7 gönderilen mesajları hiçbir yerde saklamaz. Bu nedenle, posta sunucunuzda sorun varsa veya posta yapılandırmasında bir hata yaparsanız önemli mesajları sonsuza kadar kaybedebilirsiniz.ı"

#: admin/includes/welcome-panel.php:118
msgid "Before you cry over spilt mail&#8230;"
msgstr "E-postalar için boş yere ağlamadan önce"

#: admin/includes/welcome-panel.php:73
msgid "https://contactform7.com/comment-blacklist/"
msgstr "https://contactform7.com/comment-blacklist/"

#: admin/includes/welcome-panel.php:60
msgid "Spammers target everything; your contact forms are not an exception. Before you get spammed, protect your contact forms with the powerful anti-spam features Contact Form 7 provides."
msgstr "Spam gönderenler her şeyi hedef alır; iletişim formlarınız da bir istisna değildir. Spam yemeden önce, Contact Form 7'nin sağladığı güçlü anti-spam özellikleriyle iletişim formlarınızı koruyun."

#: admin/includes/welcome-panel.php:54
msgid "Getting spammed? You have protection."
msgstr "İstenmeyen e-posta mı alıyorsunuz? Korumanız var."

#: includes/config-validator/form.php:16
msgid "Multiple form controls are in a single label element."
msgstr "Birden fazla form kontrolü tek bir etiket elemanının içine yerleştirildi."

#: admin/includes/config-validator.php:175
msgid "FAQ about Configuration Validator"
msgstr "Yapılandırma doğrulayıcı hakkında SSS"

#: admin/includes/config-validator.php:174
msgid "https://contactform7.com/configuration-validator-faq/"
msgstr "https://contactform7.com/configuration-validator-faq/"

#: modules/quiz.php:181
msgid "The answer to the quiz is incorrect."
msgstr "Soruya verilen cevap doğru değil."

#: includes/file.php:121
msgid "There was an error uploading the file."
msgstr "Dosya yüklenirken hata meydana geldi."

#: includes/file.php:111
msgid "You are not allowed to upload files of this type."
msgstr "Bu dosya türünü yükleme iznine sahip değilsiniz."

#: includes/file.php:106
msgid "There was an unknown error uploading the file."
msgstr "Dosya yüklenirken bilinmeyen bir hata oluştu."

#: includes/contact-form-template.php:184
msgid "You must accept the terms and conditions before sending your message."
msgstr "Mesajınızı göndermeden önce şartları ve koşulları kabul etmeniz gerekmektedir."

#: includes/contact-form-template.php:174
msgid "One or more fields have an error. Please check and try again."
msgstr "Bir veya daha fazla alanda hata bulundu. Lütfen kontrol edin ve tekrar deneyin."

#: includes/contact-form-template.php:169
#: includes/contact-form-template.php:179
msgid "There was an error trying to send your message. Please try again later."
msgstr "Mesajınız gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin."

#: includes/contact-form-template.php:164
msgid "Thank you for your message. It has been sent."
msgstr "Mesajınız için teşekkürler. Gönderildi."

#. translators: 1: blog name, 2: [your-subject]
#: includes/contact-form-template.php:52 includes/contact-form-template.php:96
msgctxt "mail subject"
msgid "%1$s \"%2$s\""
msgstr "%1$s \"%2$s\""

#: admin/includes/config-validator.php:47
msgid "Misconfiguration leads to mail delivery failure or other troubles. Validate your contact forms now."
msgstr "Yapılandırma hataları e-posta teslimatı ve diğer problemlere sebebiyet verebilir. İletişim formlarınızı şimdi doğrulayın."

#: admin/includes/config-validator.php:53
msgid "Validate Contact Form 7 Configuration"
msgstr "Contact Form 7 yapılandırmasını doğrulayın"

#: includes/config-validator/validator.php:56
msgid "https://contactform7.com/configuration-errors/"
msgstr "https://contactform7.com/configuration-errors/"

#: admin/admin.php:664
msgid "Configuration validation completed. No invalid contact form was found."
msgstr "Yapılandırma doğrulanması tamamlandı. Geçersiz hiçbir contact form bulunamadı."

#. translators: %s: number of contact forms
#: admin/admin.php:656
msgid "Configuration validation completed. %s invalid contact form was found."
msgid_plural "Configuration validation completed. %s invalid contact forms were found."
msgstr[0] "Yapılandırma doğrulaması tamamlandı. %s geçersiz iletişim formu bulundu."
msgstr[1] "Yapılandırma doğrulaması tamamlandı. %s geçersiz iletişim formu bulundu."

#: admin/includes/config-validator.php:142
msgid "Validate Configuration"
msgstr "Yapılandırmayı doğrula"

#. translators: %s: number of contact forms
#: admin/includes/config-validator.php:118
msgid "Validate %s contact form now"
msgid_plural "Validate %s contact forms now"
msgstr[0] "%s iletişim formunu şimdi doğrulayın"
msgstr[1] "%s iletişim formunu şimdi doğrulayın"

#: admin/includes/config-validator.php:75
msgid "You are not allowed to validate configuration."
msgstr "Yapılandırmayı doğrulamaya izniniz yok."

#: modules/recaptcha/recaptcha.php:254 modules/recaptcha/service.php:299
msgid "https://contactform7.com/recaptcha/"
msgstr "https://contactform7.com/recaptcha/"

#: modules/recaptcha/service.php:368 modules/stripe/service.php:269
#: modules/turnstile/service.php:351
msgid "Secret Key"
msgstr "Gizli anahtar"

#: modules/recaptcha/service.php:351 modules/turnstile/service.php:334
msgid "Site Key"
msgstr "Site anahtarı"

#: modules/recaptcha/service.php:269 modules/sendinblue/service.php:147
#: modules/stripe/service.php:153 modules/turnstile/service.php:246
msgid "Settings saved."
msgstr "Ayarlar kaydedildi."

#: modules/sendinblue/service.php:139
msgid "Invalid key values."
msgstr "Anahtar değeri geçersiz."

#: modules/recaptcha/service.php:29
msgid "reCAPTCHA"
msgstr "reCAPTCHA"

#. Author URI of the plugin
#: wp-contact-form-7.php
msgid "https://ideasilo.wordpress.com/"
msgstr "https://ideasilo.wordpress.com/"

#. Author of the plugin
#: wp-contact-form-7.php
msgid "Takayuki Miyoshi"
msgstr "Takayuki Miyoshi"

#. Description of the plugin
#: wp-contact-form-7.php
msgid "Just another contact form plugin. Simple but flexible."
msgstr "Sıradan bir iletişim formu eklentisi. Basit ama esnek."

#. Plugin URI of the plugin
#: wp-contact-form-7.php
msgid "https://contactform7.com/"
msgstr "https://contactform7.com/"

#. translators: title of your first contact form. %d: number fixed to '1'
#: load.php:195
msgid "Contact form %d"
msgstr "İletişim formu %d"

#: modules/textarea.php:143
msgid "text area"
msgstr "çoklu metin alanı"

#: admin/includes/welcome-panel.php:66 modules/akismet/service.php:22
msgid "Akismet"
msgstr "Akismet"

#: modules/text.php:216
msgid "tel"
msgstr "tel"

#: modules/text.php:215
msgid "URL"
msgstr "URL"

#: modules/text.php:214
msgid "email"
msgstr "eposta"

#: modules/text.php:213
msgid "text"
msgstr "metin"

#: modules/text.php:195
msgid "Telephone number that the sender entered is invalid"
msgstr "Gönderenin girdiği telefon numarası geçersiz"

#: modules/text.php:188
msgid "URL that the sender entered is invalid"
msgstr "Gönderenin girdiği URL geçersiz"

#: modules/text.php:181
msgid "Email address that the sender entered is invalid"
msgstr "Gönderenin girdiği eposta adresi geçersiz"

#: modules/submit.php:100
msgid "Label"
msgstr "Etiket"

#: modules/submit.php:56
msgid "Submit button"
msgstr "Gönder düğmesi"

#: modules/submit.php:47
msgid "submit"
msgstr "gönder"

#: modules/select.php:232
msgid "drop-down menu"
msgstr "aşağı açılır menü"

#: modules/quiz.php:258
msgid "Questions and answers"
msgstr "Sorular ve yanıtlar"

#: modules/quiz.php:205
msgid "Quiz"
msgstr "Soru"

#: modules/quiz.php:196
msgid "quiz"
msgstr "küçük soru"

#: modules/quiz.php:179
msgid "Sender does not enter the correct answer to the quiz"
msgstr "Gönderici teste doğru cevabı girmiyor"

#: modules/number.php:252
msgid "Slider"
msgstr "Kaydırıcı"

#: modules/number.php:251
msgid "Spinbox"
msgstr "Döndürme kutusu"

#: modules/number.php:204
msgid "number"
msgstr "sayı"

#: modules/number.php:190
msgid "Number is larger than maximum limit"
msgstr "Sayı, en fazla sınırdan daha büyük"

#: modules/number.php:185
msgid "Number is smaller than minimum limit"
msgstr "Sayı, en az sınırdan daha küçük"

#: modules/number.php:180
msgid "Number format that the sender entered is invalid"
msgstr "Gönderenin girdiği sayı biçimi geçersiz"

#. translators: %s: the path of the temporary folder
#: includes/file.php:434
msgid "This contact form has file uploading fields, but the temporary folder for the files (%s) does not exist or is not writable. You can create the folder or change its permission manually."
msgstr "Bu iletişim formunda dosya yükleme alanları var, ancak dosyalar için geçici klasör (%s) mevcut değil veya yazılabilir değil. Klasörü oluşturabilir veya iznini manuel olarak değiştirebilirsiniz."

#: modules/file.php:205
msgid "Acceptable file types"
msgstr "Kabul edilebilir dosya türleri"

#: modules/file.php:142
msgid "file"
msgstr "dosya"

#: includes/file.php:120
msgid "Uploading a file fails for PHP error"
msgstr "Dosyayı gönderme PHP hatasından dolayı başarısız"

#: includes/file.php:115
msgid "Uploaded file is too large"
msgstr "Gönderilen dosya çok büyük"

#: includes/file.php:110
msgid "Uploaded file is not allowed for file type"
msgstr "Yüklenen dosya izin verilmeyen dosya biçimi içeriyor"

#: includes/file.php:105
msgid "Uploading a file fails for any reason"
msgstr "Dosyayı gönderme herhangi bir nedenden başarısız"

#: admin/includes/tag-generator.php:412
msgid "Max"
msgstr "En fazla"

#: admin/includes/tag-generator.php:393
msgid "Min"
msgstr "En az"

#: modules/date.php:242 modules/number.php:261
msgid "Range"
msgstr "Aralık"

#: admin/includes/tag-generator.php:436
msgid "Default value"
msgstr "Varsayılan değer"

#: modules/date.php:194
msgid "Date field"
msgstr "Tarih alanı"

#: modules/date.php:185
msgid "date"
msgstr "tarih"

#: modules/date.php:171
msgid "Date is later than maximum limit"
msgstr "Tarih, en fazla sınırdan daha geç"

#: modules/date.php:166
msgid "Date is earlier than minimum limit"
msgstr "Tarih, en az sınırdan daha erken"

#: modules/date.php:161
msgid "Date format that the sender entered is invalid"
msgstr "Gönderenin girdiği tarih biçimi geçersiz"

#: admin/includes/tag-generator.php:219
msgid "Field type"
msgstr "Alan türü"

#: modules/checkbox.php:352
msgid "radio buttons"
msgstr "radyo düğmeleri"

#: modules/checkbox.php:351
msgid "checkboxes"
msgstr "onay kutuları"

#: modules/really-simple-captcha.php:295
msgid "This contact form contains CAPTCHA fields, but the necessary libraries (GD and FreeType) are not available on your server."
msgstr "Bu iletişim formu CAPTCHA alanları içeriyor ama gerekli kütüphaneler (GD ve FreeType) sunucunuzda mevcut değil."

#. translators: %s: Path to the temporary folder
#: modules/really-simple-captcha.php:283
msgid "This contact form contains CAPTCHA fields, but the temporary folder for the files (%s) does not exist or is not writable. You can create the folder or change its permission manually."
msgstr "Bu iletişim formu CAPTCHA alanları içeriyor ama dosyalar (%s) için geçici klasör mevcut değil ya da yazılabilir değil. Klasörü oluşturabilir veya iznini el ile değiştirebilirsiniz."

#: modules/really-simple-captcha.php:241
msgid "Your entered code is incorrect."
msgstr "Girdiğiniz kod doğru değil."

#: modules/really-simple-captcha.php:239
msgid "The code that sender entered does not match the CAPTCHA"
msgstr "Gönderenin girdiği kod CAPTCHA (Şahıs Denetim Kodu) ile eşleşmiyor"

#: admin/includes/tag-generator.php:599
msgid "Insert Tag"
msgstr "Etiket ekle"

#: admin/includes/tag-generator.php:362
msgid "Class attribute"
msgstr "Sınıf özniteliği"

#: modules/acceptance.php:266
msgid "Acceptance checkbox"
msgstr "Kabul onay kutusu"

#: modules/acceptance.php:257
msgid "acceptance"
msgstr "kabul"

#. translators: 1: property name, 2: method name
#: includes/contact-form.php:233
msgid "<code>%1$s</code> property of a <code>WPCF7_ContactForm</code> object is <strong>no longer accessible</strong>. Use <code>%2$s</code> method instead."
msgstr "<code>WPCF7_ContactForm</code> nesnesinin <code>%1$s</code> özelliği <strong>artık erişilebilir değil</strong>. Bunun yerine <code>%2$s</code> yöntemini kullanın."

#: includes/contact-form.php:114 includes/contact-form.php:437
msgid "Untitled"
msgstr "Başlıksız"

#: includes/contact-form.php:52
msgid "Contact Form"
msgstr "İletişim formu"

#: includes/contact-form-template.php:198
msgid "There is a field with input that is shorter than the minimum allowed length"
msgstr "Kullanıcı girdisinin en az izin verildiği uzunluktan daha kısa bir alan var"

#: includes/contact-form-template.php:193
msgid "There is a field with input that is longer than the maximum allowed length"
msgstr "Kullanıcı girdisinin en fazla izin verildiği uzunluktan daha uzun bir alan var"

#: includes/contact-form-template.php:188
msgid "There is a field that the sender must fill in"
msgstr "Gönderenin doldurmak zorunda olduğu bir alan var"

#: includes/contact-form-template.php:183
msgid "There are terms that the sender must accept"
msgstr "Gönderenin kabul etmek zorunda olduğu koşullar var"

#: includes/contact-form-template.php:178
msgid "Submission was referred to as spam"
msgstr "Sunuş istenmeyen ileti olarak gönderildi"

#: includes/contact-form-template.php:173
msgid "Validation errors occurred"
msgstr "Onaylama hataları meydana geldi"

#: includes/contact-form-template.php:72 includes/contact-form-template.php:106
msgid "Message Body:"
msgstr "İleti gövdesi:"

#. translators: %s: [your-subject]
#: includes/contact-form-template.php:69
msgid "Subject: %s"
msgstr "Konu: %s"

#. translators: %s: [your-name] [your-email]
#: includes/contact-form-template.php:64
msgid "From: %s"
msgstr "Kimden: %s"

#: modules/submit.php:26
msgid "Send"
msgstr "Gönder"

#: includes/contact-form-template.php:41
msgid "Your message"
msgstr "İletiniz"

#: includes/contact-form-template.php:39
msgid "Your email"
msgstr "E-posta adresiniz"

#: includes/contact-form-template.php:38
msgid "Your name"
msgstr "Adınız"

#. translators: %s: title of form-tag
#: admin/includes/tag-generator.php:85
msgid "Form-tag Generator: %s"
msgstr "Form etiket üretici: %s"

#: admin/includes/help-tabs.php:97
msgid "For more information:"
msgstr "Daha fazla bilgi için:"

#: admin/includes/help-tabs.php:89
msgid "Any information you provide will not be shared with service providers without your authorization."
msgstr "Verdiğiniz herhangi bir bilgi, izniniz olmadan hizmet sağlayıcıları ile paylaşılmayacaktır."

#: admin/includes/help-tabs.php:88
msgid "You may need to first sign up for an account with the service that you plan to use. When you do so, you would need to authorize Contact Form 7 to access the service with your account."
msgstr "Kullanmayı planladığınız hizmet ile bir hesaba önce kaydolmanız gerekebilir. Bunu yaptığınızda, hesabınızla hizmete erişmek için Contact Form 7'ye yetki vermeniz gerekir."

#: admin/includes/help-tabs.php:87
msgid "On this screen, you can manage services that are available through Contact Form 7. Using API will allow you to collaborate with any services that are available."
msgstr "Bu ekranda, Contact Form 7 aracılığıyla kullanılabilir hizmetleri yönetebilirsiniz. API kullanmak, kullanılabilir herhangi bir hizmet ile işbirliği yapmayı sağlayacak."

#: admin/includes/help-tabs.php:85
msgid "There are also special mail-tags that have specific names, but do not have corresponding form-tags. They are used to represent meta information of form submissions like the submitter&#8217;s IP address or the URL of the page."
msgstr "Ayrıca, belirli adlara sahip olan ancak karşılık gelen form etiketleri olmayan özel posta etiketleri de vardır. Bunlar, form gönderimlerinin gönderenin IP adresi veya sayfanın URL'si gibi meta bilgilerini temsil etmek için kullanılır."

#: admin/includes/help-tabs.php:84
msgid "A mail-tag is also a short code enclosed in square brackets that you can use in every Mail and Mail (2) field. A mail-tag represents a user input value through an input field of a corresponding form-tag."
msgstr "Posta-etiketi aynı zamanda her Posta ve Posta (2) alanında kullanabileceğiniz köşeli parantezlerle kapatılan bir kısa koddur. Bir posta-etiketi bir form-etiketine karşılık gelen bir girdi alanından yapılan bir kullanıcı girdi değerini temsil eder."

#: admin/includes/help-tabs.php:82
msgid "While form-tags have a comparatively complex syntax, you do not need to know the syntax to add form-tags because you can use the straightforward tag generator (<strong>Generate Tag</strong> button on this screen)."
msgstr "Form etiketleri nispeten karmaşık bir sözdizimine sahip olsa da, form etiketleri eklemek için sözdizimini bilmeniz gerekmez, çünkü basit etiket oluşturucuyu kullanabilirsiniz (bu ekrandaki <strong>Etiket Oluştur</strong> düğmesi)."

#: admin/includes/help-tabs.php:81
msgid "A form-tag is a short code enclosed in square brackets used in a form content. A form-tag generally represents an input field, and its components can be separated into four parts: type, name, options, and values. Contact Form 7 supports several types of form-tags including text fields, number fields, date fields, checkboxes, radio buttons, menus, file-uploading fields, CAPTCHAs, and quiz fields."
msgstr "Form-etiketi, bir form içeriğinde kullanılan köşeli parantezlerle kapatılan bir kısa koddur. Form-etiketi genelde bir girdi alanını temsil eder ve bileşenleri dört parçaya ayrılabilir: tür, adı, seçenekler ve değerler. Contact Form 7; metin alanlarının, sayı alanlarının, tarih alanlarının, onay kutularının, radyo düğmelerinin, menülerin, dosya gönderme alanlarının, CAPTCHA'ların ve küçük soru alanlarının birkaç türünü destekler."

#: admin/includes/help-tabs.php:79
msgid "<strong>Additional Settings</strong> provides a place where you can customize the behavior of this contact form by adding code snippets."
msgstr "<strong>Ek Ayarlar</strong>, kod parçacıkları ekleyerek bu iletişim formu davranışını özelleştirebildiğiniz bir yer sağlar."

#: admin/includes/help-tabs.php:78
msgid "In <strong>Messages</strong>, you can edit various types of messages used for this contact form. These messages are relatively short messages, like a validation error message you see when you leave a required field blank."
msgstr "<strong>İletiler</strong> içinde, bu iletişim formu için kullanılan iletilerin çeşitli türlerini düzenleyebilirsiniz. Bu iletiler, gerekli bir alanı boş bıraktığınızda gördüğünüz doğrulama hata iletisi gibi nispeten kısa iletilerdir."

#: admin/includes/help-tabs.php:77
msgid "<strong>Mail (2)</strong> is an additional mail template that works similar to Mail. Mail (2) is different in that it is sent only when Mail has been sent successfully."
msgstr "<strong>Posta (2)</strong>, Posta ile benzer çalışan ilave bir posta şablonudur. Mail (2) tek farkı sadece Posta başarılı olarak gönderildiğinde gönderilir."

#: admin/includes/help-tabs.php:76
msgid "<strong>Mail</strong> manages a mail template (headers and message body) that this contact form will send when users submit it. You can use Contact Form 7&#8217;s mail-tags here."
msgstr "<strong>Posta</strong>, kullanıcılar bunu gönderdiğinde bu iletişim formunun gönderileceği bir posta şablonunu (üstbilgiler ve ilet gövdesi) yönetir. Contact Form 7&#8217;nin posta-etiketlerini burada kullanabilirsiniz."

#: admin/includes/help-tabs.php:75
msgid "<strong>Form</strong> is a content of HTML form. You can use arbitrary HTML, which is allowed inside a form element. You can also use Contact Form 7&#8217;s form-tags here."
msgstr "<strong>Form</strong>, HTML formunun içeriğidir. Bir öğe içinde izin verilen, seçmeli HTML kullanabilirsiniz. Aynı zamanda Contact Form 7&#8217;nin form-etiketlerini burada kullanabilirsiniz."

#: admin/includes/help-tabs.php:74
msgid "<strong>Title</strong> is the title of a contact form. This title is only used for labeling a contact form, and can be edited."
msgstr "<strong>Başlık</strong>, iletişim formunun başlığıdır. Bu başlık sadece bir iletişim formunu etiketlemek için kullanılır ve düzenlenebilir."

#: admin/includes/help-tabs.php:73
msgid "On this screen, you can edit a contact form. A contact form is comprised of the following components:"
msgstr "Bu ekranda, bir iletişim formunu düzenleyebilirsiniz. İletişim formu aşağıdaki bileşenlerden oluşur:"

#: admin/includes/help-tabs.php:71
msgid "<strong>Duplicate</strong> - Clones that contact form. A cloned contact form inherits all content from the original, but has a different ID."
msgstr "<strong>Çoğalt</strong> - Bu iletişim formunu çoğaltır. Çoğaltılmış bir iletişim formu tüm içeriği orijinalinden devralır ancak farklı bir KİMLİĞE sahiptir."

#: admin/includes/help-tabs.php:70
msgid "<strong>Edit</strong> - Navigates to the editing screen for that contact form. You can also reach that screen by clicking on the contact form title."
msgstr "<strong>Düzenle</strong> - Bu iletişim formu için düzenleme ekranına yönlendirir. Aynı zamanda iletişim formu başlığına tıklayarak bu ekrana ulaşabilirsiniz."

#: admin/includes/help-tabs.php:69
msgid "Hovering over a row in the contact forms list will display action links that allow you to manage your contact form. You can perform the following actions:"
msgstr "İletişim formları listesindeki bir satır üzerinde fare imlecini tutmak iletişim formunuzu yönetmenize izin veren eylem bağlantılarını görüntüleyecek. Aşağıdaki eylemleri yapabilirsiniz:"

#: admin/includes/help-tabs.php:67
msgid "On this screen, you can manage contact forms provided by Contact Form 7. You can manage an unlimited number of contact forms. Each contact form has a unique ID and Contact Form 7 shortcode ([contact-form-7 ...]). To insert a contact form into a post or a text widget, insert the shortcode into the target."
msgstr "Bu ekranda, Contact Form 7 tarafından verilen iletişim formlarını yönetebilirsiniz. Sınırsız sayıda iletişim formlarını yönetebilirsiniz. Her iletişim formu benzersiz bir KİMLİĞE ve Contact Form 7 kısa koduna  ([contact-form-7 ...]) sahiptir. Bir iletişim formunu yazı ya da bir metin parçacığı içine eklemek için hedef içine kısa kodu ekleyin."

#: admin/includes/help-tabs.php:44
msgid "Mail-tags"
msgstr "Posta etiketleri"

#: admin/includes/help-tabs.php:38
msgid "Form-tags"
msgstr "Form etiketleri"

#: admin/includes/help-tabs.php:22
msgid "Available Actions"
msgstr "Kullanılabilir eylemler"

#: admin/includes/help-tabs.php:16 admin/includes/help-tabs.php:32
#: admin/includes/help-tabs.php:54
msgid "Overview"
msgstr "Genel bakış"

#. translators: %s: link labeled 'Additional settings'
#: admin/includes/editor.php:524
msgid "You can add customization code snippets here. For details, see %s."
msgstr "Özelleştirme kod parçacıklarını burada ekleyebilirsiniz. Ayrıntılar için bkz: %s."

#: admin/includes/editor.php:526
msgid "https://contactform7.com/additional-settings/"
msgstr "https://contactform7.com/additional-settings/"

#: admin/includes/editor.php:433
msgid "File attachments"
msgstr "Dosya ekleri"

#: admin/includes/editor.php:417
msgid "Use HTML content type"
msgstr "HTML içerik türünü kullan"

#: admin/includes/editor.php:397
msgid "Exclude lines with blank mail-tags from output"
msgstr "Çıktıda boş posta etiketleri olan satırları hariç tut"

#: admin/includes/editor.php:360
msgid "Message body"
msgstr "İleti gövdesi"

#: admin/includes/editor.php:329
msgid "Additional headers"
msgstr "Ek başlıklar"

#: admin/includes/editor.php:301 includes/contact-form-template.php:40
msgid "Subject"
msgstr "Konu"

#: admin/includes/editor.php:273
msgid "From"
msgstr "Kimden"

#: admin/includes/editor.php:245
msgid "To"
msgstr "Kime"

#: admin/includes/editor.php:217
msgid "In the following fields, you can use these mail-tags:"
msgstr "Aşağıdaki alanlarda, bu posta etiketlerini kullanabilirsiniz:"

#: admin/includes/editor.php:191
msgid "Mail (2) is an additional mail template often used as an autoresponder."
msgstr "Posta (2) bir otomatik yanıtlayıcı olarak kullanılan bir ek posta şablonudur."

#: admin/includes/editor.php:130
msgid "Use Mail (2)"
msgstr "Posta (2) kullan"

#: admin/includes/editor.php:129
msgid "Mail (2)"
msgstr "Posta (2)"

#. translators: date format, see https://www.php.net/date
#: admin/includes/class-contact-forms-list-table.php:229
msgid "Y/m/d"
msgstr "d.m.Y"

#. translators: %s: title of contact form
#: admin/includes/class-contact-forms-list-table.php:115
msgid "Edit &#8220;%s&#8221;"
msgstr "&#8220;%s&#8221; Düzenle"

#: admin/includes/class-contact-forms-list-table.php:162
msgid "Edit"
msgstr "Düzenle"

#: admin/includes/class-contact-forms-list-table.php:15
msgid "Date"
msgstr "Tarih"

#: admin/includes/class-contact-forms-list-table.php:14
msgid "Author"
msgstr "Yazar"

#: admin/includes/class-contact-forms-list-table.php:13
msgid "Shortcode"
msgstr "Kısa kod"

#: admin/includes/class-contact-forms-list-table.php:12
#: includes/block-editor/index.js:1
msgid "Title"
msgstr "Başlık"

#: admin/edit-contact-form.php:449 admin/includes/editor.php:536
msgid "Additional Settings"
msgstr "Ek Ayarlar"

#. translators: %d: number of additional settings
#: admin/edit-contact-form.php:446
msgid "Additional Settings (%d)"
msgstr "Ek Ayarlar (%d)"

#: admin/edit-contact-form.php:426 admin/includes/editor.php:481
msgid "Messages"
msgstr "Mesajlar"

#: admin/edit-contact-form.php:422 admin/includes/editor.php:138
msgid "Mail"
msgstr "Posta"

#: admin/edit-contact-form.php:418 admin/includes/editor.php:91
msgid "Form"
msgstr "Form"

#: admin/includes/help-tabs.php:100
msgid "Support"
msgstr "Destek"

#: admin/includes/help-tabs.php:100
msgid "https://contactform7.com/support/"
msgstr "https://contactform7.com/support/"

#: admin/edit-contact-form.php:369 admin/includes/help-tabs.php:99
msgid "FAQ"
msgstr "SSS"

#: admin/edit-contact-form.php:368 admin/includes/help-tabs.php:99
msgid "https://contactform7.com/faq/"
msgstr "https://contactform7.com/faq/"

#: admin/includes/help-tabs.php:98
msgid "Docs"
msgstr "Belgeler"

#: admin/edit-contact-form.php:372 admin/includes/help-tabs.php:98
msgid "https://contactform7.com/docs/"
msgstr "https://contactform7.com/docs/"

#: admin/edit-contact-form.php:311
#: admin/includes/class-contact-forms-list-table.php:83
msgid "Delete"
msgstr "Sil"

#: admin/edit-contact-form.php:282
#: admin/includes/class-contact-forms-list-table.php:180
msgid "Duplicate"
msgstr "Çoğalt"

#: admin/edit-contact-form.php:246
msgid "Status"
msgstr "Durum"

#: admin/edit-contact-form.php:205
msgid "You can also use this old-style shortcode:"
msgstr "Ayrıca bu eski stil kısa kodu kullanabilirsiniz:"

#: admin/edit-contact-form.php:173
msgid "Copy this shortcode and paste it into your post, page, or text widget content:"
msgstr "Bu kısa kodu kopyalayın ve yazınızın, sayfanızın ya da metin parçacığı içeriğinizin içine yapıştırın:"

#: admin/edit-contact-form.php:152 admin/edit-contact-form.php:153
msgid "Enter title here"
msgstr "Başlığı buraya girin"

#: admin/edit-contact-form.php:14 admin/edit-contact-form.php:272
msgid "Save"
msgstr "Kaydet"

#: admin/admin.php:730
msgid "You are not allowed to edit this contact form."
msgstr "Bu iletişim formunu düzenlemenize izin verilmiyor."

#: admin/includes/welcome-panel.php:129
msgid "https://contactform7.com/save-submitted-messages-with-flamingo/"
msgstr "https://contactform7.com/save-submitted-messages-with-flamingo/"

#: modules/akismet/service.php:65
msgid "Spam filtering with Akismet"
msgstr "Akismet ile spam filtreleme."

#: admin/includes/welcome-panel.php:65 modules/akismet/service.php:64
msgid "https://contactform7.com/spam-filtering-with-akismet/"
msgstr "https://contactform7.com/spam-filtering-with-akismet/"

#: admin/includes/editor.php:208
msgid "Setting up mail"
msgstr "Posta kurulumu"

#: admin/includes/editor.php:207
msgid "https://contactform7.com/setting-up-mail/"
msgstr "https://contactform7.com/setting-up-mail/"

#: admin/includes/welcome-panel.php:90
msgid "Contact Form 7 needs your support."
msgstr "Contact Form 7nin desteğinize ihtiyacı var."

#: admin/includes/welcome-panel.php:218
msgid "Dismiss"
msgstr "Kapat"

#. translators: 1: version of Contact Form 7, 2: version of WordPress, 3: URL
#: admin/admin.php:708
msgid "<strong>Contact Form 7 %1$s requires WordPress %2$s or higher.</strong> Please <a href=\"%3$s\">update WordPress</a> first."
msgstr "<strong>Contact Form 7 %1$s, WordPress %2$s veya daha yükseğini gerektirir.</strong> Lütfen önce <a href=\"%3$s\">WordPress'i güncelleyin</a>."

#: admin/admin.php:690
msgid "Settings"
msgstr "Ayarlar"

#: admin/admin.php:643
msgid "Contact form deleted."
msgstr "İletişim formu silindi."

#: admin/admin.php:641
msgid "Contact form saved."
msgstr "İletişim formu kaydedildi."

#: admin/admin.php:639
msgid "Contact form created."
msgstr "İletişim formu oluşturuldu."

#: admin/admin.php:503
msgid "Search Contact Forms"
msgstr "İletişim formlarını ara"

#: admin/admin.php:349
msgid "Error in deleting."
msgstr "Silerken hata oldu."

#: admin/admin.php:343
msgid "You are not allowed to delete this item."
msgstr "Bu öğeyi silmenize izin verilmiyor."

#: admin/admin.php:245 admin/admin.php:298
msgid "You are not allowed to edit this item."
msgstr "Bu öğeyi düzenlemenize izin verilmiyor."

#: admin/admin.php:66
msgid "Integration"
msgstr "Bütünleştirme"

#: admin/admin.php:41 admin/admin.php:444 includes/contact-form.php:51
msgid "Contact Forms"
msgstr "İletişim formları"

#: admin/admin.php:40 admin/edit-contact-form.php:42
msgid "Edit Contact Form"
msgstr "İletişim formunu düzenle"

#: admin/admin.php:30
msgid "Contact"
msgstr "İletişim"

#. Plugin Name of the plugin
#: wp-contact-form-7.php admin/admin.php:29 modules/flamingo.php:219
msgid "Contact Form 7"
msgstr "Contact Form 7"