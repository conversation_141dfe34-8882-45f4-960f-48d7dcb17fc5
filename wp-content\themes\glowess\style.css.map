{"version": 3, "sourceRoot": "", "sources": ["assets/scss/style.scss", "assets/scss/base/_base.scss", "assets/scss/base/_utility.scss", "assets/scss/gb-block/_gb-block.scss", "assets/scss/gb-block/_header.scss", "assets/scss/gb-block/_mobile-header.scss", "assets/scss/gb-block/_footer.scss", "assets/scss/gb-block/_single-post.scss", "assets/scss/gb-block/_blog.scss", "assets/scss/woocommerce/_product-item.scss", "assets/scss/woocommerce/_wc-blocks.scss", "assets/scss/woocommerce/_cart.scss", "assets/scss/woocommerce/_yith.scss", "assets/scss/woocommerce/_mini-cart.scss", "assets/scss/woocommerce/_my-account.scss", "assets/scss/woocommerce/_checkout.scss", "assets/scss/woocommerce/_product-filter.scss", "assets/scss/woocommerce/_single-product.scss", "assets/scss/woocommerce/_order-completed.scss", "assets/scss/icons/_boostrap.scss", "assets/scss/wp-forms/_wp-forms.scss", "assets/scss/slick/_slick.scss"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ACAA;AAAA;AAAA;AAAA;AAAA;AAMA;EACI;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;;AAKA;AAAA;EACI;EACA;;AAGJ;AAAA;EACI;;AAGJ;AAAA;AAAA;EACI;EACA;;;AAIR;AAAA;EAEC;;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAOJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKR;EACI;;;AAGJ;AAAA;AAAA;AAAA;EAII;;;AAKA;AAAA;EACI;EACA;EACA;;AAQA;AAAA;EACI;EACA;EACA;EACA;EACA;;;AAKZ;AAAA;AAAA;AAAA;EAII;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAIJ;AAAA;AAAA;AAAA;EAII;EACA;;;AAIJ;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;;AAQR;AAAA;EAEI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAKJ;EAEI;EACA;;AAGJ;EAEI;EACA;;AAGJ;AAAA;EAEI;EACA;;AAIR;AAAA;EAEI;;AAGJ;AAAA;EAEI;;;AAIR;EACI;EACA;;;AAGJ;EACI;EACA;;;AAIA;EACI;;;AAUJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAII;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;EACA;;;AAIR;EACI;;;AAGJ;EACI;;;AC7bJ;AAAA;AAAA;AAAA;AAAA;AAMA;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;EACA;EACA;;;AAIR;EACI;;;AAGJ;EACE;;;AAGF;EACI;;;AAGJ;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;EAEI;EACA;EACA;;;AAIA;EACI;;AAEA;EACI;EACA;EACA;;;AAKZ;EACC;;;AAGD;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;AAEA;EAHJ;IAIQ;;EAEA;IACI;IACA;;;;AAKZ;EACI;;;AAGJ;EACI;IACI;;;AAIR;EACI;IACI;;;ACnKR;AAAA;AAAA;AAAA;AAAA;AAOI;EACI;;;AAKJ;EACI;;;AAIR;EACI;;;AAIA;EADJ;IAEQ;;EAEA;IACI;;;;AAMZ;AAAA;EAEI;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIJ;EACI;EACA;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;;AAIR;EACI;;;AAIA;EACI;EACA;;;AAMJ;AAAA;AAAA;EAEI;EACA;;;AAIR;EACI;;AAEA;AAAA;EAEI;;;AAIR;EACI;EACA;;AAEA;EAAG;;;AAMC;AAAA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;;AAIR;EACI;;;AAGJ;AAAA;EAEI;;;AAIA;EACI;EACA;EACA;;;AAGR;EACI;IACI;;;AAGR;EACI;IACI;;EAEJ;IACI;;;AAIR;EACI;EACA;EACA;;;AAIA;EACI;EACA;;;AAKJ;EACI;EACA;EACA;;AACA;EACI;;;AAMR;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;EACA;;AAEA;EACI;;AAEA;EAHJ;IAIQ;IACA;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;;AAIR;EACI;;AAGJ;EACI;EACA;;AACA;EACI;;AAKZ;EACI;IACI;;;AAIR;EACI;IACI;;;AAIR;EACI;;;AAMJ;EACI;;AAGJ;EACI;;AAIA;EACI;;AAKJ;EADJ;IAEQ;;;AAIR;EACI;;AAEA;EACI;;AAIR;EACI;EACA;;AAGJ;EACI;EACA;;AACA;EACI;;;AAKZ;EACI;;AAEA;EACI;EACA;;AAIA;EADJ;IAEQ;;;;AAOR;AAAA;EACI;;AACA;AAAA;EACI;;AAGR;AAAA;EACI;;;AAKJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAIA;EADJ;IAEQ;;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAKJ;EADJ;IAEQ;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAIA;EADJ;IAEQ;IACA;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;;;;AAMR;EACI;;AAIA;EADJ;IAEQ;;;AAKJ;EADJ;IAEQ;;;AAKJ;EADJ;IAEQ;IACA;;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;;AAMZ;EACI;EACA;EACA;;;AAKA;EACI;EACA;;;AAKJ;EACI;;;AAIR;EACI;IACI;;EAGA;IACI;;;AAKZ;EACI;IACI;;;AAIR;EAEQ;IACI;;;AAKZ;EACI;IACQ;;EAER;IACI;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAIJ;IACI;IACA;;;AAIR;EAEI;IACI;IACA;IACA;IACA;;EAEJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAEJ;IACI;IACA;;;AAMJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;IACI;;;;AAMR;EACI;;;AAIR;EACI;;;AAIA;EACI;;AACA;EAFJ;IAGQ;;;AAIJ;EADJ;IAEQ;;;AAEN;EACE;IACI;;;AAIV;EACE;EACA;;AACA;EAHF;IAIM;;;;AAKR;EACI;;;AAKI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;EACA;EACA;EACA;EACA;;;AAMhB;EACG;IACI;IACA;IACA;IACA;;;AAGP;EACI;IACI;IACA;;;AAIR;EACG;IACI;IACA;;;AAIP;EACG;IACI;IACA;;;AAKP;EACI;EACA;EACA;;;AAGJ;EACC;IACC;IACE;IACA;;;AAGJ;EACC;IACC;;;AAIF;EACI;EACA;EACA;;;AAGJ;EACI;EACA;;;AAIA;EACI;;;AAIR;EACI;IACI;;;AAIR;EACI;IACI;;;AAIR;EACI;IACI;;EAGA;IACI;;;AAIZ;EACI;IACI;;;AAGR;EACI;IACI;;;AAIR;EACI;IACQ;;EAER;IACI;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;;;AAKJ;EACI;EACA;EACA;EACA;;AAGA;EACI;;;AAKZ;EACI;;;AAIA;EACI;IACI;;;AAGR;EACI;IACI;IACA;;EACA;IACI;;;AAMR;EACI;;AAGJ;EACI;;AAIR;EACI;EACA;;AAGJ;EACI;;AAEA;EACI;;AAIR;EACI;EACA;;AAEA;EACI;;AAIR;EACI;EACA;EACA;EACA;;;AAOI;EACI;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;EACA;EACA;;AAKJ;EACI;EACA;EACA;EACA;;AAOZ;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAEA;EACI;EACA;;AAKZ;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;;AAKP;EACO;IACL;;;AAGF;EAEC;IACC;IACA;IACA;IACA;IACA;;;AAGF;EACC;IACC;IACA;IACA;IACA;IACA;;;AAIF;EACO;;AAEP;EACC;IACE;;;;AAKJ;EACI;IACI;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEP;EACO;EACN;;;AAIF;EACC;EACA;EACA;;;AAIA;EACC;IACC;;;;AAOK;EADJ;IAEQ;IACA;IACA;IACA;IACA;;;AAIJ;EADJ;IAEQ;IACA;IACA;IACA;IACA;;EACA;IACI;;;;AAOZ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAGA;EACI;;AACA;EAFJ;IAGQ;;;;AAOhB;EACI;IACF;;;AAKD;EADD;IAEE;;;;AAGF;EACC;EACA;;;AAED;EACC;;;AAED;EACC;;;AAED;EACC;EACA;EACA;;;AAED;EACC;EACA;;;AAED;EACC;;;AAED;EACC;IACI;;;AAGL;EACC;;;AAED;EACI;;;AAGH;EACC;;;AC3iCF;AAAA;AAAA;AAAA;AAAA;AAOI;EAAG;;AAIK;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EAAK;;;AAQL;EACI;EACA;;AAIR;EACI;;;AAOJ;EADJ;IAEQ;;;AAKJ;EADJ;IAEQ;;;;AAOJ;EADJ;IAEQ;IACA;IACA;;;AAKI;AAAA;EAEI;;AAIA;EACI;;AAOJ;EACI;;AAGJ;EACI;;AAGJ;AAAA;AAAA;AAAA;AAAA;EAKI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAIA;EACI;;AAIR;EACI;;AAMQ;EACI;;;AAYpC;EACI;EACA;EACA;EACA;EACA;;;AAIA;EACI;;AAGJ;EACI;EACA;;AAGI;EACI;EACA;EACA;;;AAOZ;EACI;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAKJ;EADJ;IAEQ;IACA;;EAEA;IACI;;;AAKZ;EACI;EACA;;AAEA;EAAK;;AAEL;EACI;EACA;EACA;EACA;EACA;;;AAMR;EACI;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EAEI;;AAGJ;EACI;;;AAOJ;EACI;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;;AAIR;EACI;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;;AAEA;EAXJ;IAYQ;;;AAOZ;EAAsC;;AAG1C;EACI;EACA;;AAEA;EACI;EACA;;AAEA;EACI;EACA;;AAEA;EACI;;AAMhB;EACI;;AAIA;AAAA;EAEI;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAKJ;EACI;;AAGJ;EACI;EACA;EACA;;AAEA;EALJ;IAMQ;;;AAGJ;EACI;;AAEA;EAAoC;;AAGxC;EACI;;AAEA;EACI;;AAGJ;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AAQhB;AAAA;AAAA;AAAA;EAII;;;AAMA;EACI;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACI;EACA;;;AASA;EADJ;AAAA;IAEQ;;;AAKJ;EADJ;AAAA;IAEQ;;EAEA;AAAA;IACI;;;;AAShB;AAAA;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;AAKI;EADJ;IAEQ;;;;AASJ;EACI;;AAMR;AAAA;AAAA;AAAA;EAII;;AAIR;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIA;EACI;EACA;;AAEA;EACI;;AAGJ;EARJ;IASQ;;;AAKZ;EACI;;AAIA;EACI;EACA;EACA;;AAIA;EACI;;AAIR;EACI;;AAMR;EACI;;AAMQ;AAAA;EAEI;;AAOZ;EACI;;AAIA;EACI;;AAKI;EACI;;AAGJ;EACI;;AAKZ;EACI;;AAIA;EACI;;AAEA;EAHJ;IAIQ;;;AAKZ;EACI;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKZ;EACI;EACA;EACA;;AAKI;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;;AAUhB;EACI;;AAKZ;AAAA;AAAA;AAAA;EAII;;AAGJ;EACI;;AAGJ;AAAA;AAAA;EAGI;;;AAKJ;EACI;;;AAKJ;EACI;;;ACppBR;AAAA;AAAA;AAAA;AAAA;AAQQ;EACI;EACA;;AAIR;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAIR;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;AAAA;AAAA;EAGI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAKI;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;;AAMR;EADJ;IAEQ;IACA;;;AAMR;EACI;;AAGJ;EACI;;AAKJ;EACI;;AAIA;EACI;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGI;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKZ;EACI;;AAEA;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIA;EACI;EACA;EACA;;AAIR;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;;AAOZ;EACI;EACA;;AAKJ;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;IAQQ;;;;AAKZ;EACI;;;AAGJ;EACI;IACI;;EAGJ;IACI;;;ACtPR;AAAA;AAAA;AAAA;AAAA;AAUI;AAAA;AAAA;EAEC;;AAQF;AAAA;AAAA;EAEC;;;AAQF;EACC;EACG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGC;EAEC;;AAKN;EACI;;AAGD;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;EACA;;;AAMP;EACC;IACC;;;AAIF;AAAA;EAEI;;;AAGJ;EACI;;;AAGJ;EACC;IACC;IACA;IACA;;EAGD;IACC;IACE;IACF;;;AC3FF;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;;AACA;EAEI;EACA;;;AAIR;EACI;EACA;;AAEA;EACI;;AAGJ;EACI;;;AAIR;EACI;;;AAIJ;EACI;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAKI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EAhBJ;IAiBQ;;;AAEJ;EAnBJ;IAoBQ;;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;;AAOA;EACI;;AAGJ;EACI;;AAIR;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAKR;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAEI;EACA;EACA;;AAGJ;EACI;EACA;;AAMR;EACI;;AAEA;EACI;EACA;EACA;EACA;;AAKZ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAKZ;EACI;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAKT;EACK;;AAGJ;EACI;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AAEA;EACI;;AAMR;EADJ;IAEQ;;;;ACxQX;EACC;;AAGD;EACC;EACA;EACA;;AACA;EACC;EACA;;AAGD;EACC;;AAIF;EACO;;;AAKP;EACC;;AAGD;EACC;;AAIA;EACC;;AAED;EACC;;;AAKH;EACC;;;AAMA;AAAA;AAAA;AAAA;EAEC;;;ACpDF;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAXJ;AAAA;AAAA;IAYQ;IACA;;;;AAIR;AAAA;EAEI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAKI;AAAA;EAEI;;AAEA;EAJJ;AAAA;IAKQ;;EAEA;AAAA;IACI;;;AAKZ;EACI;;AAGJ;EACI;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;AAGJ;AAAA;EAEI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;;AAMhB;EACI;EACA;EACA;;AAEA;EALJ;IAMQ;;;;AAIR;AAAA;AAAA;AAAA;EAII;;;AAGJ;EACI;IACI;;;AAIR;AAAA;AAAA;EAGI;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;IACI;;;AAIR;EACI;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;;AAIR;EACI;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;;AASQ;EACI;;AAIR;EAEI;EACA;EACA;;AAGJ;EACI;;;AAMhB;EACI;EACA;EACA;EACA;;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;AAAA;AAAA;AAAA;AAAA;AAAA;IAQQ;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAQR;AAAA;AAAA;EACI;;AAKZ;AAAA;AAAA;EACI;;AASQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;;AAOpB;AAAA;EAEI;;;AAIA;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAGJ;EACI;;AAEA;EACI;EACA;;AAMR;EACI;;AAEA;EACI;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAKZ;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAIA;EACI;;AASZ;EACI;EACA;EACA;;;AAMhB;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;AAEA;AAAA;EAEI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;AAIR;EACI;;AAEA;EACI;;AAIR;EACI;;AAEA;EACI;;AAIR;EACI;EACA;EACA;;;AAMA;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AASZ;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EACI;EACA;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;;AAMJ;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;EACI;;AAGJ;AAAA;EACI;;;AAMA;EACI;;;AAKZ;EACI;;;AAGJ;EACI;;;AC1hBJ;AAAA;AAAA;AAAA;AAAA;AAMA;EACI;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAKI;AAAA;EAEI;;AAGJ;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;;;AAQZ;EACI;;AAGJ;EACI;;;AAKJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;AAEA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;EAEI;;AAIJ;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;EAEI;;;AAKJ;EACI;EACA;;;AAMA;EACI;;;AAMR;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACK;EACD;;;AAMR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAlBJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAmBQ;IACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;;AAKZ;AAAA;AAAA;EAGI;EACA;EACA;;AAEA;AAAA;AAAA;EACI;EACA;;;AAMA;AAAA;AAAA;EAGI;;AAEA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;;AAMhB;AAAA;AAAA;AAAA;EAII;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;EACI;EACA;;;AAKJ;EADJ;IAEQ;IACA;;;;AAIR;AAAA;EAEI;EACA;EACA;;AAEA;AAAA;EACI;EACA;;AAEA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAIR;EACI;AAAA;IACI;;;;AAKZ;EACI;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAII;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAII;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAMJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAMJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;;AAMR;EACI;;;AAKJ;EACI;EACA;;AAEJ;EACI;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;ACxdhB;AAAA;AAAA;AAAA;AAAA;AAQI;AAAA;AAAA;EACI;;;AAUR;AAAA;EAEI;;;AAGJ;EACI;EACA;;;AAIA;AAAA;EAEI;;AAEA;EAJJ;AAAA;IAKQ;IACA;IACA;IACA;;EAGA;AAAA;IACI;;EAGJ;AAAA;IACI;;EAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;AAAA;IAGI;;;AAIR;EAhCJ;AAAA;IAiCQ;;;AAMJ;EADJ;IAEQ;;EAEA;IACI;;;AAIR;EACI;;AAEA;EAIgB;IACI;;EAKJ;IACI;;;AAS5B;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EACI;;AAEA;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAGJ;EAVJ;IAWQ;;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAIA;EACI;;AAKZ;EACI;;AAKZ;AAAA;AAAA;AAAA;EAII;EACA;;;AAKJ;EACI;;;AAIR;EACI;;AAEA;EACI;;AAEA;EACI;;;AAOR;EACI;;;AAMA;EACI;;AAGJ;EACI;;AAGJ;AAAA;EAEI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;AAAA;EAEI;;AAGJ;EACI;EACA;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;;AAYR;AAAA;EACI;EACA;;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;AAEA;EALJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAMQ;IACA;;;AAIR;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;EACA;EACA;;AACA;AAAA;EACI;EACA;EACA;EACA;;AAKR;AAAA;AAAA;AAAA;EAEI;;AAIA;AAAA;EACI;EACA;;AAGJ;AAAA;EACI;;;AASR;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;EACA;;AAKZ;EACI;EACA;;AAEA;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAEA;EALJ;IAMQ;;;AAIR;EAhBJ;IAiBQ;IACA;IACA;;;AAGJ;EAtBJ;IAuBQ;;;AAGJ;EACI;EACA;EACA;EACA;;AAIR;EACI;IACI;;;AAIR;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAKJ;EACI;EACA;;AAEA;EACI;EACA;EACA;;AAGJ;EAEI;EACA;EACA;;AAGJ;EACI;EACA;EACA;EACA;;AASR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAYZ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAKZ;EACI;IAAO;;EAIC;IACI;;EAGJ;IACI;IACA;IACA;;EAEA;IACI;IACA;IACA;;EAGJ;IACI;;EAKZ;IACI;;EAIR;IACI;;EAIA;IACI;;EAIR;AAAA;AAAA;IAGI;;EAIJ;IACI;;;;AAQhB;AAAA;AAAA;AAAA;AAAA;EAKI;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACK;;;AAGL;EACK;;;AAGL;EACI;;;AAIA;EACI;;AAEA;EACI;;AAEA;EACI;;AAIR;EACI;;AAKJ;EACI;EACA;EACA;;AAGJ;EACI;;AAIA;EACI;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAKJ;EACI;EACA;;AAKJ;EACI;;AAKJ;EACI;EACA;;AAIR;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAEA;EAGI;;AAIR;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAEI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAMR;EACI;;AAIR;EACI;;AAYR;EAEQ;IACI;;;;AASZ;AAAA;EAEI;EACA;;;AAIR;EACI;;;AAOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAIA;EACI;;AAGJ;EACI;;;AAIR;EACI;;;AAIA;EACI;;AAEA;EACI;;;AAKZ;EACI;;;AAKK;EACO;;AAIZ;EACI;EACA;;AAEA;EACI;;;AAKZ;EACI;;;AAGJ;AAAA;AAAA;EAGI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIJ;EACI;EACA;EACA;EACA;;;AAIR;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;;AAEA;EATJ;AAAA;AAAA;AAAA;IAUQ;;;;AAIR;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAKJ;EACI;EACA;EACA;;AAGJ;EACI;;AAEA;EACI;EACA;EACA;;AAGJ;EACI;EACA;EACA;;AAGJ;EACI;;AAMA;EACI;;;AAOhB;EACI;;AAEA;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAKI;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAIR;EACI;;;AAOZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;;AAGI;EACI;EACA;;AAEA;EAEI;EACA;EACA;EACA;EACA;;;AAMf;EACG;EACA;EACA;EACA;EACA;EACA;;AAEA;EAGI;;;AAIR;EACI;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAIA;EACI;;AAGJ;EACI;IACI;;;AAIR;EACI;EACA;EACA;;AAGJ;EAAgC;;AAEhC;EACI;;AAGJ;EACI;;;AAIR;EACI;;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;;AAIR;AAAA;AAAA;EAGI;EACA;EACA;EACA;;;AAKA;AAAA;EAEI;EACA;EACA;EACA;;;AAIR;EACI;;AAEA;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;;AAGJ;EACI;EACA;;;AAKJ;AAAA;EAEI;EACA;EACA;;;AAIR;EACI;EACA;;;AAGJ;EACK;;;AAGL;EACI;;;AAGJ;EACK;;AAED;EACI;EACA;EACA;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;;;AAKR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGH;AAAA;EAEG;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAKI;EACI;;;AASI;AAAA;EAEI;;;AAQhB;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIA;EACI;EACA;EACA;;AACA;EACI;EACA;;AAIR;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAKZ;EACC;;AACG;EACI;IACI;;;AAGR;EACF;;AAGA;EACC;;AAED;EACC;EACA;;AAED;EACC;;AAED;EACC;IACC;;EAED;IACC;;EAED;IACC;;EAED;IACC;IACA;IACA;;EAED;IACC;;;AAGF;EACC;IACC;;EAGD;IACC;;EAED;IACC;;;AAKH;EACC;;AACM;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AACA;EACI;EACA;;;ACvzChB;AAAA;AAAA;AAAA;AAAA;AASY;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;EACA;EACA;;;AAOZ;EACI;EACA;;;AAQI;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;;AAKZ;AAAA;EACI;;AAEA;AAAA;EACI;;AAMJ;AAAA;AAAA;AAAA;EACI;EACA;;AAKJ;AAAA;EACI;;AAKJ;AAAA;EACI;;AAKJ;AAAA;EACI;;AAEA;AAAA;EACI;;AAIR;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;;AAEA;EATJ;AAAA;AAAA;AAAA;AAAA;AAAA;IAUQ;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAMR;AAAA;AAAA;AAAA;AAAA;AAAA;EAGI;EACA;;AAIR;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAIR;AAAA;AAAA;AAAA;EACI;;AACA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;;AAQR;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAEA;EACI;EACA;EACA;;AAGJ;EACI;;AAKA;AAAA;AAAA;EAEI;;AAKZ;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAKJ;AAAA;EAEI;;;ACnPR;AAAA;AAAA;AAAA;AAAA;AAMA;EACI;EACA;;;AAGJ;EACI;EACA;EACA;;;AAGJ;EACI;;;AAMQ;EAAK;;AAEL;EACI;EACA;EACA;EACA;EACA;;;AAMhB;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAjBJ;IAkBQ;;;AAGJ;EACI;EACA;;AAKJ;EACI;;AAGJ;EACI;;;AAMR;AAAA;EAEI;;AAGJ;EACI;EACA;;;AAIR;AAAA;AAAA;EAGI;;AAEA;EALJ;AAAA;AAAA;IAMQ;;;;AAIR;AAAA;AAAA;EAGI;;AAEA;EALJ;AAAA;AAAA;IAMQ;;;;AAKJ;EACI;EACA;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAOA;EACI;;AAIR;EACI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAIR;EACI;;AAIR;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAIA;EACI;;AAKJ;EACI;;;AAKZ;AAAA;AAAA;EAGI;EACA;;;AAGJ;EACI;;AAEA;EACI;EACA;;AAIA;EACI;;AAIR;AAAA;EAEI;EACA;;AAUA;EACI;EACA;;AAIR;EACI;EAEA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIR;EACI;EACA;;AAGJ;EACI;;;AAKJ;EACI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;;AC1WR;AAAA;AAAA;AAAA;AAAA;AAQI;EACI;;AAGJ;EACI;;AAIA;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;AAAA;EAEI;;AAEA;EAJJ;AAAA;IAKQ;;;AAIR;EACI;;AAEA;EAHJ;IAIQ;IACA;;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EATJ;IAUQ;;;AAGJ;EAbJ;IAcQ;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAIR;EA9CJ;IA+CQ;;;AAGJ;EAlDJ;IAmDQ;;;AAIR;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;IACA;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAKZ;EACI;;AAIR;EACI;EACA;;AAGJ;EACI;;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;;AAKJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAOJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAMR;EADJ;IAEQ;;;AAGJ;EALJ;IAMQ;;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;;AAIR;EACI;EACA;EACA;;AAGJ;EACI;;AAEA;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAIR;EACI;;AAEA;EAAQ;;AAER;EACI;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;AAIR;EACI;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;;AAEA;EACI;;AAIR;EACI;;AAMZ;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;;AAKZ;EACI;;AAEA;EACI;;AAIR;AAAA;EAEI;;AAEA;EAJJ;AAAA;IAKQ;IACA;;;AAGJ;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;EAEI;;AAIR;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAKI;EACI;;AAEJ;EACI;;AAKZ;AAAA;EAEI;EACA;;AAEA;EALJ;AAAA;IAMQ;;;AAIA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAGJ;AAAA;EACI;;AAIR;AAAA;EACI;;AAEA;AAAA;EACI;EACA;;AAIR;AAAA;EACI;;AAKI;AAAA;EACI;EACA;EACA;;AAEA;AAAA;EACI;;AAIR;AAAA;EACI;;AAKJ;AAAA;EACI;EACA;;AAEA;AAAA;EACI;;AASpB;EACI;EACA;;AAIA;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAKJ;EACI;;AAOJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMR;EACI;;AAGJ;EACI;;AAGJ;EAAuC;;AAEvC;EACI;;AAKI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMR;EACI;;AAIA;EACI;;AAGJ;EACI;EACA;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAVJ;IAWS;;;AAGL;EACI;EACA;EACA;;AAIJ;EAEI;EACA;;AAEA;EACI;;AAMhB;EACI;;AAGJ;EACI;EACA;;AAGI;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;IAQQ;;;AAGJ;EACI;EACA;;AAKZ;AAAA;AAAA;EAGI;EAEA;EACA;;AAIC;EAVL;AAAA;AAAA;IAWQ;;;AAGJ;AAAA;AAAA;EAAQ;;AAER;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;EACI;EACA;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;EAAO;;AAGX;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;EACA;;AAIR;EACI;EACA;EACA;EAEA;EAEA;EACA;;AASZ;EACI;;AAIA;EACI;EACA;;AAEJ;EACI;;AACA;EACI;;AAIZ;EACI;;AAGJ;AAAA;AAAA;AAAA;EAII;;AAGJ;EACI;;AAGJ;AAAA;EAEI;;;AAMJ;EACI;;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;;AAGJ;AAAA;EAEI;EACA;EACA;;AAEA;EANJ;AAAA;IAOQ;;;AAGJ;AAAA;EACI;;AAIR;EACI;;;AC5vBR;AAAA;AAAA;AAAA;AAAA;AAQI;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;EACI;;AAGJ;AAAA;EACI;;AAGJ;AAAA;EACI;;AAKA;EAFJ;AAAA;AAAA;AAAA;IAGQ;IACA;IACA;;EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAEI;IACA;;EAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IACI;;EAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IACI;;;AAIR;EAtBJ;AAAA;AAAA;AAAA;IAuBQ;;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAKL;AAAA;AAAA;AAAA;EACK;EACA;;AAOH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACG;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;EAZJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAaQ;IACA;IACA;;;AAIA;EADJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAEQ;;;AAKJ;EADJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAEQ;;;AAKJ;EADJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAEQ;;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAKZ;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;EACA;;AAGJ;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;EACI;EACA;EACA;;AAGJ;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;EAEI;EACA;EACA;EACA;;AAEA;EAPJ;AAAA;AAAA;AAAA;IAQS;;;AAGL;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;AAGJ;AAAA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;EACI;;AAGJ;AAAA;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;AAAA;EAAU;;AAGd;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;EACI;EACA;EACA;;AAKJ;AAAA;EACI;EACA;EACA;;AAIA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;AAAA;EACI;EACA;;AAIR;AAAA;EACI;EACA;;AACA;AAAA;EAEI;;AAGJ;AAAA;EAEI;;AAIR;AAAA;EACI;;AAGI;AAAA;EACI;EACA;;AAKZ;AAAA;AAAA;AAAA;EAEI;;AAIA;AAAA;AAAA;AAAA;AAAA;EACI;;AAQJ;AAAA;EACI;EACA;;AAGJ;AAAA;EACI;EACA;;AAEA;AAAA;EACI;;AAIR;AAAA;AAAA;EACI;;AAKJ;AAAA;AAAA;AAAA;AAAA;EACI;;AAKJ;AAAA;EACI;EACA;EACA;;AAEA;AAAA;EACI;EACA;;AAOZ;AAAA;EACI;EACA;;AAEA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;EACA;;AAEA;AAAA;EACI;EACA;;AAKZ;AAAA;EACI;EACA;;AAGJ;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;AAAA;EAAU;EAAsD;;AAK5E;AAAA;EACI;;AAMA;EAHJ;AAAA;AAAA;AAAA;AAAA;AAAA;IAIQ;;;AAIR;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EACC;;AAGD;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;EACI;;AAMhB;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;;;AAKJ;EACI;;AAGJ;AAAA;EAEI;;AAGJ;EACI;;AAGJ;AAAA;AAAA;EAGI;EACA;;AAGJ;AAAA;EAEI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAGJ;EACI;;AAKA;AAAA;EACI;;AAIR;EACI;;AAGI;EAEI;;AAGJ;EACI;;AAMJ;AAAA;EACI;;AAKJ;EADJ;IAEQ;;;AAGJ;EACI;EACA;EACA;;AAIR;EACI;;AAGJ;EACI;;AAIR;EACI;;AAEA;EACI;EACA;EACA;;AAGJ;AAAA;AAAA;AAAA;EAII;;AAGJ;EACI;EACA;;AAGJ;EACI;;AAKJ;EACI;;AAIR;EACI;;AAGJ;EACI;;AAIA;EACI;;AAIR;AAAA;EAEI;EACA;EACA;EACA;;AAEA;EAPJ;AAAA;IAQQ;;;;AAKZ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBI;;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAEA;EACI;;AAEJ;EAAI;;AAGR;EACI;;;AAIR;EACI;;;AAGJ;EACI;;;AAKA;EAFJ;IAGS;IACA;;;;AAKL;EACI;;AAGA;EACI;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;;AAIR;EACI;;;AAKR;EACI;;AACA;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAIJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;;AAEJ;EACI;;AAKR;EACI;;AAEJ;EACI;EACA;;AAGR;EACI;;;ACjvBR;AAAA;AAAA;AAAA;AAAA;AAOI;EAEQ;IACI;;;AAKZ;EAEQ;IACI;;;;AAMhB;EACI;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAMA;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;;AAMA;EACI;;AAMhB;EACI;;;AAIR;EACI;;AAEA;EACI;IACI;;EAEA;IACI;IACA;IACA;IACA;;EAGJ;IACI;;;AAKZ;AAAA;EAEI;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAIA;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAIR;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIA;EADJ;IAEQ;;;AA3CZ;EA+CY;IACI;IACA;;EAKZ;AAAA;IAEI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEA;AAAA;IACI;IACA;IACA;IACA;IACA;;;;AAMhB;EAEQ;IACI;;;AAKZ;EACI;;;AAGJ;EACI;IACI;;EAGJ;IACI;;;AAMA;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AAKZ;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;EACA;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;EAEI;EACA;;AAGJ;EACI;EACA;;;AAIR;EACI;;AAEA;EACI;EACA;EACA;;;AAIR;AAAA;AAAA;AAAA;EAII;;;AAKI;EACI;EACA;;;AAKZ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;;;AAKI;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;;AAGJ;EACI;;AAIA;EACI;;AAEA;EACI;;AAGJ;AAAA;EAEI;EACA;EACA;;AAEA;AAAA;EACI;;AAIR;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EAEI;EACA;EACA;;AAGJ;AAAA;EACI;EACA;EACA;EACA;;AAGJ;AAAA;EACI;EACA;EACA;EACA;;;AAQxB;AAAA;EAEI;EACA;EACA;EACA;EACA;;AAEA;AAAA;EACI;EACA;;;AAIR;EACI;;;AAGJ;EAEI;;;AAKA;AAAA;AAAA;EAEI;EACA;;;AAIR;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AAMR;EACI;;;AAKJ;EACI;;AAEA;EAHJ;IAIQ;;;;AAKZ;EAEQ;IACI;IACA;;EAEA;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;IACA;IACA;;EAEA;IACI;IACA;;EAKJ;IACI;IACA;IACA;;EAEA;IACI;IACA;;EAGJ;IACI;IACA;;EAOJ;IACI;;EAQA;IACI;;EAGJ;IACI;;;AP9lB5B;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAXJ;AAAA;AAAA;IAYQ;IACA;;;;AAIR;AAAA;EAEI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAKI;AAAA;EAEI;;AAEA;EAJJ;AAAA;IAKQ;;EAEA;AAAA;IACI;;;AAKZ;EACI;;AAGJ;EACI;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;AAGJ;AAAA;EAEI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;;AAMhB;EACI;EACA;EACA;;AAEA;EALJ;IAMQ;;;;AAIR;AAAA;AAAA;AAAA;EAII;;;AAGJ;EACI;IACI;;;AAIR;AAAA;AAAA;EAGI;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;IACI;;;AAIR;EACI;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;;AAIR;EACI;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;EAGJ;AAAA;AAAA;IAGI;;;AASQ;EACI;;AAIR;EAEI;EACA;EACA;;AAGJ;EACI;;;AAMhB;EACI;EACA;EACA;EACA;;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;AAAA;AAAA;AAAA;AAAA;AAAA;IAQQ;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAQR;AAAA;AAAA;EACI;;AAKZ;AAAA;AAAA;EACI;;AASQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;;AAOpB;AAAA;EAEI;;;AAIA;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAGJ;EACI;;AAEA;EACI;EACA;;AAMR;EACI;;AAEA;EACI;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;EACA;;AAKZ;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAIA;EACI;;AASZ;EACI;EACA;EACA;;;AAMhB;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;AAEA;AAAA;EAEI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;AAIR;EACI;;AAEA;EACI;;AAIR;EACI;;AAEA;EACI;;AAIR;EACI;EACA;EACA;;;AAMA;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AASZ;AAAA;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EACI;EACA;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;;;AAMJ;AAAA;AAAA;AAAA;EAEI;;AAGJ;AAAA;EACI;;AAGJ;AAAA;EACI;;;AAMA;EACI;;;AAKZ;EACI;;;AAGJ;EACI;;;AQ1hBJ;AAAA;AAAA;AAAA;AAAA;AAQQ;EACI;EACA;EACA;;AAIJ;EACI;;AACA;EAFJ;IAGQ;;;AAIR;EACI;;AAGJ;EACI;;AAIJ;EACI;EACA;EACA;;AAGJ;EACI;EACA;;AAEA;EACI;;AAIR;EACI;EACA;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;;AAIA;EAEI;;AAGJ;EAEI;;AAKZ;EACI;EACA;;AAYA;EACI;;AAGJ;EACI;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAGJ;EACI;;AAEA;EACI;EACA;;AAOZ;EACI;;AAGJ;EACI;EACA;EACA;;AAGA;EACI;;AAUA;EACI;;AAOZ;EACI;;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAOJ;EADJ;IAEQ;IACA;IACA;;;AAKZ;EACI;;AAIA;EADJ;IAEQ;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAVJ;IAWQ;IACA;IACA;;;AAIA;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;IACA;;;AAIR;EACI;EACA;;AAGJ;EACI;;AAUR;EACI;;AAEA;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EARJ;IASQ;IACA;;;AAMhB;EACI;;AAGI;EADJ;IAEQ;;;AAMQ;EADJ;IAEQ;IACA;;;AAQxB;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;;AAKJ;EACI;IACI;;;AAIR;EAAW;;AAEX;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;EAEI;EACA;EACA;;AAGJ;EAEI;;AAGJ;EACI;EACA;;AAKJ;EACI;EACA;EAGA;;AAEA;EACI;EACA;EACA;;AAMP;EACI;;AAEA;EACI;;AAGL;EACI;;AAOZ;EACI;EACA;EACA;EACA;EACA;EAEA;;AAEA;EACK;;AACA;EACG;;AAKZ;EACI;;AAGI;EACI;EACA;EACA;;AAMA;EAFJ;IAGQ;;;AAMJ;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAIR;EACI;;AAOZ;EACI;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;;AAEA;EALJ;IAMQ;;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;EACA;;AAMA;EACI;;AAMhB;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EACI;EACA;;AAMR;AAAA;EAEI;EACA;;AAKJ;EACI;;AAEA;EAHJ;IAIQ;IACA;;;AAIR;EACI;EACA;;AAEA;EACI;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;EACA;;AAMR;EADJ;IAEQ;;;AAMhB;EACI;EACA;;;AAOJ;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAIR;EACI;;;AAIR;EACI;;;AAGJ;EACI;;AAEA;EACI;IACI;;;AAIR;EACI;IACI;IACA;;;AAKR;EACI;;AAEA;EACI;IACI;;;;AASZ;EACI;;AAKJ;EAAQ;;AAER;AAAA;AAAA;EAGI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAGJ;EACI;EACA;;AAMA;EACI;EACA;EACA;;AAGJ;EACI;;AAIR;AAAA;EAEI;EACA;;AAGJ;AAAA;EAEI;EACA;;AAIJ;EACI;EACA;;AAEA;EACI;;AAEA;EAHJ;IAIQ;IACA;;;AAIR;EACI;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;EACA;;AAEA;EACI;EACA;EACA;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAMhB;EACI;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAII;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;EAA2B;;AAE3B;EACI;EACA;EACA;EACA;;;AAOZ;AAAA;AAAA;AAAA;EAII;EACA;EACA;;AAGJ;EACI;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQI;;AAGJ;EACI;EACA;EACA;;AAEA;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;;AAGJ;EACI;;;AAKZ;AAAA;EAEI;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;;;ACtxBJ;AAAA;AAAA;AAAA;AAAA;AAOI;EACF;;AAIM;EADJ;IAEQ;;;AAIR;EACI;EACN;EACA;EACA;EACM;;AAGA;EARJ;IASD;;;AAKS;EAFJ;IAGQ;IACA;IACA;IACA;;;AAMJ;EACI;EACA;;AAGJ;EACI;EACA;EACA;;AAOZ;EAEI;EACA;EACA;;AAEA;EACI;EACA;EACA;;AAGJ;EACI;;AAIA;EACI;EACA;EACA;;AAGJ;EACI;EACA;;AACA;EACI;;AAKR;EACI;EACA;;AAMZ;EACI;;AAEA;EACI;;AAIR;AAAA;EAEI;EACA;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAGJ;EACI;EACA;;;AC5HR;AAAA;AAAA;AAAA;AAAA;AAMA;EACI;EACA;EACA;;AAGF;AAAA;AAAA;EAGE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AChxOJ;AAAA;AAAA;AAAA;AAAA;AAKA;EACI;IACI;IACA;;;AAKR;EACI;EACA;;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACI;EACI;;AAGR;EACY;EACA;;;AAIhB;EACI;IACI;;;AAIR;EACI;IACI;IACA;;;AAIR;EACI;IACI;IACA;;;AAIR;EACC;;;AAGD;AAEA;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACG;EACC;EACA;EACA;;;AAIR;EACI;;;AAGJ;EACO;IACa;IACA;;;AAIpB;EACM;;;AAGN;EACO;;;AAGP;EACO;EACA;EACA;EACA;EACA;EACA;;AACE;EAEQ;EACA;;;AAIjB;EACO;IACO;IACA;IACA;IACA;;;AAId;AACA;EACI;EACA;EACA;EACA;;AACC;EAEY;EACA;;;AAIjB;AAEA;EACI;EACA;;;AAGJ;AACA;EACI;EACA;;;AC/JJ;AAAA;AAAA;AAAA;AAAA;AAKA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;IAQQ;;;AAGJ;EACI;;AAGJ;EACI;EACA;;;AAGR;AAAA;EAEI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;AAEA;EAEI;EACA;;AAGJ;EACI;;AAGJ;EACI;;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;EACA;;;AAIR;EACI;;;AAMA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;EACI;EACA;;AAIR;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;EACI;;AAEA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;EACI;EACA;;AAEA;AAAA;AAAA;EACI;;;AA6CZ;AAGI;EACI;;;AAIR;AAEI;EACI;EACA;EACA;EACA;;AAIR;AAEA;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;;AAGJ;AAAA;EACI;EACA,SAtEgB;EAuEhB;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;;AAEA;EAHJ;IAIQ;;;AAGJ;EAPJ;IAQQ;;;AAGJ;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EARJ;IASQ;;;AAIR;EAEI;;AAEA;EACI;;;AAKZ;EACI;;AAEA;EAHJ;IAIQ;;;AAGJ;EAPJ;IAQQ;;;AAGJ;EACI;EACA;;AAEA;EAJJ;IAKQ;;;AAGJ;EARJ;IASQ;;;AAIR;EAEI;;AAEA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;;AAMA;AAAA;AAAA;EAGI;;;AAKZ;AACA;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAEA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAKJ;EACI;EACA;;;AAMhB;EACI;IACI;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;;EAEJ;IACI;IACA;IACA;IACA;IACA;;;AAKJ;EACI;;AAGJ;EACI;;;AAKJ;EACI;;AAEA;EAHJ;IAIQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;AAMR;EACI;;AAEA;EAHJ;IAIQ;;;;AAMR;EACI;;AAEA;EAHJ;IAIQ;;;AAIA;EACG", "file": "style.css", "sourcesContent": ["/*\nTheme Name: Glowess\nTheme URI: https://glowess.madrasthemes.com/\nAuthor: MadrasThemes\nAuthor URI: https://madrasthemes.com/\nDescription: The Glowess Skincare WooCommerce theme is a premium theme specifically designed for online stores that focus on skincare and beauty products.\nRequires at least: 6.7\nTested up to: 6.8\nRequires PHP: 7.0\nWC requires at least: 9.6.0\nWC tested up to: 9.8.0\nVersion: 1.0.3\nLicense: GNU General Public License v2 or later\nLicense URI: http://www.gnu.org/licenses/gpl-2.0.html\nText Domain: Glowess\nTags: e-commerce, block-patterns, block-styles, editor-style, full-site-editing, full-width-template, rtl-language-support, template-editing, translation-ready, wide-blocks\n*/\n\n// Base\n@import \"base/base\";\n@import \"base/utility\";\n\n// Gutenberg Block Style\n@import \"gb-block/gb-block\"; //Write only blog style.\n@import \"gb-block/header\";\n@import \"gb-block/mobile-header\";\n@import \"gb-block/footer\";\n@import \"gb-block/single-post\";\n@import \"gb-block/blog\";\n\n// woocommerce\n@import \"woocommerce/product-item\";\n@import \"woocommerce/wc-blocks\";\n@import \"woocommerce/cart\";\n@import \"woocommerce/yith\";\n@import \"woocommerce/mini-cart\";\n@import \"woocommerce/my-account\";\n@import \"woocommerce/checkout\";\n@import \"woocommerce/product-filter\";\n@import \"woocommerce/product-item\";\n@import \"woocommerce/single-product\";\n@import \"woocommerce/order-completed\";\n// @import \"woocommerce/shop\";\n\n\n// Boostrap Icon\n@import \"icons/boostrap.scss\";\n\n// WP Forms\n@import \"wp-forms/wp-forms.scss\";\n\n// Slick\n@import \"slick/slick.scss\";", "/*\n################\n* === BASE STYLE  ===\n################\n*/\n\n*, *::before, *::after {\n    box-sizing: border-box;\n}\n\nbody {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    --drawer-width:560px;\n    --neg-drawer-width: calc(var(--drawer-width)*-2);\n}\n\nimg {\n    max-width: 100%;\n    height: auto;\n}\n\nins {\n    text-decoration: none;\n}\n\na,button,.wp-element-button {\n    transition: .3s ease-in-out;\n}\n\npre {\n    overflow: auto;\n}\n\niframe {\n    max-width: 100%;\n}\n\n.wc-block-components-notice-banner a {\n    text-decoration: none !important;\n}\n\ntable {\n    width: 100%;\n    border-collapse: collapse;\n    text-align: left;\n}\n\ntable:not([class]),\ntable.mce-item-table {\n    thead th {\n        background: var(--wp--preset--color--gray-100);\n        font-weight: 500;\n    }\n\n    th {\n        font-weight: normal;\n    }\n\n    tbody td, th {\n        padding: .5rem 1rem;\n        border: 1px solid var(--wp--preset--color--gray-100);\n    }\n}\n\ninput::-webkit-outer-spin-button,\ninput::-webkit-inner-spin-button {\n -webkit-appearance: none;\n}\n\ninput[type=text],\ninput[type=password],\ninput[type=email],\ninput[type=number],\ninput[type=url],\ninput[type=search],\ninput[type=tel],\ndiv.wpforms-container-full.wpforms-block textarea,\ndiv.wpforms-container-full.wpforms-block :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),\n.wc-block-components-form .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),\n.wc-block-components-form .wc-block-components-text-input.is-active :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),\ntextarea {\n    display: block;\n    width: 100%;\n    padding: 14px 20px;\n    font-size: 15px;\n    font-weight: 400;\n    line-height: 24px;\n    color: var(--wp--preset--color--gray-200);\n    background-color: var(--wp--preset--color--base);\n    background-clip: padding-box;\n    border: 1px solid var(--wp--preset--color--gray-100);\n    appearance: none;\n    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;\n    border-radius: 0;\n    font-family: var(--wp--preset--font-family--body);\n    max-height: 54px;\n\n    // @media (min-width:768px) {\n    //     font-size: 17px;\n    // }\n\n    &::placeholder {\n        color: var(--wp--preset--color--contrast);\n        font-size: 15px;\n        font-family: var(--wp--preset--font-family--body);\n\n        // @media (min-width:768px) {\n        //     font-size: 17px;\n        // }\n    }\n\n    &:is(:focus,:focus:invalid) {\n        color: var(--wp--preset--color--gray-200);\n        background-color: var(--wp--preset--color--base);\n        outline: 0;\n        box-shadow: none;\n        padding: 14px 20px;\n        border-color: var(--wp--preset--color--contrast);\n        box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n    }\n}\n\n\ntextarea {\n    max-height: initial;\n}\n\n.wc-block-components-form .wc-block-components-text-input input:-webkit-autofill+label, \n.wc-block-components-form .wc-block-components-text-input.is-active label, \n.wc-block-components-text-input input:-webkit-autofill+label, \n.wc-block-components-text-input.is-active label {\n    transform: translateY(-12px) scale(1);\n}\n\n.wc-block-components-form .wc-block-components-text-input, \n.wc-block-components-text-input {\n    label {\n        left: 20px;\n        font-size: 15px;\n        text-transform: capitalize;\n\n        // @media (min-width:768px) {\n        //     font-size: 17px;\n        // }\n    }\n    \n    &.is-active {\n        label {\n            font-size: 15px;\n            left: 14px;\n            color: var(--wp--preset--color--contrast);\n            background-color: var(--wp--preset--color--base);\n            padding: 2px 11px;\n        }\n    }\n}\n\n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, \n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true], \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true] {\n    box-shadow:none;\n    background-color: var(--wp--preset--color--base);\n}\n\n.wc-block-components-combobox .wc-block-components-combobox-control .components-form-token-field__suggestions-list, .wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control .components-form-token-field__suggestions-list {\n    border-color: var(--wp--preset--color--contrast);\n    background-color: var(--wp--preset--color--base);\n    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n    left: 1px;\n}\n\n.wc-block-components-form .wc-block-components-text-input, .wc-block-components-text-input,\n.wc-block-components-country-input,\n.wc-block-components-state-input {\n    margin-top: 20px;\n}\n\n\n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, \n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true], \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true] {\n    border-color: var(--wp--preset--color--contrast);\n    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n\n}\n\n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input, \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input {\n    height: inherit;\n    padding: 14.5px 20px;\n    line-height: inherit;\n    background-color: var(--wp--preset--color--base);\n    color: var(--wp--preset--color--secondary);\n    font-size: 15px;\n}\n\n.wc-block-components-form .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]), .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]),\ndiv.wpforms-container-full.wpforms-block :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]), .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]) {\n    height: inherit;\n}\n\n.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input, \n.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input {\n    border-color: var(--wp--preset--color--gray-100);\n    border-radius: 0;\n}\n\n.wc-block-components-combobox.is-active .wc-block-components-combobox-control label.components-base-control__label, \n.wc-block-components-combobox:focus-within .wc-block-components-combobox-control label.components-base-control__label, \n.wc-block-components-form .wc-block-components-combobox.is-active .wc-block-components-combobox-control label.components-base-control__label, \n.wc-block-components-form .wc-block-components-combobox:focus-within .wc-block-components-combobox-control label.components-base-control__label {\n    transform: translateY(-10px) scale(1);\n    left: 10px;\n    color: var(--wp--preset--color--contrast);\n    background-color: var(--wp--preset--color--base);\n    padding: 2px 11px;\n}\n\nselect {\n    display: block;\n    width: 100%;\n    max-width: 100%;\n    padding: 12.42px 40px 12.42px 20px;\n    -moz-padding-start: calc(1rem - 3px);\n    font-size: 15px;\n    //font-weight: 500;\n    line-height: 1.6;\n    color: var(--wp--preset--color--contrast);\n    background-color: var(--wp--preset--color--base);\n    background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23282931' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e\");\n    background-repeat: no-repeat;\n    background-position: right .65rem center;\n    background-size: 26px 12px;\n    border: 1px solid var(--wp--preset--color--gray-100);\n    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;\n    appearance: none;\n    cursor: pointer;\n    word-wrap: normal;\n\n    &:focus {\n        outline: 0;\n        border-color: var(--wp--preset--color--contrast);\n        box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n    }\n}\n\n// .woocommerce.wc-block-catalog-sorting select.orderby {\n//     font-size: 17px;\n// }\n\n.wc-block-components-form .wc-block-components-text-input label, \n.wc-block-components-text-input label {\n    transform: translateY(17px);\n    color: var(--wp--preset--color--contrast);\n}\n\n.wc-block-components-button:not(.is-link):focus {\n    box-shadow: none;\n}\n\n.components-combobox-control__suggestions-container:focus-within {\n    box-shadow: none;\n}\n\n.select2-container--default {\n    font-size: 15px;\n    text-align: left;\n\n    .select2-selection--single {\n        border: 1px solid var(--wp--preset--color--gray-100);\n        padding: 8px;\n        border-radius: 0;\n\n        &,.select2-selection__arrow {\n            height: 54px;\n        }\n\n        .select2-selection__arrow {\n            right: 16px;\n        }\n\n        .select2-selection__rendered {\n            color: var(--wp--preset--color--secondary);\n            padding-left: 12px;\n            line-height: 2.2;\n            font-size: 15px;\n        }\n    }\n\n    &.select2-container--open {\n        &.select2-container--below .select2-selection--single,\n        &.select2-container--below .select2-selection--multiple {\n            border-top-right-radius: 0;\n            border-top-left-radius: 0;\n        }\n\n        &.select2-container--above .select2-selection--single,\n        &.select2-container--above .select2-selection--multiple {\n            border-bottom-right-radius: 0;\n            border-bottom-left-radius: 0;\n        }\n\n        .select2-selection--single,\n        .select2-selection {\n            border-color: var(--wp--preset--color--contrast);\n            box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n        }\n    }\n\n    .select2-results__option[aria-selected=true],\n    .select2-results__option[data-selected=true] {\n        background-color: var(--wp--preset--color--gray-100);\n    }\n\n    .select2-results__option--highlighted[aria-selected], \n    .select2-results__option--highlighted[data-selected] {\n        color: inherit;\n    }\n}\n\n.select2-dropdown {\n    border-color: var(--wp--preset--color--gray-100);\n    background-color: var(--wp--preset--color--base);\n}\n\n.select2-container--open .select2-dropdown {\n    border-color: var(--wp--preset--color--contrast);\n    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n}\n\n.select2-search--dropdown {\n    .select2-search__field {\n        padding: 10px 16px;\n    }\n}\n\n.wc-block-components-radio-control,\nbody:not(.block-editor-page),\nbody:not(.edit-post-layout__metaboxes),\nbody:not(.edit-post-sidebar__panel),\n.wc-block-checkbox-list .wc-block-components-checkbox,\ndiv.wc-block-components-checkbox {\n    input[type=checkbox],\n    .wc-block-components-radio-control__input,\n    .wc-block-components-checkbox__input[type=checkbox] {\n        width: 14px;\n        height: 14px;\n        min-width: 14px;\n        min-height: 14px;\n        border-radius: 0;\n    }\n    \n    input[type=checkbox],\n    input[type=radio],\n    .wc-block-components-checkbox input[type=checkbox] {\n        margin: 0 15px 0 0;\n        background-color: transparent;\n        background-repeat: no-repeat;\n        background-position: center;\n        background-size: contain;\n        border: 1px solid var(--wp--preset--color--contrast);\n        appearance: none;\n        vertical-align: middle;\n        cursor: pointer;\n    }\n\n    input:checked[type=radio] {\n        background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e\");\n        background-size: 20px;\n    }\n    \n    input[type=radio]  {\n        width: 14px;\n        height: 14px;\n        padding: 0;\n        border-radius: 50%;\n    }\n\n    .wc-block-components-radio-control__option-checked {\n        font-weight: 500;\n    }\n\n    .wc-block-components-radio-control__input:checked:before {\n        background-color: var(--wp--preset--color--base);\n        min-width: 10px;\n        min-height: 10px;\n    }\n    \n    input[type=checkbox]:active,\n    input[type=radio]:active {\n        filter: 100%;\n    }\n    \n    input[type=checkbox]:focus,\n    .wc-block-components-checkbox__input[type=checkbox]:focus,\n    input[type=radio]:focus {\n        outline: 0;\n    }\n    \n    input[type=checkbox]:checked,\n    .wc-block-components-checkbox__input[type=checkbox]:checked,\n    input[type=radio]:checked,\n    input[type=checkbox]:indeterminate {\n        border-color: var(--wp--preset--color--contrast);   \n    }\n\n    input[type=radio]:checked {\n        border-width: 3px;\n    }\n\n    input:checked[type=checkbox] {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.29078 0.971021C9.01232 0.692189 8.56015 0.692365 8.28131 0.971021L3.73799 5.51452L1.71868 3.49523C1.43985 3.21639 0.987865 3.21639 0.709033 3.49523C0.4302 3.77406 0.4302 4.22604 0.709033 4.50487L3.23306 7.0289C3.37239 7.16823 3.55509 7.23807 3.7378 7.23807C3.92051 7.23807 4.10338 7.16841 4.24271 7.0289L9.29078 1.98065C9.56962 1.70201 9.56962 1.24984 9.29078 0.971021Z' fill='primary'/%3E%3C/svg%3E%0A\");\n        background-size: 17px;\n        background-repeat: no-repeat;\n        background-position: 50% 50%;\n    }\n    \n    input:checked[type=checkbox],\n    .wc-block-components-checkbox__input[type=checkbox]:checked {\n        background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 10 3 3 6-6'/%3e%3c/svg%3e\");\n        background-color: var(--wp--preset--color--contrast);\n        border-color: var(--wp--preset--color--contrast);\n    }\n    \n    input[type=checkbox]:indeterminate,\n    .wc-block-components-checkbox__input[type=checkbox]:indeterminate {\n        background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3e%3c/svg%3e\");\n    }\n    \n    input[type=checkbox]:disabled,\n    input[type=radio]:disabled {\n        pointer-events: none;\n        filter: none;\n        opacity: .5;\n    }\n}\n\n.wc-block-components-checkbox .wc-block-components-checkbox__mark {\n    fill: var(--wp--preset--color--contrast);\n}\n\n.wc-block-components-checkbox .wc-block-components-checkbox__mark {\n    display: none; \n}", "/*\n################\n* === UTILITY STYLE  ===\n################\n*/\n\n.flex {\n    display: flex;\n}\n\n.list-none {\n    list-style: none;\n    padding-left: 0;\n}\n\n.overflow-hidden {\n    overflow: hidden;\n}\n\n.clear-after {\n    &::after {\n        content: \"\";\n        display: block;\n        clear: both;\n    }\n}\n\n.f-shrink-0 {\n    flex-shrink: 0;\n}\n\n.object-cover img {\n  object-fit: cover;\n}\n\n.d-none {\n    display: none;\n}\n\n@media (min-width:1024px) {\n    .d-lg-block {\n        display: block !important;\n    }\n\n    .d-lg-none {\n        display: none !important;\n    }\n}\n\n.relative {\n    position:relative;\n}\n\n.absolute {\n    position: absolute;\n}\n\n.stretched-link,\n.stretched-link a {\n    display: block;\n}\n\n.stretched-link:before,\n.stretched-link a::before {\n    position: absolute;\n    inset: 0;\n    content: \"\";\n}\n\n.stretched-link {\n    &,a {\n        display: block;\n\n        &::before {\n            position: absolute;\n            inset: 0;\n            content: \"\";\n        }\n    }\n}\n\n.h-auto {\n\theight: auto;\n}\n\n.grid {\n    display: grid;\n}\n\n.grid-span-full {\n    grid-column: 1/-1;\n    grid-row: 1/-1;\n}\n\n.mt-auto {\n    margin-top: auto !important;\n}\n\n.z-1 {\n    z-index: 1;\n}\n\n.z-2 {\n    z-index: 2;\n}\n\n.align-self-stretch {\n    align-self: stretch;\n}\n\n.mt-0 {\n    margin-top: 0;\n}\n\n.ml-auto {\n    margin-left: auto !important;\n}\n\n.mr-0 {\n    margin-right: 0 !important;\n}\n\n.mr-auto {\n    margin-right: auto !important;\n}\n\n.ml-0 {\n    margin-left: 0 !important;\n}\n\n.mb-auto {\n    margin-bottom: auto;\n}\n\n.w-100 {\n    width: 100% !important;\n}\n\n.swipe {\n    flex-wrap: nowrap !important;\n\n    @media (max-width:767px) {\n        overflow-x: scroll;\n\n        &.wp-block-columns:not(.is-not-stacked-on-mobile)>.wp-block-column {\n            flex-basis: 80% !important;\n            flex-shrink: 0;\n        }\n    }\n}\n\n.ms-auto {\n    margin-left: auto !important;\n}\n\n@media (min-width: 1024px) {\n    .d-lg-block {\n        display: block !important;\n    }\n}\n\n@media (min-width: 1200px) {\n    .d-xl-block {\n        display: block !important;\n    }\n}", "/*\n################\n* === GB STYLE === Write oly WP blocks style\n################\n*/\n\n.wp-block-quote {\n    cite {\n        padding-right: 50px;\n    }\n}\n\n.wp-block-pullquote.has-background {\n    blockquote {\n        background-color: transparent !important;\n    }\n}\n\n.wp-block-post-comments-form textarea {\n    padding: 14px 20px;\n}\n\n.avatar-img {\n    @media (max-width:767px) {\n        padding-left: var(--wp--preset--spacing--50) !important;\n\n        .wp-block-avatar {\n            width: 60px;\n        }\n    }\n}\n    \n//wp-test markup-image-alignment \nimg.alignright,\n.mceTemp .wp-caption.alignright {\n    float: right;\n    margin-left: 32px;\n}\n\nimg.alignright,\n.mceTemp .wp-caption.alignright,\nimg.alignleft,\n.mceTemp .wp-caption.alignleft,\n.wp-block-image .alignleft,\n.wp-block-image .alignright {\n    margin-top: 10px;\n    margin-bottom: 10px;\n}\n\nimg.aligncenter {\n    display: block;\n    margin: auto;\n}\n\nimg.alignleft,\n.mceTemp .wp-caption.alignleft {\n    float: left;\n    margin-right: 32px;\n}\n\n.wp-block-image .alignleft {\n    margin-right: 32px;\n}\n\n.wp-block-image .alignright {\n    margin-left: 32px;\n}\n\n//wp-test post-format-gallery\n.gallery-item {\n    display: inline-block;\n    vertical-align: top;\n    width: 100%;\n}\n\n@media (min-width: 768px) {\n    .gallery {\n        display: grid;\n        gap: 30px;\n        grid-template-columns: repeat(3,minmax(0,1fr));\n    }\n}\n\n.wp-block-social-links.has-small-icon-size {\n    font-size: 12px;\n}\n\n.wp-site-blocks {\n    > .wp-block-group {\n        position: relative;\n        z-index: 1;\n    }\n}\n\n.wp-block-button.is-style-squared,\n.wp-block-search__button  {\n    &:hover,\n    &:focus {\n        color: var(--wp--preset--color--base) !important;\n        transition: 0.3s ease-in-out;\n    }\n}\n\n.wp-block-search__input {\n    border-color:var(--wp--preset--color--gray-100) !important;\n\n    .wp-block-button.is-style-squared,\n    .wp-block-button__link {\n        border-radius: 0;\n    }\n}\n\n.wp-block-query-no-results {\n    padding-top: 0;\n    font-size: 15px;\n\n    p {margin: 0;}\n}\n\n.wp-block-comment-template {\n    .trackback,\n    .pingback {\n        .wp-block-columns > .wp-block-column:first-child {\n            display: none;\n        }\n    }\n}\n\n.post-password-form input[type=submit] {\n    background-color: var(--wp--preset--color--primary);\n    border-width:0;\n    color: var(--wp--preset--color--base);\n    font-family: inherit;\n    font-size: var(--wp--preset--font-size--medium);\n    font-style: normal;\n    font-weight: 400;\n    padding-top: 12px;\n    padding-right: 1rem;\n    padding-bottom: 12px;\n    padding-left: 1rem;\n    text-decoration: none;\n    text-transform: uppercase;\n    cursor: pointer;\n    min-height: 54px;\n    \n    &:hover {\n        background-color: var(--wp--preset--color--secondary);\n    }\n}\n\n.wp-block-file__button {\n    text-align: center;\n}\n\n.wp-block-file__button,\n.post-password-form input[type=submit] {\n    min-width: 150px;\n}\n\n.post-password-form {\n    p:last-child {\n        display: flex;\n        align-items: flex-end;\n        gap: 20px;\n    }\n}\n@media (max-width: 768px) {\n    .min-height-img {\n        min-height: 365px !important;\n    }\n}\n@media (max-width: 1023px) {\n    .height-img img {\n        height: 100% !important;\n    }\n    .v1-box-1 .height-img img {\n        width: 100% !important;\n    }\n}\n\n.inline-img a img {\n    margin-left: 10px;\n    display: inline-block;\n    vertical-align: middle;\n}\n\n.home-v1-hero {\n    .gradient .wp-block-cover__background {\n        background: linear-gradient(180deg, rgba(19, 28, 25, 0.5) 0%, rgba(19, 28, 25, 0) 24.58%, rgba(19, 28, 25, 0) 100%);\n        opacity: .5 !important;\n    }\n}\n\n.home-v1-hero, .sp-home-v1-banner, .home-v4-hero {\n    .wp-block-button a:hover {\n        background-color: var(--wp--preset--color--secondary) !important;\n        border-color: var(--wp--preset--color--secondary) !important;\n        color: var(--wp--preset--color--base) !important;\n        img{\n            filter: brightness(0) invert(1) !important;\n        }\n    }\n}\n\n.glowess-cat {\n    .wc-block-product-categories-list--depth-0 {\n        display: flex;\n        flex-wrap: wrap;\n        padding: 0px;\n        margin: 0px;\n        gap: 25px;\n        justify-content: space-between;\n\n        .wc-block-product-categories-list-item {\n            display: flex;\n            flex-direction: column;\n            margin: 0px;\n            flex-wrap: wrap;\n            width: 18.5%;\n            text-align: center;\n        }\n        \n        .wc-block-product-categories-list-item a {\n            display: flex;\n            flex-direction: column;\n\n            .wc-block-product-categories-list-item__image {\n                margin: 0px;\n                border-width: 0px;\n                max-width: 100%;\n\n                img {\n                    aspect-ratio: 1/1;\n                    \n                    @media (min-width:1200px) {\n                        min-height: 300px;\n                        object-fit: cover;\n                    }\n                }\n            }\n\n            .wc-block-product-categories-list-item__name {\n                font-size: 20px;\n                font-family: Sen;\n                font-weight: 500;\n                text-transform: capitalize;\n                text-decoration: none;\n                margin-top: 23px;\n            }\n        }\n\n        .wc-block-product-categories-list-item-count:after, .wc-block-product-categories-list-item-count:before {\n            content: \"\";\n        }\n\n        .wc-block-product-categories-list-item-count{\n            font-size: 15px;\n            margin-top: 3px;\n            span[aria-hidden=\"true\"]::after {\n                content: \" Items\";\n            }\n        }\n    }\n\n    @media (min-width: 600px) {\n        .slick-slide > div {\n            padding: 20px 12.5px;\n        }\n    }\n\n    @media (min-width: 600px) {\n        .slick-list {\n            margin: 0 -12.5px;\n        }\n    }\n\n    .slick-dots {\n        bottom: -60px;\n    }\n}\n\n\n.gl-home-v4 {\n    .about-yours {\n        padding-bottom: var(--wp--preset--spacing--30);\n    }\n\n    .v1-best-selling-products {\n        padding-bottom: var(--wp--preset--spacing--10) !important;\n    }\n\n    .home-v4-banner {\n        .wp-block-button .wp-block-button__link {\n            min-width: 100px;\n        }\n    }\n\n    .home-v4-newsletter .v4-form {\n        @media (max-width:768px) {\n            max-width: 100%;\n        }\n    }\n\n    .home-blog {\n        margin-top: var(--wp--preset--spacing--50) !important;\n\n        .wp-block-post-featured-image img{\n            border-radius: 16px !important;\n        }\n    }\n\n    .v2-marque-brands {\n        padding-top: var(--wp--preset--spacing--30) !important;\n        padding-bottom: var(--wp--preset--spacing--30) !important;\n    }\n\n    .contact-map {\n        padding-top: var(--wp--preset--spacing--10) !important;\n        padding-bottom: var(--wp--preset--spacing--10) !important;\n        iframe, .map-inner-box {\n            border-radius: 15px;\n        }\n    }\n}\n\n.home-v4-hero {\n    padding-bottom: 14px;\n\n    .slick-slide > div {\n        padding-top: 0;\n        padding-bottom: 0;\n    }\n\n    .wp-block-cover {\n        @media (max-width:1830px) {\n            border-radius: 0 !important;\n        }\n    }\n}\n\n.home-v4-card-1,\n.home-v4-card-2 {\n    .card-banner-image {\n        height: 100%;\n        img {\n            height: 100%;\n        }\n    }\n    .wp-block-button a{\n        width: 254px;\n    }\n}\n\n.v4-single-product {\n    .product-detail {\n        opacity:0.3;\n    }\n\n    .wc-block-grid__product-image {\n        background-color: transparent;\n    }\n\n    .wp-block-post-title:hover{\n        text-decoration: none;\n    }\n\n    .wp-block-columns {\n        @media (min-width:1400px) {\n            column-gap: 150px;\n        }\n    }\n\n    .wc-block-grid__product-rating__stars {\n        margin-bottom: 0 !important;\n        color: var(--wp--preset--color--secondary) !important;\n        font-size: 11px;\n\n        &:before {\n            color: var(--wp--preset--color--secondary) !important;\n        }\n    }\n\n    .product-title a{\n        @media (min-width:1400px) {\n            border-bottom: 2px solid var(--wp--preset--color--secondary);\n        }\n    }\n\n    .wc-block-components-product-image {\n        position: relative;\n        @media (min-width:1024px) {\n            text-align: end;\n        }\n\n        img {\n            @media (min-width:1400px) {\n                width: 565px;\n                height: 650px;\n            }\n        }\n    }\n\n    figure {\n        top: 30%;\n        left: 0%;\n\n        @media (max-width:1400px) {\n            display:none;\n        }\n    }\n}\n\n.v4-product-list {\n    .wp-block-woocommerce-product-price {\n        margin-top: 0;\n    }\n\n    .wc-block-components-product-image img{\n        @media (min-width:1400px) {\n            height: 120px;\n        }\n    }\n\n    .product-list-col {\n        @media (min-width:1400px) {\n            column-gap: 123px;\n        }\n    }\n\n    .product-list-img img{\n        @media (min-width:1400px) {\n            width: 686px;\n            height: 805px;\n        }\n    }\n\n    .wc-block-grid__product-rating__stars {\n        margin-bottom: 0 !important;\n        color: var(--wp--preset--color--secondary) !important;\n        font-size: 11px;\n\n        &:before {\n            color: var(--wp--preset--color--secondary) !important;\n        }\n    }\n}\n\n\n.before-inline-img a img {\n    margin-right: 10px;\n    display: inline-block;\n    vertical-align: middle;\n}\n\n\n.v2-hero, .v3-collection, .v3-hero {\n    .wp-block-button a:hover {\n        background-color: var(--wp--preset--color--primary) !important;\n        border-color: var(--wp--preset--color--primary) !important;\n    }\n} \n\n.v2-best-seller, .v2-latest-arrivals, .home-v2-blog{\n    .before-inline-img .wp-block-button__link {\n        min-width: 98px;\n    }\n}\n\n@media only screen and (device-width: 1024px) {\n    .v2-hero .height-img img {\n        height: 600px !important; ;\n    }\n    .v2-best-skin-banner{\n        .height-img img {\n            height: 535px !important; ;\n        }\n    } \n}\n\n@media (max-width: 1024px) {\n    .height-auto img {\n        height: 100% !important;\n    }\n}\n\n@media (min-width: 1024px) {\n    .button-width, .v2-single-product-2 .wp-block-button {\n        a {\n            width: 308px;\n        }\n    }\n}\n\n@media (max-width: 767px) {\n    .flex-sm-wrap.wp-block-columns.is-not-stacked-on-mobile, .flex-sm-wrap.is-nowrap {\n            flex-wrap: wrap !important;\n    }\n    .flex-sm-wrap .wp-block-column {\n        flex-basis: 100% !important;\n    }\n}\n\n@media (max-width: 768px) {\n    .glowess-scroll {\n        flex-wrap: nowrap !important;\n        overflow-x: auto;\n        display: flex;\n        overflow-y: hidden;\n    }\n\n\n    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{\n        flex-shrink: 0;\n        flex-basis: 48% !important;\n    }\n}\n\n@media (max-width: 425px) {\n\n    .glowess-scroll {\n        flex-wrap: nowrap !important;\n        overflow-x: auto;\n        display: flex;\n        overflow-y: hidden;\n    }\n    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{\n        flex-shrink: 0;\n        flex-basis: 100% !important;\n    }\n}\n\n@media (max-width: 1024px) {\n    .glowess-scroll {\n        flex-wrap: nowrap !important;\n        overflow-x: auto;\n        display: flex;\n        overflow-y: hidden;\n    }\n    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{\n        flex-shrink: 0;\n        flex-basis: 25% !important;\n        }\n    }\n\n\n.page-template-home-v2{\n    .v2-marque-brands {\n        margin-top: 0;\n        padding-top: 33px;\n        padding-bottom: 38px;\n        border-bottom: 1px solid #D7DAD9\n    }\n    @media (min-width: 1440px) {\n        .footer-v1 {\n            margin-top: 87px !important;\n        }\n    }\n}\n\n.v1-best-selling-products, .v2-best-seller, .v2-latest-arrivals {\n    .slick-nav-wrap {\n        padding-top: 36px;\n    }\n}\n\n.mb-0 {\n    margin-bottom: 0;\n}\n\n.page-template-home-v1 {\n    .about-instagram {\n        padding-top: 36px !important;\n        @media (max-width: 768px) {\n            padding-bottom: 36px !important;\n        }\n    }\n    .v1-before-after{\n        @media (max-width: 768px) {\n            padding-top: 0 !important;\n      }\n      @media (max-width: 1024px) {\n        .pt-0, .pt-0 p{\n            padding-top: 0 !important;\n        }\n      }\n  }\n  .v2-marque-brands {\n    margin-top: 20px !important;\n    margin-bottom: var(--wp--preset--spacing--50);\n    @media (min-width: 1024px) {\n        padding-bottom: 54px;\n    }\n  }\n}\n  \n.wp-block-details summary::marker {\n    content: \"\";\n}\n\n.wp-block-details {\n    summary {\n        &::after {\n            content: \"\\F282\";\n            font-family: \"bootstrap-icons\";\n            color: #202025;\n            position: absolute;\n            right: 0;\n            font-weight: 700;\n            font-size: medium;\n       }\n    }\n    &[open] {\n        > summary {\n            &::after {\n                content: \"\\F286\";\n                font-family: \"bootstrap-icons\";\n                color: #202025;\n                font-weight: 700;\n                font-size: medium;\n            }\n        }\n    }\n}\n\n@media (max-width: 1024px) {\n   .gl-scroll {\n       flex-wrap: nowrap !important;\n       overflow-x: auto;\n       display: flex;\n       overflow-y: hidden;\n   }\n}\n@media (min-width: 768px) and (max-width: 1024px) {\n    .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {\n        flex-shrink: 0;\n        flex-basis: 30% !important;\n    }\n }\n\n@media (min-width: 768px) and (max-width: 1023px) {\n   .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {\n       flex-shrink: 0;\n       flex-basis: 50% !important;\n   }\n}\n\n@media (max-width: 767px) {\n   .wp-block-columns.gl-scroll:not(.is-not-stacked-on-mobile) > .wp-block-column {\n       flex-shrink: 0;\n       flex-basis: 100% !important;\n   }\n}\n\n\n.map-inner-box{\n    position: relative;\n    left: 11.1%;\n    top: 12.8%;\n}\n\n@media (max-width: 767px){\n\t.map-inner-box{\n\t\tposition: relative;\n    left: 10.1%;\n    width: 220px;\n\t}\n}\n@media (min-width: 768px){\n\t.contact-map .wp-block-button__link{\n\t\tmin-width:320px !important;\n\t}\n}\n\n.before-inline-img a img {\n    margin-right: 10px;\n    display: inline-block;\n    vertical-align: middle;\n}\n\n.v2-hero .wp-block-button a:hover {\n    background-color: var(--wp--preset--color--primary) !important;\n    border-color: var(--wp--preset--color--primary) !important;\n}\n\n.v2-best-seller, .v2-latest-arrivals, .home-v2-blog{\n    .before-inline-img .wp-block-button__link {\n        min-width: 98px;\n    }\n}\n\n@media (max-width: 1023px) {\n    .height-img img {\n        height: 100% !important;\n    }\n}\n\n@media (max-width: 1024px) {\n    .cover-img {\n        min-height: 430px !important;\n    }\n}\n\n@media only screen and (device-width: 1024px) {\n    .v2-hero .height-img img {\n        height: 600px !important; ;\n    }\n    .v2-best-skin-banner{\n        .height-img img {\n            height: 535px !important; ;\n        }\n    } \n}\n@media (max-width: 1024px) {\n    .height-auto img {\n        height: 100% !important;\n    }\n}\n@media (min-width: 1024px) {\n    .button-width a {\n        width: 308px;\n    }\n}\n\n@media (max-width: 767px) {\n    .flex-sm-wrap.wp-block-columns.is-not-stacked-on-mobile, .flex-sm-wrap.is-nowrap {\n            flex-wrap: wrap !important;\n    }\n    .flex-sm-wrap .wp-block-column {\n        flex-basis: 100% !important;\n    }\n}\n\n@media (max-width: 768px) {\n    .glowess-scroll {\n        flex-wrap: nowrap !important;\n        overflow-x: auto;\n        display: flex;\n        overflow-y: hidden;\n    }\n\n    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{\n        flex-shrink: 0;\n        flex-basis: 48% !important;\n    }\n}\n\n@media (max-width: 425px) {\n    .glowess-scroll {\n        flex-wrap: nowrap !important;\n        overflow-x: auto;\n        display: flex;\n        overflow-y: hidden;\n    }\n\n    .wp-block-columns.glowess-scroll:not(.is-not-stacked-on-mobile)>.wp-block-column{\n        flex-shrink: 0;\n        flex-basis: 100% !important;\n    }\n}\n\n.page-template-home-v2{\n    .v2-marque-brands {\n        margin-top: 0;\n        padding-top: 33px;\n        padding-bottom: 38px;\n        border-bottom: 1px solid #D7DAD9\n    }\n    .v2-best-seller, .v2-latest-arrivals{\n        .slick-nav-wrap {\n            padding-top: 36px;\n        }\n    }\n}\n\n.image-width img {\n    width: 100% !important;\n}\n\n.v2-testimonial {\n    @media (max-width: 425px) {\n        .image-height img {\n            height: 100% !important;\n        }\n    }\n    @media (max-width: 768px) {\n        .image-height {\n            float: left !important;\n            margin-left: 0 !important;\n            img {\n                width: 100% !important;\n            }\n        }\n    }\n\n    .gl-slick-single {\n        .slick-list {\n            margin: 0;\n        }\n\n        .slick-slide > div {\n            padding: 0;\n        }\n    }\n\n    .slick-prev:before, .slick-next:before {\n        width: 35px;\n        height: 34px;\n    }\n\n    .slick-prev {\n        left: 0;\n\n        &::before {\n            background-image: url('data:image/svg+xml,<svg width=\"35\" height=\"34\" viewBox=\"0 0 35 34\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M35 17H2.02708\" stroke=\"%23131C19\"/><path d=\"M8.96808 24.8094C7.02916 20.5422 5.49308 18.7508 1.66599 17.0118C5.57849 15.086 7.09716 13.288 8.96808 9.19067\" stroke=\"%23131C19\"/></svg>');\n        }\n    }\n\n    .slick-next {\n        left: 65px;\n        right: auto;\n        \n        &::before {\n            background-image: url('data:image/svg+xml,<svg width=\"35\" height=\"34\" viewBox=\"0 0 35 34\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 17H32.9729\" stroke=\"%23131C19\"/><path d=\"M26.0319 24.8094C27.9708 20.5422 29.5069 18.7508 33.334 17.0118C29.4215 15.086 27.9028 13.288 26.0319 9.19067\" stroke=\"%23131C19\"/></svg>');\n        }\n    }\n\n    .slick-prev, .slick-next {\n        top: auto;\n        transform: none;\n        margin: 0;\n        bottom: 60px;\n    }\n}\n\n.v1-testimonial {\n    .slider-nav {\n        .slick-slide {\n            img {\n                width: 80px;\n                height: 80px;\n                border-radius: 30px;\n                transition: all 0.4s ease-in-out;\n                opacity: .6;\n                cursor: pointer;\n            }\n    \n            &.current-before,\n            &.current-after {\n                img {\n                    width: 100px;\n                    height: 100px;\n                    border-radius: 35px;\n                }\n            }\n    \n            &.slick-current {\n                img {\n                    width: 120px;\n                    height: 120px;\n                    border-radius: 40px;\n                    opacity: 1;\n                }\n            }\n        }\n    }\n\n    .slider-nav {\n        .slick-track {\n            display: flex;\n            align-items: center;\n        }\n\n        .slick-list {\n            padding: 0 !important;\n        }\n\n        .slick-slide > div {\n            padding: 0 14px;\n\n            figure {\n                display: flex !important;\n                justify-content: center;\n            }\n        }\n    }\n\n    .slick-prev:before, .slick-next:before {\n        width: 35px;\n        height: 34px;\n    }\n\n    .slick-prev:before {\n        background-image: url('data:image/svg+xml,<svg width=\"35\" height=\"34\" viewBox=\"0 0 35 34\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M35 17H2.02708\" stroke=\"%23131C19\"/><path d=\"M8.96808 24.8094C7.02916 20.5422 5.49308 18.7508 1.66599 17.0118C5.57849 15.086 7.09716 13.288 8.96808 9.19067\" stroke=\"%23131C19\"/></svg>');\n    }\n\n    .slick-next:before {\n        background-image: url('data:image/svg+xml,<svg width=\"35\" height=\"34\" viewBox=\"0 0 35 34\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 17H32.9729\" stroke=\"%23131C19\"/><path d=\"M26.0319 24.8094C27.9708 20.5422 29.5069 18.7508 33.334 17.0118C29.4215 15.086 27.9028 13.288 26.0319 9.19067\" stroke=\"%23131C19\"/></svg>');\n    }\n }\n\n .home-v3-love-skin {\n\t@media (min-width: 1540px){\n        .row {\n\t\t\tcolumn-gap: 147px;\n\t\t}\n\t}\n\t@media (min-width: 1440px) {\n\t\t\n\t\t.slick-nav-wrap{\n\t\t\tposition: absolute;\n\t\t\tright: -6.2%;\n\t\t\trotate: 90deg;\n\t\t\ttop: 41%;\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n\t@media only screen and (device-width: 1024px) {\n\t\t.slick-nav-wrap{\n\t\t\tposition: absolute;\n\t\t\tright: -10.2%;\n\t\t\trotate: 90deg;\n\t\t\ttop: 41%;\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n\t\n\t.wc-block-components-product-rating__stars {\n        margin-bottom: 0 !important;\n    }\n\t@media (max-width: 425px) {\n\t\t.pl-0{\n\t\t\t\tpadding-left:0 !important;\n\t\t}\n\t}\n}\n\n@media (max-width: 1023px) {\n    .height-img img {\n        height: 100%!important;\n    }\n}\n\n.glowess-list {\n    padding: 0;\n    display: flex;\n    overflow-x: auto;\n    gap: 30px;\n    justify-content: center;\n    list-style: none;\n    li {\n        text-align: center;\n    }\n\tli.active a, li a:hover {\n        color: var(--wp--preset--color--secondary) !important;\n\t\ttext-decoration: underline !important;\n    }\n}\n\n.cat-img-gradient .wp-block-cover__background {\n\tbackground: linear-gradient(180deg, rgba(88, 130, 116, 0) 0%, rgba(19, 28, 25, 0.4) 100%);\n\tbackground-color: transparent !important;\n\topacity: .8 !important;\n}\n\n.v3-collection {\n\t@media (min-width: 1024px) {\n\t\t.wp-block-button a{\n\t\t\tmin-width: 308px !important;\n\t\t}\n\t}\n}\n\n.v3-hero{\n    .position-image-left {\n        @media (min-width: 1440px) {\n            position: absolute;\n            bottom: 0;\n            top: 71.5%;\n            z-index: 9999;\n            right: -28.5%;\n            }\n\t}\n    .position-image-right {\n        @media (min-width: 1440px) {\n            position: relative;\n            bottom: 0px;\n            left: -14.5%;\n            top: -2%;\n            right: 0%;\n            .ml-22 {\n                margin-left: 8px !important;\n            }\n        }\n    }\n}\n\n.page-template-home-v3 {\n    .v1-product-categories {\n        padding-top: 44px !important;\n    }\n    .v1-testimonial {\n        margin-bottom: 141px !important;\n    }\n    .about-instagram {\n        padding-top: 27px !important;\n        padding-bottom: 55px !important;\n    }\n    .home-blog .wp-block-post-featured-image img {\n        border-radius: 12px !important;\n    }\n    .footer-v3 {\n        .wpforms-container-full {\n            margin-top: 0 !important;\n            @media (min-width:1448px) {\n                width: 400px;\n            }\n        }\n    }\n}\n \n\n@media (max-width:767px) {\n    .shop-best-skin .content-wrap {\n\t\tpadding-top: 30px;\n\t}\n}\n\n.woo-sctr-shortcode-wrap-wrap {\n\t@media (max-width:1023px) {\n\t\tdisplay: none !important;\n\t}\n}\n.woo-sctr-shortcode-countdown-2 {\n\tjustify-content: start !important;\n\talign-items: start !important; \n}\n.woo-sctr-shortcode-wrap-wrap {\n\ttext-align: left !important;\n}\n.woo-sctr-shortcode-countdown-text-before {\n\tdisplay: none;\n}\n.woo-sctr-shortcode-countdown-value {\n\tfont-family: Sen;\n\tfont-weight: 500;\n\tline-height: 48px;\n}\n.woo-sctr-shortcode-countdown-text {\n\tfont-family: Sen;\n\ttext-transform: uppercase;\n}\n.woo-sctr-shortcode-countdown-unit {\n\tpadding: 0;\n}\n@media (min-width:1134px) {\n\t.woo-sctr-shortcode-countdown-unit-wrap {\n\t    margin: 0 12px !important;\n\t}\n}\n.woo-sctr-shortcode-countdown-unit-wrap:first-child {\n\tmargin-left: 0 !important;\n}\n.woo-sctr-shortcode-wrap-wrap-1 {\n    margin-top: 8px !important;\n}\n.v2-best-seller {\n\t.woo-sctr-shortcode-countdown-value, .woo-sctr-shortcode-countdown-text {\n\t\tcolor:  var(--wp--preset--color--ebony) !important;\n\t} \n}\n", "/*\n################\n* === Header STYLE  ===\n################\n*/\n\n.search-toggle {\n    a {display: flex;}\n\n    &.active {\n        a {\n            &::after {\n                font-family: \"bootstrap-icons\";\n                content: \"\\F62A\";\n                font-size: 22px;\n                position: relative;\n                top: -2px;\n                left: -3px;\n                line-height: 1;\n                width: 17px;\n                height: 17px;\n            }\n\n            img {display: none;}\n        }\n    }\n}\n\n.primary-menu {\n    > .wp-block-navigation-item {\n        &:is(:hover){\n            > .wp-block-navigation-item__content {\n                text-decoration: underline;\n                text-underline-offset: 9px;\n            }\n        }\n\n        > .wp-block-navigation-item__content {\n            text-transform: uppercase;\n        }\n    }\n}\n\nbody:not(.editor-styles-wrapper) {\n    .mobile-header {\n        @media (min-width:1200px) {\n            display: none;\n        }\n    }\n\n    .desktop-header {\n        @media (max-width:1199px) {\n            display: none;\n        }\n    }\n}\n\nbody:not(.scrolling-active) {\n    .wp-block-template-part.transparent {\n        @media (min-width:1200px) {\n            position: absolute;\n            width: 100%;\n            z-index: 3;\n        }\n\n        > .header-transparent:not(.semi) {\n            .desktop-header {\n                .wp-block-site-title,\n                .primary-menu {\n                    color: var(--wp--preset--color--base);\n                }\n    \n                .brand-logo {\n                    img {\n                        filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);\n                    }\n                }\n            }\n\n            &.header-v1 {\n                .desktop-header {\n                    .search-toggle.active a::after {\n                        color: var(--wp--preset--color--base);\n                    }\n\n                    ul.catalog-menu > .wp-block-navigation-item:is(:hover) > .wp-block-navigation-item__content {\n                        color: var(--wp--preset--color--base) !important;\n                    }\n\n                    .wp-block-site-title,\n                    .wp-block-site-title a,\n                    .primary-menu,\n                    .catalog-menu,\n                    .currency {\n                        color: var(--wp--preset--color--base) !important;\n                    }\n\n                    > .wp-block-group:last-child {\n                        border-bottom-color: rgba(from var(--wp--preset--color--base)  r g b / .3) !important;\n                    }\n\n                    .search-toggle img {\n                        filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);\n                    }\n\n                    .wp-block-woocommerce-customer-account[data-display-style=icon_only] a::after {\n                        background-image: url('data:image/svg+xml,<svg width=\"14\" height=\"18\" viewBox=\"0 0 14 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.99984 1.94855C5.45584 1.94855 4.20414 3.19221 4.20414 4.72634C4.20414 6.26045 5.45584 7.50409 6.99984 7.50409C8.54384 7.50409 9.79551 6.26045 9.79551 4.72634C9.79551 3.19221 8.54384 1.94855 6.99984 1.94855ZM2.91382 4.72634C2.91382 2.48415 4.74319 0.666504 6.99984 0.666504C9.25651 0.666504 11.0858 2.48415 11.0858 4.72634C11.0858 6.96851 9.25651 8.78617 6.99984 8.78617C4.74319 8.78617 2.91382 6.96851 2.91382 4.72634Z\" fill=\"%23fff\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.8884 11.7776C3.08529 11.7776 1.62358 13.2299 1.62358 15.0215C1.62358 15.1156 1.64088 15.182 1.65902 15.2207C1.67433 15.2533 1.68747 15.2626 1.69843 15.2685C2.19982 15.5396 3.6056 16.0511 6.99992 16.0511C10.3943 16.0511 11.8 15.5396 12.3014 15.2685C12.3123 15.2626 12.3255 15.2533 12.3408 15.2207C12.359 15.182 12.3763 15.1156 12.3763 15.0215C12.3763 13.2299 10.9146 11.7776 9.11142 11.7776H4.8884ZM0.333252 15.0215C0.333252 12.5219 2.37266 10.4956 4.8884 10.4956H9.11142C11.6272 10.4956 13.6666 12.5219 13.6666 15.0215C13.6666 15.4804 13.4994 16.0804 12.9182 16.3946C12.1482 16.8109 10.4965 17.3332 6.99992 17.3332C3.50337 17.3332 1.85164 16.8109 1.08169 16.3946C0.500444 16.0804 0.333252 15.4804 0.333252 15.0215Z\" fill=\"%23fff\"/></svg>');\n                    }\n\n                    .wc-block-mini-cart:not([data-mini-cart-icon=bag], [data-mini-cart-icon=bag-alt]) .wc-block-mini-cart__quantity-badge::before {\n                        background-image: url('data:image/svg+xml,<svg width=\"19\" height=\"18\" viewBox=\"0 0 19 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M0.833252 1.29941C0.833252 0.949871 1.12217 0.666504 1.47856 0.666504H2.11048C3.30377 0.666504 4.31032 1.538 4.45833 2.69934L4.62945 4.04203H16.8273C17.7478 4.04203 18.4528 4.84502 18.3163 5.7379L17.3938 11.7703C17.22 12.9063 16.225 13.7467 15.0538 13.7467H6.83234C5.66112 13.7467 4.66614 12.9063 4.4924 11.7703L3.42162 4.76883L3.41946 4.75345L3.17768 2.85634C3.1104 2.32846 2.65288 1.93233 2.11048 1.93233H1.47856C1.12217 1.93233 0.833252 1.64896 0.833252 1.29941ZM4.80911 5.30785L5.76874 11.5825C5.8477 12.0988 6.29997 12.4808 6.83234 12.4808H15.0538C15.5862 12.4808 16.0384 12.0988 16.1174 11.5825L17.0399 5.55012C17.0594 5.42256 16.9588 5.30785 16.8273 5.30785H4.80911Z\" fill=\"%23fff\"/><path d=\"M16.1056 16.4893C16.1056 16.9554 15.7203 17.3333 15.2452 17.3333C14.77 17.3333 14.3848 16.9554 14.3848 16.4893C14.3848 16.0233 14.77 15.6455 15.2452 15.6455C15.7203 15.6455 16.1056 16.0233 16.1056 16.4893Z\" fill=\"%23fff\"/><path d=\"M7.50133 16.4893C7.50133 16.9554 7.11612 17.3333 6.64093 17.3333C6.16573 17.3333 5.78052 16.9554 5.78052 16.4893C5.78052 16.0233 6.16573 15.6455 6.64093 15.6455C7.11612 15.6455 7.50133 16.0233 7.50133 16.4893Z\" fill=\"%23fff\"/></svg>');\n                    }\n\n                    .wc-block-mini-cart__badge {\n                        background-color: var(--wp--preset--color--base);\n                        color: var(--wp--preset--color--secondary);\n                    }\n\n                    ul.catalog-menu > .wp-block-navigation-item:first-child > .wp-block-navigation-submenu__toggle {            \n                        &::after {\n                            background-color: rgba(from var(--wp--preset--color--base)  r g b / .3);\n                        }\n                    }\n\n                    ul.currency > .wp-block-navigation-item > .wp-block-navigation-item__content::before {\n                        background-image: url('data:image/svg+xml,<svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_43_361)\"><path d=\"M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z\" fill=\"white\"/></g><defs><clipPath id=\"clip0_43_361\"><rect width=\"18\" height=\"18\" fill=\"white\"/></clipPath></defs></svg>');\n                    }\n\n                    ul.catalog-menu {\n                        > .wp-block-navigation-item:first-child {\n                            > .wp-block-navigation-item__content {                    \n                                &::before {\n                                    background-image: url('data:image/svg+xml,<svg width=\"19\" height=\"10\" viewBox=\"0 0 19 10\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect y=\"0.25\" width=\"19\" height=\"1\" fill=\"white\"/><rect y=\"8.75\" width=\"14\" height=\"1\" fill=\"white\"/></svg>');\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n\n#myModal {\n    position: absolute;\n    right: 0;\n    left: 0;\n    z-index: 999;\n    margin: 0;\n}\n\n.search-cat-btn {\n    > .wp-block-group:not(:hover) {\n        border-color: rgb(from var(--wp--preset--color--base) r g b / .2) !important;\n    }\n\n    > .wp-block-group {\n        position: relative;\n        transition: all .15s ease-in;\n\n        a {\n            &::before {\n                position: absolute;\n                content: \"\";\n                inset: 0;\n            }\n        }\n    }\n}\n\n.search-box {\n    input[type=search]{\n        border-width: 0;\n        padding: 13px 20px !important;\n\n        &, &::placeholder {\n            color:rgba(from var(--wp--preset--color--secondary)  r g b / .75);\n        }\n\n        &, &:is(:focus, :focus:invalid) {\n            background-color: transparent;\n            border-width: 0;\n            box-shadow: none;\n            color: var(--wp--preset--color--base);\n        }\n    }\n\n    > .wp-block-group {\n        @media (max-width:767px) {\n            padding-top: 20px !important;\n            padding-bottom: 40px !important;\n\n            .search-cat-btn {\n                margin-top: 30px !important;\n            }\n        }\n    }\n\n    :where(.wp-element-button, .wp-block-button__link) {\n        border-width: 0;\n        min-width: auto;\n\n        svg {display: none;}\n\n        &::after {\n            background-image: url('data:image/svg+xml,<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_117_4490)\"><path d=\"M0 9.52393H18.8417\" stroke=\"white\"/><path d=\"M14.8755 13.9863C15.9834 11.5479 16.8612 10.5242 19.0481 9.53047C16.8124 8.43004 15.9446 7.40261 14.8755 5.06128\" stroke=\"white\"/></g><defs><clipPath id=\"clip0_117_4490\"><rect width=\"20\" height=\"19.0476\" fill=\"white\"/></clipPath></defs></svg>');\n            content: \" \";\n            width: 20px;\n            height: 20px;\n            display: block;\n        }\n    }\n}\n\nbody:not(.editor-styles-wrapper) {\n    .search-box {\n        transition: all .15s ease-in;\n        transform: translate(0, -10px);\n        padding: 0 clamp(0px, 4vw, 60px);\n    \n        &:not(.flex) {\n            visibility: hidden;\n            opacity: 0;\n        }\n    \n        &.flex {\n            //display: flex !important;\n            transform: translate(0, 0);\n        }\n    \n        > .wp-block-group {\n            width: 100%;\n        }\n    }\n}\n\nul.catalog-menu {\n    > .wp-block-navigation-item:first-child {\n        > .wp-block-navigation-item__content {\n            display: flex;\n            align-items: center;\n            position: relative;\n\n            &::before {\n                content: \" \";\n                display: block;\n                width: 20px;\n                height: 10px;\n                margin-right: 10px;\n                background-image: url('data:image/svg+xml,<svg width=\"19\" height=\"10\" viewBox=\"0 0 19 10\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect y=\"0.25\" width=\"19\" height=\"1\" fill=\"%23131C19\"/><rect y=\"8.75\" width=\"14\" height=\"1\" fill=\"%23131C19\"/></svg>');\n                //background-image: url('data:image/svg+xml,<svg width=\"19\" height=\"10\" viewBox=\"0 0 19 10\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect y=\"0.25\" width=\"19\" height=\"1\" fill=\"white\"/><rect y=\"8.75\" width=\"14\" height=\"1\" fill=\"white\"/></svg>');\n                background-repeat: no-repeat;\n            }\n        }\n\n        > .wp-block-navigation-submenu__toggle {\n            font-size: 0;\n            position: relative;\n            pointer-events: none;\n\n            &::after {\n                content: \" \";\n                display: block;\n                width: 1px;\n                height: 30px;\n                //background-color: rgba(from var(--wp--preset--color--base)  r g b / .3);\n                background-color:var(--wp--preset--color--gray-100) ;\n                position: absolute;\n                right: -31px;\n                transform: translateY(-14px);\n\n                @media (min-width:1200px) and (max-width:1599px) {\n                    right: -16px;\n                }\n            }\n        }\n    }\n\n    .cat-img {\n        > .wp-block-navigation-item__content {display: none !important;}\n    }\n\n    ul.cat-img {\n        flex-direction: row !important;\n        gap: 30px;\n\n        > * {\n            flex-grow: 1;\n            width: 50%;\n\n            a {\n                width: 366px;\n                padding: 0 !important;\n\n                span {\n                    display: flex;\n                }\n            }\n        }\n    }\n\n    > .wp-block-navigation-item:is(:hover) > .wp-block-navigation-item__content {\n        color: var(--wp--preset--color--secondary) !important;\n    }\n\n    .nav-title {\n        &:hover > .wp-block-navigation-item__content,\n        > .wp-block-navigation-item__content {\n            color: rgb(from var(--wp--preset--color--base) r g b / .5) !important;\n        }\n\n        > .wp-block-navigation-item__content {\n            padding-top: 0 !important;\n        }\n\n        ul.wp-block-navigation__submenu-container {\n            display: block;\n            column-count: 2;\n            padding-right: 30px !important;\n            max-width: 330px;\n            min-width: 330px !important;\n        }\n    }\n\n    > .wp-block-navigation-item {\n        .wp-block-navigation-item__content {\n            padding: 10px 0;\n        }\n\n        > .wp-block-navigation__submenu-container {\n            right: calc(0px + 10px);\n            flex-direction: row;\n            align-items: flex-start;\n\n            @media (min-width:1700px) {\n                right: calc(0px + 406px);\n            }\n\n            > .wp-block-navigation-item {\n                display: block;\n\n                .wp-block-navigation__submenu-icon {display: none;}\n            }\n\n            > * {\n                flex-grow: 1;\n\n                &:first-child {\n                    width: 40%;\n                }\n\n                &:last-child {\n                    width: 60%;\n                    overflow: hidden;\n                }\n            }\n\n            .wp-block-navigation__submenu-container {\n                position: relative;\n                opacity: 1;\n                visibility: visible;\n                height: auto;\n                width: auto;\n                left: 0;\n                padding: 0 !important;\n                box-shadow: none !important;\n                min-width: 100%;\n                border-width: 0 !important;\n                top: 0;\n\n                .wp-block-navigation-item__content {\n                    padding: 0 0 10px 0;\n                    font-family: var(--wp--preset--font-family--body);\n                }\n            }\n        }\n    }\n}\n\n.catalog-menu {\n    &,\n    .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open),\n    .wp-block-navigation__responsive-dialog,\n    > .wp-block-navigation-item {\n        position: static;\n    }\n}\n\nul.currency {\n    > .wp-block-navigation-item {\n        > .wp-block-navigation-item__content {\n            display: flex;\n            align-items: center;\n\n            &::before {\n                content: \" \";\n                display: block;\n                width: 18px;\n                height: 18px;\n                margin-right: 10px;\n                background-repeat: no-repeat;\n                background-image: url('data:image/svg+xml,<svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_43_361)\"><path d=\"M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z\" fill=\"%23131C19\"/></g><defs><clipPath id=\"clip0_43_361\"><rect width=\"18\" height=\"18\" fill=\"%23131C19\"/></clipPath></defs></svg>');\n                //background-image: url('data:image/svg+xml,<svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_43_361)\"><path d=\"M16.8888 4.99206C14.823 0.845464 9.76622 -0.988736 5.51342 0.855065C4.98362 1.09146 5.32741 1.90386 5.86802 1.68246C9.52682 0.100265 13.857 1.52706 15.8556 4.97706L13.8048 6.11406C12.798 6.67566 12.4374 7.94646 12.999 8.95327C13.0644 9.06126 13.0842 9.19147 13.0494 9.31326C13.0188 9.42307 12.9456 9.51606 12.846 9.57126L9.84122 11.2513C8.82722 11.7991 8.44862 13.0657 8.99701 14.0797C9.55262 15.1129 10.8606 15.4831 11.8758 14.8963L12.5802 14.5027C12.8382 14.3491 13.2174 14.4931 13.2786 14.7949L13.4586 15.5749C9.34921 18.4351 3.42722 16.6027 1.62782 11.9401L6.99602 8.94606C9.37142 7.54326 7.40102 4.02366 4.96202 5.30406L3.99482 5.84406C3.87662 5.90286 3.73322 5.86566 3.65882 5.75646C3.37982 5.23626 2.88722 4.85946 2.31542 4.71666C2.56922 4.26846 2.90342 3.87366 3.25562 3.49926C3.42722 3.31986 3.42122 3.03486 3.24122 2.86266C2.59862 2.38086 2.11682 3.54786 1.74422 3.93066C-3.11758 10.9201 3.87962 20.1769 11.9394 17.3347C16.9008 15.6511 19.359 9.64207 16.8888 4.99206ZM1.83902 5.57047C2.26022 5.57287 2.66162 5.80326 2.85782 6.17826C3.16862 6.73506 3.86882 6.93966 4.43042 6.63786L5.40422 6.08886C6.76082 5.32146 7.93742 7.32126 6.60662 8.13426C6.55502 8.16967 1.39382 11.0371 1.34102 11.0707C0.831615 9.23767 1.00862 7.28166 1.83902 5.57047ZM14.2416 14.9539L14.2404 14.9581C14.1336 13.8781 13.1484 13.1923 12.1422 13.7149L11.4378 14.1079C10.0596 14.8135 8.92982 12.8281 10.2804 12.0343L13.2852 10.3543C13.9296 10.0135 14.1606 9.13326 13.7856 8.51287C13.4658 7.94046 13.671 7.21746 14.2428 6.89767L16.2708 5.77386C17.685 8.94726 16.8612 12.6721 14.2416 14.9539Z\" fill=\"white\"/></g><defs><clipPath id=\"clip0_43_361\"><rect width=\"18\" height=\"18\" fill=\"white\"/></clipPath></defs></svg>');\n            }\n        }\n\n        .wp-block-navigation__submenu-icon {\n            font-size: 16px;\n            margin-left: 10px;\n        }\n    }\n}\n\n.header-v1,\n.header-v5 {\n    .desktop-header {\n        ul.primary-menu {\n            @media (min-width:1200px) and (max-width:1599px) {\n                gap: 10px !important;\n            }\n        }\n\n        .cat-pri-nav {\n            @media (min-width:1200px) and (max-width:1599px) {\n                gap: 30px !important;\n\n                nav.catalog-menu::after {\n                    right: -15px;\n                }\n            }\n        }\n    }\n}\n\n.header-default,\n.header-v2 {\n    .desktop-header ul.wp-block-navigation > .wp-block-navigation-item.has-mega-menu > .wp-block-navigation__submenu-container {\n        top: calc(100% - 3px);\n    }\n}\n\n.header-v3 {\n    .desktop-header ul.wp-block-navigation > .wp-block-navigation-item.has-mega-menu > .wp-block-navigation__submenu-container {\n        top: calc(100% + 20px);\n    }\n}\n\n.header-v4 {\n    nav.catalog-menu::after {\n        display: none;\n    }\n\n    .desktop-header {\n        ul.primary-menu {\n            @media (min-width:1200px) and (max-width:1365px) {\n                gap: 15px !important;\n            }\n        }\n    }\n}\n\n.desktop-header {\n    ul.catalog-menu {\n        > .wp-block-navigation-item:is(:hover) {\n            > .wp-block-navigation__submenu-container {\n                padding: 58px 60px !important;\n            }\n        }\n    }\n\n    nav.primary-menu {\n        &, .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open),\n        .wp-block-navigation__responsive-dialog,\n        ul.wp-block-navigation.primary-menu,\n        .wp-block-navigation .wp-block-navigation-item.has-mega-menu {\n            position: static;\n        }\n    }\n\n    .has-mega-menu.wp-block-navigation__submenu-container {\n        flex-direction: row;\n        left: 0;\n        right: 0;\n        align-items: flex-start;\n\n        > * {\n            flex-grow: 1;\n            width: 100%;\n        }\n\n        .menu-img {\n            .wp-block-navigation-item__content {\n                width: 266px;\n                padding: 0;\n\n                span {\n                    display: flex;\n                }\n\n                @media (min-width:1500px) {\n                    width: 366px;\n                }\n            }\n        }\n\n        .wp-block-navigation-item__content {\n            padding: 0 0 10px 0;\n        }\n\n        .nav-title {\n            &:hover > .wp-block-navigation-item__content, > .wp-block-navigation-item__content {\n                color: rgb(from var(--wp--preset--color--base) r g b / .5);\n                cursor: unset;\n                line-height: 1.2;\n            }\n\n            > .wp-block-navigation-item {\n                .wp-block-navigation-item__content {\n                    font-family: var(--wp--preset--font-family--body);\n                }\n            }\n\n            li.nav-title {\n                margin-top: 40px;\n            }\n        }\n    }\n\n    ul.wp-block-navigation {\n        .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-icon {\n            margin-right: 30px;\n        }\n\n        .wp-block-navigation__submenu-container {\n            .wp-block-navigation-item:not(.nav-title) {\n                &:is(:hover) {\n                    > .wp-block-navigation-item__content,\n                    > .wp-block-navigation__submenu-icon {\n                        color: var(--wp--preset--color--primary);\n                    }\n                }\n            }\n        }\n\n        > .wp-block-navigation-item {\n            > .wp-block-navigation__submenu-container {\n                top: calc(100% + 20px);   \n            }\n\n            &.has-mega-menu {\n                > .wp-block-navigation__submenu-container {\n                    top: calc(100% - 14px);   \n                }\n\n                .wp-block-navigation__submenu-container {\n                    >.wp-block-navigation-item {\n                        >.wp-block-navigation-item__content {\n                            flex-grow: 0;\n                        }\n\n                        .wp-block-navigation__submenu-icon {\n                            display: none;\n                        }\n                    }\n                }\n\n                .has-child:not(.open-on-click):hover > .wp-block-navigation__submenu-container {\n                    min-width: 100%;\n                }\n\n                &:is(:hover) {\n                    .has-mega-menu {\n                        padding: 30px;\n\n                        @media (min-width:1500px) {\n                            padding: 58px 60px 60px 60px;\n                        }\n                    }\n                }\n                \n                .wp-block-navigation-item {\n                    border-width: 0;\n                    flex-direction: column;\n                    align-items: flex-start;\n    \n                    .wp-block-navigation__submenu-container {\n                        position: relative;\n                        opacity: 1;\n                        visibility: visible;\n                        height: auto;\n                        width: auto;\n                        left: 0;\n                        padding: 0 !important;\n                        box-shadow: none !important;\n                        min-width: 100%;\n                        border-width: 0;\n                    }\n                }\n            }\n\n            .wp-block-navigation__submenu-container {\n                padding: 12px 0;\n                border-top: 2px solid var(--wp--preset--color--primary);\n                border-width: 2px 0 0 0;\n            }\n\n            &:is(:hover) {\n                &.has-child {\n                    > .wp-block-navigation-item__content {\n                        position: relative;\n    \n                        &::after {\n                            position: absolute;\n                            content: \" \";\n                            top: 100%;\n                            height: 30px;\n                            width: calc(100% + 20px);\n                            left: 0;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    ul.catalog-menu {\n        > .wp-block-navigation-item {\n            > .wp-block-navigation__submenu-container {\n                top: calc(100% - 14px);   \n            }\n        }\n    }\n\n    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-item a:not(.wp-element-button)), \n    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu a:not(.wp-element-button)), \n    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-submenu button.wp-block-navigation-item__content), \n    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-pages-list__item button.wp-block-navigation-item__content) {\n        padding: 10px 29px;\n    }\n\n    :where(.wp-block-navigation .wp-block-navigation__submenu-container .wp-block-navigation-item.menu-img a:not(.wp-element-button)) {\n        padding: 0;\n    }\n\n    .wp-block-navigation .has-child .wp-block-navigation-submenu__toggle[aria-expanded=true]~.wp-block-navigation__submenu-container, \n    .wp-block-navigation .has-child:not(.open-on-click):hover>.wp-block-navigation__submenu-container, \n    .wp-block-navigation .has-child:not(.open-on-click):not(.open-on-hover-click):focus-within>.wp-block-navigation__submenu-container {\n        min-width: 300px;\n    }\n}\n\n.header-v3 {\n    .wp-block-site-title a {\n        color: var(--wp--preset--color--secondary) !important;\n    }\n}\n\n.header-v5 {\n    .wp-block-site-title a {\n        color: var(--wp--preset--color--primary) !important;\n    }\n}", "/*\n################\n* === Mobile Header STYLE  ===\n################\n*/\n\n.mobile-header {\n    .wp-block-navigation__responsive-container-content {\n        > ul > li {\n            border-bottom: 1px solid var(--wp--preset--color--gray-100);\n            padding-bottom: 6px;\n        }\n    }\n\n    .wp-block-navigation:not(.has-text-color) .wp-block-navigation__responsive-container.is-menu-open {\n        color: var(--wp--preset--color--contrast);\n    }\n\n    .wp-block-navigation__responsive-container-close {\n        top: 10px;\n        right: 14px;\n        z-index: 3;\n        left: auto;\n        padding: 4px;\n        border-radius: 550px;\n        background-color: var(--wp--preset--color--danger);\n        color: var(--wp--preset--color--base);\n        opacity: 1;\n\n        svg {\n            width: 20px;\n            height: 20px;\n        }\n\n        &:hover {\n            background-color: var(--wp--preset--color--danger);\n        }\n\n        &:not(:hover) {\n            opacity: 1;\n        }\n    }\n\n    .wp-block-navigation .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container:before {\n        display: none;\n    }\n\n    .wp-block-navigation__submenu-container {\n        transform: none !important;\n    }\n\n    .wp-block-navigation-submenu__toggle[aria-expanded=true] ~ .wp-block-navigation__submenu-container {\n        display: flex;\n    }\n\n    .wp-block-navigation-submenu__toggle[aria-expanded=false] ~ .wp-block-navigation__submenu-container:not(.hide),\n    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation-item__content,\n    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation-submenu__toggle {\n        display: none;\n    }\n\n    .wp-block-navigation .has-child .wp-block-navigation__submenu-container>.wp-block-navigation-item.hide>.wp-block-navigation__submenu-container {\n        padding-top: 0;\n    }\n\n    .wp-block-navigation__responsive-dialog {\n        padding: 3.6rem 1rem 1rem 1rem;\n        background-color: var(--wp--preset--color--base);\n    }\n\n    .wp-block-navigation {\n        .has-child.has-mega-menu {\n            > .wp-block-navigation__submenu-container {\n                flex-direction: column;\n                position: relative;\n                top: 0;\n    \n                > .wp-block-navigation-item > .wp-block-navigation-item__content {\n                    font-weight: 500;\n                }\n    \n                .wp-block-navigation__submenu-container {\n                    padding: 1rem;\n                    margin-bottom: 10px;\n                }\n            }\n        }\n        \n        .wp-block-navigation-item .wp-block-navigation__submenu-container {\n            @media (max-width:1023px) {\n                border-radius: 8px;\n                box-shadow: 0 4px 4px rgba(0, 0, 0, 0.04);\n            }\n        }\n    }\n    \n    .has-mega-menu {\n        .has-child .wp-block-navigation__submenu-container .wp-block-navigation__submenu-container {\n            padding: 1rem;\n        }\n\n        .wp-block-navigation__submenu-container .mega-menu-img.wp-block-navigation-item:last-child .wp-block-navigation-item__label {\n            align-items: flex-start;\n        }\n    }\n\n    .wp-block-navigation__responsive-container{\n        .wp-block-navigation__container {\n            width: 100%;\n        }\n\n        &.is-menu-open {\n            .wp-block-navigation__responsive-container-content {\n                padding-top: 0;\n                background-color: var(--wp--preset--color--base);\n\n                .has-child:not(.off-canvas-menu-head) .wp-block-navigation__submenu-container {\n                    padding: 16px 12px;\n                    width: 100%;\n                    border: 1px solid var(--wp--preset--color--gray-100);\n                    margin-top: 10px;\n                    margin-bottom: 8px;\n                    gap:6px;\n                }\n\n                .wp-block-navigation-item__content {\n                    padding: 2px 10px;\n                }\n\n                .wp-block-navigation__submenu-container {\n                    padding-top: 16px;\n                    padding-bottom: 18px;\n                }\n\n                .wp-block-navigation__container {\n                    gap: 6px;\n                    font-size: 14px;\n\n                    > .wp-block-navigation-item {\n                        > .wp-block-navigation-item__content {\n                            padding: 2px 0;\n                            color: var(--wp--preset--color--contrast);   \n                        }\n\n                        s {\n                            text-decoration: none;\n                            background-color: var(--wp--preset--color--danger);\n                            font-size: 12px;\n                            font-weight: 400;\n                            padding: 4px 13px;\n                            border-radius: 14px;\n                            color: var(--wp--preset--color--base);\n                            line-height: 1;\n                            letter-spacing: -0.4px;\n                            margin-left: 4px;\n                        }\n                    }\n                }\n\n                .open-on-click .wp-block-navigation-submenu__toggle {\n                    width: 100%;\n\n                    &:hover {\n                        background-color: transparent;\n                    }\n                }\n\n                .wp-block-navigation__submenu-icon {\n                    display: inline-block;\n                    flex-grow: 1;\n                    text-align: right;\n                    position: absolute;\n                    inset: 0;\n                    width: 100%;\n                    height: 18px;\n                    margin-right: 0;\n                    margin-top: 4px;\n                    padding-right: 10px;\n                    align-self: flex-start;\n\n                    &:hover {\n                        color: var(--wp--preset--color--contrast);\n                        background-color: transparent;\n                    }\n\n                    &[aria-expanded=true] ~ .wp-block-navigation__submenu-icon {\n                        svg {\n                            color: var(--wp--preset--color--contrast);\n                            width: 10px;\n                            margin: 0 10px 0 0;\n                        }\n                    }\n\n                    svg {\n                        width: 10px;\n                        margin: 0;\n                    }\n                }\n\n                .wp-block-navigation-item {\n                    flex-wrap: wrap;\n                    flex-direction: row;\n                    justify-content: space-between;\n                    width: 100%;\n                }\n            }\n        }\n    }\n\n    .wp-block-navigation:not(.has-background) {\n        .wp-block-navigation__responsive-container.is-menu-open {\n            background-color: #00000080;\n            padding: 0;\n        }\n    }\n\n    .has-modal-open {\n        .wp-block-navigation__responsive-close {\n            animation-name: leftslidein;\n            animation-duration: .2s;\n        }\n    }\n\n    .wp-block-navigation__responsive-close {\n        background-color: var(--wp--preset--color--base);\n        transition: transform .3s ease-in-out;\n        margin-left: 0;\n        height: 100vh;\n        padding: 0;\n\n        @media (min-width:600px) {\n            width: 400px;\n        }\n    }\n}\n\n.has-modal-open .admin-bar .mobile-header .is-menu-open .wp-block-navigation__responsive-dialog {\n    margin-top: 20px;\n}\n\n@keyframes leftslidein {\n    from {\n        transform: translateX(-100%);\n    }\n\n    to {\n        transform: translateX(0);\n    }\n}\n", "/*\n################\n* === Footer Style ===\n################\n*/\n.footer-v1,\n.footer-v2 {\n\t.wp-block-navigation__container {\n\t\tli {\n\t\t\ta {\n\t\t\t\t&:hover,\n\t\t\t\t&:focus {\n\t\t\t\t\tcolor: var(--wp--preset--color--primary);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.copyright-text {\n\t\ta {\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\tcolor: var(--wp--preset--color--primary);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.footer-language.wp-block-navigation {\n\t.has-child {\n\t\t.wp-block-navigation__submenu-container {\n\t\t\ttop: auto;\n    \t\tbottom: 100%;\n    \t\twidth: max-content;\n\t\t    padding: 12px 10px;\n\t\t    border: 1px solid var(--wp--preset--color--base);\n\t\t    border-radius: 0;\n\t\t    box-shadow: 0px 10px 60px 0 rgba(5, 16, 54, 0.05);\n\t\t    min-width: 130px;\n\t\t    margin-bottom: 10px;\n\n\t\t    a {\n\t\t    \t&:hover,\n\t\t    \t&:focus {\n\t\t    \t\tcolor: var(--wp--preset--color--primary);\n\t\t    \t}\n\t\t    }\n\t\t}\n\n\t\t.wp-block-navigation__submenu-icon {\n\t    \tmargin-left: 10px;\n\t    }\n\n\t    > a {\n\t    \tdisplay: flex;\n\t\t    align-items: center;\n\t\t    gap: 10px;\n\n\t    \t&:before {\n\t    \t\tcontent: \"\";\n\t    \t\tbackground-image: url('./assets/images/globe.svg');\n\t    \t\twidth: 16px;\n\t    \t\theight: 16px;\n\t    \t\tdisplay: inline-block;\n\t    \t}\n\t    }\n\t}\n}\n\n@media(min-width: 1400px) {\n\t.footer-widgets .widget + .widget {\n\t\tpadding-left: var(--wp--preset--spacing--30) !important;\n\t}\n}\n\n.footer-v4 .footer-language.wp-block-navigation .has-child > a:before,\n.dark-mode.footer-v2 .footer-language.wp-block-navigation .has-child > a:before {\n    filter: invert(1);\n}\n\n.footer-v3 input[type=text] {\n    border-radius: 10px !important;\n}\n\n@media(max-width: 767px){\n\t.footer-widgets .widget {\n\t\tflex: 0 0 auto;\n\t\twidth: 45%;\n\t\tmargin-bottom: 25px;\n\t}\n\t\t\n\t.footer-widgets {\n\t\tjustify-content: initial;\n\t\t  gap: 25px !important;\n\t\talign-items: initial;\n\t}\n}", "// post author block\n\n// Comment form.\n.wp-block-post-comments-form .comment-reply-title {\n    font-size: 24px;\n    font-weight: 500;\n    display: block;\n    margin-bottom: 16px;\n    line-height: 29px;\n    margin-top: 0;\n}\n\n.comment-form .comment-notes{\n    font-size: 15px;\n    font-weight: 400;\n    line-height: 30px;\n    letter-spacing: 0em;\n    text-align: left;\n}\n\n.wp-block-post-comments-form input[type=submit]{\n    border-radius:0;\n    width: 100%;\n    // background-color: var(--wp--preset--color--secondary) !important;\n    font-weight: 400;\n    padding: 10px 30px !important;\n    font-size: 15px;\n    line-height: 26px;\n    // height:56px !important;\n    color:var(--wp--preset--color--base) !important;\n    margin-top: 10px !important;\n    margin-bottom: 10px !important;\n    &:hover,\n    &:focus {\n        background-color: var(--wp--preset--color--secondary) !important;\n        border-color: var(--wp--preset--color--secondary) !important;\n    }\n}\n\n.comment-form .comment-form-cookies-consent {\n    gap: 10px !important;\n    align-items: center;\n\n    input[type=checkbox] {\n        margin-top: 0px !important;\n    }\n\n    label {\n        font-size: 15px;\n    }\n}\n\n.comment-form .comment-form-url {\n    margin-bottom: 25px!important;\n}\n\n\n.wp-block-comment-content {\n    margin-top: 0px;\n    margin-bottom: 0px;\n}\n\n.comment-form .comment-form-comment label,\n.comment-form .comment-form-author label,\n.comment-form .comment-form-email label,\n.comment-form .comment-form-url label {\n    \n    margin-bottom: 10px !important;\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 21px;\n    font-family: var(--wp--preset--font-family--heading);\n}\n\nblockquote {\n    border-width: 0 0 0 5px;\n    border-color: var(--wp--preset--color--primary);\n    background-color: var(--wp--preset--color--bg-1);\n    border-style: solid;\n    position: relative; /* Ensure positioning context */\n    \n    p {\n        font-style: normal;\n        line-height: 1.7; \n        font-size: 21px !important; \n    }\n    cite {\n        color: var(--wp--preset--color--contrast)!important;\n        font-size: 15px !important;\n    }\n    @media (min-width:1023px) {\n        padding: 55px 75px 57px 75px !important;\n    }  \n    @media (max-width:1023px) {\n        padding: calc(var(--wp--preset--spacing--30) + 0.75rem);\n    } \n}\n\nblockquote::after {\n    position: absolute;\n    right: 6.3%;\n    bottom: 11.5%;\n    transform: translateY(-50%);\n    display: inline-block;\n    content: \" \";\n    background-image: url('./assets/images/content-quote.svg');\n    background-size: contain;\n\n    @media (min-width:1023px) {\n        width: 45px;\n        height: 44px;\n    }\n\n    @media (max-width:1023px) {\n        width: 45px;\n        height: 44px;\n    }\n}\n\n\n.gl-single-post {\n    .single-social-icons {\n        img {\n            margin-right: 12px;\n        }\n\n        .wp-social-link {\n            font-size: 17px;\n        }\n    }\n\n    .post-meta > div{\n        display: flex;\n        align-items: center;\n        &::before {\n            content:'\\00B7';\n            padding-right: 8px;\n            font-size: 24px;\n            font-weight: 600;\n            color: var(--wp--preset--color--secondary);\n        }\n\n        &:first-child::before{\n            display: none;\n        }\n    }\n\n\n    .wp-block-post-navigation-link {\n        display: flex;\n\n        .post-navigation-link__title {\n            display: block;\n            font-size: 15px;\n            font-weight: normal;\n            line-height: 28px;\n            text-transform: none;\n            padding-top: 5px;\n            font-family: var(--wp--preset--font-family--body);\n            color: var(--wp--preset--color--contrast);\n        }\n\n\n        .wp-block-post-navigation-link__arrow-previous, .wp-block-post-navigation-link__arrow-next {\n            height: 100%;\n            display: flex;\n            justify-content: start;\n            align-items: center;\n            border-color: var(--wp--preset--color--primary) !important;\n            border-radius: 0px;\n            border: 1px solid;\n\n            &:hover,\n            &:focus {\n                border-color: var(--wp--preset--color--primary);\n                color: var(--wp--preset--color--primary);\n                background-color: var(--wp--preset--color--bg-5);\n            }\n\n            &.is-arrow-arrow {\n                font-size: 0px;\n                padding: 17px 17px;\n            }\n        }\n    }\n\n    .post-navigation-link-previous {\n        .wp-block-post-navigation-link__arrow-previous {\n            margin-right: 20px;\n\n            &.is-arrow-arrow:after {\n                content:\"\\F12F\";\n                font-family: \"bootstrap-icons\";\n                font-size: 18px;\n                color: var(--wp--preset--color--primary);\n            }\n        }\n    }\n\n    .post-navigation-link-next {\n        justify-content: end;\n        .post-navigation-link__label, .post-navigation-link__title {\n            display: block;\n            text-align: end;\n        }\n        .wp-block-post-navigation-link__arrow-next {\n            margin-left: 20px;\n\n            &.is-arrow-arrow {\n                transform: none;\n            }\n\n            &.is-arrow-arrow:after {\n                content:\"\\F138\";\n                font-family: \"bootstrap-icons\";\n                font-size: 18px;\n                color: var(--wp--preset--color--primary);\n            }\n        }\n    }\n\n    .wp-block-latest-posts {\n        column-gap: 20px;\n        row-gap: 20px;\n        li {\n            margin: 0;\n        }\n        .wp-block-latest-posts__featured-image img{\n            height: 330px;\n            width: 440px;\n            padding-bottom: 15px;\n        }\n        .wp-block-latest-posts__post-date {\n            padding-top: 15px;\n            font-size: 15px;\n            font-weight: 400;\n            color: var(--wp--preset--color--secondary) !important;\n\n        }\n    }\n\n   .pagination-img {\n        text-align: center;\n    }\n\n    .single-tags {\n        display: flex;\n        flex-wrap: wrap;\n        span {\n            display: none;\n        }\n        a {\n            border: 1px solid var(--wp--preset--color--primary);\n            color: var(--wp--preset--color--primary);\n            margin-right: 10px;\n            margin-bottom: 10px;\n\n            &:hover {\n                background-color: var(--wp--preset--color--bg-5);\n            }\n        }\n    }\n\n    .avatar-img img {\n        @media (min-width:768px) {\n            min-width: 112px;\n        }\n    }\n}\n\n", ".glowess-blog-grid {\n\t.wp-block-post-template {\n\t\trow-gap: 19px;\n\t}\n\n\t.grid-tags a {\n\t\tpadding: 4px 20px;\n\t\tborder: 1px solid var(--wp--preset--color--primary);\n\t\tcolor: var(--wp--preset--color--primary);\n\t\t&:not(:last-child) {\n\t\t\tmargin-right: 17px;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground-color: var(--wp--preset--color--bg-5);\n\t\t}\n\t}\n\n\t.wp-block-query-pagination {\n        margin-top: 21px;\n    }\n}\n\n.gl-single-post { \n\t.single-tags a{\n\t\tpadding: 5px 20px;\n\t}\n\n\tul li {\n\t\tmargin-bottom: 12px;\n\t}\n\n\tblockquote {\n\t\tcite {\n\t\t\ttext-transform: capitalize;\n\t\t}\n\t\tp {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n}\n\n.wp-block-post-title:hover a {\n\ttext-decoration: underline;\n}\n\n\ntable.wp-block-calendar,\ntable.wp-calendar-table {\n\tth,\n\ttd {\n\t\ttext-align: center;\n\t}\n}\n", "/*\n################\n* === Prodcut Item Style  ===\n################\n*/\n\n\nul.products,\n.wc-block-grid__products:not(.slick-slider),\n.products-block-post-template:is(.columns-1,.columns-2,.columns-3,.columns-4,.columns-5,.columns-6) {\n    column-gap: 15px;\n    row-gap: 15px;\n    margin: 0;\n    display: grid;\n    padding: 0;\n    list-style: none;\n    align-items: flex-start;\n\n    @media (min-width:768px) {\n        column-gap: 28px;\n        row-gap: 30px;\n    }\n}\n\n.wc-block-grid__products:not(.slick-slider),\n.wp-block-product-template {\n    display: grid !important;\n}\n\n.wc-block-grid__products:not(.slick-slider) {\n    margin: 0;\n}\n\n.wc-block-grid__product {\n    max-width: 100% !important;\n    width: 100%;\n}\n\n.wc-block-grid__product,\n.wc-block-grid,\n.products-block-post-template .product .has-text-align-center {\n    text-align: left;\n}\n\n.products-block-post-template {\n    .product {\n        .has-medium-font-size,\n        .has-small-font-size {\n            font-size: 14px !important;\n    \n            @media (min-width:768px) {\n                font-size: 16px !important;\n\n                &.wp-element-button {\n                    font-size: 15px !important;\n                }\n            }\n        }\n\n        > .wc-block-components-product-button {\n            display: none;\n        }\n    \n        .wc-block-grid__product-rating .wc-block-grid__product-rating__stars {\n            margin-bottom: 0;\n        }\n    \n        .wp-block-post-title a,\n        .taxonomy-product_cat {\n            display: -webkit-box;\n            -webkit-line-clamp: 1;\n            -webkit-box-orient: vertical;  \n            overflow: hidden;\n        }\n    \n        .wp-block-post-title,\n        .wp-block-woocommerce-product-price {\n            font-weight: 500;\n        }\n\n        .wp-block-woocommerce-product-price {\n            color: var(--wp--preset--color--secondary);\n            margin-bottom: 12px;\n        }\n    \n        .wp-block-post-title {\n            margin-bottom: 11px;\n            line-height: 1.2;\n\n            &:hover {\n                text-decoration: underline;\n                text-underline-offset: 2px;\n            }\n        }\n    }\n}\n\n.wc-block-grid__product-title {\n    font-weight: 500;\n    font-size: 14px;\n    line-height: 1.2;\n\n    @media (min-width:768px) {\n        font-size: 16px;\n    }\n}\n\n.wc-block-grid .wc-block-grid__products,\nul.products,\n.products-block-post-template:is(.columns-3,.columns-4,.columns-5,.columns-6),\n.wp-block-woocommerce-product-template:is(.columns-3,.columns-4,.columns-5,.columns-6) {\n    grid-template-columns: repeat(2,minmax(0,1fr));\n}\n\n@media(max-width: 600px) {\n    .products-block-post-template.wp-block-post-template-is-layout-grid {\n        grid-template-columns: repeat(2,minmax(0,1fr));\n    }\n}\n\n.wc-block-grid.has-1-columns .wc-block-grid__products,\nul.products.columns-1,\n.products-block-post-template.columns-1 {\n    grid-template-columns: repeat(1,minmax(0,1fr));\n}\n\n.wc-block-grid.has-2-columns .wc-block-grid__products,\nul.products.columns-2,\n.products-block-post-template.columns-2 {\n    grid-template-columns: repeat(2,minmax(0,1fr));\n}\n\n@media (min-width: 600px) {\n    .products-block-post-template.is-flex-container.is-flex-container:is(.columns-2,.columns-3,.columns-4,.columns-5,.columns-6)>li {\n        width: 100%;\n    }\n}\n\n@media (min-width: 768px) {\n    .wc-block-grid.has-3-columns .wc-block-grid__products,\n    ul.products.columns-3,\n    .products-block-post-template.columns-3 {\n        grid-template-columns: repeat(3,minmax(0,1fr));\n    }\n\n    .wc-block-grid .wc-block-grid__products,\n    ul.products,\n    .products-block-post-template:is(.columns-4,.columns-5,.columns-6) {\n        grid-template-columns: repeat(3,minmax(0,1fr));\n    }\n}\n\n@media (min-width: 1200px) {\n    .wc-block-grid.has-4-columns .wc-block-grid__products,\n    ul.products.columns-4,\n    .products-block-post-template.columns-4 {\n        grid-template-columns: repeat(4,minmax(0,1fr));\n    }\n\n    .wc-block-grid.has-5-columns .wc-block-grid__products,\n    ul.products.columns-5,\n    .products-block-post-template.columns-5 {\n        grid-template-columns: repeat(5,minmax(0,1fr));\n    }\n\n    .wc-block-grid.has-6-columns .wc-block-grid__products,\n    ul.products.columns-6,\n    .products-block-post-template.columns-6 {\n        grid-template-columns: repeat(6,minmax(0,1fr));\n    }\n}\n\n// Product Items\n.wc-block-grid__product-add-to-cart {\n    &.wp-block-button {\n        .wp-block-button__link {\n            &.added {\n                &:after {\n                    content:\"\\F26A\";\n                }\n            }\n\n            &.loading:after,\n            &.added:after {\n                font-family: \"bootstrap-icons\";\n                font-size: 18px;\n                line-height: 1;\n            }\n\n            &.loading:after {\n                content: \"\\F116\";\n            }\n        }\n    }\n}\n\n.wp-block-button.wc-block-components-product-button .wp-block-button__link.loading::after {\n    font-family: \"bootstrap-icons\";\n    font-size: 18px;\n    line-height: 1;\n    content: \"\\F116\";\n}\n\n.products-block-post-template,\n.wc-block-grid__products,\n.wp-block-woocommerce-cart-cross-sells-block {\n    .wc-block-grid__product-image,\n    .wp-block-cart-cross-sells-product__product-image {\n        display: grid;\n        transition: 0.3s ease-in-out;\n\n        > * {\n            grid-column: 1 / -1;\n            grid-row: 1 / -1;\n        }\n\n        > .wp-block-button {\n            margin: auto auto 10px;\n            width: calc(100% - 20px);\n            opacity: 0;\n            visibility: hidden;\n            transition: 0.3s ease-in-out;\n\n            @media (max-width:1199px) {\n                display: none;\n            }\n\n            .wp-element-button {\n                gap: 6px;\n                align-items: center;\n\n                &::after {\n                    content: \" \";\n                    display: inline-block;\n                    width: 11px;\n                    height: 11px;\n                    flex-shrink: 0;\n                    background-image: url('data:image/svg+xml,<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z\" fill=\"%23131C19\"/></svg>');\n                }\n\n                &:hover {\n                    &::after {\n                        background-image: url('data:image/svg+xml,<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z\" fill=\"%23ffffff\"/></svg>');\n                    }\n                }\n\n                &:not(:hover) {\n                    background-color: var(--wp--preset--color--base);\n                    border-color: var(--wp--preset--color--base);\n                }\n            }\n        }\n    }\n\n    .wp-block-post {\n        .wc-block-grid__product-image {  \n            > .wp-block-button {\n                margin-bottom: 0;\n            }\n        }\n    }\n\n    .wp-block-button.wc-block-components-product-button .wc-block-components-product-button__button {\n        line-height: 1.867;\n    }\n\n    .wp-block-post,\n    .wc-block-grid__product,\n    .cross-sells-product {\n        &:is(:hover) {\n            .wc-block-grid__product-image,\n            .wp-block-cart-cross-sells-product__product-image {\n                > .wp-block-button {\n                    opacity: 1;\n                    visibility: visible;\n                }\n            }\n        }\n    }\n}\n\n.wc-block-grid.has-4-columns:not(.alignwide):not(.alignfull) .wc-block-grid__product, \n.wc-block-grid.has-5-columns.alignfull .wc-block-grid__product {\n    font-size: 16px;\n}\n\n.products-block-post-template {\n    div[data-block-name=\"woocommerce/product-button\"] {\n        width: calc(100% - 40px);\n        opacity: 0;\n        visibility: hidden;\n        align-items: stretch;\n\n        @media (max-width:1199px) {\n            display: none;\n        }\n\n        .wp-element-button {\n            margin-bottom: 10px !important;\n\n            &:not(:hover) {\n                background-color: var(--wp--preset--color--base);\n                border-color: var(--wp--preset--color--base);\n            }\n        }\n    }\n\n    div[data-block-name=\"woocommerce/product-button\"] {\n        button#added {\n            display: none;\n\n            ~ span {\n                display: block;\n\n                &,a {\n                    width: 100%;\n                }\n\n                a {\n                    display: flex;\n                    justify-content: center;\n                    align-items: center;\n                    gap: 6px;\n                }\n            }\n        }\n\n        > span {\n            margin-bottom: 10px;\n\n            a[title=\"View cart\"] {\n                padding: 12.5px 16px;\n                text-transform: uppercase;\n                background-color: var(--wp--preset--color--base);\n                text-align: center;\n                display: block;\n\n                &:is(:hover, :focus) {\n                    background-color: var(--wp--preset--color--secondary);\n                    color: var(--wp--preset--color--base);\n                }\n\n                &::after {\n                    content: \" \";\n                    display: inline-block;\n                    width: 11px;\n                    height: 11px;\n                    background-image: url('data:image/svg+xml,<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z\" fill=\"%23131C19\"/></svg>');\n                }\n\n                &:hover {\n                    &::after {\n                        background-image: url('data:image/svg+xml,<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z\" fill=\"%23ffffff\"/></svg>');\n                    }\n                }\n            }\n        }\n    }\n\n    .wp-block-post {\n        &:is(:hover) {\n            div[data-block-name=\"woocommerce/product-button\"] {\n                position: relative;\n                opacity: 1;\n                visibility: visible;\n            }\n        }\n    }\n}\n\n.wc-block-grid .wc-block-grid__product-onsale, \n.wc-block-grid__product-image .wc-block-grid__product-onsale {\n    color: var(--wp--preset--color--base);\n    border-radius: 0;\n    border-width: 0;\n    font-weight: 500;\n    background-color: var(--wp--preset--color--ebony);\n    right:10px;\n    top: 10px;\n    font-size: 12px;\n    z-index: 1;\n    font-family: var(--wp--preset--font-family--heading);\n    padding: 6px 12px;\n    line-height: 1.334;\n}\n\n.wc-block-grid__product-image {\n    background-color: var(--wp--preset--color--bg-3);\n}\n\n.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-right, \n.wc-block-components-product-image .wc-block-components-product-sale-badge--align-right {\n    right: 10px;\n    top: 10px;\n}\n\n.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-left, \n.wc-block-components-product-image .wc-block-components-product-sale-badge--align-left {\n    left: 10px;\n    top: 10px;\n    right: auto;\n}\n\n.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-center, \n.wc-block-components-product-image .wc-block-components-product-sale-badge--align-center {\n    top: 10px;\n    right: auto;\n}\n\n.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link,\n.products-block-post-template .product .wp-element-button {\n    width: 100%;\n}\n\n.slick-nav-group .wc-block-grid__product {\n    display: flex !important;\n}\n\n.wc-block-grid__product {\n    border-width: 0;\n    display: flex;\n    flex-direction: column;\n\n    .wc-block-grid__product-price, \n    .wc-block-grid__product-rating {\n        margin-left: 0;\n    }\n\n    .wc-block-grid__product-price {\n        order: 1;\n    }\n\n    .wc-block-grid__product-title {\n        margin-bottom:10px;\n        font-family: var(--wp--preset--font-family--heading);\n\n        &:hover {\n            text-decoration: underline;\n            text-underline-offset: 2px;\n        }\n    }\n\n    > .wc-block-grid__product-add-to-cart {\n        display: none;\n\n        a:not(:hover) {\n            background-color: var(--wp--preset--color--base);\n        }\n    }\n\n    .wc-block-grid__product-rating {\n        display: flex;\n\n        .star-rating {\n            margin-bottom: 0;\n        }\n    }\n\n    .wc-block-grid__product-price {\n        font-size: 16px;\n        font-weight: 500;\n        color: var(--wp--preset--color--secondary);\n    }\n}\n\n.wc-block-grid__products {\n    .wc-block-grid__product-add-to-cart {\n        a.added {\n            display: none;\n        }\n\n        a.added_to_cart {\n            padding: 14.5px 16px;\n            text-transform: uppercase;\n            background-color: var(--wp--preset--color--base);\n            text-align: center;\n            display: block;\n\n            &:is(:hover, :focus) {\n                background-color: var(--wp--preset--color--contrast);\n                color: var(--wp--preset--color--base);\n            }\n        }\n    }\n}\n\n.wc-block-grid__products,\n.wc-block-all-products,\n.wp-block-post {\n    .wc-block-grid__product-price {\n        display: flex;\n        gap: 5px;\n        flex-direction: row-reverse;\n        justify-content: flex-end;\n        align-items: center;\n    \n        del {\n            color: var(--wp--preset--color--contrast);\n            font-size: 12px;\n            font-weight: 400;\n        }\n    }\n\n    .wp-block-button .wc-block-components-product-button__button:not(:hover),\n    .wc-block-grid__product-add-to-cart a:not(:hover) {\n        color: var(--wp--preset--color--secondary);\n    }\n}\n\n.wc-block-grid__products,\n.products-block-post-template {\n    .wc-block-grid__product-rating,\n    .wc-block-components-product-rating {\n        margin-bottom: 8px;\n    }\n\n    .woocommerce-Price-amount {\n        font-family: var(--wp--preset--font-family--heading);\n    }\n\n    .wc-block-components-product-rating__container {\n        display: flex;\n    }\n}\n\n.wc-block-all-products {\n    .wc-block-grid__product {\n        > .wc-block-grid__product-add-to-cart {\n            display: none;\n        }\n    }\n}\n\n.wc-block-components-product-price__value.is-discounted {\n    margin-left: 0;\n}\n\n.wc-block-grid__product .wc-block-grid__product-image:not(.wc-block-components-product-image) {\n    margin-bottom: clamp(16px, 2.237vw - 0.185rem, 27px);\n}", "/*\n################\n* === WooCommerce - Blocks style  ===\n################\n*/\n\n.wc-block-components-button:not(.is-link) {\n    transition: .3s ease-in-out;\n}\n\n.wc_payment_methods {\n    list-style: none;\n    padding: 0;\n}\n\n.wc-block-components-payment-method-label,\n.wc_payment_method label {\n    font-weight: 500;\n    font-size: 17px;\n}\n\n.wp-block-woocommerce-customer-account {\n    &[data-display-style=\"icon_only\"] {\n        span,\n        svg {\n            display: none;\n        }\n\n        a {\n            display: flex;\n\n            &::after {\n                width: 14px;\n                height: 18px;\n                content: \" \";\n                display: block;\n                background-image: url('data:image/svg+xml,<svg width=\"14\" height=\"18\" viewBox=\"0 0 14 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.99984 1.94855C5.45584 1.94855 4.20414 3.19221 4.20414 4.72634C4.20414 6.26045 5.45584 7.50409 6.99984 7.50409C8.54384 7.50409 9.79551 6.26045 9.79551 4.72634C9.79551 3.19221 8.54384 1.94855 6.99984 1.94855ZM2.91382 4.72634C2.91382 2.48415 4.74319 0.666504 6.99984 0.666504C9.25651 0.666504 11.0858 2.48415 11.0858 4.72634C11.0858 6.96851 9.25651 8.78617 6.99984 8.78617C4.74319 8.78617 2.91382 6.96851 2.91382 4.72634Z\" fill=\"%23131C19\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.8884 11.7776C3.08529 11.7776 1.62358 13.2299 1.62358 15.0215C1.62358 15.1156 1.64088 15.182 1.65902 15.2207C1.67433 15.2533 1.68747 15.2626 1.69843 15.2685C2.19982 15.5396 3.6056 16.0511 6.99992 16.0511C10.3943 16.0511 11.8 15.5396 12.3014 15.2685C12.3123 15.2626 12.3255 15.2533 12.3408 15.2207C12.359 15.182 12.3763 15.1156 12.3763 15.0215C12.3763 13.2299 10.9146 11.7776 9.11142 11.7776H4.8884ZM0.333252 15.0215C0.333252 12.5219 2.37266 10.4956 4.8884 10.4956H9.11142C11.6272 10.4956 13.6666 12.5219 13.6666 15.0215C13.6666 15.4804 13.4994 16.0804 12.9182 16.3946C12.1482 16.8109 10.4965 17.3332 6.99992 17.3332C3.50337 17.3332 1.85164 16.8109 1.08169 16.3946C0.500444 16.0804 0.333252 15.4804 0.333252 15.0215Z\" fill=\"%23131C19\"/></svg>');\n                //filter: invert(94%) sepia(6%) saturate(0%) hue-rotate(62deg) brightness(107%) contrast(107%);\n            }\n        }\n    }\n}\n\n.woocommerce-breadcrumb {\n    span {\n        padding: 0 6px;\n    }\n\n    i {\n        color: var(--wp--preset--color--contrast);\n    }\n}\n\n.wp-block-woocommerce-product-meta {\n    strong {\n        font-weight: normal;\n    }\n}\n\n.wc-block-components-quantity-selector:after {\n    display: none;\n}\n\n.quantity {\n    display: inline-block;\n}\n\n.quantity,\n.wc-block-components-quantity-selector {\n    margin: 0 !important;\n    border: 1px solid var(--wp--preset--color--gray-100);\n    border-radius: 0;\n    align-items: center;\n\n    .qty-container {\n        display: flex;\n        align-items: center;\n    }\n\n    button,\n    .wc-block-components-quantity-selector__button {\n        width: 30px;\n        height: 30px;\n        background-color: transparent;\n        border-width: 0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 0;\n        margin: 0;\n        border-radius: 20px;\n        cursor: pointer;\n        opacity: 1;\n        color: var(--wp--preset--color--secondary);\n    }\n\n    .qty-minus,\n    .wc-block-components-quantity-selector__button--minus {\n        order: -1;\n\n    }\n\n    .qty,\n    .wc-block-components-quantity-selector__input {\n        margin-right: 0;\n        min-width: 48px;\n        padding: 0 !important;\n        min-height: 50px;\n        border-width: 0;\n        font-size: 16px;\n        color: var(--wp--preset--color--secondary);\n        box-shadow: none !important;\n        flex-grow: 1;\n        text-align: center;\n        width: 48px !important;\n        font-family: var(--wp--preset--font-family--heading);\n    }\n\n    .qty-minus,\n    .wc-block-components-quantity-selector__button--minus {\n        margin-left: 9px;\n    }\n\n    .qty-plus,\n    .wc-block-components-quantity-selector__button--plus {\n        margin-right: 9px;\n    }\n}\n\n.wc-block-components-quantity-selector {\n    input.wc-block-components-quantity-selector__input {\n        order: 1;\n        font-weight: normal;\n    }\n}\n\n.wc-block-add-to-cart-form {\n    .quantity {\n        .qty {\n            margin-right: 0;\n        }\n    }\n}\n\n.theme-glowess.woocommerce {\n    .star-rating {\n        font-size: 10px;\n        width: 8.2em;\n        letter-spacing: 8px;\n        margin: 0 0 17px;\n        span::before {\n            content: '\\F586\\F586\\F586\\F586\\F586';\n            font-family: \"bootstrap-icons\";\n        }\n    }\n    .star-rating::before {\n         content: '\\F588\\F588\\F588\\F588\\F588';\n        font-family: \"bootstrap-icons\";\n    }\n}\n\n\n\n.woocommerce-message,\n.woocommerce-error,\n.woocommerce-notice--success,\n.woocommerce-info,\n.wc-block-components-notice-banner,\n.woocommerce-noreviews,\n.wp-block-query-no-results {\n    padding: 14px 24px;\n    border: 1px solid transparent;\n    border-radius: 0;\n    display: flex;\n    gap: 10px;\n    font-size: 14px;\n    list-style: none;\n    margin-bottom: 16px;\n    flex-direction: column;\n    align-items: flex-start;\n    \n    @media (min-width:1024px) {\n        align-items: center;\n        flex-direction: row;\n    }\n\n    a {\n        color: inherit;\n        text-decoration: underline;\n        text-underline-offset: 2px;\n        padding: 0;\n        background-color: transparent;\n        border: 0;\n\n        &:hover {\n            background-color: transparent;\n            color: inherit;\n            border-color: transparent;\n        }\n    }\n}\n\n.woocommerce-notice--success,\n.woocommerce-message,\n.wc-block-components-notice-banner.is-success {\n    background-color: var(--wp--preset--color--success);\n    color: var(--wp--preset--color--base);\n    border-color: var(--wp--preset--color--success);\n\n    >.wc-block-components-notice-banner__content .wc-forward {\n        color: var(--wp--preset--color--base) !important;\n        opacity:1;\n    }\n}\n\n.woocommerce-account {\n    .woocommerce-MyAccount-content {\n        .woocommerce-error,\n        .woocommerce-info,\n        .woocommerce-message {\n            justify-content: space-between;\n            \n            .button {\n                float: right;\n                text-decoration: none;\n                text-underline-offset: 0;\n                padding: 14px 28px;\n                background-color: var(--wp--preset--color--primary);\n            }\n        }\n    }\n}\n\n.woocommerce-error,\n.wp-block-query-no-results,\n.wc-block-components-notice-banner.is-error,\n.woocommerce-noreviews {\n    background-color: var(--wp--preset--color--danger);\n    color: var(--wp--preset--color--base);\n    border-color: var(--wp--preset--color--danger);\n\n    >.wc-block-components-notice-banner__content .wc-forward {\n        color: var(--wp--preset--color--base) !important;\n        opacity:1;\n    }\n}\n\n.woocommerce-error {    \n    @media (min-width:1024px) {\n        flex-direction: column;\n        align-items: flex-start;\n    }\n}\n\n.woocommerce-info,\n.wc-block-components-notice-banner.is-info {\n    background-color: #ecf2fa;\n    color: #3972b6;\n    border-color: var(--wp--preset--color--info);\n    \n    >.wc-block-components-notice-banner__content .wc-forward {\n        color: #3972b6 !important;\n        opacity:1;\n\n        &.wp-element-button {\n            color: #3972b6 !important;\n            text-transform: capitalize;\n            text-decoration: underline !important;\n            text-underline-offset: 2px;\n            font-weight: 500;\n        }\n    }\n\n    @media (max-width:1024px) {\n        svg {\n            display: none;\n        }\n    }\n}\n\n.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward.wp-element-button {\n    color: var(--wp--preset--color--base) !important;\n    float: none;\n    opacity: 1;\n}\n\n.page-numbers,\n.wc-block-pagination,\n.wp-block-query-pagination-numbers,\n.wp-block-query-pagination,\n.wp-block-comments-pagination-numbers,\n.post-nav-links {\n    display: flex;\n    list-style: none;\n    padding: 0;\n    justify-content: center;\n    margin: 2.5rem 0;\n    flex-wrap: wrap;\n    gap: 0;\n}\n\n.wp-block-query-pagination-numbers {\n    border: 1px solid var(--wp--preset--color--gray-100);\n    padding: 0 20px;\n}\n\n.post-nav-links {\n    align-items: center;\n}\n\n.page-numbers,\n.wc-block-pagination,\n.wp-block-query-pagination-numbers,\n.wp-block-query-pagination,\n.wp-block-comments-pagination-numbers,\n.wp-block-comments-pagination,\n.post-nav-links {\n    .page-numbers,\n    .wc-block-pagination-page,\n    .post-page-numbers {\n        width: 30px;\n        color: rgb(from var(--wp--preset--color--secondary) r g b / .5);\n\n        &:is(:hover, :focus) {\n            box-shadow: 0px 1px 0px 0px var(--wp--preset--color--secondary);\n        }\n    }\n\n    .wp-block-query-pagination-next,\n    .wp-block-comments-pagination-next,\n    .wp-block-query-pagination-previous,\n    .wp-block-comments-pagination-previous {\n        border-style: solid;\n        border-color: var(--wp--preset--color--gray-100);\n    }\n\n    .wp-block-query-pagination-previous,\n    .wp-block-comments-pagination-previous {\n        border-width: 1px 0 1px 1px;\n    }\n\n    .wp-block-query-pagination-next,\n    .wp-block-comments-pagination-next {\n        border-width: 1px 1px 1px 0;\n    }\n\n    .page-numbers,\n    .wc-block-pagination-page,\n    .post-page-numbers,\n    .wp-block-query-pagination-next,\n    .wp-block-query-pagination-previous,\n    .wp-block-comments-pagination-next,\n    .wp-block-comments-pagination-previous {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0;\n        height: 50px;\n        font-size: 15px;\n        line-height: 1.6;\n\n        &:not(.current),\n        &:not(.wc-block-pagination-page--active),\n        &:not(.wc-block-components-pagination__page--active) {\n            color: rgb(from var(--wp--preset--color--secondary) r g b / 0.5);\n        }\n\n        &.current,\n        &.wc-block-pagination-page--active {\n            &,\n            &:is(:hover, :focus) {\n                color: rgb(from var(--wp--preset--color--secondary) r g b / 1);\n                box-shadow: 0px 1px 0px 0px var(--wp--preset--color--secondary);\n            }\n        }\n\n        &:is(:hover, :focus) {\n            color: rgb(from var(--wp--preset--color--secondary) r g b / 1);\n        }\n    }\n\n    .wp-block-query-pagination-next,\n    .wp-block-comments-pagination-next,\n    .wp-block-query-pagination-previous,\n    .wp-block-comments-pagination-previous {\n        height: 52px;\n\n        &:not([aria-label=\"Next Page\"],[aria-label=\"Previous Page\"]) {\n            padding: 0 20px; \n\n            .is-arrow-chevron,\n            .is-arrow-arrow {\n                width: 25px;\n            }\n        }\n\n        &,\n        &:is(:hover, :focus) {\n            border-color: var(--wp--preset--color--gray-100);\n        }\n        \n        .is-arrow-chevron,\n        .is-arrow-arrow {\n            margin-right: 0;\n            margin-left: 0;\n            width: 49px;\n            justify-content: center;\n            display: flex;\n        }\n\n        .is-arrow-arrow {\n            font-size: 0;\n\n            &::before {\n                display: block;\n                content: \" \";\n                height: 18px;\n                width: 18px;\n                background-repeat: no-repeat;\n                background-position: center;\n            }\n        }\n\n        .is-arrow-chevron {\n            font-size: 20px;\n        }\n    }\n\n    .wp-block-query-pagination-previous,\n    .wp-block-comments-pagination-previous {\n        .is-arrow-arrow::before {\n            background-image: url('data:image/svg+xml,<svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_101_2154)\"><path d=\"M18 9H1.0425\" stroke=\"%23131C19\"/><path d=\"M4.61215 13.2171C3.615 10.9128 2.82501 9.9454 0.856795 9.00634C2.86894 7.96644 3.64997 6.99552 4.61215 4.78296\" stroke=\"%23131C19\"/></g><defs><clipPath id=\"clip0_101_2154\"><rect width=\"18\" height=\"18\" fill=\"white\" transform=\"matrix(-1 0 0 1 18 0)\"/></clipPath></defs></svg>');\n        }\n    }\n\n    .wp-block-query-pagination-next,\n    .wp-block-comments-pagination-next {\n        .is-arrow-arrow::before {\n            background-image: url('data:image/svg+xml,<svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g clip-path=\"url(%23clip0_101_2158)\"><path d=\"M0 9H16.9575\" stroke=\"%23131C19\"/><path d=\"M13.3878 13.2171C14.385 10.9128 15.175 9.9454 17.1432 9.00634C15.1311 7.96644 14.35 6.99552 13.3878 4.78296\" stroke=\"%23131C19\"/></g><defs><clipPath id=\"clip0_101_2158\"><rect width=\"18\" height=\"18\" fill=\"white\"/></clipPath></defs></svg>');\n        }\n    }\n}\n\n.wc-block-pagination-page {\n    &.wc-block-components-pagination__page:not(.wc-block-pagination-page--active ) {\n        cursor:pointer !important;\n    }\n}\n\n.home-v2-single-product {\n    .wc-block-components-product-rating__stars {\n        margin-bottom: 0;\n        color: var(--wp--preset--color--secondary);;\n    }\n    .wp-block-add-to-cart-form {\n        margin-top: 0;\n        .quantity {\n            margin-right: 7px !important;\n        }\n        .single_add_to_cart_button {\n            font-size: 15px;\n            font-weight: 400;\n            line-height: 26px;\n            margin-top: 30px;\n            @media (min-width:768px) {\n                min-width: 308px !important;\n            }\n            &:after {\n                margin-left: 10px;\n                display: inline-block;\n                vertical-align: middle;\n                height: 23px;\n                content: url('../../themes/glowess/assets/images/arrow-white.svg');\n            }\n        }\n    }\n}\n", "/*\n################\n* === Cart Style ===\n################\n*/\n.woocommerce-cart,\n.woocommerce-checkout,\n.woocommerce-account {\n    h1.wp-block-post-title {\n        font-size: clamp(1.5rem, 0.3571rem + 1.7857vw, 2.5rem);\n    }\n}\n\n// .woocommerce-cart {\n//     .is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull):not(.wp-block-cover)) {\n//         max-width: 1400px;\n//     }\n// }\n\n.wc-block-cart-item__remove-link,\n.wc-block-components-product-metadata__description p {\n    color: var(--wp--preset--color--secondary);\n}\n\ntable.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__quantity .wc-block-cart-item__remove-link {\n    font-size: 12px;\n    margin-top: 10px;\n}\n\nbody {\n    &.woocommerce-cart  .woocommerce,\n    .wp-block-woocommerce-cart:not(.block-editor-block-list__block) .wc-block-cart {\n        row-gap: 40px;\n\n        @media (min-width:1024px) {\n            display: grid;\n            column-gap: 30px;\n            row-gap: 30px;\n            grid-template-columns: repeat(12, minmax(0, 1fr));\n            \n            \n            .woocommerce-notices-wrapper {\n                grid-column: span 12 / span 12;\n            }\n\n            &.wc-block-breadcrumbs {\n                display: block;\n            }\n    \n            .woocommerce-cart-form,  \n            .cross-sells,\n            .wc-block-cart__main {\n                grid-column: span 8 / span 8;\n            }\n    \n            .cart-collaterals,\n            .wc-block-cart__sidebar {\n\n                grid-column: span 4 / span 4;\n            }\n        }\n\n        @media (min-width:1200px) {\n            column-gap: 76px;\n        }\n\n    }        \n\n    &.woocommerce-cart  .woocommerce {\n        @media (min-width:1024px) {\n            grid-template-rows: 2fr;\n\n            .cart-collaterals {\n                grid-row: span 2/span 2;\n            }\n        }\n\n        .cross-sells {\n            margin-bottom: 50px;\n\n            @media (min-width:1200px) {\n                .columns-2 {\n                    .product {\n                        &:nth-child(2n) {\n                            &::after {\n                                content: \" \";\n                            }\n                        }\n\n                        &:nth-child(4n), &:last-child {\n                            &::after {\n                                content: none;\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .wp-block-woocommerce-cart {\n        margin-top: 56px;\n        margin-bottom: 60px;\n\n        @media(min-width:1024px) {\n            margin-bottom: 120px;\n        }\n        \n        .wp-block-woocommerce-cart-cross-sells-block {\n            margin-top: 30px;\n\n            > div {\n                display: grid;\n                column-gap: 0;\n                gap: 20px;\n                grid-template-columns: repeat(2, minmax(0, 1fr));\n\n                @media (min-width:768px) {\n                    grid-template-columns: repeat(3, minmax(0, 1fr));\n                }\n\n                @media (min-width:1024px) {\n                    grid-template-columns: repeat(4, minmax(0, 1fr));\n                }\n            }\n\n            .cross-sells-product {\n                margin-bottom: 0;\n                width: 100%;\n                position: relative;\n\n                > .wp-block-button {\n                    display: none;\n                }\n\n                .wp-block-button {\n                    button {\n                        width: 100%;\n                    }\n                }\n            }\n\n            > h2 {\n                margin-bottom: 30px;\n            }\n        }\n    }\n\n    .cart-empty,\n    .wc-block-checkout-empty,\n    .wc-empty-cart-message,\n    .return-to-shop {\n        width: 100%;\n        grid-column: span 12 / span 12;\n    }\n}\n\n.wp-block-woocommerce-cart {\n    .wp-block-woocommerce-cart-cross-sells-block .cross-sells-product div .wc-block-components-product-name {\n        font-weight: 500;\n    }\n}\n\n.return-to-shop {\n    text-align: center;\n\n    a {\n        display: inline-block;\n\n        br {\n            display: none;\n        }\n        \n    }\n}\n\n.wc-block-components-notice-banner {\n    > p:not([class]) {\n        display: none;\n    }\n}\n\n.wp-block-woocommerce-cart {\n    .wp-block-woocommerce-cart-cross-sells-block {  \n        > div {\n            gap:30px;\n        }\n\n        .cross-sells-product div .wp-block-cart-cross-sells-product__product-title {\n            margin-bottom: 6px;\n        }\n        \n        .cross-sells-product div .wp-block-cart-cross-sells-product__product-title,\n        .wp-block-cart-cross-sells-product__product-price {\n            font-size: 15px;\n        }\n\n        .cross-sells-product {\n            display: grid;\n            transition: .3s ease-in-out;\n            position: relative;\n            border-width: 0;\n            padding-right: 0;\n\n            .price,\n            div .wc-block-components-product-name {\n                text-align: left;\n            }\n\n            .wp-block-cart-cross-sells-product__product-title {\n                font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.278), 15px);\n                font-family: var(--wp--preset--font-family--body);\n                text-transform: capitalize;\n            }\n\n            .price {\n                display: flex !important;\n            }\n\n            .wp-block-cart-cross-sells-product__product-onsale {\n                margin-bottom: 0 !important;\n            }\n\n            .wc-block-components-product-button__button {\n                margin-top: 0;\n            }\n\n            .wc-block-components-product-image {\n                margin-bottom: 0;\n            }            \n\n            .wc-block-components-product-rating {\n                display: none;\n            }\n\n            .wp-block-button {\n                align-items: flex-start;\n            }\n            \n        }\n    }\n}\n\n\n// Table Styles.\n.wc-block-components-main,\n.woocommerce-cart .woocommerce-cart-form {\n    table {\n        img {\n            object-fit: contain;\n            aspect-ratio: 1/1;\n        }\n\n        .product-thumbnail,\n        .wc-block-cart-item__image {\n            a img,\n            img {\n                max-width: 100px;\n                object-fit: cover;\n\n                @media (min-width:768px) {\n                    height: 120px;\n                    object-fit: cover;\n                }\n            }\n\n            a {\n                display:inline-flex;\n            }\n        }\n\n        .product-thumbnail {\n            padding-right:0px !important\n        }\n\n        .product-remove a {\n            padding: 0px;\n            font-size: 0px;\n            font-weight: 300;\n            text-decoration: none;\n            &::before {\n                content: '\\F623';\n                font-family: \"bootstrap-icons\";\n                font-size: 15px;\n                color: var(--wp--preset--color--primary) !important;\n                \n            }\n        }\n\n        .product-name a,\n        .wc-block-components-product-name {\n            line-height: 26px;\n        }\n\n        .product-name {\n            a {\n                display: flex;\n                text-decoration: none;\n            }\n\n            .variation-Size {\n                margin-left: 0;\n            }\n        }\n        \n    }\n}\n\n.woocommerce-cart {\n    .woocommerce-cart-form {\n        thead {\n            background: var(--wp--preset--color--bg-1);\n\n            th {\n                border-top-width: 0;\n                border-bottom-width: 0;\n                text-transform: Capitalize;\n                padding-top: 16.8px;\n                padding-bottom: 16.8px;\n                font-size: 15px;\n                font-weight: 500;\n                line-height: 26px;\n                text-transform: uppercase;\n                border-bottom: 1px solid transparent;\n\n                &:first-child {\n                    border-top-left-radius: 4px;\n                    border-bottom-left-radius: 4px;\n                }\n\n                &:last-child {\n                    border-top-right-radius: 4px;\n                    border-bottom-right-radius: 4px;\n                }\n            }\n        }\n\n        .actions {\n            text-align: left;\n            padding-right: 0;\n\n            > .coupon {\n                display: flex;\n                flex-direction: column;\n                gap: 15px;\n                margin-bottom: 20px;\n\n                .input-text {\n                    padding: 14px 19px !important;\n                    font-size: 15px;\n                    font-weight: 400;\n\n                    @media (min-width:1300px) {\n                        width: 300px !important;\n                    }\n                }\n\n                @media (min-width:768px) {\n                    float: left;\n                    flex-direction: row;\n                    margin-bottom: 0;\n                }\n\n                @media (min-width:1200px) {\n                    gap: 20px;\n                }\n\n                .wp-element-button {\n                    flex-shrink: 0;\n                    align-self: flex-start;\n                    display: flex;\n                    align-items: center;\n                }\n            }\n\n            @media (min-width:768px) {\n                >.wp-element-button {\n                    float: right;\n                }\n            }       \n                \n            button[name=update_cart] {\n                background-color:var(--wp--preset--color--transparent) !important;\n                border-color:var(--wp--preset--color--primary) !important;\n                color: var(--wp--preset--color--primary) !important;\n                padding: 12px 30px;\n\n                &:hover {\n                    color: var(--wp--preset--color--base) !important;\n                    background-color: var(--wp--preset--color--secondary) !important;\n                    border-color:var(--wp--preset--color--secondary) !important;\n                }\n            }\n\n            .coupon {\n                button {\n                    padding: 12px 30px;\n                    text-transform: uppercase;\n\n                    &:not(:hover) {\n                        background-color: var(--wp--preset--color--primary);\n                        border-color: var(--wp--preset--color--primary);\n                        color: var(--wp--preset--color--base);\n                    }\n\n                    &:hover,\n                    &:focus {\n                        border-color: var(--wp--preset--color--secondary);\n                        color: var(--wp--preset--color--base);\n                        background-color: var(--wp--preset--color--secondary);\n                    }\n\n                    &::after {\n                        display: inline-block;\n                        margin-left: 10px;\n                        width: 16px;\n                        font-family: \"bootstrap-icons\";\n                    }\n                }\n                \n            }\n        }\n\n        tbody {\n            tr {\n                td {\n                    font-size: 15px;\n                    font-weight: 400;\n                    border-top: 1px solid var(--wp--preset--color--gray-100);\n                    padding-top:20px;\n                    padding-bottom: 13px;\n                    @media (min-width:1200px) {\n                        padding-right: 30px;\n                        \n                    }\n                }\n            }\n            \n\n\n            // td.product-subtotal {\n            //     text-align: center;\n            // } \n\n            td.product-name .variation {\n                display: flex;\n                margin: 0px !important;\n                align-items: center;\n                justify-content: start;\n                gap:5px;\n\n                dt {\n                    font-weight: 700;\n                }\n            }\n        }\n\n        @media (max-width:767px) {\n            thead {display: none;}\n\n            tbody{\n                tr {\n                    &:not(:last-child) {\n                        border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                    }\n\n                    td {\n                        text-align: right;\n                        display: block;\n                        border-width: 0;\n\n                        &::before {\n                            content: attr(data-title) \": \";\n                            float: left;\n                            font-weight: 700;\n                        }\n\n                        &:not(.actions ){\n                            padding: 10px;\n                        }\n                    }\n                }\n                \n                td.product-subtotal {\n                    text-align: right;\n                } \n            }\n\n            .variation {\n                text-align: left;\n            }\n\n            .coupon {\n                input[type=text] {\n                    width: auto;\n                }\n            }\n\n            .product-remove::before,\n            .actions::before,\n            .product-thumbnail {\n                display: none;\n\n            }\n\n            table .product-name a {\n                display: block;\n\n            }\n        }  \n\n    }\n}\n\n.is-large .wc-block-components-sidebar .wc-block-components-panel,\n.is-large .wc-block-components-sidebar .wc-block-components-totals-coupon,\n.is-large .wc-block-components-sidebar .wc-block-components-totals-item,\n.is-large.wc-block-cart .wc-block-components-sidebar .wc-block-components-shipping-calculator,\n.is-large.wc-block-cart .wc-block-components-sidebar .wc-block-components-shipping-rates-control__package:not(.wc-block-components-panel) {\n    padding: 0 !important;\n}\n\n.cart-collaterals .cart_totals >h2, \n.wc-block-cart__totals-title,\n.is-large.wc-block-cart .wc-block-cart__totals-title,\n.cross-sells > h2,\n.up-sells > h2, \n.wp-block-woocommerce-cart-cross-sells-block > h2 {\n    font-size: var(--wp--preset--font-size--grande);\n    position: relative;\n    text-align: left;\n    font-family: var(--wp--preset--font-family--heading);\n    text-transform: capitalize;\n    line-height: 30px;\n    font-weight: 500 !important;\n    margin-top: 0px;\n    display: block;\n}\n\n.is-large.wc-block-cart .wc-block-cart__totals-title {\n     margin-bottom: 6px !important;\n}\n\n.wp-block-woocommerce-cart-cross-sells-block > h2 {\n     font-size: var(--wp--preset--font-size--grande) !important;\n}\n\n.cross-sells > h2 {\n    margin-top: 0 !important;\n}\n\n.cart-collaterals {\n    .shipping-calculator-form {\n        margin: 20px 0;\n\n        .form-row {\n            display: flex;\n\n            > span {\n                width: 100%;\n            }            \n        }\n\n        button[type=submit] {\n            padding: 5px 18px;\n        }\n    }\n\n    .cart_totals {\n        th, td {\n            font-weight: 400 !important;\n            padding: 16px 0;\n            border-top: 1px solid var(--wp--preset--color--gray-100);\n        }\n\n        td {\n            text-align: right;\n        }\n\n        .woocommerce-shipping-destination {\n            strong {\n                font-weight: 500;\n            }\n        }\n\n        .woocommerce-shipping-methods {\n            list-style: none;\n            padding: 0;\n            margin-top: 0;\n\n            li + li {\n                margin-top: 10px;\n            }\n        }\n\n        .order-total {\n            td, th {\n                font-size: 18px;\n                font-weight: 500!important;\n            }\n        }\n\n        .order-total {\n            th {\n                font-weight: 500 !important;\n            }\n        }\n\n        .woocommerce-shipping-methods {\n            label {\n                font-weight: 400 !important;\n                cursor:pointer;\n            }\n        }\n\n        .woocommerce-Price-amount {\n            font-weight: 500 !important;\n        }\n\n        .shipping-calculator-button {\n            display: flex;\n            justify-content: flex-end;\n            gap: 10px;\n            text-decoration:none;\n\n            &,\n            &:hover,\n            &:focus {\n                color: var(--wp--preset--color--primary);\n            }            \n        }\n\n        .wc-proceed-to-checkout {\n            margin-top: 15px;\n\n            .checkout-button {\n                display: flex;\n                text-align: center;\n                padding: 14px;\n                background-color: var(--wp--preset--color--primary);\n                color: var(--wp--preset--color--base);\n                border-radius: 0;\n                align-items: center;\n                justify-content: center;\n\n                &:hover,\n                &:focus {\n                    color: var(--wp--preset--color--base);\n                    background-color: var(--wp--preset--color--secondary);\n                    border-color: var(--wp--preset--color--secondary); \n                }\n                &::after {\n                    display: inline-block;\n                    margin-left: 10px;\n                    width: 16px;\n                    font-family: \"bootstrap-icons\";\n                }\n            }\n        }\n\n        .woocommerce-shipping-totals {\n            th {\n                vertical-align: top;\n            }\n        }\n\n        table tr:first-child th,table tr:first-child td {\n            border-top-width: 0;\n        }\n        // .shipping-calculator-form {\n        //     .input-text {\n        //         padding: 8px 20px;\n        //         font-size: 14px;\n        //         font-weight: 400;\n        //         line-height: 37.5px !important;\n        //     }\n        // }\n    }\n\n    @media(min-width: 600px) {\n        ul.products {\n            .product {\n                padding: 0;\n            }\n        }\n    }\n\n}\n\n// WC Cart Blocks\n.wc-block-cart.wc-block-components-sidebar-layout {\n    .wc-block-components-sidebar,\n    .wc-block-components-main {\n        width: 100%;\n        padding: 0;\n    }\n}\n\n.wp-block-woocommerce-cart-order-summary-coupon-form-block.wc-block-components-totals-wrapper {\n    border-top: 0px !important;\n    \n}\n\n\n\n.wc-block-cart__totals-title {\n    textarea {\n        padding: 0;\n        font-size: clamp(16.834px, 1.052rem + ((1vw - 3.2px) * 0.849), 26px);\n        border-width: 0;\n        text-align: left;\n        font-weight: 600;\n        text-transform: capitalize;\n        color: var(--wp--preset--color--primary);\n    }\n}\n\n.is-large.wc-block-cart .wc-block-cart__totals-title {\n    margin-bottom: 0;\n    font-weight: 500;\n    padding-top: 0;\n    padding-bottom: 0;\n}\n\n.woocommerce-cart-form {\n    .product-thumbnail {\n        width: 100px;\n    }\n\n    .product-name {\n        padding-left: 30px;\n    }\n}\n\n.wp-block-woocommerce-cart-order-summary-coupon-form-block.wc-block-components-totals-wrapper:after {\n    border-top-width: 0;\n}\n\n.wc-block-cart__submit-container {\n    .wp-element-button {\n        padding: 14px;\n\n        &:not(:hover) {\n            color: var(--wp--preset--color--base);\n        }\n    }\n}\n\n.wc-block-cart {\n    font-size: 14px;\n}\n\n.is-large.wc-block-cart .wc-block-cart-items {\n    tbody tr:first-child {\n         td{\n                border-top: 0px;\n            }\n        }\n\n    td {\n        padding-top: 20px;\n        padding-bottom: 25px;\n\n        &:last-child {\n            padding-right: 30px;\n        }\n    }\n}\n\n.is-large.wc-block-cart .wc-block-cart-items td.wc-block-cart-item__image {\n    padding-right: 0 !important;\n}\n\n.is-large.wc-block-cart .wc-block-cart-items td:after,\n.is-large.wc-block-cart .wc-block-cart-items:after,\n.wc-block-components-totals-wrapper:after {\n    color: var(--wp--preset--color--gray-100);\n    opacity: 1;\n}\n\n.wc-block-components-totals-coupon {\n    font-size: 15px;\n    line-height: 26px;  \n}\n\n.wc-block-cart-item__remove-link {\n    cursor: pointer;\n}\n\n.wc-block-cart-item__wrap {\n    .wc-block-components-product-badge {\n        margin-bottom: 0;\n        letter-spacing: 0;\n        background-color: var(--wp--preset--color--secondary);\n        padding: 3px 6px;\n        color: var(--wp--preset--color--base);\n        font-weight:400;\n        border-width: 1;\n        border-radius: 0px;\n        position: relative;\n        //top: -8px;\n    }\n\n    .wc-block-components-product-name {\n        font-weight: 400;\n        font-size: 15px;\n        line-height: 26px;\n        text-decoration: none;  \n    }\n}\n\n.woocommerce-cart .woocommerce .cart-collaterals,\n.woocommerce-cart .woocommerce .wc-block-cart__sidebar,\n.wp-block-woocommerce-cart .wc-block-cart .cart-collaterals,\n.wp-block-woocommerce-cart .wc-block-cart .wc-block-cart__sidebar {\n    border: 1px solid var(--wp--preset--color--gray-100);\n    padding: 30px 12px;\n    border-radius: 0;\n    height: fit-content;\n\n    @media(min-width: 768px) {\n        padding: 30px 39px 39px 39px;\n    }\n}\n\n.wc-block-cart-item__total {\n    text-align: left !important;\n\n    .wc-block-components-sale-badge {\n        background-color: var(--wp--preset--color--secondary);\n        color: var(--wp--preset--color--base);\n        text-transform: uppercase;\n        border-radius: 0px;\n        padding: 3px 6px;\n        font-weight: 400;\n    }\n\n    .wc-block-cart-item__total-price-and-sale-badge-wrapper {\n        align-items: flex-start;\n    }\n\n    .wc-block-components-product-price{\n        font-size: 15px !important;\n        font-weight: 400;\n        line-height: 26px;\n        color: var(--wp--preset--color--secondary);\n        text-align: left;\n    }\n}\n\n.is-large.wc-block-cart {\n    .wc-block-cart-items thead {\n        font-size: 14px ;\n        text-transform: capitalize;\n        background-color: var(--wp--preset--color--bg-1);\n    }\n\n    .wc-block-cart-items th {\n        padding: 15px 30px ; /*10px 16px 10px 0;*/\n\n        &.wc-block-cart-items__header-product {\n            visibility: visible;\n            background: var(--wp--preset--color--gray-100);\n            text-align: left;\n        }\n\n        &:last-child {\n            padding-left: 15px;\n            padding-right: 30px;\n            text-align: left;\n        }\n\n        .wc-block-cart-items__header-total {\n            text-align: left;\n        }\n    }\n\n    tbody {\n        tr:first-child {\n            td:after {\n                content: none !important;\n            }       \n        }\n    }\n    \n}\n\ntable.wc-block-cart-items {\n    border-collapse: collapse;\n\n    .wc-block-components-product-metadata {\n        margin-bottom: 10px;\n        display:none;\n    }\n\n    .wc-block-cart-item__prices {\n        margin: 5px 0;\n    }\n\n    .wc-block-cart-items__header-product {\n        padding-left: 16px !important;\n    }\n\n    .wc-block-cart-item__quantity {\n         .wc-block-components-quantity-selector {\n            .wc-block-components-quantity-selector__input {\n                min-height: 30px;\n                width: 40px !important;\n\n                @media(min-width: 1024px) {\n                    min-height: 44px;\n                }\n            }\n\n            .wc-block-components-quantity-selector__button {\n                min-width: 20px;\n            }\n        }\n    }\n}\n\n.wc-block-cart-item__prices {\n    .wc-block-components-product-price {\n        display: flex;\n        align-items: center;\n        gap: 7px;\n        flex-direction: row-reverse;\n        justify-content: flex-end;\n        color: var(--wp--preset--color--secondary) !important;\n        font-size: 15px !important;\n        font-weight: 400 !important;\n        margin-bottom: 12px !important;\n    }\n}\n\n.wc-block-cart__submit {\n    margin-top: 14px;\n    margin-bottom: 0;\n\n    .wc-block-cart__submit-container {\n        .wc-block-cart__submit-button {\n            background: var(--wp--preset--color--primary);\n            color: var(--wp--preset--color--base);\n\n            &:hover,\n            &:focus {\n                border-color: var(--wp--preset--color--secondary);\n                color: var(--wp--preset--color--base);\n                background: var(--wp--preset--color--secondary);\n                box-shadow: none !important;\n                outline: 0 ! important;\n            }\n        }\n    }\n}\n\n .wc-block-components-totals-shipping__change-address__link {\n    font-size: 14px;\n    font-weight: 400 !important;\n    margin-bottom: 10px;\n    text-decoration:none !important;\n    font-family: var(--wp--preset--font-family--body);\n    padding-top: 20px !important;\n\n    &,\n    &:hover,\n    &:focus {\n        color: var(--wp--preset--color--primary);\n    }\n}\n\n.wc-block-components-shipping-calculator-address {\n    margin-top: 15px;\n }\n\n.is-medium.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title,\n.is-mobile.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title, \n.is-small.wc-block-cart .wc-block-components-sidebar .wc-block-cart__totals-title {\n    display: block;\n}\n\n.wc-block-cart__empty-cart__title.with-empty-cart-icon:before {\n    font-size: 20px;\n    margin-top: 70px;\n}\n\n.wp-block-woocommerce-empty-cart-block {\n    .wp-block-image {\n        margin: auto auto 30px;\n    }\n\n    @media (min-width:1200px) {\n        > .wp-block-product-new {\n            margin: auto;\n        }\n    }\n\n    .wc-block-cart__empty-cart__title {\n        font-family: var(--wp--preset--color--base);\n        font-size: var(--wp--preset--font-size--x-large);\n        font-weight: 500;\n    }\n\n    .wc-block-grid__product-rating {display: none;}\n\n    .yith-wcwl-add-to-wishlist {\n        font-size: 0;\n    }\n\n    .wp-block-separator + h2 {\n        margin: clamp(1.35rem, 2vw + 1rem, 4rem) 0 clamp(1.35rem, 2vw + 1rem, 2.25rem);\n    }\n}\n\n.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link {\n    font-size: 15px;\n}\n\n.wc-block-mini-cart__items {\n    padding-left: 0;\n    padding-right: 0;\n    padding-top: 0;\n\n    &::-webkit-scrollbar {\n        width: 2px;\n        height: 2px;\n    }\n\n    &::-webkit-scrollbar-thumb {\n        background-color: var(--wp--preset--color--gray-700);\n    }\n\n    &::-webkit-scrollbar-track {\n        background-color: var(--wp--preset--color--gray-200);\n    }\n}\n\n.wc-block-cart-items__header .wc-block-cart-items__header-image, \n.wc-block-cart-items__header-total,\n.wc-block-cart-items__header-product {\n    font-size: 15px !important;\n    font-weight: 400 !important;\n    line-height: 30px;\n    text-transform: uppercase;\n     \n}\n\n.wc-block-components-totals-coupon__content {\n    .wc-block-components-totals-coupon__form input[type=text],\n    .wc-block-components-totals-coupon__form button[type=submit] {\n        padding-top: 10px !important;\n        padding-bottom: 10px !important;\n        height: 50px;\n        min-width: auto;\n    }\n}\n\n.wp-block-woocommerce-cart-order-summary-block {\n    font-weight: 500;\n    \n    #wc-block-components-totals-coupon__input-0 {\n        padding-left: 17px;\n        text-indent: 0;\n    }\n\n    .wc-block-components-totals-item__value {\n        font-weight: 500; \n    }\n\n    .wc-block-components-totals-item .wc-block-components-totals-item__label {\n        font-size: 15px;\n        font-weight: 400;\n        line-height: 26px;\n    }\n\n    .wc-block-components-radio-control__option-layout {\n        display: flex;\n        justify-content: space-between;\n    }\n}\n\n.wc-block-components-totals-wrapper:not(.wp-block-woocommerce-cart-order-summary-subtotal-block):not(.wp-block-woocommerce-cart-order-summary-shipping-block):not(.wp-block-woocommerce-cart-order-summary-discount-block) {\n    .wc-block-components-totals-footer-item .wc-block-components-totals-item__label,\n    .wc-block-components-totals-item__value {\n        font-size: 18px;\n        font-weight: 500;\n        line-height: 30px;\n    }\n}\n\n.wp-block-woocommerce-cart-order-summary-subtotal-block {\n    border-bottom: 1px solid hsla(0,0%,7%,.11) !important;\n    padding-bottom: 12px !important;\n}\n\n.wc-block-components-totals-wrapper{\n     padding: 12px 0;\n}\n\n.wc-block-components-text-input.is-active input[type=text] {\n    padding: 14px 20px;\n}\n\n.wc-block-components-totals-item__description {\n     text-align:right;\n\n    .wc-block-components-shipping-address, a {\n        display:block; \n        text-align:right;\n        font-size:15px !important;\n        font-weight:400; \n\n    }\n    a {\n        margin-left:7px;\n        margin-top:25px;\n    }\n\n    .wc-block-components-shipping-address {\n        margin-top:0px;\n    }\n    \n}\n\n.wc-block-components-radio-control .wc-block-components-radio-control__option-layout {\n    display: flex;\n    align-items: center;\n    gap: 4px;\n    font-size: 14px;\n    margin-left: 5px;\n    color: var(--wp--preset--color--secondary) !important;\n    font-weight: 600;\n}\n\n .wc-block-components-shipping-rates-control__package .wc-block-components-radio-control__label-group,\n .wc-block-checkout__payment-method .wc-block-components-radio-control__label-group{\n    font-weight: 500;\n    font-size: 15px;\n    line-height: 26px;\n    color: var(--wp--preset--color--secondary) !important;\n }\n\n.wc-block-components-shipping-rates-control__package .wc-block-components-radio-control__description-group,\n.wc-block-components-radio-control__description, .wc-block-components-radio-control__secondary-description {\n    font-weight: 500;\n    font-size: 15px;\n    line-height: 26px;\n    color: var(--wp--preset--color--secondary) !important;\n}\n\n.wc-block-components-radio-control__option {\n    padding-left: 22px;\n}\n\n.wc-block-components-radio-control .wc-block-components-radio-control__input {\n    min-height: 14px;\n    min-width: 14px;\n    height: 14px;\n    width: 14px;\n    border-width: 1px;\n}\n\n.wc-block-components-radio-control .wc-block-components-radio-control__input:checked {\n    border-color: var(--wp--preset--color--primary);\n    background-color: transparent !important;\n}\n\n.wc-block-components-radio-control .wc-block-components-radio-control__input:checked::before {\n    background-color: var(--wp--preset--color--secondary);\n    min-height: 9px;\n    min-width: 9px;\n    height: 9px;\n    width: 9px;\n}\n\n.wc-block-components-totals-item__description .wc-block-components-totals-shipping__via {\n    text-align: right;\n    font-size: 15px !important;\n    font-weight: 400;\n}\n\n.wc-block-cart-item__prices .wc-block-components-product-price__value.is-discounted {\n    margin-left: 0px;\n}\n\n.wc-block-components-product-price__regular {\n    color:var(--wp--preset--color--gray-300);\n}\n\n.wc-block-cart-item__image {\n    padding-left:0px !important;\n}\n\n.woocommerce-cart-form__cart-item .product-remove {\n    padding-right: 3px !important;\n}\n\n.wp-block-woocommerce-cart {\n    .wp-block-woocommerce-cart-cross-sells-block {\n        .cross-sells-product {\n            text-align: left;\n        }\n    }\n}\n\n.woocommerce-cart {\n    .product-quantity {\n        .qty-container {\n            .qty-container {\n                .qty-minus, \n                .qty-plus {\n                    display: none;\n                }\n            }\n        }\n    }\n}\n\n.wp-block-woocommerce-cart {\n    .wc-block-components-button:not(.is-link):disabled .wc-block-components-button__text {\n        opacity: 1;\n    }\n    .wc-block-components-form .wc-block-components-text-input:not(.is-active) label, .wc-block-components-text-input:not(.is-active) label {\n        transform: translateY(-8px) !important;\n    }\n    .wc-block-components-totals-shipping .wc-block-components-totals-shipping__change-address__link {\n        color: var(--wp--preset--color--primary);\n    }\n\n    .wc-blocks-components-select {\n        .wc-blocks-components-select__select {\n            border: 1px solid var(--wp--preset--color--gray-100);\n            padding: 12.42px 40px 12.42px 20px;\n            border-radius: 0;\n            &:focus {\n                border: 1px solid var(--wp--preset--color--contrast);\n                box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n            }\n        }\n        \n        .wc-blocks-components-select__container{\n            height: 54px;\n        }\n        .wc-blocks-components-select__label {\n            top: -14px;\n            font-size: 20px;\n            background-color: var(--wp--preset--color--base);\n            padding: 2px 11px;\n            left: 14px;\n        }\n    }\n}\n\n.woocommerce-cart main .woocommerce {\n\tmax-width: var(--wp--style--global--wide-size);\n    @media (max-width: 768px) {\n        table.shop_table_responsive tr:nth-child(2n) td, table.shop_table_responsive tr:nth-child(2n) td {\n            background-color: transparent;\n        }\n    }\n    table.shop_table{\n\t\tborder: none;\n\t}\n    .woocommerce-cart-form {\n\t\ttbody tr td {\n\t\t\tpadding-left: 1px;\n\t\t}\n\t\t#coupon_code {\n\t\t\tmargin-right: 0;\n\t\t\theight: 54px;\n\t\t}\n\t\tbutton.button:disabled[disabled]{\n\t\t\topacity: 1;\n\t\t}\n\t\t@media (max-width: 768px) {\n\t\t\ttable.shop_table_responsive tr:nth-child(2n) td, table.shop_table_responsive tr:nth-child(2n) td{\n\t\t\t\tbackground-color: transparent;\n\t\t\t}\n\t\t\t#coupon_code {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\ttable.cart td.actions .coupon .button{\n\t\t\t\twidth: auto;\n\t\t\t}\n\t\t\ttable .product-remove a{\n\t\t\t\twidth: auto;\n\t\t\t\ttext-align: right;\n\t\t\t\tmargin-bottom: 10px;\n\t\t\t}\n\t\t\ttable .product-name a{\n\t\t\t\tjustify-content: end;\n\t\t\t}\n\t\t}\n\t\t@media (min-width: 1200px) {\n\t\t\t.product-name {\n\t\t\t\tpadding-left: 30px !important;\n\t\t\t}\n\t\t\t\n\t\t\ttable.shop_table th{\n\t\t\t\tpadding: 16.8px 1px;\n\t\t\t}\n\t\t\t.product-remove {\n\t\t\t\twidth: 1rem;\n\t\t\t}\n\t\t}\n    }\n\n\t.cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals {\n\t\twidth: 100%;\n        table th {\n            padding: 16px 0;\n            width: auto;\n        }\n        .shipping-calculator-form {\n            margin: 20px 0;\n        }\n        table td {\n            padding: 16px 0;\n        }\n        .select2-container .select2-selection {\n            height: 54px;\n            padding: 8px;\n        }\n        form .form-row {\n            padding: 0;\n        }\n        .input-text {\n            padding: 14px 20px;\n            height: 54px;\n        }\n        .wc-proceed-to-checkout {\n            padding: 0;\n            margin-top: 15px;\n            a.checkout-button {\n                margin-bottom: 0;\n                font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.093), 15px);\n            }\n        }\n\t}\n}\n\n\n", "/*\n################\n* === YITH - Compare, Whislist, Quick view  ===\n################\n*/\n\n.products-block-post-template {\n    .wp-block-post {\n        &,.grid {\n            div[style=\"text-align: center\"],\n            .yith-wccp-compare,\n            .yith-wcwl-add-to-wishlist {\n                grid-column: 1 / -1;\n                grid-row: 1 / -1;\n                margin-bottom: auto;\n                margin-left: auto;\n            }\n        }\n    }\n}\n\n.wc-block-grid__products {\n    .yith-wcwl-add-to-wishlist {\n        margin-bottom: auto;\n        margin-left: auto !important;\n    }\n}\n\n.wc-block-grid__product,\n.wp-block-post {\n    .wp-block-button {\n        & + {\n            div[style=\"text-align: center\"],\n            .yith-wccp-compare,\n            .yith-wcwl-add-to-wishlist {\n                margin-top: 20px;\n            }\n        }\n    }\n\n    .yith-wcwl-add-to-wishlist {\n        margin-top: 70px;\n\n        & + .yith-wccp-compare {\n            margin-top: 70px;\n        }\n    }\n\n    .yith-wcqv-button,\n    .compare {\n        &::before {\n            font-family: \"bootstrap-icons\";\n            font-size: 14px;\n        }\n    }\n\n    .compare {\n        &::before {\n            content: \"\\F12B\";\n        }\n    }\n\n    .yith-wcqv-button {\n        &::before {\n            content: \"\\F341\";\n        }\n    }\n\n    div[style=\"text-align: center\"] {\n        & + .yith-wcwl-add-to-wishlist {\n            margin-top: 70px;\n\n            & + .yith-wccp-compare {\n                margin-top: 120px;\n            }\n        }\n\n        & + .yith-wccp-compare {\n            margin-top: 70px;\n        }\n    }\n\n    div[style=\"text-align: center\"],\n    .yith-wccp-compare,\n    .yith-wcwl-add-to-wishlist {\n        z-index: 1;\n        margin-right: 20px !important;\n        opacity: 0;\n        visibility: hidden;\n        transition: 0.3s ease-in-out;\n\n        @media (max-width:1119px) {\n            display: none;\n        }\n\n        a {\n            font-size: 0;\n            height: 40px;\n            width: 40px;\n            background-color: var(--wp--preset--color--base);\n            color: var(--wp--preset--color--contrast);\n            border-radius: 200px;\n            display: inline-flex;\n            align-items: center;\n            justify-content: center;\n\n            &:is(:hover, :focus) {\n                background-color: var(--wp--preset--color--contrast);\n                color: var(--wp--preset--color--base);\n            }\n\n            i {\n                font-size: 14px;\n                margin: 0;\n            }\n        }\n    }\n\n    &:is(:hover, :focus) {\n        div[style=\"text-align: center\"],\n        .yith-wccp-compare,\n        .yith-wcwl-add-to-wishlist {\n            opacity: 1;\n            visibility: visible;\n        }\n    }\n\n    .yith-wcwl-wishlistaddedbrowse,\n    .yith-wcwl-wishlistexistsbrowse {\n        position: relative;\n        display: inline-grid;\n        background-color: var(--wp--preset--color--base);\n        color: var(--wp--preset--color--contrast);\n        border-radius: 200px;\n        transition: 0.3s ease-in-out;\n\n        &:is(:hover, :focus) {\n            background-color: var(--wp--preset--color--contrast);\n            color: var(--wp--preset--color--base);\n        }\n\n        > * {\n            grid-column: 1 / -1;\n            grid-row: 1 / -1;\n        }\n\n        &,.feedback {\n            font-size: 0;\n            margin: auto;\n            z-index: 1;\n\n            i {\n                font-size: 14px;\n                margin: 0;\n            }\n        }\n\n        > a {\n            background-color: transparent;\n            &::after {\n                position: absolute;\n                inset: 0;\n                content: \"\";\n                z-index: 1;\n            }\n        }\n    }\n}\n\n#yith-quick-view-modal {\n    .product {\n        &, .product,.woocommerce-product-gallery,.woocommerce-product-gallery * {\n            height: 100%;\n        }\n\n        .woocommerce-product-gallery__trigger {\n            display: none;\n        }\n\n        img {\n            object-fit: cover;\n        }\n\n        .summary {\n            padding: 10px 40px;\n        }\n\n        .product_title {\n            font-size: clamp(1.125rem, -0.4464rem + 2.4554vw, 2rem);\n        }\n\n        .cart {\n            margin-bottom: 20px;\n\n            .quantity {\n                display: inline-block;\n                max-width: 100px;\n                margin-right: 14px;\n            }\n\n            .wp-element-button {\n                padding: 13.5px 40px;\n            }\n\n            &.variations_form,\n            &.grouped_form {\n                .variations,\n                .group_table {\n                    margin-bottom: 16px;\n                }\n            }\n        }\n\n        .price {\n            font-size: 18px;\n        }\n    }\n\n    #yith-quick-view-close {\n        width: 30px;\n        height: 30px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background-color: var(--wp--preset--color--danger);\n        border-radius: 200px;\n        opacity: 1;\n        color: var(--wp--preset--color--base);\n    }\n\n    .onsale {\n        color: var(--wp--preset--color--base);\n        padding: 4px 10px;\n        background-color: var(--wp--preset--color--success);\n        position: relative;\n        left: -51px;\n        z-index: 1;\n    }\n}\n\n.single-product {\n    .yith-wcwl-add-to-wishlist,\n    .yith-wccp-compare {\n        display: none;\n    }\n}", "/*\n################\n* === Mini Cart  ===\n################\n*/\n\n.wc-block-mini-cart__button {\n    padding: 0 11px 0 0;\n    cursor: pointer;\n}\n\n.wc-block-mini-cart__icon.changed {\n    margin: 0;\n    width: 18px;\n    height: 20px;\n}\n\n.wc-block-mini-cart {\n    cursor: pointer;\n}\n\nbody:not(.editor-styles-wrapper) {\n    .wc-block-mini-cart:not([data-mini-cart-icon=\"bag\"], [data-mini-cart-icon=\"bag-alt\"]) {\n        .wc-block-mini-cart__quantity-badge {\n            svg {display: none;}\n    \n            &::before {\n                width: 19px;\n                height: 18px;\n                content: \" \";\n                display: block;\n                background-image: url('data:image/svg+xml,<svg width=\"19\" height=\"18\" viewBox=\"0 0 19 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M0.833252 1.29941C0.833252 0.949871 1.12217 0.666504 1.47856 0.666504H2.11048C3.30377 0.666504 4.31032 1.538 4.45833 2.69934L4.62945 4.04203H16.8273C17.7478 4.04203 18.4528 4.84502 18.3163 5.7379L17.3938 11.7703C17.22 12.9063 16.225 13.7467 15.0538 13.7467H6.83234C5.66112 13.7467 4.66614 12.9063 4.4924 11.7703L3.42162 4.76883L3.41946 4.75345L3.17768 2.85634C3.1104 2.32846 2.65288 1.93233 2.11048 1.93233H1.47856C1.12217 1.93233 0.833252 1.64896 0.833252 1.29941ZM4.80911 5.30785L5.76874 11.5825C5.8477 12.0988 6.29997 12.4808 6.83234 12.4808H15.0538C15.5862 12.4808 16.0384 12.0988 16.1174 11.5825L17.0399 5.55012C17.0594 5.42256 16.9588 5.30785 16.8273 5.30785H4.80911Z\" fill=\"%23131C19\"/><path d=\"M16.1056 16.4893C16.1056 16.9554 15.7203 17.3333 15.2452 17.3333C14.77 17.3333 14.3848 16.9554 14.3848 16.4893C14.3848 16.0233 14.77 15.6455 15.2452 15.6455C15.7203 15.6455 16.1056 16.0233 16.1056 16.4893Z\" fill=\"%23131C19\"/><path d=\"M7.50133 16.4893C7.50133 16.9554 7.11612 17.3333 6.64093 17.3333C6.16573 17.3333 5.78052 16.9554 5.78052 16.4893C5.78052 16.0233 6.16573 15.6455 6.64093 15.6455C7.11612 15.6455 7.50133 16.0233 7.50133 16.4893Z\" fill=\"%23131C19\"/></svg>');\n            }\n        }\n    }\n}\n\n.wc-block-mini-cart__button:hover:not([disabled]) {\n    opacity: 1;\n}\n\n.wc-block-mini-cart__quantity-badge {\n    gap: 6px;\n}\n\n.wc-block-mini-cart[style=\"visibility:hidden\"] {\n    display: none;\n}\n\n.wc-block-mini-cart__badge {\n    width: 16px;\n    height: 16px;\n    background-color: var(--wp--preset--color--secondary);\n    color: var(--wp--preset--color--base);\n    font-size: 8px;\n    font-weight: 400;\n    margin-left: -5px;\n    top: 4px;\n}\n\n.wc-block-components-button:not(.is-link).outlined:not(:focus) {\n    box-shadow: none;\n}\n\n.wc-block-mini-cart__template-part {\n    .wc-block-components-drawer__close {\n        width: 30px;\n        height: 30px;\n        border-radius: 50px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 0 !important;\n        color: var(--wp--preset--color--base);\n        opacity: 1;\n        top: 36px;\n        right: 45px;\n        border-color: var(--wp--preset--color--gray-100);\n        z-index: 9999;\n        min-width: auto;\n        background-color: var(--wp--preset--color--bg-1) !important;\n\n        @media (min-width:768px) {\n            top: 40px;\n        }\n\n        svg {\n            height: 14px;\n            width: 14px;\n        }\n    }\n\n    .wc-block-components-product-metadata__description {\n        .has-global-padding {\n            padding: 0;\n        }\n\n        p {\n            font-size: 12px;\n        }\n    }\n} \n\n.wc-block-components-quantity-selector {\n    input.wc-block-components-quantity-selector__input:focus,\n    .wc-block-components-quantity-selector__button:focus {\n        box-shadow: none;\n    }\n\n    &::after {\n        border-color: var(--wp--preset--color--base);\n        opacity: 1;\n    }\n}\n\n.is-medium table.wc-block-cart-items .wc-block-cart-items__row, \n.is-mobile table.wc-block-cart-items .wc-block-cart-items__row, \n.is-small table.wc-block-cart-items .wc-block-cart-items__row {\n    grid-template-columns:50px 132px;\n\n    @media (min-width:768px) {\n        grid-template-columns:130px 132px;\n    }\n}\n\n.is-medium table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image, \n.is-mobile table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image, \n.is-small table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__image {\n    padding-right: 16px;\n\n    @media (min-width:768px) {\n        padding-right: 30px;\n    }\n}\n\n.wc-block-components-quantity-selector {\n    input.wc-block-components-quantity-selector__input {\n        font-size: 14px;\n        padding: 10px 0;\n    }\n}\n\n.wc-block-components-drawer:after {\n    border-color: var(--wp--preset--color--base);\n}\n\n.wc-block-cart-item__remove-link {\n    cursor: pointer;\n}\n\n.wc-block-components-product-badge {\n    border-width: 0;\n    background-color: color-mix(in srgb, var(--wp--preset--color--info) 90%, var(--wp--preset--color--info) 0%);\n    color: var(--wp--preset--color--base);\n    font-size: 10px;\n    border-radius: 0;\n    padding: 4px 10px;\n}\n\nh2.wc-block-mini-cart__title {\n    font-size: clamp(1rem, 0.9074rem + 0.4938vw, 1.5rem);\n    margin: 0;\n    padding: 25px 30px;\n    border-bottom: 1px solid var(--wp--preset--color--gray-100);\n    text-transform: uppercase;\n    mask-image:none;\n}\n\nh2.wc-block-mini-cart__title span:first-child {\n    margin-right: 0;\n}\n\n.wc-block-mini-cart__footer:after {\n    border-color: var(--wp--preset--color--gray-100);\n    opacity: 0;\n}\n\n.wc-block-mini-cart__items {\n    padding: 16px;\n    margin-right: 2px;\n\n    @media (min-width:768px) {\n        padding: 30px;\n    }\n\n    table.wc-block-cart-items .wc-block-cart-items__row .wc-block-cart-item__quantity .wc-block-cart-item__remove-link {\n        font-size: 15px;\n        margin-top: 14px;\n    }\n\n    .wc-block-components-quantity-selector {\n        min-width:98px;\n        width: 92px;\n        margin-top: 16px !important;\n\n        .wc-block-components-quantity-selector__button--plus {\n            margin-left: 14px;\n        }\n\n        .wc-block-components-quantity-selector__button--minus {\n            margin-right: 14px;\n        }\n\n        .wc-block-components-quantity-selector__input {\n            min-height: 24px;\n        }\n\n        button,\n        .wc-block-components-quantity-selector__button {\n            width: 20px;\n            height: 20px;\n            min-width: 20px;\n            font-size: 11px;\n            color: var(--wp--preset--color--secondary);\n            opacity: 1;\n        }\n\n        input.wc-block-components-quantity-selector__input {\n            min-width: 20px;\n        }\n    }\n    \n\n    .wc-block-mini-cart-items {\n        tbody {\n            .wc-block-cart-items__row {\n                border-bottom: 1px solid var(--wp--preset--color--gray-100);\n            }\n        }\n\n        .wc-block-components-product-price__value.is-discounted {\n            margin-left: 0;\n        }\n\n        .wc-block-components-product-name {\n            font-size: 15px;\n            font-weight: 500;\n        }\n\n        .wc-block-cart-item__prices .wc-block-components-product-price {\n            padding: 0;\n            justify-content: flex-end;\n            display: flex;\n            flex-direction: row-reverse;\n            align-items: center;\n            gap: 4px;\n            font-size: 15px;\n            \n            del {\n                font-size: 12px;\n                color: var(--wp--preset--color--gray-300);\n                font-weight: normal;\n            }\n        }\n\n        .wc-block-cart-item__total .wc-block-components-product-price {\n            padding: 0 0 0 30px;\n        }\n    }\n\n    &::-webkit-scrollbar {\n        width: 2px;\n        height: 2px;\n    }\n\n    &::-webkit-scrollbar-thumb {\n        background-color: var(--wp--preset--color--contrast);\n    }\n\n    &::-webkit-scrollbar-track {\n        background-color: var(--wp--preset--color--gray-100);\n    }\n\n    .wc-block-cart-item__total {\n        .wc-block-components-product-price {\n            justify-content: flex-end;\n        }\n    }\n\n    .wc-block-cart-item__image {\n        a {\n            display: block;\n        }\n    }\n}\n\n.wc-block-mini-cart__drawer.is-medium table.wc-block-cart-items .wc-block-cart-items__row, \n.wc-block-mini-cart__drawer.is-mobile table.wc-block-cart-items .wc-block-cart-items__row, \n.wc-block-mini-cart__drawer.is-small table.wc-block-cart-items .wc-block-cart-items__row {\n    padding: 0 0 30px 0;\n    margin-bottom: 30px;\n}\n\n.wc-block-mini-cart__footer {\n    padding: 20px 30px;\n\n    .wc-block-mini-cart__footer-actions {\n        column-gap: 27px;\n        row-gap: 14px;\n    }\n\n    .wc-block-mini-cart__footer-actions{\n        .components-button {\n            min-height: 50px;\n        }\n    }\n\n    .wc-block-components-totals-item__value,\n    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal {\n        font-weight: 500;\n        font-size: 19px;\n    }\n\n    .wp-block-woocommerce-mini-cart-checkout-button-block {\n        &:not(:hover) {\n            //color: var(--wp--preset--color--contrast);\n        }\n    }\n\n    .wp-block-woocommerce-mini-cart-cart-button-block {\n        &:not(:hover) {\n            border-color: var(--wp--preset--color--primary);\n            color: var(--wp--preset--color--primary);\n        }\n    }\n\n    .wp-element-button {\n        padding: 14.22px;\n        //border-radius: 50px;\n        line-height: 1.572;\n        color: var(--wp--preset--color--base);\n        gap: 10px;\n        transition: 0.3s ease-in-out;\n\n        &::after{\n            content: \"\\F144\";\n            font-family: bootstrap-icons;\n        }\n    }\n\n    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal {\n        padding-bottom: 6px;\n        margin-bottom: 20px;\n    }\n\n    .wc-block-components-totals-item.wc-block-mini-cart__footer-subtotal .wc-block-components-totals-item__description {\n        font-size: 12px;\n    }\n}\n\n.wc-block-mini-cart__empty-cart-wrapper {\n    strong {\n        font-weight: 500;\n    }\n\n    p {\n        font-size: 20px;\n        margin-bottom: 30px;\n    }\n\n    a {\n        padding: 10px 30px;\n        min-height: 50px;\n    }\n}", "/*\n################\n* === MY ACCOUNT STYLE  ===\n################\n*/\n\n\n.woocommerce-account  {\n    .woocommerce-notices-wrapper {\n        margin-bottom: 24px;\n    }\n\n    .alignfull {\n        padding-top: 0;\n    }\n\n    .wp-block-post-content {\n        > .woocommerce {\n            gap: 20px;\n            display: grid;\n\n            @media (min-width:768px) {\n                grid-template-columns: repeat(12, minmax(0, 1fr));\n            }\n\n            > .woocommerce-MyAccount-navigation,\n            > .woocommerce-MyAccount-content {\n                padding: 38px 0;\n\n                @media (min-width:768px) {\n                    padding: 48px 0;\n                }\n            }\n\n            > .woocommerce-MyAccount-navigation {\n                border-bottom: 1px solid var(--wp--preset--color--gray-100);\n\n                @media (min-width:768px) {\n                    border-right: 1px solid var(--wp--preset--color--gray-100);\n                    border-block-width: 0;\n                }\n\n                ul {\n                    list-style: none;\n                    padding: 0;\n                    margin: 0;\n\n                    > li:not(:last-child) {\n                        margin-bottom: 8px;\n                    }\n\n                    @media (min-width:768px) {\n                        padding: 0 30px 0 0;\n                    }\n\n                    @media (min-width:1200px) {\n                        padding: 0 60px 0 0;\n                    }\n\n                    a {\n                        display: flex;\n                        align-items: center;\n                        gap: 12px;\n                        color: var(--wp--preset--color--secondary);\n                        padding: 10px 16px;\n                        border-radius: 4px;\n                        transition: 0.3s ease-in-out;\n                        font-weight: 500;\n                    }\n\n                    li:not(.is-active) a:hover {\n                        background-color: var(--wp--preset--color--bg-2);\n                    }\n\n                    li.is-active a {\n                        color: var(--wp--preset--color--contrast);\n                        background-color: var(--wp--preset--color--bg-2);\n                    }\n                }\n\n                @media (min-width:768px) {\n                    grid-column: span 4 / span 4;\n                }\n\n                @media (min-width:1200px) {\n                    grid-column: span 3 / span 3;\n                }\n            }\n\n            > .woocommerce-MyAccount-content {\n                padding-top: 14px;\n                overflow: auto;\n\n                @media (min-width:768px) {\n                    grid-column: span 8 / span 8;\n                    padding-top: 48px;\n                    padding-left: 20px;\n                    overflow: visible;\n                }\n\n                @media (min-width:1200px) {\n                    grid-column: span 9 / span 9;\n                    padding-left: 40px;\n                }\n            }\n        }\n\n        & + .wp-block-group {\n            display: none;\n        }\n    }\n\n    .wp-block-post-content .woocommerce {\n        max-width: 1200px;\n        font-size: 16px;\n    }\n\n    .woocommerce-customer-details {\n        margin-top: 30px;\n    }\n\n    .woocommerce-orders-table__row {\n        .woocommerce-orders-table__cell-order-status span {\n            font-size: 12px;\n            padding: 4px 7px;\n            color: var(--wp--preset--color--base);\n            font-weight: 700;\n            border-radius: 4px;\n            background-color: var(--wp--preset--color--gray-500);\n        }\n    }\n\n    .woocommerce-orders-table__cell-order-actions {\n        .view {\n            display: inline-flex;\n            font-size: 12px;\n            align-items: center;\n            gap:6px;\n            padding: 4px 10px;\n            border-radius: 4px;\n            transition: 0.3s ease-in-out;\n            min-width: auto;\n            \n            &::before {\n                font-family: bootstrap-icons;\n                content: \"\\F341\";\n            }\n        }\n    }\n\n    .woocommerce-orders-table__row--status {\n        &-on-hold {\n            .woocommerce-orders-table__cell-order-status span {\n                background-color: var(--wp--preset--color--warning);\n            }\n        }\n\n        &-failed {\n            .woocommerce-orders-table__cell-order-status span {\n                background-color: var(--wp--preset--color--danger);\n            }\n        }\n\n        &-processing {\n            .woocommerce-orders-table__cell-order-status span {\n                background-color: var(--wp--preset--color--info);\n            }\n        }\n\n        &-completed {\n            .woocommerce-orders-table__cell-order-status span {\n                background-color: var(--wp--preset--color--success);\n            }\n        }\n    }\n\n    .woocommerce-MyAccount-content {\n        @media (min-width:768px) {\n            padding-right: 14px;\n        }\n\n        @media (min-width:1200px) {\n            padding-right: 0;\n        }\n\n        h2 {\n            font-size: var(--wp--preset--font-size--grande);\n            margin-top: 30px;\n        }\n\n        .woocommerce-Address-title {\n            display: flex;\n            justify-content: space-between;\n\n            h3 {\n                margin-bottom: 12px;\n            }\n        }\n\n        .woocommerce-PaymentMethods {\n            list-style: none;\n            padding: 0;\n            margin: 0;\n        }\n\n        .woocommerce-address-fields__field-wrapper {\n            gap:16px;\n\n            &,.form-row {\n                display: flex;\n                flex-direction: column;\n            }\n\n            .form-row {\n                gap:8px;\n            }\n\n            .woocommerce-input-wrapper {\n                display: flex;\n            }\n\n            input {\n                width: 100%;\n            }\n        }\n\n        .woocommerce-EditAccountForm {\n            gap:16px;\n\n            .clear {display: none;}\n\n            &,.woocommerce-form-row {\n                display: flex;\n                flex-direction: column;\n            }\n\n            .woocommerce-form-row {\n                gap:8px;\n                margin-bottom: 0;\n\n                input {\n                    width: auto;\n                    font-size: 14px;\n                }\n            }\n\n            fieldset {\n                border: 1px solid var(--wp--preset--color--gray-100);\n                padding: 20px;\n                border-radius: 6px;\n\n                legend {\n                    color: var(--wp--preset--color--contrast);\n                    font-weight: 600;\n                }\n\n                .password-input {\n                    display: flex;\n\n                    input {\n                        width: 100%;\n                    }\n                }\n\n                .woocommerce-form-row:not(:last-child) {\n                    margin-bottom: 16px;\n                }\n            }\n        }\n\n\n        .order-again {\n            text-align: center;\n\n            a {\n                display: inline-flex;\n                margin: 20px 0;\n                font-size: 14px;\n                align-items: center;\n                gap:6px;\n                padding: 8px 20px;\n                border-radius: 4px;\n                color: var(--wp--preset--color--base);\n                transition: 0.3s ease-in-out;\n                background-color: var(--wp--preset--color--secondary);\n\n                &::before {\n                    font-family: bootstrap-icons;\n                    content: \"\\F406\";\n                }\n\n                &:hover {\n                    background-color: var(--wp--preset--color--secondary-hover);\n                }\n            }\n        }\n\n        .woocommerce-Addresses.col2-set {\n            margin-top: 20px;\n\n            h3 {\n                font-size: var(--wp--preset--font-size--grande);\n            }\n        }\n\n        .woocommerce-Addresses.col2-set,\n        .woocommerce-columns {\n            border: 1px solid var(--wp--preset--color--gray-100);\n\n            @media (min-width:1024px) {\n                display: grid;\n                grid-template-columns: repeat(2, minmax(0, 1fr));\n            }\n\n            h2,h3 {\n                margin-top: 0;\n            }\n\n            .woocommerce-column,\n            .woocommerce-Address {\n                padding: 26px 30px;\n            }\n        }\n\n        strong {\n            color: var(--wp--preset--color--contrast);\n        }\n\n        a:not(.button):not(:hover) {\n            color: var(--wp--preset--color--secondary);   \n        }\n\n        a:not(.button):hover {\n            text-decoration: underline;\n            text-underline-offset: 4px;\n        }\n\n        .woocommerce-table--order-details {\n            tfoot {\n                th {\n                    text-align: left;\n                }\n                th,td {\n                    padding: 8px 12px;\n                }\n            }\n        }\n\n        .woocommerce-MyAccount-orders,\n        .woocommerce-table {\n            border-collapse: collapse;\n            border: 1px solid var(--wp--preset--color--gray-100);\n\n            @media (min-width:1024px) {\n                min-width: 500px;\n            }\n\n            thead {\n                th {\n                    border-top-color: transparent;\n                    color: var(--wp--preset--color--secondary);\n                    background-color: var(--wp--preset--color--bg-2);\n                    padding: 8px 12px;\n                    text-align: left;\n                }\n        \n                .product-name {\n                    text-align: left;\n                }\n            }\n\n            tr td.product-thumbnail a {\n                display: flex;\n        \n                img {\n                    height: auto;\n                    width: 64px;\n                }\n            }\n        \n            tr td.product-remove {\n                padding: 8px;\n            }\n        \n            tbody {\n                tr {\n                    td {\n                        border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                        text-align: left;\n                        padding: 8px;\n\n                        span {\n                            white-space: nowrap;\n                        }\n                    }\n            \n                    &:first-child td {\n                        border-top: 1px solid var(--wp--preset--color--gray-100);\n                    }\n                }\n        \n                .product-name {\n                    a {\n                        font-size: 14px;\n                        font-weight: 600;\n                        \n                        &:not(:hover) {\n                            color: var(--wp--preset--color--secondary);\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .woocommerce-MyAccount-navigation-link {\n        a::before {\n            font-family: bootstrap-icons;\n            font-size: 16px;\n        }\n\n        &--dashboard {\n            a::before {\n                content: \"\\F2EE\";\n            }\n        }\n\n        &--orders {\n            a::before {\n                content: \"\\F180\";\n            }\n        }\n\n        &--downloads {\n            a::before {\n                content: \"\\F30A\";\n            }\n        }\n\n        &--edit-address {\n            a::before {\n                content: \"\\F3E8\";\n            }\n        }\n\n        &--payment-methods {\n            a::before {\n                content: \"\\F2DC\";\n            }\n        }\n\n        &--edit-account {\n            a::before {\n                content: \"\\F4D7\";\n            }\n        }\n\n        &--customer-logout {\n            a::before {\n                content: \"\\F1C3\";\n            }\n        }\n    }\n\n    &.woocommerce-lost-password {\n        .woocommerce {\n            .woocommerce-message {\n                order: -1;\n                padding: 10px 16px;\n                border-radius: 6px;\n                background-color: #d1e7dd;\n                color: var(--wp--preset--color--success);\n                display: flex;\n                align-items: center;\n                gap: 10px;\n            }\n        }\n    }\n\n    &:not(.logged-in) {\n        .wp-block-post-title {\n            display: none;\n        }\n\n        header + main {\n            padding: var(--wp--preset--spacing--20) 0 !important;\n        }\n\n        main > section.wp-block-template-part {display: none;}\n\n        main > .wp-block-post-content {\n            margin-block-start:0;\n        }\n\n        &.woocommerce-lost-password{\n            .woocommerce {\n                .woocommerce-message {\n                    order: -1;\n                    padding: 10px 16px;\n                    border-radius: 6px;\n                    background-color: #d1e7dd;\n                    color: var(--wp--preset--color--success);\n                    display: flex;\n                    align-items: center;\n                    gap: 10px;\n                }\n            }\n        }\n\n        #customer_login {\n            > div:not(.active) {\n                display: none;\n            }\n\n            > div {\n                h2 {\n                    display: none;\n                }\n\n                a {\n                    color: var(--wp--preset--color--secondary);\n                    transition: 0.3s ease-in-out;\n                }\n            }\n        }\n\n        .customer_login_toggle {\n            list-style: none;\n            padding: 0;\n            margin: 0;\n            display: flex;\n            justify-content: space-between;\n            background-color: var(--wp--preset--color--base);\n            border-bottom: 1px solid var(--wp--preset--color--gray-100);\n            border: none;\n\n            li {\n                padding: 10px 28px;\n                position: relative;\n                cursor: pointer;\n                flex-grow: 1;\n                text-align: center;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                @media(min-width: 768px) {\n                     padding: 19px 28px;\n                }\n\n                h4 {\n                    color: var(--wp--preset--color--secondary);\n                    margin: 0;\n                    font-size: 18px;\n                    //text-transform: uppercase;\n                }\n\n                &.active {\n                    //box-shadow: inset 0px -3px 0px 0px var(--wp--preset--color--secondary);\n                    background-color: var(--wp--preset--color--secondary);\n                    border-radius: 0;\n                    \n                    h4 {\n                        color: var(--wp--preset--color--base);\n                    }\n                }\n            }\n        }\n\n        .entry-content {\n            margin-top: 0 !important;\n        }\n\n        .woocommerce {\n            max-width: 443px;\n            display: block;\n\n            .woocommerce-form-login {\n                p:last-child {\n                    flex-direction: row;\n                    flex-wrap: wrap;\n                    justify-content: space-between;\n                    align-items: center;\n                    gap: 14px;\n                    \n                    @media (min-width:768px) {\n                        gap: 20px;\n                    }\n\n                    button {\n                        width: 100%;\n                        margin: 0;\n                    }\n                }\n            }\n\n            .woocommerce-form-login,\n            .woocommerce-form-register,\n            .woocommerce-ResetPassword {\n                padding: 30px 0;\n               // box-shadow: 4px 12px 15.48px 2.52px rgba(137, 137, 137, 0.1);\n                justify-content: center;\n                gap: 20px;\n                // background-color: var(--wp--preset--color--base);\n                // border-radius: 0 0 12px 12px;\n\n                 @media(min-width: 768px) {\n                    padding: 40px 0;\n                 }\n\n                .clear {display: none;}\n\n                &,.form-row {\n                    display: flex;\n                    flex-direction: column;\n                    margin-top: 0;\n                }\n\n                > p,\n                .woocommerce-privacy-policy-text p {\n                    margin-bottom: 0;\n                }\n\n                .form-row {\n                    gap: 10px;\n                    margin-bottom: 0;\n\n                    .woocommerce-LostPassword {\n                        margin-bottom: 0;\n                        margin-top: 0;\n                    }\n\n                    .woocommerce-form__label-for-checkbox {\n                        display: inline-flex;\n                        align-items: center;\n\n                        span {\n                            font-weight: 400;\n                        }\n\n                        input[type=checkbox] {\n                            width: 14px;\n                            height: 14px;\n                            margin: 0 10px 0 0;\n                        }\n                    }\n\n                    > input,\n                    .password-input input {\n                        width: auto;\n                    }\n\n                    .password-input {\n                        display: flex;\n\n                        input {width: 100%;}\n                    }\n\n                    &:not(.woocommerce-form-row--wide) {\n                        margin-top: 5px;\n                    }\n                }\n\n                button.woocommerce-button,\n                button.woocommerce-Button {\n                    display: block;\n                    font-size: 18px;\n                    font-weight: 500;\n                    transition: 0.3s ease-in-out;\n                    text-transform: capitalize;\n                }\n            }\n\n            > h2 {\n                margin-bottom: 0;\n                position: relative;\n                margin-top: 0;\n                //border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                color: var(--wp--preset--color--secondary);\n                //padding: 21px 28px;\n                text-align: center;\n                font-size: var(--wp--preset--font-size--x-large);\n                // text-transform: uppercase;\n                // background-color: var(--wp--preset--color--secondary);\n                // border-radius: 12px 12px 0 0;\n                // box-shadow: inset 0px -3px 0px 0px var(--wp--preset--color--secondary);\n            }\n        }\n    }\n\n    wc-order-attribution-inputs {\n        display: none;\n    }\n\n    .woocommerce {\n        .woocommerce-MyAccount-navigation, .woocommerce-MyAccount-content{\n            float: none;\n            width: 100%;\n        }\n        .woocommerce-MyAccount-navigation li {\n            padding: 0;\n            a:hover{\n                text-decoration: none !important;\n            }\n        }\n    }\n    .woocommerce::before, .woocommerce::after {\n        display: none;\n    }\n\n    .woocommerce .col2-set .col-1,\n    .woocommerce-page .col2-set .col-1,\n    .woocommerce .col2-set .col-2,\n    .woocommerce-page .col2-set .col-2 {\n        width: 100%;\n    }\n\n    form .form-row .woocommerce-form-login__rememberme {\n        margin-bottom: 0;\n    }\n\n    form.login,\n    form.register {\n        border: none;\n    }\n\n}\n\n .woocommerce-LostPassword {\n    a {\n        color: var(--wp--preset--color--primary) !important;\n    }\n}\n\nform.woocommerce-form-track-order {\n    display: grid;\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n    gap: 20px;\n\n    .clear {\n        display: none;\n    }\n\n    > * {\n        grid-column: span 12 / span 12;\n        margin-bottom: 0;\n    }\n\n    .form-row-first,\n    .form-row-last {\n        display: flex;\n        flex-direction: column;\n        gap: 4px;\n        \n        @media (min-width:768px) {\n            grid-column: span 6 / span 6;\n        }\n\n        input {\n            width: auto;\n        }\n    }\n\n    .wp-element-button {\n        min-width: 100px;\n    }\n}\n\n", "/*\n################\n* === Checkout Style  ===\n################\n*/\n\n.woocommerce-checkout, \n.wp-block-woocommerce-checkout:not(.block-editor-block-list__block) {\n    .woocommerce:not(.wc-block-breadcrumbs) {\n        margin: clamp(2.75rem, 2.0556rem + 2.7037vw, 3rem) 0;\n    }\n\n    #order_review_heading {\n        font-size: 20px;\n        font-weight: 500;\n        margin-top: 0px;\n        font-family: var(--wp--preset--font-family--heading);\n    }\n\n    wc-order-attribution-inputs {\n        display: none;\n    }\n\n    .wc-block-components-product-price__value.is-discounted {\n        margin-left: 0;\n    }\n\n    form.checkout {\n        margin-top: 40px;\n    }\n\n    .wc-block-components-sidebar-layout.wc-block-checkout,\n    form.checkout {\n        @media (min-width:1024px) {\n            display: grid;\n            grid-template-columns: repeat(12, minmax(0, 1fr));\n            gap: 30px;\n\n            .wc-block-components-main,\n            #customer_details {\n                grid-column: span 8 / span 8;\n                padding: 0 !important;\n            }\n\n            .wc-block-components-sidebar, #order_review {\n                grid-column: span 4 / span 4;\n            }\n\n            .wc-block-components-main, .wc-block-components-sidebar {\n                width:100%;\n            }\n        }\n\n        @media (min-width:1200px) {\n            column-gap: 76px;\n        }\n\n        .woocommerce-billing-fields,\n        .woocommerce-additional-fields,\n        .woocommerce-shipping-fields {\n            h3 {\n                font-size: 20px;\n                font-weight: 500;\n                line-height: 30px;\n                margin: 0px 0px 27px 0px;\n                font-family: var(--wp--preset--font-family--heading);\n            }\n        }\n\n        .woocommerce-shipping-fields {\n           label.woocommerce-form__label-for-checkbox {\n                display: flex;\n                align-items: center;\n            }\n        }\n            \n        .woocommerce-billing-fields__field-wrapper,\n        .woocommerce-shipping-fields__field-wrapper,\n        .woocommerce-additional-fields__field-wrapper {\n             > p:not([class]) {\n                display: none;\n            }\n            \n            p {\n                margin-top: 0;\n                margin-bottom: 24px;\n            }\n\n            @media (min-width: 768px) {\n                display: grid;\n                column-gap: 30px;\n                grid-template-columns: repeat(12, minmax(0, 1fr));\n            }\n\n            .form-row:not(.form-row-first):not(.form-row-last) {\n                @media (min-width: 768px) {\n                    grid-column: span 12/span 12;\n                }\n            }\n\n            .form-row-first, .form-row-last {\n                @media (min-width: 768px) {\n                    grid-column: span 6/span 6;\n                }\n            }\n\n            .form-row.notes {\n                @media (min-width: 1025px) {\n                    margin: 0;\n                }\n            }\n\n            label {\n                font-size: 15px;\n                font-weight: 400;\n                margin-bottom: 10px !important;\n                display: inline-block;\n                color: var(--wp--preset--color--secondary);\n            }\n        }\n    }\n\n    .woocommerce-info {\n        margin-bottom: 30px;\n    }\n\n    table {\n        width: 100%;\n        border-collapse: collapse;\n        text-align: left;\n    }\n\n    form.checkout_coupon {\n        margin-top: 20px;\n        font-size: 14px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 14px;\n        margin-bottom: 30px;\n\n        p:first-child {\n            width: 100%;\n            margin-bottom: 0;\n            margin-top: 0;\n        }\n\n        .form-row {\n            display: flex;\n        }\n    }\n\n    .wc-block-checkout__sidebar,\n    .woocommerce-checkout-review-order {\n        border: 1px solid var(--wp--preset--color--gray-100);\n        border-radius: 0 !important;\n        padding: 20px;\n        align-self: flex-start;\n\n        @media(min-width: 1200px) {\n             padding: 30px 40px 35px;\n        }\n\n        .wc-block-components-order-summary__button-text {\n            font-size: 20px;\n            font-weight: 500;\n            line-height: 30px;\n            font-family: var(--wp--preset--font-family--heading);\n        }\n\n        .wc-block-components-panel__button {\n            cursor: pointer;\n        }\n\n        .wc-block-components-order-summary__content .wc-block-components-order-summary-item__image {\n            width: 70px;\n            padding-bottom: 0;\n\n            img {\n                height: 70px;\n                min-width: 70px;\n                background-color: var(--wp--preset--color--base);\n            }\n        }\n\n        .wc-block-components-order-summary-item__description,\n        .wc-block-components-order-summary-item__total-price {\n            margin-top: 10px;\n            font-size: 15px;\n        }\n\n        .wc-block-components-order-summary-item__total-price {\n            font-weight:400;\n        }\n\n        .wc-block-components-product-metadata {\n            display: none;\n        }\n        \n        .wc-block-components-totals-coupon-link{\n            font-weight: 500;\n            text-transform: capitalize;\n\n            &:hover { color:var(--wp--preset--color--secondary); }\n        }\n\n        .wc-block-components-order-summary-item__quantity {\n            font-size: 12px;\n            background-color: var(--wp--preset--color--secondary);\n            border: 0;\n            color: var(--wp--preset--color--base);\n        }\n\n        .wp-block-woocommerce-checkout-order-summary-cart-items-block {\n            border-width: 0;\n            padding-top: 0;\n        }\n\n        .wc-block-components-totals-wrapper:last-child {\n            padding-bottom: 0;\n            padding-top: 0;\n            border-top: 0;\n        }\n    }\n\n    .woocommerce-checkout-review-order {\n        .shop_table thead tr {\n            font-weight: 600;\n            letter-spacing: 0em;\n            text-align: left;\n        }\n\n        table {\n            thead tr {\n                font-size: 15px;\n                line-height: 26px;\n                border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                color: var(--wp--preset--color--secondary);\n                text-transform: uppercase;\n\n                th {\n                    padding-bottom: 14px;\n                    font-weight: 500;\n                }\n            }\n\n            tbody {\n                color: var(--wp--preset--color--secondary); \n                border-bottom: 1px solid var(--wp--preset--color--gray-100);               \n                tr:first-child td {\n                    // padding-top: 20px;\n                    padding: 20px 1px;\n                }\n\n                tr td {\n                    // padding-bottom: 20px;\n                    padding: 1px 1px 20px 1px;\n                }\n            }\n\n            tfoot {\n                color: var(--wp--preset--color--secondary);\n\n                tr {\n                    & + tr {\n                        border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                        border-top: 1px solid var(--wp--preset--color--gray-100);\n                    }\n                }\n            }\n\n            tr th:last-child,\n            tr td:last-child {\n                text-align: right;\n            }\n\n            .cart-subtotal, .shipping {\n                th,td  {\n                    padding: 14px 0;\n                }\n            }\n\n            .order-total {\n                // border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                // border-top: 1px solid var(--wp--preset--color--gray-100);\n\n                th {\n                    font-weight: 500;\n                    font-size: 18px;\n                }\n\n                td {\n                    font-weight: 500;\n                    font-size: 18px;\n\n                    strong {\n                        font-weight: 500;\n                    }\n                }\n\n                th,td {\n                    padding: 24px 0;\n                }\n            }\n\n            .cart-subtotal, .shipping {\n                th, td {\n                    font-weight: 400;\n                }\n            }\n\n            .shipping {\n                ul {\n                    list-style: none;\n                    padding-left: 0;\n                    margin: 0;\n\n                    li label {\n                        padding: 5px 0;\n                        cursor: pointer;\n                    }\n                }\n            }\n        }\n\n        #payment {\n            ul {\n                list-style: none;\n                padding-left: 0;\n\n                li label {\n                    font-size: 16px;\n                    font-weight: 500;\n                    line-height: 26px;\n                    letter-spacing: 0em;\n                    text-align: left;\n                    cursor: pointer;\n                }\n\n                li:not(:last-child) {\n                    margin-bottom: 22px;\n                }\n\n                li {\n                    display: flex;\n                    align-items: center;\n                    flex-wrap: wrap;\n\n                    .payment_box {\n                        line-height: 24px;\n                        width: 100%;\n                    }\n                }\n            }\n\n            .woocommerce-terms-and-conditions-wrapper {\n                padding-bottom: 1px;\n                margin-bottom: 18px;\n            }\n\n            .place-order .button {\n                width: 100%;\n                background-color: var(--wp--preset--color--primary);\n                color: var(--wp--preset--color--base);\n                padding: 12px;\n                font-size: 15px;\n\n                &:hover { background-color:var(--wp--preset--color--secondary); color: var(--wp--preset--color--base);}\n            }\n        }\n    }\n\n    .wc-block-components-product-price__regular {\n        margin-right: 10px !important;\n    }\n\n    .woocommerce-NoticeGroup-checkout, \n    .woocommerce-checkout form.checkout #wc-stripe-payment-request-wrapper, \n    .woocommerce-checkout form.checkout #wc-stripe-payment-request-button-separator {\n        @media (min-width: 768px) {\n            grid-column: span 12/span 12;\n        }\n    }\n\n    .woocommerce-error, .wc-block-components-notice-banner.is-error {\n        background-color: #fdeeee;\n        color: #d54848;\n        border-color: #f6a8a8;\n        font-size: 14px;\n\n        svg {\n         display: none;\n        }\n\n        .wc-block-components-notice-banner__content {\n            align-self: center;\n            flex-basis: 100%;\n            padding-right: 16px;\n            white-space: normal;\n\n            ul {\n                margin: 0;\n                \n                li {\n                    list-style: none;\n                }\n            }\n        }\n    }\n\n    #payment .is-info {\n        margin-bottom: 0;\n    }\n\n    .woocommerce-shipping-methods label {\n        font-size: 14px;\n        line-height: 30px;\n    }\n}\n\n.wp-block-woocommerce-checkout {\n    .wc-block-components-sidebar .wc-block-components-panel {\n        padding: 0!important;\n    }\n\n    .wc-block-components-radio-control__option:before,\n    .wc-block-components-address-card {\n        border-color: var(--wp--preset--color--gray-100);\n    }\n\n    .wc-block-components-totals-wrapper {\n        color: var(--wp--preset--color--secondary);\n    }\n\n    .is-large .wc-block-components-sidebar .wc-block-components-panel, \n    .is-large .wc-block-components-sidebar .wc-block-components-totals-coupon, \n    .is-large .wc-block-components-sidebar .wc-block-components-totals-item {\n        padding-left: 0px;\n        padding-right: 0px;\n    }\n\n    .wc-block-checkout__sidebar .wc-block-components-order-summary-item__quantity, \n    .woocommerce-checkout .woocommerce-checkout-review-order .wc-block-components-order-summary-item__quantity {       \n        line-height:21px;\n        font-weight: 700;\n    }\n\n    .wc-block-checkout__use-address-for-billing label {\n        margin-left:0 !important;\n    }\n\n    .wc-block-components-checkbox .wc-block-components-checkbox__mark {\n        display: none;\n    }\n\n    .wc-block-components-checkout-step__description {\n        font-size: 15px;\n        display: inline-block;\n    }\n\n    .wc-block-components-textarea {\n        border-color: var(--wp--preset--color--gray-100);\n    }\n\n    .wc-block-components-shipping-rates-control,\n    .wc-block-components-radio-control {\n        .wc-block-components-radio-control__label-group {\n            font-size: 15px;\n        }\n    }\n\n    .wp-block-woocommerce-checkout-order-summary-block {\n        border: 0;\n\n        .wp-block-woocommerce-checkout-order-summary {\n            &-subtotal-block,\n            &-coupon-form-block {\n                padding: 12px 0;\n            }\n\n            &-shipping-block {\n                padding: 10px 0;\n            }\n        }\n\n        .wc-block-components-totals-item,\n        .wc-block-components-totals-shipping {\n            .wc-block-components-totals-item__value {\n                font-weight: 400;\n            }\n        } \n\n        .wc-block-components-totals-footer-item {\n            @media(min-width: 1024px) {\n                padding-top: 32px;\n            }\n\n            .wc-block-components-totals-item__value {\n                font-size: 24px;\n                line-height: 18px;\n                font-weight: 500;\n            }\n        }\n\n        .wc-block-components-totals-item {\n            align-items: center;\n        }\n\n        .wp-block-woocommerce-checkout-order-summary-subtotal-block {\n            border-bottom: 1px solid hsla(0,0%,7%,.11) !important;\n        }\n    }\n\n    .wc-block-checkout__main {\n        width: 70%;\n\n        .wc-block-components-checkout-step__title {\n            font-family: var(--wp--preset--font-family--heading);\n            font-weight: 500;\n            font-size: var(--wp--preset--font-size--grande);\n        }\n\n        .wc-block-components-form .wc-block-components-text-input, \n        .wc-block-components-text-input, \n        .wc-block-components-country-input, \n        .wc-block-components-state-input {\n            margin-top: 30px;\n        }\n\n        .wc-block-components-checkbox label {\n            font-size: 14px;\n            align-items: center;\n        }\n\n        .wc-block-components-radio-control-accordion-content {\n            padding: 20px 16px;\n        }\n    }\n\n    .wc-block-checkout__shipping-option {\n        .wc-block-components-radio-control__option {\n            padding-left: 35px !important;\n        }\n    }\n\n    .wc-block-checkout__payment-method .wc-block-components-radio-control__option {\n        padding-left: 38px;\n    }\n\n    .wc-block-components-checkout-step__heading {\n        margin: 30px 0 20px;\n    }\n\n    .wc-block-components-checkout-step__container {\n        > p.wc-block-components-checkout-step__description {\n            margin-bottom: 30px;\n        } \n    }\n\n    .wc-block-checkout__sidebar, \n    .woocommerce-checkout-review-order {\n        border-radius: 0;\n        padding: 20px;\n        align-self: flex-start;\n        width: 30%;\n\n        @media (min-width:1200px) {\n            padding: 30px 35px;\n        }\n    }\n}\n\n.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form, \n.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form, \n.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form, \n.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form, \n.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form, \n.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form {\n    gap: 0 30px;\n}\n\n.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, \n.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input, \n.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, \n.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input, \n.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, \n.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, \n.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, \n.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input  {\n    flex: 1 0 calc(50% - 15px);\n}\n\n.wc-item-meta { \n    list-style: none;\n    padding-left: 0px;\n    margin: 5px 0 0;\n\n    li {\n        display: flex;\n        align-items: center;\n        gap: 5px;\n\n        .wc-item-meta-label {\n            font-weight: 700;\n        }\n        p { margin: 0px;}\n    }\n\n    li + li {\n        margin-top: 5px;\n    }\n}\n\n.wc-block-checkout__payment-method .wc-block-components-radio-control {\n    border-color: var(--wp--preset--color--gray-100);\n}\n\n.wc-block-components-totals-footer-item .wc-block-components-totals-item__label {\n    font-weight: 500;\n}\n\n.is-mobile .wp-block-woocommerce-checkout-order-summary-cart-items-block.wc-block-components-totals-wrapper {\n\n    @media(max-width: 1023px) {\n         padding-left: 0 !important; \n         padding-right: 0 !important; \n    }   \n}\n\n.wp-block-woocommerce-checkout {\n    .wc-block-components-form .wc-block-components-text-input:not(.is-active) label, .wc-block-components-text-input:not(.is-active) label {\n        transform: translateY(-8px) !important;\n    }\n    .wc-blocks-components-select {\n        .wc-blocks-components-select__select{\n            border: 1px solid var(--wp--preset--color--gray-100);\n            padding: 12.42px 40px 12.42px 20px;\n            border-radius: 0;\n            &:focus {\n                border: 1px solid var(--wp--preset--color--contrast);\n                box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n            }\n        }\n        .wc-blocks-components-select__container{\n            height: 54px;\n            margin-top: 0;\n        }\n        .wc-blocks-components-select__label {\n            top: -14px;\n            font-size: 20px;\n            background-color: var(--wp--preset--color--base);\n            padding: 2px 11px;\n            left: 14px;\n        }\n    } \n    .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-checkout-order-summary__title {\n        margin: 0;\n        .wc-block-components-checkout-order-summary__title-text{\n            margin: 0 0 8px 0;\n            padding: 0 0 8px 0;\n            font-size: 20px;\n            font-weight: 500;\n            line-height: 30px;\n        }\n    }   \n    .checkout-order-summary-block-fill .wc-block-components-totals-coupon__input.wc-block-components-text-input {\n        margin-top: auto !important;\n    }\n    .wc-block-components-radio-control__label-group {\n        box-sizing: border-box;\n        display: flex;\n        flex-wrap: wrap;\n        width: 100%;\n        .wc-block-components-radio-control__label {\n            flex-grow: 1;\n        }\n    }\n\n    .wc-block-components-order-summary {\n        padding: 0;\n    }\n}    \n\n//ss\n.woocommerce-page.woocommerce-checkout main .woocommerce {\n    max-width: var(--wp--style--global--wide-size);\n    form .form-row input.input-text {\n        height: 54px;\n    }\n    .select2-container .select2-selection {\n        padding: 8px;\n        height: 54px;\n    }\n    form .form-row {\n        padding: 0;\n    }\n    form.checkout .woocommerce-billing-fields__field-wrapper label {\n        line-height: 1.4;\n    }\n    #payment div.form-row {\n        padding: 0;\n        margin: 0;\n    }\n    form.checkout_coupon {\n        border: none;\n        padding: 0;\n    }\n    .checkout.woocommerce-checkout {\n        .col2-set .col-1, .col2-set .col-2 {\n            float: none;\n            width: 100%;\n        }\n        #billing_first_name_field, #billing_last_name_field, #shipping_first_name_field, #shipping_last_name_field {\n            width: 100%;\n        }\n        form .form-row input.input-text {\n            height: 54px;\n        }\n    }\n    .woocommerce-checkout-review-order {\n        #payment {\n            background: none;\n        }\n        table.shop_table {\n            width: 100%;\n            border: none;\n            th {\n                padding: 1px 1px 14px 1px;\n            }\n            td {\n                border-top: 0;\n            }\n        }\n    }\n    .woocommerce-shipping-totals {\n        td {\n            width: 50%;\n        }\n        ul#shipping_method li input {\n            margin: 2px 5px 5px 5px;\n            vertical-align: middle;\n        }\n    }\n    .woocommerce-checkout-review-order #payment ul li label {\n        margin: 0;\n    }\n}\n", "/*\n################\n* === Prodcut Filter Style  ===\n################\n*/\n\nbody:not(.editor-styles-wrapper) {\n    @media (min-width:1024px) {\n        #shop-filter-toggle:not(.active) {\n            #showFilter {\n                display: none;\n            }\n        }\n    }   \n\n    @media (min-width:1200px) {\n        #shop-filter-toggle.active {\n            #showFilter {\n                display: none;\n            }\n        }\n    }\n}\n\n@media (max-width: 1023px) {\n    .filter-content .filter-close {\n        position: absolute;\n        right: 30px !important;\n        top:20px !important;\n        padding: 12px;\n        background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath d='M8 1.2A6.74 6.74 0 0 0 1.2 8 6.74 6.74 0 0 0 8 14.8 6.74 6.74 0 0 0 14.8 8 6.74 6.74 0 0 0 8 1.2zM0 8c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8zm10.6-2.6a.61.61 0 0 1 0 .8L8.8 8l1.9 1.9a.61.61 0 0 1 0 .8.61.61 0 0 1-.8 0L8 8.8l-1.9 1.9a.61.61 0 0 1-.8 0 .61.61 0 0 1 0-.8L7.2 8 5.4 6.1a.61.61 0 0 1 0-.8.61.61 0 0 1 .8 0l1.9 1.9L10 5.3c.1-.1.4-.1.6.1z' fill-rule='evenodd' fill='%23000'/%3e%3c/svg%3e\");\n        background-repeat: no-repeat;\n        background-position: center;\n        margin-top: 0;\n        opacity: .55;\n    }\n}\n\n.filter-content {\n    details {\n        summary {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            font-family: var(--wp--preset--font-family--heading);\n\n            &::after {\n                font-family: \"bootstrap-icons\";\n                content:\"\\F285\";\n                font-size: 13px;\n                font-weight: 700;\n            }\n        }\n\n        &[open] {\n            summary {\n                &::after {\n                    content:\"\\F282\";\n                }\n            }\n        }\n    }\n\n    .wp-block-woocommerce-product-categories .wc-block-product-categories-list-item:not(:last-child) {\n        margin-bottom: 14px;\n    }\n}\n\n#shop-filter-toggle {\n    transition: .3s ease-in-out;\n\n    @media (min-width:1024px) {\n        &.active {\n            gap: 0;\n\n            .filter-block {\n                flex-basis: 0% !important;\n                visibility: hidden;\n                opacity: 0;\n                max-height: 200px;\n            }\n    \n            .archive-content {\n                flex-basis: 100% !important;\n            }\n        }\n    }\n\n    .archive-content,\n    .filter-block {\n        transition: .3s ease-in-out;\n    }\n\n    @media (max-width:1023px) {\n        &::after {\n            content: \"\";\n            background-color: color-mix(in srgb, var(--wp--preset--color--black) 70%, var(--wp--preset--color--white) 10%);\n            position: fixed;\n            inset: 0;\n            z-index: 2;\n            transition: .3s ease-in-out;\n            opacity: 0;\n            visibility: hidden;\n        }\n\n        &.active  {\n            .filter-block {\n                opacity: 1;\n                visibility: visible;\n                left: 0;\n            }\n\n            &::after{\n                opacity: 1;\n                visibility: visible;\n            }\n        }\n\n        .filter-block {\n            left: -100%;\n            z-index: 9;\n            background-color: var(--wp--preset--color--white);\n            top: 0;\n            bottom: 90px;\n            padding: 30px;\n            max-width: 400px;\n            opacity: 0;\n            visibility: hidden;\n            transition: all 0.2s ease;\n            overflow-y: auto;\n            margin-top: 0;\n            position: fixed;\n        }\n\n        #showFilter {\n            @media (min-width:1200px) {\n                display: none;\n            }\n\n            .wp-block-button__link {\n                &::before {\n                    content: '\\F3E1';\n                    font-family: \"bootstrap-icons\";\n                }\n            }\n        }\n\n        #showFilter,\n        #CloseFilter {\n            position: fixed;\n            bottom: 0;\n            right: 0;\n            left: 0;\n            z-index: 9;\n            width: 100%;\n            background-color: rgb(from var(--wp--preset--color--secondary) r g b / .86);\n            justify-content: center;\n            padding: 25px 0;\n\n            .wp-block-button__link {\n                background-color: var(--wp--preset--color--white);\n                display: flex;\n                gap: 10px;\n                border-color: transparent !important;\n                font-weight: 700;\n            }\n        }\n    }\n}\n\n@media (max-width:1023px) {\n    .toggle-overflow-hidden {\n        #shop-filter-toggle #showFilter .wp-block-button__link::before {\n            content: \"\\F659\";\n        }\n    }\n}\n\n.wc-block-product-categories.wc-block-product-categories ul {\n    margin-left: 0;\n}\n\n@media (min-width:1024px) and (max-width:1199px) {\n    .filter-block {\n        flex-basis: 30%  !important;\n    }\n\n    .archive-content {\n        flex-basis: 70%  !important;\n    }\n}\n\n.archive-content {\n    .wc-block-catalog-sorting {\n        select {\n            border-width: 0;\n            padding: 0;\n        }\n    }\n\n    .woocommerce.wc-block-catalog-sorting select.orderby {\n        font-size: 15px;\n        text-transform: capitalize;\n        background-size: 18px 10px;\n        background-position: right -4px center;\n\n        &:focus {\n            border-width: 0;\n            box-shadow: none;\n        }\n    }\n}\n\n@media (max-width:1023px) {\n    .admin-bar #shop-filter-toggle .filter-block {\n        margin-top: 46px;\n    }\n\n    body.toggle-overflow-hidden {\n        overflow: hidden;\n    }\n}\n\n.wc-block-components-price-slider__actions {\n    justify-content: flex-start !important;  \n    margin-top: 10px;\n}\n\n.wc-block-product-categories-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n\n    ul {\n        padding-left: 16px;\n        list-style: none;\n    }\n}\n\n.wc-block-components-product-rating__stars {\n    color: var(--wp--preset--color--warning);\n    font-family: \"bootstrap-icons\";\n    font-size: 12px;\n    width: 6.2em;\n    letter-spacing: 2.8px;\n    margin: 0 0 8px;\n    font-weight: 400;\n    height: 1.618em;\n    line-height: 1.618;\n    overflow: hidden;\n    position: relative;\n    text-align: left;\n    display: inline-block;\n\n\n    &::before {\n        content: '\\F586\\F586\\F586\\F586\\F586';\n        font-family: \"bootstrap-icons\";\n        color: var(--wp--preset--color--warning);\n        opacity: 1;\n        position: absolute;\n        right: 0;\n        top: 0;\n        white-space: nowrap;\n    }\n\n    span:before,\n    strong:before {\n        font-family: \"bootstrap-icons\";\n        content: '\\F586\\F586\\F586\\F586\\F586';\n    }\n\n    > span {\n        display: block;\n        overflow: hidden;\n    }\n}\n\n.wc-block-attribute-filter__actions {\n    justify-content: flex-start;\n\n    .wc-block-components-filter-reset-button {\n        margin-bottom: 20px;\n        margin-top: 20px;\n        color: var(--wp--preset--color--danger);\n    }\n}\n\nbody:not(.editor-styles-wrapper) .filter-block input[type=checkbox],\nbody:not(.editor-styles-wrapper) .filter-block .wc-block-components-checkbox input[type=checkbox], \n.filter-block .wc-block-checkbox-list .wc-block-components-checkbox input[type=checkbox],\n.filter-block div.wc-block-components-checkbox input[type=checkbox] {\n    margin: 0 11px 0 0;\n}\n\n.filter-block {\n    .wc-block-components-checkbox {\n        label {\n            align-items: center;\n            font-size: 16px;\n        }\n    }\n}\n\n.wc-block-components-price-slider__range-input-wrapper:before {\n    color: var(--wp--preset--color--gray-100);\n    opacity: 1;\n}\n\n.wc-block-components-price-slider__range-input-progress {\n    color: var(--wp--preset--color--secondary);   \n}\n\n.wc-block-components-price-slider__range-input-wrapper,\n.wc-block-components-price-slider__range-input-progress {\n    height: 3px;\n}\n\nbody:not(.editor-styles-wrapper) {\n    .filter-color {\n        #blue::before {\n            background-color: #0016F5;\n        }\n    \n        #gray::before {\n            background-color: #808080;\n        }\n    \n        #green::before {\n            background-color: #3A7E21;\n        }\n    \n        #red::before {\n            background-color: #F5513A;\n        }\n    \n        #yellow::before {\n            background-color: #f2de2c;\n        }\n\n        #brown::before {\n            background-color: #973530;\n        }\n\n        #orange::before {\n            background-color: #F2A93B;\n        }\n\n        #pink::before {\n            background-color: #FAE5E2;\n        }\n    \n        #black::before {\n            background-color: #000;\n        }\n    \n        .wc-block-attribute-filter-list {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 10px 20px;\n        }\n    \n        &.wp-block-woocommerce-filter-wrapper .wc-block-components-checkbox {\n            margin-top: 0;\n        }\n    \n        .wc-block-checkbox-list {\n            .wc-block-components-checkbox {\n                position: relative;\n    \n                label {\n                    position: static;\n                }\n    \n                input:checked[type=checkbox],\n                .wc-block-components-checkbox__input[type=checkbox]:checked {\n                    background-image: none;\n                    background-color: var(--wp--preset--color--base);\n                    border-width: 0;\n                    \n                    &::after {\n                        border: 1px solid var(--wp--preset--color--secondary);\n                    }                \n                }\n        \n                input[type=checkbox],\n                .wc-block-components-checkbox__input[type=checkbox] {\n                    border-width: 0;\n                    display: grid;\n                    overflow: visible;\n                    min-width: 30px;\n                    width: 30px;\n                    height: 30px;\n\n                    &::before,\n                    &::after {\n                        grid-column: 1 / -1;\n                        grid-row: 1 / -1;\n                        margin: auto;\n                    }\n    \n                    &::before {\n                        width: 20px;\n                        height: 20px;\n                        border-radius: 100px;\n                        display: block;\n                    }\n    \n                    &::after {\n                        border: 1px solid transparent;\n                        border-radius: 50px;\n                        height: 30px;\n                        width: 30px;\n                    }\n                }\n            }\n        }\n    }\n}\n\n.wc-block-components-price-slider__range-input--min::-webkit-slider-thumb, \n.wc-block-components-price-slider__range-input--max::-webkit-slider-thumb {\n    box-shadow: 0;\n    border: 2px solid var(--wp--preset--color--secondary) !important;\n    background: var(--wp--preset--color--base) !important;\n    height: 25px;\n    width: 25px;\n    \n    &:is(:hover,:active,:focus,:focus-visible,:focus-within) {\n        background: var(--wp--preset--color--base);\n        box-shadow: 0 0 0 0.236em rgba(0,0,0,.1);\n    }\n}\n\n.wc-block-components-price-slider__range-input {\n    top: -7px;\n}\n\n.wc-block-components-price-slider__range-input--min:focus \n.wc-block-components-price-slider__range-input--max:focus{\n    background-color: var(--wp--preset--color--base);\n}\n\n.wc-block-components-price-slider__range-input--min:hover, \n.wc-block-components-price-slider__range-input--max:hover {\n    &,\n    &::-webkit-slider-thumb {\n        box-shadow: 0 2px 8px rgba(0, 0, 0, .09);\n        background: var(--wp--preset--color--base);\n    }\n}\n\n.wc-block-components-product-rating__stars {\n    margin: 0;\n}\n\n.wc-block-components-product-rating {\n    display: flex;\n}\n\n.wp-block-woocommerce-filter-wrapper .wc-block-components-checkbox {\n    margin-top: 11px;\n}\n\n.wp-block-woocommerce-filter-wrapper .wc-block-rating-filter .wc-block-components-checkbox {\n    margin-top: 17px;\n}\n\n.wc-block-components-price-slider__range-text {\n    justify-content: flex-start;\n    gap: 6px;\n    align-items: center;\n    margin: 21px 0 0 0;\n\n    span:first-child {\n        order: -1;\n    }\n\n    &::before {\n        content: \"__\";\n        display: block;\n        line-height: 1;\n        position: relative;\n        top: -6px;\n    }\n}\n\n.wc-block-components-price-slider {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n}\n\n.wc-block-components-price-slider__range-input-wrapper {\n    width: 100%;\n}\n\n.wc-block-components-price-slider__actions button[type=submit]:not(.wp-block-search__button).wc-block-components-filter-submit-button {\n    font-size: 15px;\n    font-weight: 500;\n    text-transform: capitalize;\n    background-color: transparent;\n    border-width: 0;\n    padding: 0;\n    color: var(--wp--preset--color--contrast);\n    text-align: right;\n}\n\n.wc-block-components-filter-reset-button {\n    color: var(--wp--preset--color--secondary);\n}\n\n.wc-block-components-price-slider__actions {\n    margin-top: 20px;\n}\n\n#openFilter {\n    a {\n        display: flex;\n        align-items: center;\n        gap: 10px;\n        font-weight: 700;\n\n        &::after {\n            content: '\\F3E1';\n            font-family: \"bootstrap-icons\";\n        }\n    }\n}\n\n.open {\n    #openFilter a::after {\n        content: \"\\F659\";\n    }\n}\n\nbody:not(.editor-styles-wrapper) {\n    #openFilter {\n        display: none;\n\n        @media (min-width:1024px) {\n            display: block;\n        }\n    }\n}\n\n@media (min-width:1024px) {\n    body:not(.editor-styles-wrapper) {    \n        .shop-v1 {\n            display: grid;\n            margin-bottom: 0;\n        \n            > div {\n                grid-column: 1/-1;\n                grid-row: 1/-1;\n            }\n        \n            &:not(.open) .filter-block {\n                display: none;\n            }\n        \n            .filter-block {\n                width: 100%;\n                z-index: 2;\n                margin-top: 66px;\n        \n                > div {\n                    padding: 40px;\n                    border: 1px solid var(--wp--preset--color--contrast);\n                }\n            }\n        \n            .filter-content {\n                > div {\n                    max-width: var(--wp--style--global--wide-size);\n                    display: flex;\n                    gap: 40px;\n        \n                    > * {\n                        margin-top: 0 !important;\n                        flex: 0 100%;\n                    }\n        \n                    .wp-block-heading {\n                        margin-top: 0;\n                        padding-top: 0 !important;\n                    }\n                }\n            }\n        \n            #openFilter {\n                .wp-block-button__link {\n                    img:last-child {\n                        display: none;\n                    }\n                }\n            }\n        \n            &.open {\n                #openFilter {\n                    .wp-block-button__link {\n                        img:first-child {\n                            display: none;\n                        }\n        \n                        img:last-child {\n                            display: block;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n\n", "/*\n################\n* === Single Product - Blocks style  ===\n################\n*/\n\n.single-product {\n    form.cart {\n        &,.variations_button {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 10px;\n\n        }\n\n        .variations_button {\n            flex-wrap: wrap;\n            @media(min-width: 768px) {\n                flex-wrap: nowrap;\n            }\n        }\n\n        .single_variation_wrap {\n            width: 100%;\n        }\n\n        &.grouped_form {\n            gap: 30px;\n            //flex-wrap: wrap !important;\n        }\n\n        .reset_variations {\n            position: absolute;\n            right: 0;\n            top: -28px;\n        }\n\n        .woocommerce-variation-description {\n            font-size: 15px;\n            line-height: 1.9;\n\n            p {\n                margin-top: 0;\n            }\n        }\n\n        .woocommerce-variation-price {\n            font-size: 18px;\n            padding: 10px 0 20px;\n            font-weight: 500;\n        }\n\n        .woocommerce-variation-availability {\n            padding-bottom: 4px;\n        }\n\n        .variations {\n            position: relative;\n            margin-bottom: 20px;\n\n            th.label {\n                font-weight: 500;\n                text-transform: uppercase;\n            }\n\n            tr {\n                &:not(:first-child):not(:last-child) td,\n                &:not(:first-child):not(:last-child) th {\n                    padding: 0 0 15px;\n                }\n\n                &:first-child td,\n                &:first-child th {\n                    padding-bottom: 16px;\n                }\n            }\n        }\n\n        button.wp-element-button {\n            line-height: 1.1;\n            flex: 1;\n\n            // @media (max-width:767px) {\n            //     width: 100%;\n            // }\n\n            // @media (min-width:768px) {\n            //     min-width: 300px;\n            // }\n        }\n\n        .woocommerce-grouped-product-list-item {\n            &:not(:first-child):not(:last-child) td {\n                padding: 0 0 19px;\n            }\n\n            &:first-child td {\n                padding-bottom: 20px;\n            }\n        }\n\n        .woocommerce-grouped-product-list-item__quantity {\n            width: 110px;\n            @media(min-width: 1200px) {\n                width: 120px; \n            }\n\n            .qty-container {\n                width: 100px;\n\n                .qty {\n                    min-width: 40px;\n                    width: 40px !important;\n                }\n            }\n        }\n    }\n\n    .single-product-content {\n        .wp-block-post-title + .wp-block-woocommerce-product-price {\n            margin-top: 11px;\n        }\n\n        .wc-block-components-product-rating__container {\n            display: flex;\n            align-items: center;\n            gap: 7px;\n\n           \n            .wc-block-grid__product-rating__stars {\n                margin: 0;\n            }\n            \n        }\n    }\n\n\n    &.product-type-external {\n        .single-product-content {\n            form.cart {\n                .wp-element-button {\n                    padding: 18px clamp(1.125rem, 0.7778rem + 1.8519vw, 3rem);\n                }\n            }\n        }\n    }\n\n    &.woocommerce {\n        .wp-block-woocommerce-product-image-gallery {\n            max-width: 100%;\n        }\n\n        .wp-block-woocommerce-product-image-gallery {\n            span.onsale {\n                position: absolute;\n                right: 0;\n                top: 0;\n                left: auto;\n                color: var(--wp--preset--color--base);\n                background-color: var(--wp--preset--color--ebony);\n                padding: 2px 10px;\n            }\n        }\n    }\n\n    .single-product-gallery {\n        .wp-block-woocommerce-product-image-gallery  > .woocommerce-product-gallery {\n            @media (min-width:1200px) {\n                display: flex;\n                flex-direction: row-reverse;\n                gap: 10px;\n            }\n        }\n    }\n\n    .flex-viewport {\n        position: relative;\n    }\n\n    .single-product-main {\n        @media (max-width:1199px) {\n            gap: 30px;\n        }\n    }\n\n    .flex-control-nav {\n        list-style: none;\n        margin: 0;\n        padding: 0;\n        flex-shrink: 0;\n        display: flex;\n        gap: 10px;\n        flex-wrap: wrap;\n        margin-top: 10px;\n\n        @media (min-width:1200px) {\n            width: 100px;\n            margin-top: 0;\n            flex-direction: column;\n        }\n\n        li {\n            &,img {\n                height: 60px;\n                width: 60px;\n\n                @media (min-width:1200px) {\n                    width: 100px;\n                    height: 120px;\n                    object-fit: cover;\n                }\n            }\n\n            img {\n                aspect-ratio: 1/1;\n                cursor:pointer;\n            }\n\n            img:not(.flex-active) {\n                opacity: 0.5;\n            }\n\n            // .flex-active {\n            //     box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);\n            // }\n        }\n    }\n\n    .woocommerce-product-gallery__image {\n        > a {\n            display: flex;\n\n            img {\n                object-fit: cover;\n                aspect-ratio: 1/1;\n\n                @media (max-width:1023px) {\n                    width: 100%;\n                }\n            \n                @media (min-width:1200px) {\n                    width: 694px;\n                    max-width: 694px;\n                }\n            }\n        }\n    }\n\n    .woocommerce-product-gallery {\n        position: relative;\n\n        > .woocommerce-product-gallery__wrapper {\n            @media (min-width:1200px) {\n                width: 100%;\n            }\n\n            .woocommerce-product-gallery__image {\n                > a {        \n                    img {                    \n                        @media (min-width:1200px) {\n                            width: 100%;\n                            max-width: 100%;\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .woocommerce-product-gallery__trigger {\n        position: absolute;\n        right: 30px;\n        top: 30px;\n        z-index: 1;\n        font-size: 0;\n\n        &::before {\n            content: \"\";\n            font-size: 14px;\n            display: block;\n            background-repeat: no-repeat;\n            width: 50px;\n            height: 50px;\n\n            background-image: url('data:image/svg+xml,<svg width=\"50\" height=\"50\" viewBox=\"0 0 50 50\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect width=\"50\" height=\"50\" rx=\"25\" fill=\"white\"/><path d=\"M24.2 17H25V33H24.2V17Z\" fill=\"%23131C19\"/><path d=\"M17 25L17 24.2L33 24.2V25L17 25Z\" fill=\"%23131C19\"/></svg>');\n        }\n    }\n\n    .single-product-content {\n        @media (max-width:1199px) {\n            > div {\n                padding-left: 0 !important;\n            }\n        }\n\n        .no-stock {display: none;}\n\n        .stock {\n            display: block;\n            min-width: clamp(1.25rem, -1.9907rem + 17.284vw, 18.75rem);\n            text-transform: uppercase;\n            text-align: center;\n            padding: 15.5px 1rem;\n            margin: 0;\n\n            &.out-of-stock,\n            &.available-on-backorder {\n                border: 1px solid var(--wp--preset--color--primary);\n                color: var(--wp--preset--color--primary);\n                font-family: var(--wp--preset--font-family--heading);\n            }\n\n            &.in-stock,\n            &.available-on-backorder {\n                margin-bottom: 20px;\n            }\n\n            &.in-stock {\n                border: 1px solid var(--wp--preset--color--success);\n                color: var(--wp--preset--color--success);\n            }\n        }\n\n        .wp-block-woocommerce-product-price {\n            .wc-block-grid__product-price {\n                display: flex;\n                gap: 8px;\n                // flex-direction: row-reverse;\n                // justify-content: flex-end;\n                align-items: center;\n\n                del {\n                    font-size: clamp(14px, 0.875rem + ((1vw - 3.2px) * 0.37), 18px);\n                    color: var(--wp--preset--color--gray-300);\n                    line-height: 1;\n                }\n            }\n        }\n\n        .quantity {\n             .qty-container {\n                 width: 130px;\n\n                 .qty-plus {\n                     padding-top: 3.5px;\n                }\n\n                button {\n                    font-size: 16px;\n                }\n             }\n        }\n    }\n\n    .wp-block-woocommerce-product-details {\n        .wc-tabs {\n            font-size: var(--wp--preset--font-size--x-large);\n            font-family: var(--wp--preset--font-family--heading);\n            text-align: center;\n            font-weight: 500;\n            line-height: 1.2;\n\n            display: none;\n\n            li {\n                 font-weight: 500 !important;\n                 a {\n                    color: var(--wp--preset--color--secondary);\n                }\n            }\n        }\n\n        .woocommerce-tabs {\n            padding-top: 5px;\n\n            .wc-tab {\n                > h2 {\n                    font-size: var(--wp--preset--font-size--x-large);\n                    line-height: 48px;\n                    font-weight: 500;\n                }\n            }\n            .woocommerce-product-attributes {\n                th {\n                \n                    @media (min-width:768px) {\n                        width: 35%;\n                    }\n                }\n\n\n                tr {\n                    th,td {\n                        padding: 11px 0;\n                        border-bottom: 1px solid var(--wp--preset--color--gray-100);\n                        color: var(--wp--preset--color--secondary);\n                        font-size: 15px;\n                        font-weight: 400;\n                        line-height: 30px;\n\n                        p {\n                            margin: 0;\n                        }\n                    }\n\n                    td {\n                        text-align: right;\n                    }\n                }\n\n                \n            }\n\n            .woocommerce-Reviews-title {\n                font-size: 24px;\n                font-weight: 500;\n                margin-bottom: 33px;\n                line-height: 29px;\n            }\n\n            .panel-title {\n                font-size: 18px;\n                font-weight: 700;\n                margin-bottom: 30px;\n\n                @media (min-width:768px) {\n                    font-size: 20px;\n                }\n            \n                a {\n                    display: flex;\n                    align-items: center;\n                    justify-content: space-between;\n\n                    &::after {\n                        font-family: \"bootstrap-icons\";\n                        content:\"\\F64D\";\n                    }\n                }\n\n                &.active {\n                    a {\n                        &::after {\n                            content:\"\\F63B\";\n                        }\n                    }\n                }\n            }\n\n            .panel {\n                margin-bottom: 30px;\n                display: block !important;\n\n                @media (min-width:1024px) {\n                    margin-bottom: 0;\n                }\n\n                & + .panel {\n                    margin-top: var(--wp--preset--spacing--50);\n                    padding-top: 1px;\n                }\n            }\n        }\n\n        .woocommerce-Reviews {\n            .woocommerce-noreviews,\n            #review_form_wrapper {\n                font-size: 16px;\n                font-family: var(--wp--preset--font-family--heading);\n            }\n        }\n\n        #tab-description {\n            > p {\n                line-height: 1.6;\n\n                @media (min-width:768px) {\n                    font-size: 15px;\n                    line-height: 30px;\n                }\n            }\n\n            ul {\n                line-height: 1.7;\n                padding-left: 0;\n\n                > li {\n                    display: flex;\n                    align-items: center;\n\n                    &::marker {\n                        content: none;\n                    }\n\n                    &:before {\n                        content: \"\\f309\";\n                        font-size: 18px;\n                        font-family: bootstrap-icons;\n                    }\n                }\n            }\n\n            .has-x-large-font-size {\n                @media (max-width:1199px) {\n                    font-size: clamp(18px, 1rem + ((1vw - 3.2px) * 1.481), 36px) !important;\n                }\n            }\n        }\n    }\n\n    .single_add_to_cart_button {\n        padding-top: 18px;\n        padding-bottom: 18px;\n    }\n\n\n}\n\nhtml body.single-product .wp-block-woocommerce-product-details.is-style-minimal ul.tabs.wc-tabs {\n    li {\n        border-bottom-width: 1px;\n        padding: 0;\n        opacity: 1;\n\n        a {\n            padding: 9px 0;\n        }\n    \n        &:not(:first-child):not(:last-child) {\n            margin: 0 30px;\n        }\n\n        &:first-child:nth-last-child(2) {\n            margin: 0 30px 0 0;\n        }\n    \n        &:not(.active) {\n            color: color-mix(in srgb, var(--wp--preset--color--gray-200) 50%, var(--wp--preset--color--gray-200) 0%);\n        }\n    }\n\n    li.active {\n        border-bottom-width: 1px;\n    }\n}\n\n.pswp--open[aria-hidden=\"true\"] {\n    display: none;\n}\n\n.wp-block-woocommerce-related-products {\n    margin-bottom: var(--wp--preset--spacing--50) !important;\n\n    @media (min-width: 600px) {\n        .slick-slide > div {\n            padding: 20px 13.5px;\n        }\n    }\n\n    @media (min-width: 768px) {\n        .products-block-post-template .product .has-medium-font-size {\n            font-size: 15px !important;\n            margin-bottom: 5px;\n        }\n    }\n    \n    \n    .slick-list {\n        margin: 0 -14px !important;\n\n        @media (max-width:767px) {\n            .slick-slide > div {\n                padding: 0 10px;\n            }\n        }\n    }\n}\n\n.woocommerce .woocommerce-Reviews {\n    \n\n    #review_form_wrapper {\n        margin-top: 52px;\n    }\n\n    \n\n    .clear {display: none;}\n\n    .comment-form-comment,\n    .comment-form-author,\n    .comment-form-email {\n        margin-bottom: 22px;\n    }\n\n    .comment-reply-title {\n        font-size: 24px;\n        font-weight: 500;\n        margin-top: 0;\n        margin-bottom: 33px;\n        display: block;\n    }\n\n    .comment-notes {\n        margin-top: 0;\n        margin-bottom: 25px;\n    }\n\n    .comment-form {\n       \n\n        label {\n            font-weight: 500;\n            display: inline-block;\n            margin-bottom: 13px;\n        }\n\n        .comment-form-comment {\n            margin-top: 25px;\n        }\n    }\n\n    p.stars.selected a.active::before,\n    p.stars.selected a:not(.active)::before {\n        content: '\\F586';\n        font-family: \"bootstrap-icons\";\n    }\n\n    p.stars.selected a.active~a::before,\n    p.stars a::before {\n        content: '\\F588';\n        font-family: \"bootstrap-icons\";\n    }\n\n\n    .commentlist {\n        list-style: none;\n        padding: 0;\n\n        .avatar {\n            border-radius: 50%;\n\n            @media (min-width: 768px) {\n                width: 70px;\n                height: 70px;\n            }\n        }\n\n        .comment_container {\n            display: flex;\n            gap: 26px;\n            flex-wrap: wrap;\n            align-items: center;\n        }\n\n        .review {\n            padding: clamp(1.25rem, 1.0185rem + 1.2346vw, 2.5rem) 0 clamp(1rem, 0.9074rem + 0.4938vw, 1.5rem);\n            border-top: 1px solid var(--wp--preset--color--gray-100);\n            display: flex;\n            flex-direction: column;\n            gap: 30px;\n\n            &:last-child {\n                border-bottom: 1px solid var(--wp--preset--color--gray-100);\n            }\n\n            .children {\n                list-style: none;\n                order: 2;\n\n                > .comment {\n                    display: flex;\n                    flex-direction: column;\n                    gap: 20px;\n                }\n            }\n\n            .description {\n                line-height: 30px;\n                font-size: 15px;\n                width: 100%;\n    \n                p {\n                    margin: 0;\n                }\n            }\n        }\n    }\n\n    .woocommerce-review__author {\n        text-transform: capitalize;\n        font-size: 20px;\n        font-weight: 500;\n        font-family: var(--wp--preset--font-family--heading);\n    }\n\n    .comment-text {\n        display: flex;\n        flex-wrap: wrap;\n        flex-grow: 1;\n        justify-content: space-between;\n        align-items: center;\n\n        .star-rating {\n        \n            &:before {\n                left: 0;\n            }\n        }\n        \n        .meta {\n            margin: 0;\n            line-height: 1.4;\n            display: flex;\n            order: -1;\n            flex-direction: column;\n            gap: 5px;\n\n            .woocommerce-review__dash {display: none;}\n\n            time {\n                color: var(--wp--preset--color--secondary);\n                font-size: 15px;\n                font-weight: 300;\n                font-family: var(--wp--preset--font-family--body);\n            }\n        }\n    }\n}\n\n#commentform {\n    .comment-form-comment,\n    .comment-form-author,\n    .comment-form-email,\n    .comment-form-url {\n        display: flex;\n        flex-direction: column;\n        gap:5px;\n    }\n\n    input[type=submit] {\n        width: 100%;\n    }\n\n    input[type=text],\n    input[type=password],\n    input[type=email],\n    input[type=number],\n    input[type=url],\n    input[type=search],\n    input[type=tel],\n    textarea {\n        width: auto;\n    }\n\n    .comment-form-cookies-consent {\n        margin-bottom: 26px;\n        display: flex;\n        gap: 10px !important;\n\n        input[type=checkbox] {\n            flex-shrink: 0;\n            margin-right: 0;\n        }\n\n        label {\n            margin-bottom: 0;\n            font-size: 15px;\n            font-weight: 400;\n            font-family: var(--wp--preset--font-family--body);\n        }\n\n        #wp-comment-cookies-consent {\n            margin-top: 4px;\n        }\n    }\n}\n\n.woocommerce div.product .wc-block-add-to-cart-form form.cart .quantity, \n.woocommerce div.product .wc-block-add-to-cart-form form.cart button.single_add_to_cart_button {\n    margin-bottom: 0;\n}\n\n.single-product-details .wp-block-details[open] > summary::after,\n.single-product-details .wp-block-details summary::after{\n    font-size: 8px;\n}\n\n.single-product-details summary {\n    display: flex;\n    align-items: center;\n}", "/*\n################\n* === Order Complete Style  ===\n################\n*/\n\n.woocommerce-order-received {\n    .wc-block-order-confirmation-status p {\n\t\ttext-align: center;\n\t}\n\n    img[src*=\"order-icon.svg\"] {\n        @media (max-width:767px) {\n            max-width: 60px;\n        }\n    }\n\n    .wc-block-order-confirmation-summary-list {\n        list-style:none;\n\t\tpadding: 40px 80px;\n\t\tborder-radius: 0 ;\n\t\tborder: 1px dashed var(--wp--preset--color--secondary);\n        background-color: var(--wp--preset--color--bg-1);\n       // background-color: color-mix(in srgb, var(--wp--preset--color--contrast) 5%, var(--wp--preset--color--base) 5%);\n\n        @media (max-width:768px) { \n\t\t\tpadding: 30px 40px;\n\t\t}\n\n        li {\n\n            @media (max-width:768px) { \n                display: flex;\n                flex-direction: column;\n                flex-basis: 0;\n                flex-grow: 1;\n            }\n\n           \n\n\n            .wc-block-order-confirmation-summary-list-item__key {\n                font-size: 15px;\n                font-weight: 400;\n            }\n\n            .wc-block-order-confirmation-summary-list-item__value {\n                font-size: 15px;\n                font-weight: 500;\n                margin-top: 8px;\n            }\n        }\n\n        \n    }\n\n    .wc-block-order-confirmation-totals-wrapper {\n\n        border: 1px solid var(--wp--preset--color--gray-100);\n        padding: 0px 40px;\n        border-radius: 0;\n\n        h3.wp-block-heading {\n            font-family: var(--wp--preset--font-family--heading) !important;\n            text-transform: capitalize !important;\n            font-size: 20px !important;\n        }\n\n        .wc-block-order-confirmation-totals table {\n            border: 0;\n        }\n\n        .wc-block-order-confirmation-totals__table {\n            thead {\n                font-size: 15px;\n                font-weight: 500;\n                text-transform: uppercase;\n            }\n\n            tfoot {\n                font-size: 17px;\n                font-weight: 500;\n                th{\n                    font-weight: 500;\n                }\n               \n            }\n\n            .wc-block-order-confirmation-totals__product, .wc-block-order-confirmation-totals__total, .wc-block-order-confirmation-totals__label, .wc-block-order-confirmation-totals__total {\n                padding-left: 0px;\n                padding-right: 0px;\n            }\n        }\n\n    }\n\n    .woocommerce-order-confirmation-address-wrapper {\n        margin-top: 30px;\n\n        h3.wp-block-heading {\n            margin-bottom: 10px;\n        }\n    }\n\n    .wc-block-order-confirmation-billing-address,\n    .wc-block-order-confirmation-shipping-address {\n        line-height: 26px;\n        border-color: var(--wp--preset--color--gray-100);\n    }\n\n    .product-quantity {\n        font-weight: 400;\n    }\n\n    .wc-bacs-bank-details {\n        border: 1px solid var(--wp--preset--color--gray-100);\n        border-radius: 4px;\n        padding: 16px 20px;\n        line-height: 1.8;\n        list-style: none;\n    }\n\n    .wc-bacs-bank-details-heading {\n        text-align: center;\n        margin-top: clamp(1.25rem, 0.787rem + 2.4691vw, 3.75rem);\n    }\n}", "/*\n################\n* === BOOSTRAP ICONS  ===\n################\n*/\n\n@font-face {\n    font-display: block;\n    font-family: \"bootstrap-icons\";\n    src: url(\"assets/fonts/bootstrap-icons/bootstrap-icons.woff2?8d200481aa7f02a2d63a331fc782cfaf\") format(\"woff2\"), url(\"assets/fonts/bootstrap-icons/bootstrap-icons.woff?8d200481aa7f02a2d63a331fc782cfaf\") format(\"woff\");\n  }\n  \n  .bi::before,\n  [class^=\"bi-\"]::before,\n  [class*=\" bi-\"]::before {\n    display: inline-block;\n    font-family: bootstrap-icons !important;\n    font-style: normal;\n    font-weight: normal !important;\n    font-variant: normal;\n    text-transform: none;\n    line-height: 1;\n    vertical-align: -.125em;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  \n  .bi-123::before {\n    content: \"\\f67f\";\n  }\n  \n  .bi-alarm-fill::before {\n    content: \"\\f101\";\n  }\n  \n  .bi-alarm::before {\n    content: \"\\f102\";\n  }\n  \n  .bi-align-bottom::before {\n    content: \"\\f103\";\n  }\n  \n  .bi-align-center::before {\n    content: \"\\f104\";\n  }\n  \n  .bi-align-end::before {\n    content: \"\\f105\";\n  }\n  \n  .bi-align-middle::before {\n    content: \"\\f106\";\n  }\n  \n  .bi-align-start::before {\n    content: \"\\f107\";\n  }\n  \n  .bi-align-top::before {\n    content: \"\\f108\";\n  }\n  \n  .bi-alt::before {\n    content: \"\\f109\";\n  }\n  \n  .bi-app-indicator::before {\n    content: \"\\f10a\";\n  }\n  \n  .bi-app::before {\n    content: \"\\f10b\";\n  }\n  \n  .bi-archive-fill::before {\n    content: \"\\f10c\";\n  }\n  \n  .bi-archive::before {\n    content: \"\\f10d\";\n  }\n  \n  .bi-arrow-90deg-down::before {\n    content: \"\\f10e\";\n  }\n  \n  .bi-arrow-90deg-left::before {\n    content: \"\\f10f\";\n  }\n  \n  .bi-arrow-90deg-right::before {\n    content: \"\\f110\";\n  }\n  \n  .bi-arrow-90deg-up::before {\n    content: \"\\f111\";\n  }\n  \n  .bi-arrow-bar-down::before {\n    content: \"\\f112\";\n  }\n  \n  .bi-arrow-bar-left::before {\n    content: \"\\f113\";\n  }\n  \n  .bi-arrow-bar-right::before {\n    content: \"\\f114\";\n  }\n  \n  .bi-arrow-bar-up::before {\n    content: \"\\f115\";\n  }\n  \n  .bi-arrow-clockwise::before {\n    content: \"\\f116\";\n  }\n  \n  .bi-arrow-counterclockwise::before {\n    content: \"\\f117\";\n  }\n  \n  .bi-arrow-down-circle-fill::before {\n    content: \"\\f118\";\n  }\n  \n  .bi-arrow-down-circle::before {\n    content: \"\\f119\";\n  }\n  \n  .bi-arrow-down-left-circle-fill::before {\n    content: \"\\f11a\";\n  }\n  \n  .bi-arrow-down-left-circle::before {\n    content: \"\\f11b\";\n  }\n  \n  .bi-arrow-down-left-square-fill::before {\n    content: \"\\f11c\";\n  }\n  \n  .bi-arrow-down-left-square::before {\n    content: \"\\f11d\";\n  }\n  \n  .bi-arrow-down-left::before {\n    content: \"\\f11e\";\n  }\n  \n  .bi-arrow-down-right-circle-fill::before {\n    content: \"\\f11f\";\n  }\n  \n  .bi-arrow-down-right-circle::before {\n    content: \"\\f120\";\n  }\n  \n  .bi-arrow-down-right-square-fill::before {\n    content: \"\\f121\";\n  }\n  \n  .bi-arrow-down-right-square::before {\n    content: \"\\f122\";\n  }\n  \n  .bi-arrow-down-right::before {\n    content: \"\\f123\";\n  }\n  \n  .bi-arrow-down-short::before {\n    content: \"\\f124\";\n  }\n  \n  .bi-arrow-down-square-fill::before {\n    content: \"\\f125\";\n  }\n  \n  .bi-arrow-down-square::before {\n    content: \"\\f126\";\n  }\n  \n  .bi-arrow-down-up::before {\n    content: \"\\f127\";\n  }\n  \n  .bi-arrow-down::before {\n    content: \"\\f128\";\n  }\n  \n  .bi-arrow-left-circle-fill::before {\n    content: \"\\f129\";\n  }\n  \n  .bi-arrow-left-circle::before {\n    content: \"\\f12a\";\n  }\n  \n  .bi-arrow-left-right::before {\n    content: \"\\f12b\";\n  }\n  \n  .bi-arrow-left-short::before {\n    content: \"\\f12c\";\n  }\n  \n  .bi-arrow-left-square-fill::before {\n    content: \"\\f12d\";\n  }\n  \n  .bi-arrow-left-square::before {\n    content: \"\\f12e\";\n  }\n  \n  .bi-arrow-left::before {\n    content: \"\\f12f\";\n  }\n  \n  .bi-arrow-repeat::before {\n    content: \"\\f130\";\n  }\n  \n  .bi-arrow-return-left::before {\n    content: \"\\f131\";\n  }\n  \n  .bi-arrow-return-right::before {\n    content: \"\\f132\";\n  }\n  \n  .bi-arrow-right-circle-fill::before {\n    content: \"\\f133\";\n  }\n  \n  .bi-arrow-right-circle::before {\n    content: \"\\f134\";\n  }\n  \n  .bi-arrow-right-short::before {\n    content: \"\\f135\";\n  }\n  \n  .bi-arrow-right-square-fill::before {\n    content: \"\\f136\";\n  }\n  \n  .bi-arrow-right-square::before {\n    content: \"\\f137\";\n  }\n  \n  .bi-arrow-right::before {\n    content: \"\\f138\";\n  }\n  \n  .bi-arrow-up-circle-fill::before {\n    content: \"\\f139\";\n  }\n  \n  .bi-arrow-up-circle::before {\n    content: \"\\f13a\";\n  }\n  \n  .bi-arrow-up-left-circle-fill::before {\n    content: \"\\f13b\";\n  }\n  \n  .bi-arrow-up-left-circle::before {\n    content: \"\\f13c\";\n  }\n  \n  .bi-arrow-up-left-square-fill::before {\n    content: \"\\f13d\";\n  }\n  \n  .bi-arrow-up-left-square::before {\n    content: \"\\f13e\";\n  }\n  \n  .bi-arrow-up-left::before {\n    content: \"\\f13f\";\n  }\n  \n  .bi-arrow-up-right-circle-fill::before {\n    content: \"\\f140\";\n  }\n  \n  .bi-arrow-up-right-circle::before {\n    content: \"\\f141\";\n  }\n  \n  .bi-arrow-up-right-square-fill::before {\n    content: \"\\f142\";\n  }\n  \n  .bi-arrow-up-right-square::before {\n    content: \"\\f143\";\n  }\n  \n  .bi-arrow-up-right::before {\n    content: \"\\f144\";\n  }\n  \n  .bi-arrow-up-short::before {\n    content: \"\\f145\";\n  }\n  \n  .bi-arrow-up-square-fill::before {\n    content: \"\\f146\";\n  }\n  \n  .bi-arrow-up-square::before {\n    content: \"\\f147\";\n  }\n  \n  .bi-arrow-up::before {\n    content: \"\\f148\";\n  }\n  \n  .bi-arrows-angle-contract::before {\n    content: \"\\f149\";\n  }\n  \n  .bi-arrows-angle-expand::before {\n    content: \"\\f14a\";\n  }\n  \n  .bi-arrows-collapse::before {\n    content: \"\\f14b\";\n  }\n  \n  .bi-arrows-expand::before {\n    content: \"\\f14c\";\n  }\n  \n  .bi-arrows-fullscreen::before {\n    content: \"\\f14d\";\n  }\n  \n  .bi-arrows-move::before {\n    content: \"\\f14e\";\n  }\n  \n  .bi-aspect-ratio-fill::before {\n    content: \"\\f14f\";\n  }\n  \n  .bi-aspect-ratio::before {\n    content: \"\\f150\";\n  }\n  \n  .bi-asterisk::before {\n    content: \"\\f151\";\n  }\n  \n  .bi-at::before {\n    content: \"\\f152\";\n  }\n  \n  .bi-award-fill::before {\n    content: \"\\f153\";\n  }\n  \n  .bi-award::before {\n    content: \"\\f154\";\n  }\n  \n  .bi-back::before {\n    content: \"\\f155\";\n  }\n  \n  .bi-backspace-fill::before {\n    content: \"\\f156\";\n  }\n  \n  .bi-backspace-reverse-fill::before {\n    content: \"\\f157\";\n  }\n  \n  .bi-backspace-reverse::before {\n    content: \"\\f158\";\n  }\n  \n  .bi-backspace::before {\n    content: \"\\f159\";\n  }\n  \n  .bi-badge-3d-fill::before {\n    content: \"\\f15a\";\n  }\n  \n  .bi-badge-3d::before {\n    content: \"\\f15b\";\n  }\n  \n  .bi-badge-4k-fill::before {\n    content: \"\\f15c\";\n  }\n  \n  .bi-badge-4k::before {\n    content: \"\\f15d\";\n  }\n  \n  .bi-badge-8k-fill::before {\n    content: \"\\f15e\";\n  }\n  \n  .bi-badge-8k::before {\n    content: \"\\f15f\";\n  }\n  \n  .bi-badge-ad-fill::before {\n    content: \"\\f160\";\n  }\n  \n  .bi-badge-ad::before {\n    content: \"\\f161\";\n  }\n  \n  .bi-badge-ar-fill::before {\n    content: \"\\f162\";\n  }\n  \n  .bi-badge-ar::before {\n    content: \"\\f163\";\n  }\n  \n  .bi-badge-cc-fill::before {\n    content: \"\\f164\";\n  }\n  \n  .bi-badge-cc::before {\n    content: \"\\f165\";\n  }\n  \n  .bi-badge-hd-fill::before {\n    content: \"\\f166\";\n  }\n  \n  .bi-badge-hd::before {\n    content: \"\\f167\";\n  }\n  \n  .bi-badge-tm-fill::before {\n    content: \"\\f168\";\n  }\n  \n  .bi-badge-tm::before {\n    content: \"\\f169\";\n  }\n  \n  .bi-badge-vo-fill::before {\n    content: \"\\f16a\";\n  }\n  \n  .bi-badge-vo::before {\n    content: \"\\f16b\";\n  }\n  \n  .bi-badge-vr-fill::before {\n    content: \"\\f16c\";\n  }\n  \n  .bi-badge-vr::before {\n    content: \"\\f16d\";\n  }\n  \n  .bi-badge-wc-fill::before {\n    content: \"\\f16e\";\n  }\n  \n  .bi-badge-wc::before {\n    content: \"\\f16f\";\n  }\n  \n  .bi-bag-check-fill::before {\n    content: \"\\f170\";\n  }\n  \n  .bi-bag-check::before {\n    content: \"\\f171\";\n  }\n  \n  .bi-bag-dash-fill::before {\n    content: \"\\f172\";\n  }\n  \n  .bi-bag-dash::before {\n    content: \"\\f173\";\n  }\n  \n  .bi-bag-fill::before {\n    content: \"\\f174\";\n  }\n  \n  .bi-bag-plus-fill::before {\n    content: \"\\f175\";\n  }\n  \n  .bi-bag-plus::before {\n    content: \"\\f176\";\n  }\n  \n  .bi-bag-x-fill::before {\n    content: \"\\f177\";\n  }\n  \n  .bi-bag-x::before {\n    content: \"\\f178\";\n  }\n  \n  .bi-bag::before {\n    content: \"\\f179\";\n  }\n  \n  .bi-bar-chart-fill::before {\n    content: \"\\f17a\";\n  }\n  \n  .bi-bar-chart-line-fill::before {\n    content: \"\\f17b\";\n  }\n  \n  .bi-bar-chart-line::before {\n    content: \"\\f17c\";\n  }\n  \n  .bi-bar-chart-steps::before {\n    content: \"\\f17d\";\n  }\n  \n  .bi-bar-chart::before {\n    content: \"\\f17e\";\n  }\n  \n  .bi-basket-fill::before {\n    content: \"\\f17f\";\n  }\n  \n  .bi-basket::before {\n    content: \"\\f180\";\n  }\n  \n  .bi-basket2-fill::before {\n    content: \"\\f181\";\n  }\n  \n  .bi-basket2::before {\n    content: \"\\f182\";\n  }\n  \n  .bi-basket3-fill::before {\n    content: \"\\f183\";\n  }\n  \n  .bi-basket3::before {\n    content: \"\\f184\";\n  }\n  \n  .bi-battery-charging::before {\n    content: \"\\f185\";\n  }\n  \n  .bi-battery-full::before {\n    content: \"\\f186\";\n  }\n  \n  .bi-battery-half::before {\n    content: \"\\f187\";\n  }\n  \n  .bi-battery::before {\n    content: \"\\f188\";\n  }\n  \n  .bi-bell-fill::before {\n    content: \"\\f189\";\n  }\n  \n  .bi-bell::before {\n    content: \"\\f18a\";\n  }\n  \n  .bi-bezier::before {\n    content: \"\\f18b\";\n  }\n  \n  .bi-bezier2::before {\n    content: \"\\f18c\";\n  }\n  \n  .bi-bicycle::before {\n    content: \"\\f18d\";\n  }\n  \n  .bi-binoculars-fill::before {\n    content: \"\\f18e\";\n  }\n  \n  .bi-binoculars::before {\n    content: \"\\f18f\";\n  }\n  \n  .bi-blockquote-left::before {\n    content: \"\\f190\";\n  }\n  \n  .bi-blockquote-right::before {\n    content: \"\\f191\";\n  }\n  \n  .bi-book-fill::before {\n    content: \"\\f192\";\n  }\n  \n  .bi-book-half::before {\n    content: \"\\f193\";\n  }\n  \n  .bi-book::before {\n    content: \"\\f194\";\n  }\n  \n  .bi-bookmark-check-fill::before {\n    content: \"\\f195\";\n  }\n  \n  .bi-bookmark-check::before {\n    content: \"\\f196\";\n  }\n  \n  .bi-bookmark-dash-fill::before {\n    content: \"\\f197\";\n  }\n  \n  .bi-bookmark-dash::before {\n    content: \"\\f198\";\n  }\n  \n  .bi-bookmark-fill::before {\n    content: \"\\f199\";\n  }\n  \n  .bi-bookmark-heart-fill::before {\n    content: \"\\f19a\";\n  }\n  \n  .bi-bookmark-heart::before {\n    content: \"\\f19b\";\n  }\n  \n  .bi-bookmark-plus-fill::before {\n    content: \"\\f19c\";\n  }\n  \n  .bi-bookmark-plus::before {\n    content: \"\\f19d\";\n  }\n  \n  .bi-bookmark-star-fill::before {\n    content: \"\\f19e\";\n  }\n  \n  .bi-bookmark-star::before {\n    content: \"\\f19f\";\n  }\n  \n  .bi-bookmark-x-fill::before {\n    content: \"\\f1a0\";\n  }\n  \n  .bi-bookmark-x::before {\n    content: \"\\f1a1\";\n  }\n  \n  .bi-bookmark::before {\n    content: \"\\f1a2\";\n  }\n  \n  .bi-bookmarks-fill::before {\n    content: \"\\f1a3\";\n  }\n  \n  .bi-bookmarks::before {\n    content: \"\\f1a4\";\n  }\n  \n  .bi-bookshelf::before {\n    content: \"\\f1a5\";\n  }\n  \n  .bi-bootstrap-fill::before {\n    content: \"\\f1a6\";\n  }\n  \n  .bi-bootstrap-reboot::before {\n    content: \"\\f1a7\";\n  }\n  \n  .bi-bootstrap::before {\n    content: \"\\f1a8\";\n  }\n  \n  .bi-border-all::before {\n    content: \"\\f1a9\";\n  }\n  \n  .bi-border-bottom::before {\n    content: \"\\f1aa\";\n  }\n  \n  .bi-border-center::before {\n    content: \"\\f1ab\";\n  }\n  \n  .bi-border-inner::before {\n    content: \"\\f1ac\";\n  }\n  \n  .bi-border-left::before {\n    content: \"\\f1ad\";\n  }\n  \n  .bi-border-middle::before {\n    content: \"\\f1ae\";\n  }\n  \n  .bi-border-outer::before {\n    content: \"\\f1af\";\n  }\n  \n  .bi-border-right::before {\n    content: \"\\f1b0\";\n  }\n  \n  .bi-border-style::before {\n    content: \"\\f1b1\";\n  }\n  \n  .bi-border-top::before {\n    content: \"\\f1b2\";\n  }\n  \n  .bi-border-width::before {\n    content: \"\\f1b3\";\n  }\n  \n  .bi-border::before {\n    content: \"\\f1b4\";\n  }\n  \n  .bi-bounding-box-circles::before {\n    content: \"\\f1b5\";\n  }\n  \n  .bi-bounding-box::before {\n    content: \"\\f1b6\";\n  }\n  \n  .bi-box-arrow-down-left::before {\n    content: \"\\f1b7\";\n  }\n  \n  .bi-box-arrow-down-right::before {\n    content: \"\\f1b8\";\n  }\n  \n  .bi-box-arrow-down::before {\n    content: \"\\f1b9\";\n  }\n  \n  .bi-box-arrow-in-down-left::before {\n    content: \"\\f1ba\";\n  }\n  \n  .bi-box-arrow-in-down-right::before {\n    content: \"\\f1bb\";\n  }\n  \n  .bi-box-arrow-in-down::before {\n    content: \"\\f1bc\";\n  }\n  \n  .bi-box-arrow-in-left::before {\n    content: \"\\f1bd\";\n  }\n  \n  .bi-box-arrow-in-right::before {\n    content: \"\\f1be\";\n  }\n  \n  .bi-box-arrow-in-up-left::before {\n    content: \"\\f1bf\";\n  }\n  \n  .bi-box-arrow-in-up-right::before {\n    content: \"\\f1c0\";\n  }\n  \n  .bi-box-arrow-in-up::before {\n    content: \"\\f1c1\";\n  }\n  \n  .bi-box-arrow-left::before {\n    content: \"\\f1c2\";\n  }\n  \n  .bi-box-arrow-right::before {\n    content: \"\\f1c3\";\n  }\n  \n  .bi-box-arrow-up-left::before {\n    content: \"\\f1c4\";\n  }\n  \n  .bi-box-arrow-up-right::before {\n    content: \"\\f1c5\";\n  }\n  \n  .bi-box-arrow-up::before {\n    content: \"\\f1c6\";\n  }\n  \n  .bi-box-seam::before {\n    content: \"\\f1c7\";\n  }\n  \n  .bi-box::before {\n    content: \"\\f1c8\";\n  }\n  \n  .bi-braces::before {\n    content: \"\\f1c9\";\n  }\n  \n  .bi-bricks::before {\n    content: \"\\f1ca\";\n  }\n  \n  .bi-briefcase-fill::before {\n    content: \"\\f1cb\";\n  }\n  \n  .bi-briefcase::before {\n    content: \"\\f1cc\";\n  }\n  \n  .bi-brightness-alt-high-fill::before {\n    content: \"\\f1cd\";\n  }\n  \n  .bi-brightness-alt-high::before {\n    content: \"\\f1ce\";\n  }\n  \n  .bi-brightness-alt-low-fill::before {\n    content: \"\\f1cf\";\n  }\n  \n  .bi-brightness-alt-low::before {\n    content: \"\\f1d0\";\n  }\n  \n  .bi-brightness-high-fill::before {\n    content: \"\\f1d1\";\n  }\n  \n  .bi-brightness-high::before {\n    content: \"\\f1d2\";\n  }\n  \n  .bi-brightness-low-fill::before {\n    content: \"\\f1d3\";\n  }\n  \n  .bi-brightness-low::before {\n    content: \"\\f1d4\";\n  }\n  \n  .bi-broadcast-pin::before {\n    content: \"\\f1d5\";\n  }\n  \n  .bi-broadcast::before {\n    content: \"\\f1d6\";\n  }\n  \n  .bi-brush-fill::before {\n    content: \"\\f1d7\";\n  }\n  \n  .bi-brush::before {\n    content: \"\\f1d8\";\n  }\n  \n  .bi-bucket-fill::before {\n    content: \"\\f1d9\";\n  }\n  \n  .bi-bucket::before {\n    content: \"\\f1da\";\n  }\n  \n  .bi-bug-fill::before {\n    content: \"\\f1db\";\n  }\n  \n  .bi-bug::before {\n    content: \"\\f1dc\";\n  }\n  \n  .bi-building::before {\n    content: \"\\f1dd\";\n  }\n  \n  .bi-bullseye::before {\n    content: \"\\f1de\";\n  }\n  \n  .bi-calculator-fill::before {\n    content: \"\\f1df\";\n  }\n  \n  .bi-calculator::before {\n    content: \"\\f1e0\";\n  }\n  \n  .bi-calendar-check-fill::before {\n    content: \"\\f1e1\";\n  }\n  \n  .bi-calendar-check::before {\n    content: \"\\f1e2\";\n  }\n  \n  .bi-calendar-date-fill::before {\n    content: \"\\f1e3\";\n  }\n  \n  .bi-calendar-date::before {\n    content: \"\\f1e4\";\n  }\n  \n  .bi-calendar-day-fill::before {\n    content: \"\\f1e5\";\n  }\n  \n  .bi-calendar-day::before {\n    content: \"\\f1e6\";\n  }\n  \n  .bi-calendar-event-fill::before {\n    content: \"\\f1e7\";\n  }\n  \n  .bi-calendar-event::before {\n    content: \"\\f1e8\";\n  }\n  \n  .bi-calendar-fill::before {\n    content: \"\\f1e9\";\n  }\n  \n  .bi-calendar-minus-fill::before {\n    content: \"\\f1ea\";\n  }\n  \n  .bi-calendar-minus::before {\n    content: \"\\f1eb\";\n  }\n  \n  .bi-calendar-month-fill::before {\n    content: \"\\f1ec\";\n  }\n  \n  .bi-calendar-month::before {\n    content: \"\\f1ed\";\n  }\n  \n  .bi-calendar-plus-fill::before {\n    content: \"\\f1ee\";\n  }\n  \n  .bi-calendar-plus::before {\n    content: \"\\f1ef\";\n  }\n  \n  .bi-calendar-range-fill::before {\n    content: \"\\f1f0\";\n  }\n  \n  .bi-calendar-range::before {\n    content: \"\\f1f1\";\n  }\n  \n  .bi-calendar-week-fill::before {\n    content: \"\\f1f2\";\n  }\n  \n  .bi-calendar-week::before {\n    content: \"\\f1f3\";\n  }\n  \n  .bi-calendar-x-fill::before {\n    content: \"\\f1f4\";\n  }\n  \n  .bi-calendar-x::before {\n    content: \"\\f1f5\";\n  }\n  \n  .bi-calendar::before {\n    content: \"\\f1f6\";\n  }\n  \n  .bi-calendar2-check-fill::before {\n    content: \"\\f1f7\";\n  }\n  \n  .bi-calendar2-check::before {\n    content: \"\\f1f8\";\n  }\n  \n  .bi-calendar2-date-fill::before {\n    content: \"\\f1f9\";\n  }\n  \n  .bi-calendar2-date::before {\n    content: \"\\f1fa\";\n  }\n  \n  .bi-calendar2-day-fill::before {\n    content: \"\\f1fb\";\n  }\n  \n  .bi-calendar2-day::before {\n    content: \"\\f1fc\";\n  }\n  \n  .bi-calendar2-event-fill::before {\n    content: \"\\f1fd\";\n  }\n  \n  .bi-calendar2-event::before {\n    content: \"\\f1fe\";\n  }\n  \n  .bi-calendar2-fill::before {\n    content: \"\\f1ff\";\n  }\n  \n  .bi-calendar2-minus-fill::before {\n    content: \"\\f200\";\n  }\n  \n  .bi-calendar2-minus::before {\n    content: \"\\f201\";\n  }\n  \n  .bi-calendar2-month-fill::before {\n    content: \"\\f202\";\n  }\n  \n  .bi-calendar2-month::before {\n    content: \"\\f203\";\n  }\n  \n  .bi-calendar2-plus-fill::before {\n    content: \"\\f204\";\n  }\n  \n  .bi-calendar2-plus::before {\n    content: \"\\f205\";\n  }\n  \n  .bi-calendar2-range-fill::before {\n    content: \"\\f206\";\n  }\n  \n  .bi-calendar2-range::before {\n    content: \"\\f207\";\n  }\n  \n  .bi-calendar2-week-fill::before {\n    content: \"\\f208\";\n  }\n  \n  .bi-calendar2-week::before {\n    content: \"\\f209\";\n  }\n  \n  .bi-calendar2-x-fill::before {\n    content: \"\\f20a\";\n  }\n  \n  .bi-calendar2-x::before {\n    content: \"\\f20b\";\n  }\n  \n  .bi-calendar2::before {\n    content: \"\\f20c\";\n  }\n  \n  .bi-calendar3-event-fill::before {\n    content: \"\\f20d\";\n  }\n  \n  .bi-calendar3-event::before {\n    content: \"\\f20e\";\n  }\n  \n  .bi-calendar3-fill::before {\n    content: \"\\f20f\";\n  }\n  \n  .bi-calendar3-range-fill::before {\n    content: \"\\f210\";\n  }\n  \n  .bi-calendar3-range::before {\n    content: \"\\f211\";\n  }\n  \n  .bi-calendar3-week-fill::before {\n    content: \"\\f212\";\n  }\n  \n  .bi-calendar3-week::before {\n    content: \"\\f213\";\n  }\n  \n  .bi-calendar3::before {\n    content: \"\\f214\";\n  }\n  \n  .bi-calendar4-event::before {\n    content: \"\\f215\";\n  }\n  \n  .bi-calendar4-range::before {\n    content: \"\\f216\";\n  }\n  \n  .bi-calendar4-week::before {\n    content: \"\\f217\";\n  }\n  \n  .bi-calendar4::before {\n    content: \"\\f218\";\n  }\n  \n  .bi-camera-fill::before {\n    content: \"\\f219\";\n  }\n  \n  .bi-camera-reels-fill::before {\n    content: \"\\f21a\";\n  }\n  \n  .bi-camera-reels::before {\n    content: \"\\f21b\";\n  }\n  \n  .bi-camera-video-fill::before {\n    content: \"\\f21c\";\n  }\n  \n  .bi-camera-video-off-fill::before {\n    content: \"\\f21d\";\n  }\n  \n  .bi-camera-video-off::before {\n    content: \"\\f21e\";\n  }\n  \n  .bi-camera-video::before {\n    content: \"\\f21f\";\n  }\n  \n  .bi-camera::before {\n    content: \"\\f220\";\n  }\n  \n  .bi-camera2::before {\n    content: \"\\f221\";\n  }\n  \n  .bi-capslock-fill::before {\n    content: \"\\f222\";\n  }\n  \n  .bi-capslock::before {\n    content: \"\\f223\";\n  }\n  \n  .bi-card-checklist::before {\n    content: \"\\f224\";\n  }\n  \n  .bi-card-heading::before {\n    content: \"\\f225\";\n  }\n  \n  .bi-card-image::before {\n    content: \"\\f226\";\n  }\n  \n  .bi-card-list::before {\n    content: \"\\f227\";\n  }\n  \n  .bi-card-text::before {\n    content: \"\\f228\";\n  }\n  \n  .bi-caret-down-fill::before {\n    content: \"\\f229\";\n  }\n  \n  .bi-caret-down-square-fill::before {\n    content: \"\\f22a\";\n  }\n  \n  .bi-caret-down-square::before {\n    content: \"\\f22b\";\n  }\n  \n  .bi-caret-down::before {\n    content: \"\\f22c\";\n  }\n  \n  .bi-caret-left-fill::before {\n    content: \"\\f22d\";\n  }\n  \n  .bi-caret-left-square-fill::before {\n    content: \"\\f22e\";\n  }\n  \n  .bi-caret-left-square::before {\n    content: \"\\f22f\";\n  }\n  \n  .bi-caret-left::before {\n    content: \"\\f230\";\n  }\n  \n  .bi-caret-right-fill::before {\n    content: \"\\f231\";\n  }\n  \n  .bi-caret-right-square-fill::before {\n    content: \"\\f232\";\n  }\n  \n  .bi-caret-right-square::before {\n    content: \"\\f233\";\n  }\n  \n  .bi-caret-right::before {\n    content: \"\\f234\";\n  }\n  \n  .bi-caret-up-fill::before {\n    content: \"\\f235\";\n  }\n  \n  .bi-caret-up-square-fill::before {\n    content: \"\\f236\";\n  }\n  \n  .bi-caret-up-square::before {\n    content: \"\\f237\";\n  }\n  \n  .bi-caret-up::before {\n    content: \"\\f238\";\n  }\n  \n  .bi-cart-check-fill::before {\n    content: \"\\f239\";\n  }\n  \n  .bi-cart-check::before {\n    content: \"\\f23a\";\n  }\n  \n  .bi-cart-dash-fill::before {\n    content: \"\\f23b\";\n  }\n  \n  .bi-cart-dash::before {\n    content: \"\\f23c\";\n  }\n  \n  .bi-cart-fill::before {\n    content: \"\\f23d\";\n  }\n  \n  .bi-cart-plus-fill::before {\n    content: \"\\f23e\";\n  }\n  \n  .bi-cart-plus::before {\n    content: \"\\f23f\";\n  }\n  \n  .bi-cart-x-fill::before {\n    content: \"\\f240\";\n  }\n  \n  .bi-cart-x::before {\n    content: \"\\f241\";\n  }\n  \n  .bi-cart::before {\n    content: \"\\f242\";\n  }\n  \n  .bi-cart2::before {\n    content: \"\\f243\";\n  }\n  \n  .bi-cart3::before {\n    content: \"\\f244\";\n  }\n  \n  .bi-cart4::before {\n    content: \"\\f245\";\n  }\n  \n  .bi-cash-stack::before {\n    content: \"\\f246\";\n  }\n  \n  .bi-cash::before {\n    content: \"\\f247\";\n  }\n  \n  .bi-cast::before {\n    content: \"\\f248\";\n  }\n  \n  .bi-chat-dots-fill::before {\n    content: \"\\f249\";\n  }\n  \n  .bi-chat-dots::before {\n    content: \"\\f24a\";\n  }\n  \n  .bi-chat-fill::before {\n    content: \"\\f24b\";\n  }\n  \n  .bi-chat-left-dots-fill::before {\n    content: \"\\f24c\";\n  }\n  \n  .bi-chat-left-dots::before {\n    content: \"\\f24d\";\n  }\n  \n  .bi-chat-left-fill::before {\n    content: \"\\f24e\";\n  }\n  \n  .bi-chat-left-quote-fill::before {\n    content: \"\\f24f\";\n  }\n  \n  .bi-chat-left-quote::before {\n    content: \"\\f250\";\n  }\n  \n  .bi-chat-left-text-fill::before {\n    content: \"\\f251\";\n  }\n  \n  .bi-chat-left-text::before {\n    content: \"\\f252\";\n  }\n  \n  .bi-chat-left::before {\n    content: \"\\f253\";\n  }\n  \n  .bi-chat-quote-fill::before {\n    content: \"\\f254\";\n  }\n  \n  .bi-chat-quote::before {\n    content: \"\\f255\";\n  }\n  \n  .bi-chat-right-dots-fill::before {\n    content: \"\\f256\";\n  }\n  \n  .bi-chat-right-dots::before {\n    content: \"\\f257\";\n  }\n  \n  .bi-chat-right-fill::before {\n    content: \"\\f258\";\n  }\n  \n  .bi-chat-right-quote-fill::before {\n    content: \"\\f259\";\n  }\n  \n  .bi-chat-right-quote::before {\n    content: \"\\f25a\";\n  }\n  \n  .bi-chat-right-text-fill::before {\n    content: \"\\f25b\";\n  }\n  \n  .bi-chat-right-text::before {\n    content: \"\\f25c\";\n  }\n  \n  .bi-chat-right::before {\n    content: \"\\f25d\";\n  }\n  \n  .bi-chat-square-dots-fill::before {\n    content: \"\\f25e\";\n  }\n  \n  .bi-chat-square-dots::before {\n    content: \"\\f25f\";\n  }\n  \n  .bi-chat-square-fill::before {\n    content: \"\\f260\";\n  }\n  \n  .bi-chat-square-quote-fill::before {\n    content: \"\\f261\";\n  }\n  \n  .bi-chat-square-quote::before {\n    content: \"\\f262\";\n  }\n  \n  .bi-chat-square-text-fill::before {\n    content: \"\\f263\";\n  }\n  \n  .bi-chat-square-text::before {\n    content: \"\\f264\";\n  }\n  \n  .bi-chat-square::before {\n    content: \"\\f265\";\n  }\n  \n  .bi-chat-text-fill::before {\n    content: \"\\f266\";\n  }\n  \n  .bi-chat-text::before {\n    content: \"\\f267\";\n  }\n  \n  .bi-chat::before {\n    content: \"\\f268\";\n  }\n  \n  .bi-check-all::before {\n    content: \"\\f269\";\n  }\n  \n  .bi-check-circle-fill::before {\n    content: \"\\f26a\";\n  }\n  \n  .bi-check-circle::before {\n    content: \"\\f26b\";\n  }\n  \n  .bi-check-square-fill::before {\n    content: \"\\f26c\";\n  }\n  \n  .bi-check-square::before {\n    content: \"\\f26d\";\n  }\n  \n  .bi-check::before {\n    content: \"\\f26e\";\n  }\n  \n  .bi-check2-all::before {\n    content: \"\\f26f\";\n  }\n  \n  .bi-check2-circle::before {\n    content: \"\\f270\";\n  }\n  \n  .bi-check2-square::before {\n    content: \"\\f271\";\n  }\n  \n  .bi-check2::before {\n    content: \"\\f272\";\n  }\n  \n  .bi-chevron-bar-contract::before {\n    content: \"\\f273\";\n  }\n  \n  .bi-chevron-bar-down::before {\n    content: \"\\f274\";\n  }\n  \n  .bi-chevron-bar-expand::before {\n    content: \"\\f275\";\n  }\n  \n  .bi-chevron-bar-left::before {\n    content: \"\\f276\";\n  }\n  \n  .bi-chevron-bar-right::before {\n    content: \"\\f277\";\n  }\n  \n  .bi-chevron-bar-up::before {\n    content: \"\\f278\";\n  }\n  \n  .bi-chevron-compact-down::before {\n    content: \"\\f279\";\n  }\n  \n  .bi-chevron-compact-left::before {\n    content: \"\\f27a\";\n  }\n  \n  .bi-chevron-compact-right::before {\n    content: \"\\f27b\";\n  }\n  \n  .bi-chevron-compact-up::before {\n    content: \"\\f27c\";\n  }\n  \n  .bi-chevron-contract::before {\n    content: \"\\f27d\";\n  }\n  \n  .bi-chevron-double-down::before {\n    content: \"\\f27e\";\n  }\n  \n  .bi-chevron-double-left::before {\n    content: \"\\f27f\";\n  }\n  \n  .bi-chevron-double-right::before {\n    content: \"\\f280\";\n  }\n  \n  .bi-chevron-double-up::before {\n    content: \"\\f281\";\n  }\n  \n  .bi-chevron-down::before {\n    content: \"\\f282\";\n  }\n  \n  .bi-chevron-expand::before {\n    content: \"\\f283\";\n  }\n  \n  .bi-chevron-left::before {\n    content: \"\\f284\";\n  }\n  \n  .bi-chevron-right::before {\n    content: \"\\f285\";\n  }\n  \n  .bi-chevron-up::before {\n    content: \"\\f286\";\n  }\n  \n  .bi-circle-fill::before {\n    content: \"\\f287\";\n  }\n  \n  .bi-circle-half::before {\n    content: \"\\f288\";\n  }\n  \n  .bi-circle-square::before {\n    content: \"\\f289\";\n  }\n  \n  .bi-circle::before {\n    content: \"\\f28a\";\n  }\n  \n  .bi-clipboard-check::before {\n    content: \"\\f28b\";\n  }\n  \n  .bi-clipboard-data::before {\n    content: \"\\f28c\";\n  }\n  \n  .bi-clipboard-minus::before {\n    content: \"\\f28d\";\n  }\n  \n  .bi-clipboard-plus::before {\n    content: \"\\f28e\";\n  }\n  \n  .bi-clipboard-x::before {\n    content: \"\\f28f\";\n  }\n  \n  .bi-clipboard::before {\n    content: \"\\f290\";\n  }\n  \n  .bi-clock-fill::before {\n    content: \"\\f291\";\n  }\n  \n  .bi-clock-history::before {\n    content: \"\\f292\";\n  }\n  \n  .bi-clock::before {\n    content: \"\\f293\";\n  }\n  \n  .bi-cloud-arrow-down-fill::before {\n    content: \"\\f294\";\n  }\n  \n  .bi-cloud-arrow-down::before {\n    content: \"\\f295\";\n  }\n  \n  .bi-cloud-arrow-up-fill::before {\n    content: \"\\f296\";\n  }\n  \n  .bi-cloud-arrow-up::before {\n    content: \"\\f297\";\n  }\n  \n  .bi-cloud-check-fill::before {\n    content: \"\\f298\";\n  }\n  \n  .bi-cloud-check::before {\n    content: \"\\f299\";\n  }\n  \n  .bi-cloud-download-fill::before {\n    content: \"\\f29a\";\n  }\n  \n  .bi-cloud-download::before {\n    content: \"\\f29b\";\n  }\n  \n  .bi-cloud-drizzle-fill::before {\n    content: \"\\f29c\";\n  }\n  \n  .bi-cloud-drizzle::before {\n    content: \"\\f29d\";\n  }\n  \n  .bi-cloud-fill::before {\n    content: \"\\f29e\";\n  }\n  \n  .bi-cloud-fog-fill::before {\n    content: \"\\f29f\";\n  }\n  \n  .bi-cloud-fog::before {\n    content: \"\\f2a0\";\n  }\n  \n  .bi-cloud-fog2-fill::before {\n    content: \"\\f2a1\";\n  }\n  \n  .bi-cloud-fog2::before {\n    content: \"\\f2a2\";\n  }\n  \n  .bi-cloud-hail-fill::before {\n    content: \"\\f2a3\";\n  }\n  \n  .bi-cloud-hail::before {\n    content: \"\\f2a4\";\n  }\n  \n  .bi-cloud-haze-1::before {\n    content: \"\\f2a5\";\n  }\n  \n  .bi-cloud-haze-fill::before {\n    content: \"\\f2a6\";\n  }\n  \n  .bi-cloud-haze::before {\n    content: \"\\f2a7\";\n  }\n  \n  .bi-cloud-haze2-fill::before {\n    content: \"\\f2a8\";\n  }\n  \n  .bi-cloud-lightning-fill::before {\n    content: \"\\f2a9\";\n  }\n  \n  .bi-cloud-lightning-rain-fill::before {\n    content: \"\\f2aa\";\n  }\n  \n  .bi-cloud-lightning-rain::before {\n    content: \"\\f2ab\";\n  }\n  \n  .bi-cloud-lightning::before {\n    content: \"\\f2ac\";\n  }\n  \n  .bi-cloud-minus-fill::before {\n    content: \"\\f2ad\";\n  }\n  \n  .bi-cloud-minus::before {\n    content: \"\\f2ae\";\n  }\n  \n  .bi-cloud-moon-fill::before {\n    content: \"\\f2af\";\n  }\n  \n  .bi-cloud-moon::before {\n    content: \"\\f2b0\";\n  }\n  \n  .bi-cloud-plus-fill::before {\n    content: \"\\f2b1\";\n  }\n  \n  .bi-cloud-plus::before {\n    content: \"\\f2b2\";\n  }\n  \n  .bi-cloud-rain-fill::before {\n    content: \"\\f2b3\";\n  }\n  \n  .bi-cloud-rain-heavy-fill::before {\n    content: \"\\f2b4\";\n  }\n  \n  .bi-cloud-rain-heavy::before {\n    content: \"\\f2b5\";\n  }\n  \n  .bi-cloud-rain::before {\n    content: \"\\f2b6\";\n  }\n  \n  .bi-cloud-slash-fill::before {\n    content: \"\\f2b7\";\n  }\n  \n  .bi-cloud-slash::before {\n    content: \"\\f2b8\";\n  }\n  \n  .bi-cloud-sleet-fill::before {\n    content: \"\\f2b9\";\n  }\n  \n  .bi-cloud-sleet::before {\n    content: \"\\f2ba\";\n  }\n  \n  .bi-cloud-snow-fill::before {\n    content: \"\\f2bb\";\n  }\n  \n  .bi-cloud-snow::before {\n    content: \"\\f2bc\";\n  }\n  \n  .bi-cloud-sun-fill::before {\n    content: \"\\f2bd\";\n  }\n  \n  .bi-cloud-sun::before {\n    content: \"\\f2be\";\n  }\n  \n  .bi-cloud-upload-fill::before {\n    content: \"\\f2bf\";\n  }\n  \n  .bi-cloud-upload::before {\n    content: \"\\f2c0\";\n  }\n  \n  .bi-cloud::before {\n    content: \"\\f2c1\";\n  }\n  \n  .bi-clouds-fill::before {\n    content: \"\\f2c2\";\n  }\n  \n  .bi-clouds::before {\n    content: \"\\f2c3\";\n  }\n  \n  .bi-cloudy-fill::before {\n    content: \"\\f2c4\";\n  }\n  \n  .bi-cloudy::before {\n    content: \"\\f2c5\";\n  }\n  \n  .bi-code-slash::before {\n    content: \"\\f2c6\";\n  }\n  \n  .bi-code-square::before {\n    content: \"\\f2c7\";\n  }\n  \n  .bi-code::before {\n    content: \"\\f2c8\";\n  }\n  \n  .bi-collection-fill::before {\n    content: \"\\f2c9\";\n  }\n  \n  .bi-collection-play-fill::before {\n    content: \"\\f2ca\";\n  }\n  \n  .bi-collection-play::before {\n    content: \"\\f2cb\";\n  }\n  \n  .bi-collection::before {\n    content: \"\\f2cc\";\n  }\n  \n  .bi-columns-gap::before {\n    content: \"\\f2cd\";\n  }\n  \n  .bi-columns::before {\n    content: \"\\f2ce\";\n  }\n  \n  .bi-command::before {\n    content: \"\\f2cf\";\n  }\n  \n  .bi-compass-fill::before {\n    content: \"\\f2d0\";\n  }\n  \n  .bi-compass::before {\n    content: \"\\f2d1\";\n  }\n  \n  .bi-cone-striped::before {\n    content: \"\\f2d2\";\n  }\n  \n  .bi-cone::before {\n    content: \"\\f2d3\";\n  }\n  \n  .bi-controller::before {\n    content: \"\\f2d4\";\n  }\n  \n  .bi-cpu-fill::before {\n    content: \"\\f2d5\";\n  }\n  \n  .bi-cpu::before {\n    content: \"\\f2d6\";\n  }\n  \n  .bi-credit-card-2-back-fill::before {\n    content: \"\\f2d7\";\n  }\n  \n  .bi-credit-card-2-back::before {\n    content: \"\\f2d8\";\n  }\n  \n  .bi-credit-card-2-front-fill::before {\n    content: \"\\f2d9\";\n  }\n  \n  .bi-credit-card-2-front::before {\n    content: \"\\f2da\";\n  }\n  \n  .bi-credit-card-fill::before {\n    content: \"\\f2db\";\n  }\n  \n  .bi-credit-card::before {\n    content: \"\\f2dc\";\n  }\n  \n  .bi-crop::before {\n    content: \"\\f2dd\";\n  }\n  \n  .bi-cup-fill::before {\n    content: \"\\f2de\";\n  }\n  \n  .bi-cup-straw::before {\n    content: \"\\f2df\";\n  }\n  \n  .bi-cup::before {\n    content: \"\\f2e0\";\n  }\n  \n  .bi-cursor-fill::before {\n    content: \"\\f2e1\";\n  }\n  \n  .bi-cursor-text::before {\n    content: \"\\f2e2\";\n  }\n  \n  .bi-cursor::before {\n    content: \"\\f2e3\";\n  }\n  \n  .bi-dash-circle-dotted::before {\n    content: \"\\f2e4\";\n  }\n  \n  .bi-dash-circle-fill::before {\n    content: \"\\f2e5\";\n  }\n  \n  .bi-dash-circle::before {\n    content: \"\\f2e6\";\n  }\n  \n  .bi-dash-square-dotted::before {\n    content: \"\\f2e7\";\n  }\n  \n  .bi-dash-square-fill::before {\n    content: \"\\f2e8\";\n  }\n  \n  .bi-dash-square::before {\n    content: \"\\f2e9\";\n  }\n  \n  .bi-dash::before {\n    content: \"\\f2ea\";\n  }\n  \n  .bi-diagram-2-fill::before {\n    content: \"\\f2eb\";\n  }\n  \n  .bi-diagram-2::before {\n    content: \"\\f2ec\";\n  }\n  \n  .bi-diagram-3-fill::before {\n    content: \"\\f2ed\";\n  }\n  \n  .bi-diagram-3::before {\n    content: \"\\f2ee\";\n  }\n  \n  .bi-diamond-fill::before {\n    content: \"\\f2ef\";\n  }\n  \n  .bi-diamond-half::before {\n    content: \"\\f2f0\";\n  }\n  \n  .bi-diamond::before {\n    content: \"\\f2f1\";\n  }\n  \n  .bi-dice-1-fill::before {\n    content: \"\\f2f2\";\n  }\n  \n  .bi-dice-1::before {\n    content: \"\\f2f3\";\n  }\n  \n  .bi-dice-2-fill::before {\n    content: \"\\f2f4\";\n  }\n  \n  .bi-dice-2::before {\n    content: \"\\f2f5\";\n  }\n  \n  .bi-dice-3-fill::before {\n    content: \"\\f2f6\";\n  }\n  \n  .bi-dice-3::before {\n    content: \"\\f2f7\";\n  }\n  \n  .bi-dice-4-fill::before {\n    content: \"\\f2f8\";\n  }\n  \n  .bi-dice-4::before {\n    content: \"\\f2f9\";\n  }\n  \n  .bi-dice-5-fill::before {\n    content: \"\\f2fa\";\n  }\n  \n  .bi-dice-5::before {\n    content: \"\\f2fb\";\n  }\n  \n  .bi-dice-6-fill::before {\n    content: \"\\f2fc\";\n  }\n  \n  .bi-dice-6::before {\n    content: \"\\f2fd\";\n  }\n  \n  .bi-disc-fill::before {\n    content: \"\\f2fe\";\n  }\n  \n  .bi-disc::before {\n    content: \"\\f2ff\";\n  }\n  \n  .bi-discord::before {\n    content: \"\\f300\";\n  }\n  \n  .bi-display-fill::before {\n    content: \"\\f301\";\n  }\n  \n  .bi-display::before {\n    content: \"\\f302\";\n  }\n  \n  .bi-distribute-horizontal::before {\n    content: \"\\f303\";\n  }\n  \n  .bi-distribute-vertical::before {\n    content: \"\\f304\";\n  }\n  \n  .bi-door-closed-fill::before {\n    content: \"\\f305\";\n  }\n  \n  .bi-door-closed::before {\n    content: \"\\f306\";\n  }\n  \n  .bi-door-open-fill::before {\n    content: \"\\f307\";\n  }\n  \n  .bi-door-open::before {\n    content: \"\\f308\";\n  }\n  \n  .bi-dot::before {\n    content: \"\\f309\";\n  }\n  \n  .bi-download::before {\n    content: \"\\f30a\";\n  }\n  \n  .bi-droplet-fill::before {\n    content: \"\\f30b\";\n  }\n  \n  .bi-droplet-half::before {\n    content: \"\\f30c\";\n  }\n  \n  .bi-droplet::before {\n    content: \"\\f30d\";\n  }\n  \n  .bi-earbuds::before {\n    content: \"\\f30e\";\n  }\n  \n  .bi-easel-fill::before {\n    content: \"\\f30f\";\n  }\n  \n  .bi-easel::before {\n    content: \"\\f310\";\n  }\n  \n  .bi-egg-fill::before {\n    content: \"\\f311\";\n  }\n  \n  .bi-egg-fried::before {\n    content: \"\\f312\";\n  }\n  \n  .bi-egg::before {\n    content: \"\\f313\";\n  }\n  \n  .bi-eject-fill::before {\n    content: \"\\f314\";\n  }\n  \n  .bi-eject::before {\n    content: \"\\f315\";\n  }\n  \n  .bi-emoji-angry-fill::before {\n    content: \"\\f316\";\n  }\n  \n  .bi-emoji-angry::before {\n    content: \"\\f317\";\n  }\n  \n  .bi-emoji-dizzy-fill::before {\n    content: \"\\f318\";\n  }\n  \n  .bi-emoji-dizzy::before {\n    content: \"\\f319\";\n  }\n  \n  .bi-emoji-expressionless-fill::before {\n    content: \"\\f31a\";\n  }\n  \n  .bi-emoji-expressionless::before {\n    content: \"\\f31b\";\n  }\n  \n  .bi-emoji-frown-fill::before {\n    content: \"\\f31c\";\n  }\n  \n  .bi-emoji-frown::before {\n    content: \"\\f31d\";\n  }\n  \n  .bi-emoji-heart-eyes-fill::before {\n    content: \"\\f31e\";\n  }\n  \n  .bi-emoji-heart-eyes::before {\n    content: \"\\f31f\";\n  }\n  \n  .bi-emoji-laughing-fill::before {\n    content: \"\\f320\";\n  }\n  \n  .bi-emoji-laughing::before {\n    content: \"\\f321\";\n  }\n  \n  .bi-emoji-neutral-fill::before {\n    content: \"\\f322\";\n  }\n  \n  .bi-emoji-neutral::before {\n    content: \"\\f323\";\n  }\n  \n  .bi-emoji-smile-fill::before {\n    content: \"\\f324\";\n  }\n  \n  .bi-emoji-smile-upside-down-fill::before {\n    content: \"\\f325\";\n  }\n  \n  .bi-emoji-smile-upside-down::before {\n    content: \"\\f326\";\n  }\n  \n  .bi-emoji-smile::before {\n    content: \"\\f327\";\n  }\n  \n  .bi-emoji-sunglasses-fill::before {\n    content: \"\\f328\";\n  }\n  \n  .bi-emoji-sunglasses::before {\n    content: \"\\f329\";\n  }\n  \n  .bi-emoji-wink-fill::before {\n    content: \"\\f32a\";\n  }\n  \n  .bi-emoji-wink::before {\n    content: \"\\f32b\";\n  }\n  \n  .bi-envelope-fill::before {\n    content: \"\\f32c\";\n  }\n  \n  .bi-envelope-open-fill::before {\n    content: \"\\f32d\";\n  }\n  \n  .bi-envelope-open::before {\n    content: \"\\f32e\";\n  }\n  \n  .bi-envelope::before {\n    content: \"\\f32f\";\n  }\n  \n  .bi-eraser-fill::before {\n    content: \"\\f330\";\n  }\n  \n  .bi-eraser::before {\n    content: \"\\f331\";\n  }\n  \n  .bi-exclamation-circle-fill::before {\n    content: \"\\f332\";\n  }\n  \n  .bi-exclamation-circle::before {\n    content: \"\\f333\";\n  }\n  \n  .bi-exclamation-diamond-fill::before {\n    content: \"\\f334\";\n  }\n  \n  .bi-exclamation-diamond::before {\n    content: \"\\f335\";\n  }\n  \n  .bi-exclamation-octagon-fill::before {\n    content: \"\\f336\";\n  }\n  \n  .bi-exclamation-octagon::before {\n    content: \"\\f337\";\n  }\n  \n  .bi-exclamation-square-fill::before {\n    content: \"\\f338\";\n  }\n  \n  .bi-exclamation-square::before {\n    content: \"\\f339\";\n  }\n  \n  .bi-exclamation-triangle-fill::before {\n    content: \"\\f33a\";\n  }\n  \n  .bi-exclamation-triangle::before {\n    content: \"\\f33b\";\n  }\n  \n  .bi-exclamation::before {\n    content: \"\\f33c\";\n  }\n  \n  .bi-exclude::before {\n    content: \"\\f33d\";\n  }\n  \n  .bi-eye-fill::before {\n    content: \"\\f33e\";\n  }\n  \n  .bi-eye-slash-fill::before {\n    content: \"\\f33f\";\n  }\n  \n  .bi-eye-slash::before {\n    content: \"\\f340\";\n  }\n  \n  .bi-eye::before {\n    content: \"\\f341\";\n  }\n  \n  .bi-eyedropper::before {\n    content: \"\\f342\";\n  }\n  \n  .bi-eyeglasses::before {\n    content: \"\\f343\";\n  }\n  \n  .bi-facebook::before {\n    content: \"\\f344\";\n  }\n  \n  .bi-file-arrow-down-fill::before {\n    content: \"\\f345\";\n  }\n  \n  .bi-file-arrow-down::before {\n    content: \"\\f346\";\n  }\n  \n  .bi-file-arrow-up-fill::before {\n    content: \"\\f347\";\n  }\n  \n  .bi-file-arrow-up::before {\n    content: \"\\f348\";\n  }\n  \n  .bi-file-bar-graph-fill::before {\n    content: \"\\f349\";\n  }\n  \n  .bi-file-bar-graph::before {\n    content: \"\\f34a\";\n  }\n  \n  .bi-file-binary-fill::before {\n    content: \"\\f34b\";\n  }\n  \n  .bi-file-binary::before {\n    content: \"\\f34c\";\n  }\n  \n  .bi-file-break-fill::before {\n    content: \"\\f34d\";\n  }\n  \n  .bi-file-break::before {\n    content: \"\\f34e\";\n  }\n  \n  .bi-file-check-fill::before {\n    content: \"\\f34f\";\n  }\n  \n  .bi-file-check::before {\n    content: \"\\f350\";\n  }\n  \n  .bi-file-code-fill::before {\n    content: \"\\f351\";\n  }\n  \n  .bi-file-code::before {\n    content: \"\\f352\";\n  }\n  \n  .bi-file-diff-fill::before {\n    content: \"\\f353\";\n  }\n  \n  .bi-file-diff::before {\n    content: \"\\f354\";\n  }\n  \n  .bi-file-earmark-arrow-down-fill::before {\n    content: \"\\f355\";\n  }\n  \n  .bi-file-earmark-arrow-down::before {\n    content: \"\\f356\";\n  }\n  \n  .bi-file-earmark-arrow-up-fill::before {\n    content: \"\\f357\";\n  }\n  \n  .bi-file-earmark-arrow-up::before {\n    content: \"\\f358\";\n  }\n  \n  .bi-file-earmark-bar-graph-fill::before {\n    content: \"\\f359\";\n  }\n  \n  .bi-file-earmark-bar-graph::before {\n    content: \"\\f35a\";\n  }\n  \n  .bi-file-earmark-binary-fill::before {\n    content: \"\\f35b\";\n  }\n  \n  .bi-file-earmark-binary::before {\n    content: \"\\f35c\";\n  }\n  \n  .bi-file-earmark-break-fill::before {\n    content: \"\\f35d\";\n  }\n  \n  .bi-file-earmark-break::before {\n    content: \"\\f35e\";\n  }\n  \n  .bi-file-earmark-check-fill::before {\n    content: \"\\f35f\";\n  }\n  \n  .bi-file-earmark-check::before {\n    content: \"\\f360\";\n  }\n  \n  .bi-file-earmark-code-fill::before {\n    content: \"\\f361\";\n  }\n  \n  .bi-file-earmark-code::before {\n    content: \"\\f362\";\n  }\n  \n  .bi-file-earmark-diff-fill::before {\n    content: \"\\f363\";\n  }\n  \n  .bi-file-earmark-diff::before {\n    content: \"\\f364\";\n  }\n  \n  .bi-file-earmark-easel-fill::before {\n    content: \"\\f365\";\n  }\n  \n  .bi-file-earmark-easel::before {\n    content: \"\\f366\";\n  }\n  \n  .bi-file-earmark-excel-fill::before {\n    content: \"\\f367\";\n  }\n  \n  .bi-file-earmark-excel::before {\n    content: \"\\f368\";\n  }\n  \n  .bi-file-earmark-fill::before {\n    content: \"\\f369\";\n  }\n  \n  .bi-file-earmark-font-fill::before {\n    content: \"\\f36a\";\n  }\n  \n  .bi-file-earmark-font::before {\n    content: \"\\f36b\";\n  }\n  \n  .bi-file-earmark-image-fill::before {\n    content: \"\\f36c\";\n  }\n  \n  .bi-file-earmark-image::before {\n    content: \"\\f36d\";\n  }\n  \n  .bi-file-earmark-lock-fill::before {\n    content: \"\\f36e\";\n  }\n  \n  .bi-file-earmark-lock::before {\n    content: \"\\f36f\";\n  }\n  \n  .bi-file-earmark-lock2-fill::before {\n    content: \"\\f370\";\n  }\n  \n  .bi-file-earmark-lock2::before {\n    content: \"\\f371\";\n  }\n  \n  .bi-file-earmark-medical-fill::before {\n    content: \"\\f372\";\n  }\n  \n  .bi-file-earmark-medical::before {\n    content: \"\\f373\";\n  }\n  \n  .bi-file-earmark-minus-fill::before {\n    content: \"\\f374\";\n  }\n  \n  .bi-file-earmark-minus::before {\n    content: \"\\f375\";\n  }\n  \n  .bi-file-earmark-music-fill::before {\n    content: \"\\f376\";\n  }\n  \n  .bi-file-earmark-music::before {\n    content: \"\\f377\";\n  }\n  \n  .bi-file-earmark-person-fill::before {\n    content: \"\\f378\";\n  }\n  \n  .bi-file-earmark-person::before {\n    content: \"\\f379\";\n  }\n  \n  .bi-file-earmark-play-fill::before {\n    content: \"\\f37a\";\n  }\n  \n  .bi-file-earmark-play::before {\n    content: \"\\f37b\";\n  }\n  \n  .bi-file-earmark-plus-fill::before {\n    content: \"\\f37c\";\n  }\n  \n  .bi-file-earmark-plus::before {\n    content: \"\\f37d\";\n  }\n  \n  .bi-file-earmark-post-fill::before {\n    content: \"\\f37e\";\n  }\n  \n  .bi-file-earmark-post::before {\n    content: \"\\f37f\";\n  }\n  \n  .bi-file-earmark-ppt-fill::before {\n    content: \"\\f380\";\n  }\n  \n  .bi-file-earmark-ppt::before {\n    content: \"\\f381\";\n  }\n  \n  .bi-file-earmark-richtext-fill::before {\n    content: \"\\f382\";\n  }\n  \n  .bi-file-earmark-richtext::before {\n    content: \"\\f383\";\n  }\n  \n  .bi-file-earmark-ruled-fill::before {\n    content: \"\\f384\";\n  }\n  \n  .bi-file-earmark-ruled::before {\n    content: \"\\f385\";\n  }\n  \n  .bi-file-earmark-slides-fill::before {\n    content: \"\\f386\";\n  }\n  \n  .bi-file-earmark-slides::before {\n    content: \"\\f387\";\n  }\n  \n  .bi-file-earmark-spreadsheet-fill::before {\n    content: \"\\f388\";\n  }\n  \n  .bi-file-earmark-spreadsheet::before {\n    content: \"\\f389\";\n  }\n  \n  .bi-file-earmark-text-fill::before {\n    content: \"\\f38a\";\n  }\n  \n  .bi-file-earmark-text::before {\n    content: \"\\f38b\";\n  }\n  \n  .bi-file-earmark-word-fill::before {\n    content: \"\\f38c\";\n  }\n  \n  .bi-file-earmark-word::before {\n    content: \"\\f38d\";\n  }\n  \n  .bi-file-earmark-x-fill::before {\n    content: \"\\f38e\";\n  }\n  \n  .bi-file-earmark-x::before {\n    content: \"\\f38f\";\n  }\n  \n  .bi-file-earmark-zip-fill::before {\n    content: \"\\f390\";\n  }\n  \n  .bi-file-earmark-zip::before {\n    content: \"\\f391\";\n  }\n  \n  .bi-file-earmark::before {\n    content: \"\\f392\";\n  }\n  \n  .bi-file-easel-fill::before {\n    content: \"\\f393\";\n  }\n  \n  .bi-file-easel::before {\n    content: \"\\f394\";\n  }\n  \n  .bi-file-excel-fill::before {\n    content: \"\\f395\";\n  }\n  \n  .bi-file-excel::before {\n    content: \"\\f396\";\n  }\n  \n  .bi-file-fill::before {\n    content: \"\\f397\";\n  }\n  \n  .bi-file-font-fill::before {\n    content: \"\\f398\";\n  }\n  \n  .bi-file-font::before {\n    content: \"\\f399\";\n  }\n  \n  .bi-file-image-fill::before {\n    content: \"\\f39a\";\n  }\n  \n  .bi-file-image::before {\n    content: \"\\f39b\";\n  }\n  \n  .bi-file-lock-fill::before {\n    content: \"\\f39c\";\n  }\n  \n  .bi-file-lock::before {\n    content: \"\\f39d\";\n  }\n  \n  .bi-file-lock2-fill::before {\n    content: \"\\f39e\";\n  }\n  \n  .bi-file-lock2::before {\n    content: \"\\f39f\";\n  }\n  \n  .bi-file-medical-fill::before {\n    content: \"\\f3a0\";\n  }\n  \n  .bi-file-medical::before {\n    content: \"\\f3a1\";\n  }\n  \n  .bi-file-minus-fill::before {\n    content: \"\\f3a2\";\n  }\n  \n  .bi-file-minus::before {\n    content: \"\\f3a3\";\n  }\n  \n  .bi-file-music-fill::before {\n    content: \"\\f3a4\";\n  }\n  \n  .bi-file-music::before {\n    content: \"\\f3a5\";\n  }\n  \n  .bi-file-person-fill::before {\n    content: \"\\f3a6\";\n  }\n  \n  .bi-file-person::before {\n    content: \"\\f3a7\";\n  }\n  \n  .bi-file-play-fill::before {\n    content: \"\\f3a8\";\n  }\n  \n  .bi-file-play::before {\n    content: \"\\f3a9\";\n  }\n  \n  .bi-file-plus-fill::before {\n    content: \"\\f3aa\";\n  }\n  \n  .bi-file-plus::before {\n    content: \"\\f3ab\";\n  }\n  \n  .bi-file-post-fill::before {\n    content: \"\\f3ac\";\n  }\n  \n  .bi-file-post::before {\n    content: \"\\f3ad\";\n  }\n  \n  .bi-file-ppt-fill::before {\n    content: \"\\f3ae\";\n  }\n  \n  .bi-file-ppt::before {\n    content: \"\\f3af\";\n  }\n  \n  .bi-file-richtext-fill::before {\n    content: \"\\f3b0\";\n  }\n  \n  .bi-file-richtext::before {\n    content: \"\\f3b1\";\n  }\n  \n  .bi-file-ruled-fill::before {\n    content: \"\\f3b2\";\n  }\n  \n  .bi-file-ruled::before {\n    content: \"\\f3b3\";\n  }\n  \n  .bi-file-slides-fill::before {\n    content: \"\\f3b4\";\n  }\n  \n  .bi-file-slides::before {\n    content: \"\\f3b5\";\n  }\n  \n  .bi-file-spreadsheet-fill::before {\n    content: \"\\f3b6\";\n  }\n  \n  .bi-file-spreadsheet::before {\n    content: \"\\f3b7\";\n  }\n  \n  .bi-file-text-fill::before {\n    content: \"\\f3b8\";\n  }\n  \n  .bi-file-text::before {\n    content: \"\\f3b9\";\n  }\n  \n  .bi-file-word-fill::before {\n    content: \"\\f3ba\";\n  }\n  \n  .bi-file-word::before {\n    content: \"\\f3bb\";\n  }\n  \n  .bi-file-x-fill::before {\n    content: \"\\f3bc\";\n  }\n  \n  .bi-file-x::before {\n    content: \"\\f3bd\";\n  }\n  \n  .bi-file-zip-fill::before {\n    content: \"\\f3be\";\n  }\n  \n  .bi-file-zip::before {\n    content: \"\\f3bf\";\n  }\n  \n  .bi-file::before {\n    content: \"\\f3c0\";\n  }\n  \n  .bi-files-alt::before {\n    content: \"\\f3c1\";\n  }\n  \n  .bi-files::before {\n    content: \"\\f3c2\";\n  }\n  \n  .bi-film::before {\n    content: \"\\f3c3\";\n  }\n  \n  .bi-filter-circle-fill::before {\n    content: \"\\f3c4\";\n  }\n  \n  .bi-filter-circle::before {\n    content: \"\\f3c5\";\n  }\n  \n  .bi-filter-left::before {\n    content: \"\\f3c6\";\n  }\n  \n  .bi-filter-right::before {\n    content: \"\\f3c7\";\n  }\n  \n  .bi-filter-square-fill::before {\n    content: \"\\f3c8\";\n  }\n  \n  .bi-filter-square::before {\n    content: \"\\f3c9\";\n  }\n  \n  .bi-filter::before {\n    content: \"\\f3ca\";\n  }\n  \n  .bi-flag-fill::before {\n    content: \"\\f3cb\";\n  }\n  \n  .bi-flag::before {\n    content: \"\\f3cc\";\n  }\n  \n  .bi-flower1::before {\n    content: \"\\f3cd\";\n  }\n  \n  .bi-flower2::before {\n    content: \"\\f3ce\";\n  }\n  \n  .bi-flower3::before {\n    content: \"\\f3cf\";\n  }\n  \n  .bi-folder-check::before {\n    content: \"\\f3d0\";\n  }\n  \n  .bi-folder-fill::before {\n    content: \"\\f3d1\";\n  }\n  \n  .bi-folder-minus::before {\n    content: \"\\f3d2\";\n  }\n  \n  .bi-folder-plus::before {\n    content: \"\\f3d3\";\n  }\n  \n  .bi-folder-symlink-fill::before {\n    content: \"\\f3d4\";\n  }\n  \n  .bi-folder-symlink::before {\n    content: \"\\f3d5\";\n  }\n  \n  .bi-folder-x::before {\n    content: \"\\f3d6\";\n  }\n  \n  .bi-folder::before {\n    content: \"\\f3d7\";\n  }\n  \n  .bi-folder2-open::before {\n    content: \"\\f3d8\";\n  }\n  \n  .bi-folder2::before {\n    content: \"\\f3d9\";\n  }\n  \n  .bi-fonts::before {\n    content: \"\\f3da\";\n  }\n  \n  .bi-forward-fill::before {\n    content: \"\\f3db\";\n  }\n  \n  .bi-forward::before {\n    content: \"\\f3dc\";\n  }\n  \n  .bi-front::before {\n    content: \"\\f3dd\";\n  }\n  \n  .bi-fullscreen-exit::before {\n    content: \"\\f3de\";\n  }\n  \n  .bi-fullscreen::before {\n    content: \"\\f3df\";\n  }\n  \n  .bi-funnel-fill::before {\n    content: \"\\f3e0\";\n  }\n  \n  .bi-funnel::before {\n    content: \"\\f3e1\";\n  }\n  \n  .bi-gear-fill::before {\n    content: \"\\f3e2\";\n  }\n  \n  .bi-gear-wide-connected::before {\n    content: \"\\f3e3\";\n  }\n  \n  .bi-gear-wide::before {\n    content: \"\\f3e4\";\n  }\n  \n  .bi-gear::before {\n    content: \"\\f3e5\";\n  }\n  \n  .bi-gem::before {\n    content: \"\\f3e6\";\n  }\n  \n  .bi-geo-alt-fill::before {\n    content: \"\\f3e7\";\n  }\n  \n  .bi-geo-alt::before {\n    content: \"\\f3e8\";\n  }\n  \n  .bi-geo-fill::before {\n    content: \"\\f3e9\";\n  }\n  \n  .bi-geo::before {\n    content: \"\\f3ea\";\n  }\n  \n  .bi-gift-fill::before {\n    content: \"\\f3eb\";\n  }\n  \n  .bi-gift::before {\n    content: \"\\f3ec\";\n  }\n  \n  .bi-github::before {\n    content: \"\\f3ed\";\n  }\n  \n  .bi-globe::before {\n    content: \"\\f3ee\";\n  }\n  \n  .bi-globe2::before {\n    content: \"\\f3ef\";\n  }\n  \n  .bi-google::before {\n    content: \"\\f3f0\";\n  }\n  \n  .bi-graph-down::before {\n    content: \"\\f3f1\";\n  }\n  \n  .bi-graph-up::before {\n    content: \"\\f3f2\";\n  }\n  \n  .bi-grid-1x2-fill::before {\n    content: \"\\f3f3\";\n  }\n  \n  .bi-grid-1x2::before {\n    content: \"\\f3f4\";\n  }\n  \n  .bi-grid-3x2-gap-fill::before {\n    content: \"\\f3f5\";\n  }\n  \n  .bi-grid-3x2-gap::before {\n    content: \"\\f3f6\";\n  }\n  \n  .bi-grid-3x2::before {\n    content: \"\\f3f7\";\n  }\n  \n  .bi-grid-3x3-gap-fill::before {\n    content: \"\\f3f8\";\n  }\n  \n  .bi-grid-3x3-gap::before {\n    content: \"\\f3f9\";\n  }\n  \n  .bi-grid-3x3::before {\n    content: \"\\f3fa\";\n  }\n  \n  .bi-grid-fill::before {\n    content: \"\\f3fb\";\n  }\n  \n  .bi-grid::before {\n    content: \"\\f3fc\";\n  }\n  \n  .bi-grip-horizontal::before {\n    content: \"\\f3fd\";\n  }\n  \n  .bi-grip-vertical::before {\n    content: \"\\f3fe\";\n  }\n  \n  .bi-hammer::before {\n    content: \"\\f3ff\";\n  }\n  \n  .bi-hand-index-fill::before {\n    content: \"\\f400\";\n  }\n  \n  .bi-hand-index-thumb-fill::before {\n    content: \"\\f401\";\n  }\n  \n  .bi-hand-index-thumb::before {\n    content: \"\\f402\";\n  }\n  \n  .bi-hand-index::before {\n    content: \"\\f403\";\n  }\n  \n  .bi-hand-thumbs-down-fill::before {\n    content: \"\\f404\";\n  }\n  \n  .bi-hand-thumbs-down::before {\n    content: \"\\f405\";\n  }\n  \n  .bi-hand-thumbs-up-fill::before {\n    content: \"\\f406\";\n  }\n  \n  .bi-hand-thumbs-up::before {\n    content: \"\\f407\";\n  }\n  \n  .bi-handbag-fill::before {\n    content: \"\\f408\";\n  }\n  \n  .bi-handbag::before {\n    content: \"\\f409\";\n  }\n  \n  .bi-hash::before {\n    content: \"\\f40a\";\n  }\n  \n  .bi-hdd-fill::before {\n    content: \"\\f40b\";\n  }\n  \n  .bi-hdd-network-fill::before {\n    content: \"\\f40c\";\n  }\n  \n  .bi-hdd-network::before {\n    content: \"\\f40d\";\n  }\n  \n  .bi-hdd-rack-fill::before {\n    content: \"\\f40e\";\n  }\n  \n  .bi-hdd-rack::before {\n    content: \"\\f40f\";\n  }\n  \n  .bi-hdd-stack-fill::before {\n    content: \"\\f410\";\n  }\n  \n  .bi-hdd-stack::before {\n    content: \"\\f411\";\n  }\n  \n  .bi-hdd::before {\n    content: \"\\f412\";\n  }\n  \n  .bi-headphones::before {\n    content: \"\\f413\";\n  }\n  \n  .bi-headset::before {\n    content: \"\\f414\";\n  }\n  \n  .bi-heart-fill::before {\n    content: \"\\f415\";\n  }\n  \n  .bi-heart-half::before {\n    content: \"\\f416\";\n  }\n  \n  .bi-heart::before {\n    content: \"\\f417\";\n  }\n  \n  .bi-heptagon-fill::before {\n    content: \"\\f418\";\n  }\n  \n  .bi-heptagon-half::before {\n    content: \"\\f419\";\n  }\n  \n  .bi-heptagon::before {\n    content: \"\\f41a\";\n  }\n  \n  .bi-hexagon-fill::before {\n    content: \"\\f41b\";\n  }\n  \n  .bi-hexagon-half::before {\n    content: \"\\f41c\";\n  }\n  \n  .bi-hexagon::before {\n    content: \"\\f41d\";\n  }\n  \n  .bi-hourglass-bottom::before {\n    content: \"\\f41e\";\n  }\n  \n  .bi-hourglass-split::before {\n    content: \"\\f41f\";\n  }\n  \n  .bi-hourglass-top::before {\n    content: \"\\f420\";\n  }\n  \n  .bi-hourglass::before {\n    content: \"\\f421\";\n  }\n  \n  .bi-house-door-fill::before {\n    content: \"\\f422\";\n  }\n  \n  .bi-house-door::before {\n    content: \"\\f423\";\n  }\n  \n  .bi-house-fill::before {\n    content: \"\\f424\";\n  }\n  \n  .bi-house::before {\n    content: \"\\f425\";\n  }\n  \n  .bi-hr::before {\n    content: \"\\f426\";\n  }\n  \n  .bi-hurricane::before {\n    content: \"\\f427\";\n  }\n  \n  .bi-image-alt::before {\n    content: \"\\f428\";\n  }\n  \n  .bi-image-fill::before {\n    content: \"\\f429\";\n  }\n  \n  .bi-image::before {\n    content: \"\\f42a\";\n  }\n  \n  .bi-images::before {\n    content: \"\\f42b\";\n  }\n  \n  .bi-inbox-fill::before {\n    content: \"\\f42c\";\n  }\n  \n  .bi-inbox::before {\n    content: \"\\f42d\";\n  }\n  \n  .bi-inboxes-fill::before {\n    content: \"\\f42e\";\n  }\n  \n  .bi-inboxes::before {\n    content: \"\\f42f\";\n  }\n  \n  .bi-info-circle-fill::before {\n    content: \"\\f430\";\n  }\n  \n  .bi-info-circle::before {\n    content: \"\\f431\";\n  }\n  \n  .bi-info-square-fill::before {\n    content: \"\\f432\";\n  }\n  \n  .bi-info-square::before {\n    content: \"\\f433\";\n  }\n  \n  .bi-info::before {\n    content: \"\\f434\";\n  }\n  \n  .bi-input-cursor-text::before {\n    content: \"\\f435\";\n  }\n  \n  .bi-input-cursor::before {\n    content: \"\\f436\";\n  }\n  \n  .bi-instagram::before {\n    content: \"\\f437\";\n  }\n  \n  .bi-intersect::before {\n    content: \"\\f438\";\n  }\n  \n  .bi-journal-album::before {\n    content: \"\\f439\";\n  }\n  \n  .bi-journal-arrow-down::before {\n    content: \"\\f43a\";\n  }\n  \n  .bi-journal-arrow-up::before {\n    content: \"\\f43b\";\n  }\n  \n  .bi-journal-bookmark-fill::before {\n    content: \"\\f43c\";\n  }\n  \n  .bi-journal-bookmark::before {\n    content: \"\\f43d\";\n  }\n  \n  .bi-journal-check::before {\n    content: \"\\f43e\";\n  }\n  \n  .bi-journal-code::before {\n    content: \"\\f43f\";\n  }\n  \n  .bi-journal-medical::before {\n    content: \"\\f440\";\n  }\n  \n  .bi-journal-minus::before {\n    content: \"\\f441\";\n  }\n  \n  .bi-journal-plus::before {\n    content: \"\\f442\";\n  }\n  \n  .bi-journal-richtext::before {\n    content: \"\\f443\";\n  }\n  \n  .bi-journal-text::before {\n    content: \"\\f444\";\n  }\n  \n  .bi-journal-x::before {\n    content: \"\\f445\";\n  }\n  \n  .bi-journal::before {\n    content: \"\\f446\";\n  }\n  \n  .bi-journals::before {\n    content: \"\\f447\";\n  }\n  \n  .bi-joystick::before {\n    content: \"\\f448\";\n  }\n  \n  .bi-justify-left::before {\n    content: \"\\f449\";\n  }\n  \n  .bi-justify-right::before {\n    content: \"\\f44a\";\n  }\n  \n  .bi-justify::before {\n    content: \"\\f44b\";\n  }\n  \n  .bi-kanban-fill::before {\n    content: \"\\f44c\";\n  }\n  \n  .bi-kanban::before {\n    content: \"\\f44d\";\n  }\n  \n  .bi-key-fill::before {\n    content: \"\\f44e\";\n  }\n  \n  .bi-key::before {\n    content: \"\\f44f\";\n  }\n  \n  .bi-keyboard-fill::before {\n    content: \"\\f450\";\n  }\n  \n  .bi-keyboard::before {\n    content: \"\\f451\";\n  }\n  \n  .bi-ladder::before {\n    content: \"\\f452\";\n  }\n  \n  .bi-lamp-fill::before {\n    content: \"\\f453\";\n  }\n  \n  .bi-lamp::before {\n    content: \"\\f454\";\n  }\n  \n  .bi-laptop-fill::before {\n    content: \"\\f455\";\n  }\n  \n  .bi-laptop::before {\n    content: \"\\f456\";\n  }\n  \n  .bi-layer-backward::before {\n    content: \"\\f457\";\n  }\n  \n  .bi-layer-forward::before {\n    content: \"\\f458\";\n  }\n  \n  .bi-layers-fill::before {\n    content: \"\\f459\";\n  }\n  \n  .bi-layers-half::before {\n    content: \"\\f45a\";\n  }\n  \n  .bi-layers::before {\n    content: \"\\f45b\";\n  }\n  \n  .bi-layout-sidebar-inset-reverse::before {\n    content: \"\\f45c\";\n  }\n  \n  .bi-layout-sidebar-inset::before {\n    content: \"\\f45d\";\n  }\n  \n  .bi-layout-sidebar-reverse::before {\n    content: \"\\f45e\";\n  }\n  \n  .bi-layout-sidebar::before {\n    content: \"\\f45f\";\n  }\n  \n  .bi-layout-split::before {\n    content: \"\\f460\";\n  }\n  \n  .bi-layout-text-sidebar-reverse::before {\n    content: \"\\f461\";\n  }\n  \n  .bi-layout-text-sidebar::before {\n    content: \"\\f462\";\n  }\n  \n  .bi-layout-text-window-reverse::before {\n    content: \"\\f463\";\n  }\n  \n  .bi-layout-text-window::before {\n    content: \"\\f464\";\n  }\n  \n  .bi-layout-three-columns::before {\n    content: \"\\f465\";\n  }\n  \n  .bi-layout-wtf::before {\n    content: \"\\f466\";\n  }\n  \n  .bi-life-preserver::before {\n    content: \"\\f467\";\n  }\n  \n  .bi-lightbulb-fill::before {\n    content: \"\\f468\";\n  }\n  \n  .bi-lightbulb-off-fill::before {\n    content: \"\\f469\";\n  }\n  \n  .bi-lightbulb-off::before {\n    content: \"\\f46a\";\n  }\n  \n  .bi-lightbulb::before {\n    content: \"\\f46b\";\n  }\n  \n  .bi-lightning-charge-fill::before {\n    content: \"\\f46c\";\n  }\n  \n  .bi-lightning-charge::before {\n    content: \"\\f46d\";\n  }\n  \n  .bi-lightning-fill::before {\n    content: \"\\f46e\";\n  }\n  \n  .bi-lightning::before {\n    content: \"\\f46f\";\n  }\n  \n  .bi-link-45deg::before {\n    content: \"\\f470\";\n  }\n  \n  .bi-link::before {\n    content: \"\\f471\";\n  }\n  \n  .bi-linkedin::before {\n    content: \"\\f472\";\n  }\n  \n  .bi-list-check::before {\n    content: \"\\f473\";\n  }\n  \n  .bi-list-nested::before {\n    content: \"\\f474\";\n  }\n  \n  .bi-list-ol::before {\n    content: \"\\f475\";\n  }\n  \n  .bi-list-stars::before {\n    content: \"\\f476\";\n  }\n  \n  .bi-list-task::before {\n    content: \"\\f477\";\n  }\n  \n  .bi-list-ul::before {\n    content: \"\\f478\";\n  }\n  \n  .bi-list::before {\n    content: \"\\f479\";\n  }\n  \n  .bi-lock-fill::before {\n    content: \"\\f47a\";\n  }\n  \n  .bi-lock::before {\n    content: \"\\f47b\";\n  }\n  \n  .bi-mailbox::before {\n    content: \"\\f47c\";\n  }\n  \n  .bi-mailbox2::before {\n    content: \"\\f47d\";\n  }\n  \n  .bi-map-fill::before {\n    content: \"\\f47e\";\n  }\n  \n  .bi-map::before {\n    content: \"\\f47f\";\n  }\n  \n  .bi-markdown-fill::before {\n    content: \"\\f480\";\n  }\n  \n  .bi-markdown::before {\n    content: \"\\f481\";\n  }\n  \n  .bi-mask::before {\n    content: \"\\f482\";\n  }\n  \n  .bi-megaphone-fill::before {\n    content: \"\\f483\";\n  }\n  \n  .bi-megaphone::before {\n    content: \"\\f484\";\n  }\n  \n  .bi-menu-app-fill::before {\n    content: \"\\f485\";\n  }\n  \n  .bi-menu-app::before {\n    content: \"\\f486\";\n  }\n  \n  .bi-menu-button-fill::before {\n    content: \"\\f487\";\n  }\n  \n  .bi-menu-button-wide-fill::before {\n    content: \"\\f488\";\n  }\n  \n  .bi-menu-button-wide::before {\n    content: \"\\f489\";\n  }\n  \n  .bi-menu-button::before {\n    content: \"\\f48a\";\n  }\n  \n  .bi-menu-down::before {\n    content: \"\\f48b\";\n  }\n  \n  .bi-menu-up::before {\n    content: \"\\f48c\";\n  }\n  \n  .bi-mic-fill::before {\n    content: \"\\f48d\";\n  }\n  \n  .bi-mic-mute-fill::before {\n    content: \"\\f48e\";\n  }\n  \n  .bi-mic-mute::before {\n    content: \"\\f48f\";\n  }\n  \n  .bi-mic::before {\n    content: \"\\f490\";\n  }\n  \n  .bi-minecart-loaded::before {\n    content: \"\\f491\";\n  }\n  \n  .bi-minecart::before {\n    content: \"\\f492\";\n  }\n  \n  .bi-moisture::before {\n    content: \"\\f493\";\n  }\n  \n  .bi-moon-fill::before {\n    content: \"\\f494\";\n  }\n  \n  .bi-moon-stars-fill::before {\n    content: \"\\f495\";\n  }\n  \n  .bi-moon-stars::before {\n    content: \"\\f496\";\n  }\n  \n  .bi-moon::before {\n    content: \"\\f497\";\n  }\n  \n  .bi-mouse-fill::before {\n    content: \"\\f498\";\n  }\n  \n  .bi-mouse::before {\n    content: \"\\f499\";\n  }\n  \n  .bi-mouse2-fill::before {\n    content: \"\\f49a\";\n  }\n  \n  .bi-mouse2::before {\n    content: \"\\f49b\";\n  }\n  \n  .bi-mouse3-fill::before {\n    content: \"\\f49c\";\n  }\n  \n  .bi-mouse3::before {\n    content: \"\\f49d\";\n  }\n  \n  .bi-music-note-beamed::before {\n    content: \"\\f49e\";\n  }\n  \n  .bi-music-note-list::before {\n    content: \"\\f49f\";\n  }\n  \n  .bi-music-note::before {\n    content: \"\\f4a0\";\n  }\n  \n  .bi-music-player-fill::before {\n    content: \"\\f4a1\";\n  }\n  \n  .bi-music-player::before {\n    content: \"\\f4a2\";\n  }\n  \n  .bi-newspaper::before {\n    content: \"\\f4a3\";\n  }\n  \n  .bi-node-minus-fill::before {\n    content: \"\\f4a4\";\n  }\n  \n  .bi-node-minus::before {\n    content: \"\\f4a5\";\n  }\n  \n  .bi-node-plus-fill::before {\n    content: \"\\f4a6\";\n  }\n  \n  .bi-node-plus::before {\n    content: \"\\f4a7\";\n  }\n  \n  .bi-nut-fill::before {\n    content: \"\\f4a8\";\n  }\n  \n  .bi-nut::before {\n    content: \"\\f4a9\";\n  }\n  \n  .bi-octagon-fill::before {\n    content: \"\\f4aa\";\n  }\n  \n  .bi-octagon-half::before {\n    content: \"\\f4ab\";\n  }\n  \n  .bi-octagon::before {\n    content: \"\\f4ac\";\n  }\n  \n  .bi-option::before {\n    content: \"\\f4ad\";\n  }\n  \n  .bi-outlet::before {\n    content: \"\\f4ae\";\n  }\n  \n  .bi-paint-bucket::before {\n    content: \"\\f4af\";\n  }\n  \n  .bi-palette-fill::before {\n    content: \"\\f4b0\";\n  }\n  \n  .bi-palette::before {\n    content: \"\\f4b1\";\n  }\n  \n  .bi-palette2::before {\n    content: \"\\f4b2\";\n  }\n  \n  .bi-paperclip::before {\n    content: \"\\f4b3\";\n  }\n  \n  .bi-paragraph::before {\n    content: \"\\f4b4\";\n  }\n  \n  .bi-patch-check-fill::before {\n    content: \"\\f4b5\";\n  }\n  \n  .bi-patch-check::before {\n    content: \"\\f4b6\";\n  }\n  \n  .bi-patch-exclamation-fill::before {\n    content: \"\\f4b7\";\n  }\n  \n  .bi-patch-exclamation::before {\n    content: \"\\f4b8\";\n  }\n  \n  .bi-patch-minus-fill::before {\n    content: \"\\f4b9\";\n  }\n  \n  .bi-patch-minus::before {\n    content: \"\\f4ba\";\n  }\n  \n  .bi-patch-plus-fill::before {\n    content: \"\\f4bb\";\n  }\n  \n  .bi-patch-plus::before {\n    content: \"\\f4bc\";\n  }\n  \n  .bi-patch-question-fill::before {\n    content: \"\\f4bd\";\n  }\n  \n  .bi-patch-question::before {\n    content: \"\\f4be\";\n  }\n  \n  .bi-pause-btn-fill::before {\n    content: \"\\f4bf\";\n  }\n  \n  .bi-pause-btn::before {\n    content: \"\\f4c0\";\n  }\n  \n  .bi-pause-circle-fill::before {\n    content: \"\\f4c1\";\n  }\n  \n  .bi-pause-circle::before {\n    content: \"\\f4c2\";\n  }\n  \n  .bi-pause-fill::before {\n    content: \"\\f4c3\";\n  }\n  \n  .bi-pause::before {\n    content: \"\\f4c4\";\n  }\n  \n  .bi-peace-fill::before {\n    content: \"\\f4c5\";\n  }\n  \n  .bi-peace::before {\n    content: \"\\f4c6\";\n  }\n  \n  .bi-pen-fill::before {\n    content: \"\\f4c7\";\n  }\n  \n  .bi-pen::before {\n    content: \"\\f4c8\";\n  }\n  \n  .bi-pencil-fill::before {\n    content: \"\\f4c9\";\n  }\n  \n  .bi-pencil-square::before {\n    content: \"\\f4ca\";\n  }\n  \n  .bi-pencil::before {\n    content: \"\\f4cb\";\n  }\n  \n  .bi-pentagon-fill::before {\n    content: \"\\f4cc\";\n  }\n  \n  .bi-pentagon-half::before {\n    content: \"\\f4cd\";\n  }\n  \n  .bi-pentagon::before {\n    content: \"\\f4ce\";\n  }\n  \n  .bi-people-fill::before {\n    content: \"\\f4cf\";\n  }\n  \n  .bi-people::before {\n    content: \"\\f4d0\";\n  }\n  \n  .bi-percent::before {\n    content: \"\\f4d1\";\n  }\n  \n  .bi-person-badge-fill::before {\n    content: \"\\f4d2\";\n  }\n  \n  .bi-person-badge::before {\n    content: \"\\f4d3\";\n  }\n  \n  .bi-person-bounding-box::before {\n    content: \"\\f4d4\";\n  }\n  \n  .bi-person-check-fill::before {\n    content: \"\\f4d5\";\n  }\n  \n  .bi-person-check::before {\n    content: \"\\f4d6\";\n  }\n  \n  .bi-person-circle::before {\n    content: \"\\f4d7\";\n  }\n  \n  .bi-person-dash-fill::before {\n    content: \"\\f4d8\";\n  }\n  \n  .bi-person-dash::before {\n    content: \"\\f4d9\";\n  }\n  \n  .bi-person-fill::before {\n    content: \"\\f4da\";\n  }\n  \n  .bi-person-lines-fill::before {\n    content: \"\\f4db\";\n  }\n  \n  .bi-person-plus-fill::before {\n    content: \"\\f4dc\";\n  }\n  \n  .bi-person-plus::before {\n    content: \"\\f4dd\";\n  }\n  \n  .bi-person-square::before {\n    content: \"\\f4de\";\n  }\n  \n  .bi-person-x-fill::before {\n    content: \"\\f4df\";\n  }\n  \n  .bi-person-x::before {\n    content: \"\\f4e0\";\n  }\n  \n  .bi-person::before {\n    content: \"\\f4e1\";\n  }\n  \n  .bi-phone-fill::before {\n    content: \"\\f4e2\";\n  }\n  \n  .bi-phone-landscape-fill::before {\n    content: \"\\f4e3\";\n  }\n  \n  .bi-phone-landscape::before {\n    content: \"\\f4e4\";\n  }\n  \n  .bi-phone-vibrate-fill::before {\n    content: \"\\f4e5\";\n  }\n  \n  .bi-phone-vibrate::before {\n    content: \"\\f4e6\";\n  }\n  \n  .bi-phone::before {\n    content: \"\\f4e7\";\n  }\n  \n  .bi-pie-chart-fill::before {\n    content: \"\\f4e8\";\n  }\n  \n  .bi-pie-chart::before {\n    content: \"\\f4e9\";\n  }\n  \n  .bi-pin-angle-fill::before {\n    content: \"\\f4ea\";\n  }\n  \n  .bi-pin-angle::before {\n    content: \"\\f4eb\";\n  }\n  \n  .bi-pin-fill::before {\n    content: \"\\f4ec\";\n  }\n  \n  .bi-pin::before {\n    content: \"\\f4ed\";\n  }\n  \n  .bi-pip-fill::before {\n    content: \"\\f4ee\";\n  }\n  \n  .bi-pip::before {\n    content: \"\\f4ef\";\n  }\n  \n  .bi-play-btn-fill::before {\n    content: \"\\f4f0\";\n  }\n  \n  .bi-play-btn::before {\n    content: \"\\f4f1\";\n  }\n  \n  .bi-play-circle-fill::before {\n    content: \"\\f4f2\";\n  }\n  \n  .bi-play-circle::before {\n    content: \"\\f4f3\";\n  }\n  \n  .bi-play-fill::before {\n    content: \"\\f4f4\";\n  }\n  \n  .bi-play::before {\n    content: \"\\f4f5\";\n  }\n  \n  .bi-plug-fill::before {\n    content: \"\\f4f6\";\n  }\n  \n  .bi-plug::before {\n    content: \"\\f4f7\";\n  }\n  \n  .bi-plus-circle-dotted::before {\n    content: \"\\f4f8\";\n  }\n  \n  .bi-plus-circle-fill::before {\n    content: \"\\f4f9\";\n  }\n  \n  .bi-plus-circle::before {\n    content: \"\\f4fa\";\n  }\n  \n  .bi-plus-square-dotted::before {\n    content: \"\\f4fb\";\n  }\n  \n  .bi-plus-square-fill::before {\n    content: \"\\f4fc\";\n  }\n  \n  .bi-plus-square::before {\n    content: \"\\f4fd\";\n  }\n  \n  .bi-plus::before {\n    content: \"\\f4fe\";\n  }\n  \n  .bi-power::before {\n    content: \"\\f4ff\";\n  }\n  \n  .bi-printer-fill::before {\n    content: \"\\f500\";\n  }\n  \n  .bi-printer::before {\n    content: \"\\f501\";\n  }\n  \n  .bi-puzzle-fill::before {\n    content: \"\\f502\";\n  }\n  \n  .bi-puzzle::before {\n    content: \"\\f503\";\n  }\n  \n  .bi-question-circle-fill::before {\n    content: \"\\f504\";\n  }\n  \n  .bi-question-circle::before {\n    content: \"\\f505\";\n  }\n  \n  .bi-question-diamond-fill::before {\n    content: \"\\f506\";\n  }\n  \n  .bi-question-diamond::before {\n    content: \"\\f507\";\n  }\n  \n  .bi-question-octagon-fill::before {\n    content: \"\\f508\";\n  }\n  \n  .bi-question-octagon::before {\n    content: \"\\f509\";\n  }\n  \n  .bi-question-square-fill::before {\n    content: \"\\f50a\";\n  }\n  \n  .bi-question-square::before {\n    content: \"\\f50b\";\n  }\n  \n  .bi-question::before {\n    content: \"\\f50c\";\n  }\n  \n  .bi-rainbow::before {\n    content: \"\\f50d\";\n  }\n  \n  .bi-receipt-cutoff::before {\n    content: \"\\f50e\";\n  }\n  \n  .bi-receipt::before {\n    content: \"\\f50f\";\n  }\n  \n  .bi-reception-0::before {\n    content: \"\\f510\";\n  }\n  \n  .bi-reception-1::before {\n    content: \"\\f511\";\n  }\n  \n  .bi-reception-2::before {\n    content: \"\\f512\";\n  }\n  \n  .bi-reception-3::before {\n    content: \"\\f513\";\n  }\n  \n  .bi-reception-4::before {\n    content: \"\\f514\";\n  }\n  \n  .bi-record-btn-fill::before {\n    content: \"\\f515\";\n  }\n  \n  .bi-record-btn::before {\n    content: \"\\f516\";\n  }\n  \n  .bi-record-circle-fill::before {\n    content: \"\\f517\";\n  }\n  \n  .bi-record-circle::before {\n    content: \"\\f518\";\n  }\n  \n  .bi-record-fill::before {\n    content: \"\\f519\";\n  }\n  \n  .bi-record::before {\n    content: \"\\f51a\";\n  }\n  \n  .bi-record2-fill::before {\n    content: \"\\f51b\";\n  }\n  \n  .bi-record2::before {\n    content: \"\\f51c\";\n  }\n  \n  .bi-reply-all-fill::before {\n    content: \"\\f51d\";\n  }\n  \n  .bi-reply-all::before {\n    content: \"\\f51e\";\n  }\n  \n  .bi-reply-fill::before {\n    content: \"\\f51f\";\n  }\n  \n  .bi-reply::before {\n    content: \"\\f520\";\n  }\n  \n  .bi-rss-fill::before {\n    content: \"\\f521\";\n  }\n  \n  .bi-rss::before {\n    content: \"\\f522\";\n  }\n  \n  .bi-rulers::before {\n    content: \"\\f523\";\n  }\n  \n  .bi-save-fill::before {\n    content: \"\\f524\";\n  }\n  \n  .bi-save::before {\n    content: \"\\f525\";\n  }\n  \n  .bi-save2-fill::before {\n    content: \"\\f526\";\n  }\n  \n  .bi-save2::before {\n    content: \"\\f527\";\n  }\n  \n  .bi-scissors::before {\n    content: \"\\f528\";\n  }\n  \n  .bi-screwdriver::before {\n    content: \"\\f529\";\n  }\n  \n  .bi-search::before {\n    content: \"\\f52a\";\n  }\n  \n  .bi-segmented-nav::before {\n    content: \"\\f52b\";\n  }\n  \n  .bi-server::before {\n    content: \"\\f52c\";\n  }\n  \n  .bi-share-fill::before {\n    content: \"\\f52d\";\n  }\n  \n  .bi-share::before {\n    content: \"\\f52e\";\n  }\n  \n  .bi-shield-check::before {\n    content: \"\\f52f\";\n  }\n  \n  .bi-shield-exclamation::before {\n    content: \"\\f530\";\n  }\n  \n  .bi-shield-fill-check::before {\n    content: \"\\f531\";\n  }\n  \n  .bi-shield-fill-exclamation::before {\n    content: \"\\f532\";\n  }\n  \n  .bi-shield-fill-minus::before {\n    content: \"\\f533\";\n  }\n  \n  .bi-shield-fill-plus::before {\n    content: \"\\f534\";\n  }\n  \n  .bi-shield-fill-x::before {\n    content: \"\\f535\";\n  }\n  \n  .bi-shield-fill::before {\n    content: \"\\f536\";\n  }\n  \n  .bi-shield-lock-fill::before {\n    content: \"\\f537\";\n  }\n  \n  .bi-shield-lock::before {\n    content: \"\\f538\";\n  }\n  \n  .bi-shield-minus::before {\n    content: \"\\f539\";\n  }\n  \n  .bi-shield-plus::before {\n    content: \"\\f53a\";\n  }\n  \n  .bi-shield-shaded::before {\n    content: \"\\f53b\";\n  }\n  \n  .bi-shield-slash-fill::before {\n    content: \"\\f53c\";\n  }\n  \n  .bi-shield-slash::before {\n    content: \"\\f53d\";\n  }\n  \n  .bi-shield-x::before {\n    content: \"\\f53e\";\n  }\n  \n  .bi-shield::before {\n    content: \"\\f53f\";\n  }\n  \n  .bi-shift-fill::before {\n    content: \"\\f540\";\n  }\n  \n  .bi-shift::before {\n    content: \"\\f541\";\n  }\n  \n  .bi-shop-window::before {\n    content: \"\\f542\";\n  }\n  \n  .bi-shop::before {\n    content: \"\\f543\";\n  }\n  \n  .bi-shuffle::before {\n    content: \"\\f544\";\n  }\n  \n  .bi-signpost-2-fill::before {\n    content: \"\\f545\";\n  }\n  \n  .bi-signpost-2::before {\n    content: \"\\f546\";\n  }\n  \n  .bi-signpost-fill::before {\n    content: \"\\f547\";\n  }\n  \n  .bi-signpost-split-fill::before {\n    content: \"\\f548\";\n  }\n  \n  .bi-signpost-split::before {\n    content: \"\\f549\";\n  }\n  \n  .bi-signpost::before {\n    content: \"\\f54a\";\n  }\n  \n  .bi-sim-fill::before {\n    content: \"\\f54b\";\n  }\n  \n  .bi-sim::before {\n    content: \"\\f54c\";\n  }\n  \n  .bi-skip-backward-btn-fill::before {\n    content: \"\\f54d\";\n  }\n  \n  .bi-skip-backward-btn::before {\n    content: \"\\f54e\";\n  }\n  \n  .bi-skip-backward-circle-fill::before {\n    content: \"\\f54f\";\n  }\n  \n  .bi-skip-backward-circle::before {\n    content: \"\\f550\";\n  }\n  \n  .bi-skip-backward-fill::before {\n    content: \"\\f551\";\n  }\n  \n  .bi-skip-backward::before {\n    content: \"\\f552\";\n  }\n  \n  .bi-skip-end-btn-fill::before {\n    content: \"\\f553\";\n  }\n  \n  .bi-skip-end-btn::before {\n    content: \"\\f554\";\n  }\n  \n  .bi-skip-end-circle-fill::before {\n    content: \"\\f555\";\n  }\n  \n  .bi-skip-end-circle::before {\n    content: \"\\f556\";\n  }\n  \n  .bi-skip-end-fill::before {\n    content: \"\\f557\";\n  }\n  \n  .bi-skip-end::before {\n    content: \"\\f558\";\n  }\n  \n  .bi-skip-forward-btn-fill::before {\n    content: \"\\f559\";\n  }\n  \n  .bi-skip-forward-btn::before {\n    content: \"\\f55a\";\n  }\n  \n  .bi-skip-forward-circle-fill::before {\n    content: \"\\f55b\";\n  }\n  \n  .bi-skip-forward-circle::before {\n    content: \"\\f55c\";\n  }\n  \n  .bi-skip-forward-fill::before {\n    content: \"\\f55d\";\n  }\n  \n  .bi-skip-forward::before {\n    content: \"\\f55e\";\n  }\n  \n  .bi-skip-start-btn-fill::before {\n    content: \"\\f55f\";\n  }\n  \n  .bi-skip-start-btn::before {\n    content: \"\\f560\";\n  }\n  \n  .bi-skip-start-circle-fill::before {\n    content: \"\\f561\";\n  }\n  \n  .bi-skip-start-circle::before {\n    content: \"\\f562\";\n  }\n  \n  .bi-skip-start-fill::before {\n    content: \"\\f563\";\n  }\n  \n  .bi-skip-start::before {\n    content: \"\\f564\";\n  }\n  \n  .bi-slack::before {\n    content: \"\\f565\";\n  }\n  \n  .bi-slash-circle-fill::before {\n    content: \"\\f566\";\n  }\n  \n  .bi-slash-circle::before {\n    content: \"\\f567\";\n  }\n  \n  .bi-slash-square-fill::before {\n    content: \"\\f568\";\n  }\n  \n  .bi-slash-square::before {\n    content: \"\\f569\";\n  }\n  \n  .bi-slash::before {\n    content: \"\\f56a\";\n  }\n  \n  .bi-sliders::before {\n    content: \"\\f56b\";\n  }\n  \n  .bi-smartwatch::before {\n    content: \"\\f56c\";\n  }\n  \n  .bi-snow::before {\n    content: \"\\f56d\";\n  }\n  \n  .bi-snow2::before {\n    content: \"\\f56e\";\n  }\n  \n  .bi-snow3::before {\n    content: \"\\f56f\";\n  }\n  \n  .bi-sort-alpha-down-alt::before {\n    content: \"\\f570\";\n  }\n  \n  .bi-sort-alpha-down::before {\n    content: \"\\f571\";\n  }\n  \n  .bi-sort-alpha-up-alt::before {\n    content: \"\\f572\";\n  }\n  \n  .bi-sort-alpha-up::before {\n    content: \"\\f573\";\n  }\n  \n  .bi-sort-down-alt::before {\n    content: \"\\f574\";\n  }\n  \n  .bi-sort-down::before {\n    content: \"\\f575\";\n  }\n  \n  .bi-sort-numeric-down-alt::before {\n    content: \"\\f576\";\n  }\n  \n  .bi-sort-numeric-down::before {\n    content: \"\\f577\";\n  }\n  \n  .bi-sort-numeric-up-alt::before {\n    content: \"\\f578\";\n  }\n  \n  .bi-sort-numeric-up::before {\n    content: \"\\f579\";\n  }\n  \n  .bi-sort-up-alt::before {\n    content: \"\\f57a\";\n  }\n  \n  .bi-sort-up::before {\n    content: \"\\f57b\";\n  }\n  \n  .bi-soundwave::before {\n    content: \"\\f57c\";\n  }\n  \n  .bi-speaker-fill::before {\n    content: \"\\f57d\";\n  }\n  \n  .bi-speaker::before {\n    content: \"\\f57e\";\n  }\n  \n  .bi-speedometer::before {\n    content: \"\\f57f\";\n  }\n  \n  .bi-speedometer2::before {\n    content: \"\\f580\";\n  }\n  \n  .bi-spellcheck::before {\n    content: \"\\f581\";\n  }\n  \n  .bi-square-fill::before {\n    content: \"\\f582\";\n  }\n  \n  .bi-square-half::before {\n    content: \"\\f583\";\n  }\n  \n  .bi-square::before {\n    content: \"\\f584\";\n  }\n  \n  .bi-stack::before {\n    content: \"\\f585\";\n  }\n  \n  .bi-star-fill::before {\n    content: \"\\f586\";\n  }\n  \n  .bi-star-half::before {\n    content: \"\\f587\";\n  }\n  \n  .bi-star::before {\n    content: \"\\f588\";\n  }\n  \n  .bi-stars::before {\n    content: \"\\f589\";\n  }\n  \n  .bi-stickies-fill::before {\n    content: \"\\f58a\";\n  }\n  \n  .bi-stickies::before {\n    content: \"\\f58b\";\n  }\n  \n  .bi-sticky-fill::before {\n    content: \"\\f58c\";\n  }\n  \n  .bi-sticky::before {\n    content: \"\\f58d\";\n  }\n  \n  .bi-stop-btn-fill::before {\n    content: \"\\f58e\";\n  }\n  \n  .bi-stop-btn::before {\n    content: \"\\f58f\";\n  }\n  \n  .bi-stop-circle-fill::before {\n    content: \"\\f590\";\n  }\n  \n  .bi-stop-circle::before {\n    content: \"\\f591\";\n  }\n  \n  .bi-stop-fill::before {\n    content: \"\\f592\";\n  }\n  \n  .bi-stop::before {\n    content: \"\\f593\";\n  }\n  \n  .bi-stoplights-fill::before {\n    content: \"\\f594\";\n  }\n  \n  .bi-stoplights::before {\n    content: \"\\f595\";\n  }\n  \n  .bi-stopwatch-fill::before {\n    content: \"\\f596\";\n  }\n  \n  .bi-stopwatch::before {\n    content: \"\\f597\";\n  }\n  \n  .bi-subtract::before {\n    content: \"\\f598\";\n  }\n  \n  .bi-suit-club-fill::before {\n    content: \"\\f599\";\n  }\n  \n  .bi-suit-club::before {\n    content: \"\\f59a\";\n  }\n  \n  .bi-suit-diamond-fill::before {\n    content: \"\\f59b\";\n  }\n  \n  .bi-suit-diamond::before {\n    content: \"\\f59c\";\n  }\n  \n  .bi-suit-heart-fill::before {\n    content: \"\\f59d\";\n  }\n  \n  .bi-suit-heart::before {\n    content: \"\\f59e\";\n  }\n  \n  .bi-suit-spade-fill::before {\n    content: \"\\f59f\";\n  }\n  \n  .bi-suit-spade::before {\n    content: \"\\f5a0\";\n  }\n  \n  .bi-sun-fill::before {\n    content: \"\\f5a1\";\n  }\n  \n  .bi-sun::before {\n    content: \"\\f5a2\";\n  }\n  \n  .bi-sunglasses::before {\n    content: \"\\f5a3\";\n  }\n  \n  .bi-sunrise-fill::before {\n    content: \"\\f5a4\";\n  }\n  \n  .bi-sunrise::before {\n    content: \"\\f5a5\";\n  }\n  \n  .bi-sunset-fill::before {\n    content: \"\\f5a6\";\n  }\n  \n  .bi-sunset::before {\n    content: \"\\f5a7\";\n  }\n  \n  .bi-symmetry-horizontal::before {\n    content: \"\\f5a8\";\n  }\n  \n  .bi-symmetry-vertical::before {\n    content: \"\\f5a9\";\n  }\n  \n  .bi-table::before {\n    content: \"\\f5aa\";\n  }\n  \n  .bi-tablet-fill::before {\n    content: \"\\f5ab\";\n  }\n  \n  .bi-tablet-landscape-fill::before {\n    content: \"\\f5ac\";\n  }\n  \n  .bi-tablet-landscape::before {\n    content: \"\\f5ad\";\n  }\n  \n  .bi-tablet::before {\n    content: \"\\f5ae\";\n  }\n  \n  .bi-tag-fill::before {\n    content: \"\\f5af\";\n  }\n  \n  .bi-tag::before {\n    content: \"\\f5b0\";\n  }\n  \n  .bi-tags-fill::before {\n    content: \"\\f5b1\";\n  }\n  \n  .bi-tags::before {\n    content: \"\\f5b2\";\n  }\n  \n  .bi-telegram::before {\n    content: \"\\f5b3\";\n  }\n  \n  .bi-telephone-fill::before {\n    content: \"\\f5b4\";\n  }\n  \n  .bi-telephone-forward-fill::before {\n    content: \"\\f5b5\";\n  }\n  \n  .bi-telephone-forward::before {\n    content: \"\\f5b6\";\n  }\n  \n  .bi-telephone-inbound-fill::before {\n    content: \"\\f5b7\";\n  }\n  \n  .bi-telephone-inbound::before {\n    content: \"\\f5b8\";\n  }\n  \n  .bi-telephone-minus-fill::before {\n    content: \"\\f5b9\";\n  }\n  \n  .bi-telephone-minus::before {\n    content: \"\\f5ba\";\n  }\n  \n  .bi-telephone-outbound-fill::before {\n    content: \"\\f5bb\";\n  }\n  \n  .bi-telephone-outbound::before {\n    content: \"\\f5bc\";\n  }\n  \n  .bi-telephone-plus-fill::before {\n    content: \"\\f5bd\";\n  }\n  \n  .bi-telephone-plus::before {\n    content: \"\\f5be\";\n  }\n  \n  .bi-telephone-x-fill::before {\n    content: \"\\f5bf\";\n  }\n  \n  .bi-telephone-x::before {\n    content: \"\\f5c0\";\n  }\n  \n  .bi-telephone::before {\n    content: \"\\f5c1\";\n  }\n  \n  .bi-terminal-fill::before {\n    content: \"\\f5c2\";\n  }\n  \n  .bi-terminal::before {\n    content: \"\\f5c3\";\n  }\n  \n  .bi-text-center::before {\n    content: \"\\f5c4\";\n  }\n  \n  .bi-text-indent-left::before {\n    content: \"\\f5c5\";\n  }\n  \n  .bi-text-indent-right::before {\n    content: \"\\f5c6\";\n  }\n  \n  .bi-text-left::before {\n    content: \"\\f5c7\";\n  }\n  \n  .bi-text-paragraph::before {\n    content: \"\\f5c8\";\n  }\n  \n  .bi-text-right::before {\n    content: \"\\f5c9\";\n  }\n  \n  .bi-textarea-resize::before {\n    content: \"\\f5ca\";\n  }\n  \n  .bi-textarea-t::before {\n    content: \"\\f5cb\";\n  }\n  \n  .bi-textarea::before {\n    content: \"\\f5cc\";\n  }\n  \n  .bi-thermometer-half::before {\n    content: \"\\f5cd\";\n  }\n  \n  .bi-thermometer-high::before {\n    content: \"\\f5ce\";\n  }\n  \n  .bi-thermometer-low::before {\n    content: \"\\f5cf\";\n  }\n  \n  .bi-thermometer-snow::before {\n    content: \"\\f5d0\";\n  }\n  \n  .bi-thermometer-sun::before {\n    content: \"\\f5d1\";\n  }\n  \n  .bi-thermometer::before {\n    content: \"\\f5d2\";\n  }\n  \n  .bi-three-dots-vertical::before {\n    content: \"\\f5d3\";\n  }\n  \n  .bi-three-dots::before {\n    content: \"\\f5d4\";\n  }\n  \n  .bi-toggle-off::before {\n    content: \"\\f5d5\";\n  }\n  \n  .bi-toggle-on::before {\n    content: \"\\f5d6\";\n  }\n  \n  .bi-toggle2-off::before {\n    content: \"\\f5d7\";\n  }\n  \n  .bi-toggle2-on::before {\n    content: \"\\f5d8\";\n  }\n  \n  .bi-toggles::before {\n    content: \"\\f5d9\";\n  }\n  \n  .bi-toggles2::before {\n    content: \"\\f5da\";\n  }\n  \n  .bi-tools::before {\n    content: \"\\f5db\";\n  }\n  \n  .bi-tornado::before {\n    content: \"\\f5dc\";\n  }\n  \n  .bi-trash-fill::before {\n    content: \"\\f5dd\";\n  }\n  \n  .bi-trash::before {\n    content: \"\\f5de\";\n  }\n  \n  .bi-trash2-fill::before {\n    content: \"\\f5df\";\n  }\n  \n  .bi-trash2::before {\n    content: \"\\f5e0\";\n  }\n  \n  .bi-tree-fill::before {\n    content: \"\\f5e1\";\n  }\n  \n  .bi-tree::before {\n    content: \"\\f5e2\";\n  }\n  \n  .bi-triangle-fill::before {\n    content: \"\\f5e3\";\n  }\n  \n  .bi-triangle-half::before {\n    content: \"\\f5e4\";\n  }\n  \n  .bi-triangle::before {\n    content: \"\\f5e5\";\n  }\n  \n  .bi-trophy-fill::before {\n    content: \"\\f5e6\";\n  }\n  \n  .bi-trophy::before {\n    content: \"\\f5e7\";\n  }\n  \n  .bi-tropical-storm::before {\n    content: \"\\f5e8\";\n  }\n  \n  .bi-truck-flatbed::before {\n    content: \"\\f5e9\";\n  }\n  \n  .bi-truck::before {\n    content: \"\\f5ea\";\n  }\n  \n  .bi-tsunami::before {\n    content: \"\\f5eb\";\n  }\n  \n  .bi-tv-fill::before {\n    content: \"\\f5ec\";\n  }\n  \n  .bi-tv::before {\n    content: \"\\f5ed\";\n  }\n  \n  .bi-twitch::before {\n    content: \"\\f5ee\";\n  }\n  \n  .bi-twitter::before {\n    content: \"\\f5ef\";\n  }\n  \n  .bi-type-bold::before {\n    content: \"\\f5f0\";\n  }\n  \n  .bi-type-h1::before {\n    content: \"\\f5f1\";\n  }\n  \n  .bi-type-h2::before {\n    content: \"\\f5f2\";\n  }\n  \n  .bi-type-h3::before {\n    content: \"\\f5f3\";\n  }\n  \n  .bi-type-italic::before {\n    content: \"\\f5f4\";\n  }\n  \n  .bi-type-strikethrough::before {\n    content: \"\\f5f5\";\n  }\n  \n  .bi-type-underline::before {\n    content: \"\\f5f6\";\n  }\n  \n  .bi-type::before {\n    content: \"\\f5f7\";\n  }\n  \n  .bi-ui-checks-grid::before {\n    content: \"\\f5f8\";\n  }\n  \n  .bi-ui-checks::before {\n    content: \"\\f5f9\";\n  }\n  \n  .bi-ui-radios-grid::before {\n    content: \"\\f5fa\";\n  }\n  \n  .bi-ui-radios::before {\n    content: \"\\f5fb\";\n  }\n  \n  .bi-umbrella-fill::before {\n    content: \"\\f5fc\";\n  }\n  \n  .bi-umbrella::before {\n    content: \"\\f5fd\";\n  }\n  \n  .bi-union::before {\n    content: \"\\f5fe\";\n  }\n  \n  .bi-unlock-fill::before {\n    content: \"\\f5ff\";\n  }\n  \n  .bi-unlock::before {\n    content: \"\\f600\";\n  }\n  \n  .bi-upc-scan::before {\n    content: \"\\f601\";\n  }\n  \n  .bi-upc::before {\n    content: \"\\f602\";\n  }\n  \n  .bi-upload::before {\n    content: \"\\f603\";\n  }\n  \n  .bi-vector-pen::before {\n    content: \"\\f604\";\n  }\n  \n  .bi-view-list::before {\n    content: \"\\f605\";\n  }\n  \n  .bi-view-stacked::before {\n    content: \"\\f606\";\n  }\n  \n  .bi-vinyl-fill::before {\n    content: \"\\f607\";\n  }\n  \n  .bi-vinyl::before {\n    content: \"\\f608\";\n  }\n  \n  .bi-voicemail::before {\n    content: \"\\f609\";\n  }\n  \n  .bi-volume-down-fill::before {\n    content: \"\\f60a\";\n  }\n  \n  .bi-volume-down::before {\n    content: \"\\f60b\";\n  }\n  \n  .bi-volume-mute-fill::before {\n    content: \"\\f60c\";\n  }\n  \n  .bi-volume-mute::before {\n    content: \"\\f60d\";\n  }\n  \n  .bi-volume-off-fill::before {\n    content: \"\\f60e\";\n  }\n  \n  .bi-volume-off::before {\n    content: \"\\f60f\";\n  }\n  \n  .bi-volume-up-fill::before {\n    content: \"\\f610\";\n  }\n  \n  .bi-volume-up::before {\n    content: \"\\f611\";\n  }\n  \n  .bi-vr::before {\n    content: \"\\f612\";\n  }\n  \n  .bi-wallet-fill::before {\n    content: \"\\f613\";\n  }\n  \n  .bi-wallet::before {\n    content: \"\\f614\";\n  }\n  \n  .bi-wallet2::before {\n    content: \"\\f615\";\n  }\n  \n  .bi-watch::before {\n    content: \"\\f616\";\n  }\n  \n  .bi-water::before {\n    content: \"\\f617\";\n  }\n  \n  .bi-whatsapp::before {\n    content: \"\\f618\";\n  }\n  \n  .bi-wifi-1::before {\n    content: \"\\f619\";\n  }\n  \n  .bi-wifi-2::before {\n    content: \"\\f61a\";\n  }\n  \n  .bi-wifi-off::before {\n    content: \"\\f61b\";\n  }\n  \n  .bi-wifi::before {\n    content: \"\\f61c\";\n  }\n  \n  .bi-wind::before {\n    content: \"\\f61d\";\n  }\n  \n  .bi-window-dock::before {\n    content: \"\\f61e\";\n  }\n  \n  .bi-window-sidebar::before {\n    content: \"\\f61f\";\n  }\n  \n  .bi-window::before {\n    content: \"\\f620\";\n  }\n  \n  .bi-wrench::before {\n    content: \"\\f621\";\n  }\n  \n  .bi-x-circle-fill::before {\n    content: \"\\f622\";\n  }\n  \n  .bi-x-circle::before {\n    content: \"\\f623\";\n  }\n  \n  .bi-x-diamond-fill::before {\n    content: \"\\f624\";\n  }\n  \n  .bi-x-diamond::before {\n    content: \"\\f625\";\n  }\n  \n  .bi-x-octagon-fill::before {\n    content: \"\\f626\";\n  }\n  \n  .bi-x-octagon::before {\n    content: \"\\f627\";\n  }\n  \n  .bi-x-square-fill::before {\n    content: \"\\f628\";\n  }\n  \n  .bi-x-square::before {\n    content: \"\\f629\";\n  }\n  \n  .bi-x::before {\n    content: \"\\f62a\";\n  }\n  \n  .bi-youtube::before {\n    content: \"\\f62b\";\n  }\n  \n  .bi-zoom-in::before {\n    content: \"\\f62c\";\n  }\n  \n  .bi-zoom-out::before {\n    content: \"\\f62d\";\n  }\n  \n  .bi-bank::before {\n    content: \"\\f62e\";\n  }\n  \n  .bi-bank2::before {\n    content: \"\\f62f\";\n  }\n  \n  .bi-bell-slash-fill::before {\n    content: \"\\f630\";\n  }\n  \n  .bi-bell-slash::before {\n    content: \"\\f631\";\n  }\n  \n  .bi-cash-coin::before {\n    content: \"\\f632\";\n  }\n  \n  .bi-check-lg::before {\n    content: \"\\f633\";\n  }\n  \n  .bi-coin::before {\n    content: \"\\f634\";\n  }\n  \n  .bi-currency-bitcoin::before {\n    content: \"\\f635\";\n  }\n  \n  .bi-currency-dollar::before {\n    content: \"\\f636\";\n  }\n  \n  .bi-currency-euro::before {\n    content: \"\\f637\";\n  }\n  \n  .bi-currency-exchange::before {\n    content: \"\\f638\";\n  }\n  \n  .bi-currency-pound::before {\n    content: \"\\f639\";\n  }\n  \n  .bi-currency-yen::before {\n    content: \"\\f63a\";\n  }\n  \n  .bi-dash-lg::before {\n    content: \"\\f63b\";\n  }\n  \n  .bi-exclamation-lg::before {\n    content: \"\\f63c\";\n  }\n  \n  .bi-file-earmark-pdf-fill::before {\n    content: \"\\f63d\";\n  }\n  \n  .bi-file-earmark-pdf::before {\n    content: \"\\f63e\";\n  }\n  \n  .bi-file-pdf-fill::before {\n    content: \"\\f63f\";\n  }\n  \n  .bi-file-pdf::before {\n    content: \"\\f640\";\n  }\n  \n  .bi-gender-ambiguous::before {\n    content: \"\\f641\";\n  }\n  \n  .bi-gender-female::before {\n    content: \"\\f642\";\n  }\n  \n  .bi-gender-male::before {\n    content: \"\\f643\";\n  }\n  \n  .bi-gender-trans::before {\n    content: \"\\f644\";\n  }\n  \n  .bi-headset-vr::before {\n    content: \"\\f645\";\n  }\n  \n  .bi-info-lg::before {\n    content: \"\\f646\";\n  }\n  \n  .bi-mastodon::before {\n    content: \"\\f647\";\n  }\n  \n  .bi-messenger::before {\n    content: \"\\f648\";\n  }\n  \n  .bi-piggy-bank-fill::before {\n    content: \"\\f649\";\n  }\n  \n  .bi-piggy-bank::before {\n    content: \"\\f64a\";\n  }\n  \n  .bi-pin-map-fill::before {\n    content: \"\\f64b\";\n  }\n  \n  .bi-pin-map::before {\n    content: \"\\f64c\";\n  }\n  \n  .bi-plus-lg::before {\n    content: \"\\f64d\";\n  }\n  \n  .bi-question-lg::before {\n    content: \"\\f64e\";\n  }\n  \n  .bi-recycle::before {\n    content: \"\\f64f\";\n  }\n  \n  .bi-reddit::before {\n    content: \"\\f650\";\n  }\n  \n  .bi-safe-fill::before {\n    content: \"\\f651\";\n  }\n  \n  .bi-safe2-fill::before {\n    content: \"\\f652\";\n  }\n  \n  .bi-safe2::before {\n    content: \"\\f653\";\n  }\n  \n  .bi-sd-card-fill::before {\n    content: \"\\f654\";\n  }\n  \n  .bi-sd-card::before {\n    content: \"\\f655\";\n  }\n  \n  .bi-skype::before {\n    content: \"\\f656\";\n  }\n  \n  .bi-slash-lg::before {\n    content: \"\\f657\";\n  }\n  \n  .bi-translate::before {\n    content: \"\\f658\";\n  }\n  \n  .bi-x-lg::before {\n    content: \"\\f659\";\n  }\n  \n  .bi-safe::before {\n    content: \"\\f65a\";\n  }\n  \n  .bi-apple::before {\n    content: \"\\f65b\";\n  }\n  \n  .bi-microsoft::before {\n    content: \"\\f65d\";\n  }\n  \n  .bi-windows::before {\n    content: \"\\f65e\";\n  }\n  \n  .bi-behance::before {\n    content: \"\\f65c\";\n  }\n  \n  .bi-dribbble::before {\n    content: \"\\f65f\";\n  }\n  \n  .bi-line::before {\n    content: \"\\f660\";\n  }\n  \n  .bi-medium::before {\n    content: \"\\f661\";\n  }\n  \n  .bi-paypal::before {\n    content: \"\\f662\";\n  }\n  \n  .bi-pinterest::before {\n    content: \"\\f663\";\n  }\n  \n  .bi-signal::before {\n    content: \"\\f664\";\n  }\n  \n  .bi-snapchat::before {\n    content: \"\\f665\";\n  }\n  \n  .bi-spotify::before {\n    content: \"\\f666\";\n  }\n  \n  .bi-stack-overflow::before {\n    content: \"\\f667\";\n  }\n  \n  .bi-strava::before {\n    content: \"\\f668\";\n  }\n  \n  .bi-wordpress::before {\n    content: \"\\f669\";\n  }\n  \n  .bi-vimeo::before {\n    content: \"\\f66a\";\n  }\n  \n  .bi-activity::before {\n    content: \"\\f66b\";\n  }\n  \n  .bi-easel2-fill::before {\n    content: \"\\f66c\";\n  }\n  \n  .bi-easel2::before {\n    content: \"\\f66d\";\n  }\n  \n  .bi-easel3-fill::before {\n    content: \"\\f66e\";\n  }\n  \n  .bi-easel3::before {\n    content: \"\\f66f\";\n  }\n  \n  .bi-fan::before {\n    content: \"\\f670\";\n  }\n  \n  .bi-fingerprint::before {\n    content: \"\\f671\";\n  }\n  \n  .bi-graph-down-arrow::before {\n    content: \"\\f672\";\n  }\n  \n  .bi-graph-up-arrow::before {\n    content: \"\\f673\";\n  }\n  \n  .bi-hypnotize::before {\n    content: \"\\f674\";\n  }\n  \n  .bi-magic::before {\n    content: \"\\f675\";\n  }\n  \n  .bi-person-rolodex::before {\n    content: \"\\f676\";\n  }\n  \n  .bi-person-video::before {\n    content: \"\\f677\";\n  }\n  \n  .bi-person-video2::before {\n    content: \"\\f678\";\n  }\n  \n  .bi-person-video3::before {\n    content: \"\\f679\";\n  }\n  \n  .bi-person-workspace::before {\n    content: \"\\f67a\";\n  }\n  \n  .bi-radioactive::before {\n    content: \"\\f67b\";\n  }\n  \n  .bi-webcam-fill::before {\n    content: \"\\f67c\";\n  }\n  \n  .bi-webcam::before {\n    content: \"\\f67d\";\n  }\n  \n  .bi-yin-yang::before {\n    content: \"\\f67e\";\n  }\n  \n  .bi-bandaid-fill::before {\n    content: \"\\f680\";\n  }\n  \n  .bi-bandaid::before {\n    content: \"\\f681\";\n  }\n  \n  .bi-bluetooth::before {\n    content: \"\\f682\";\n  }\n  \n  .bi-body-text::before {\n    content: \"\\f683\";\n  }\n  \n  .bi-boombox::before {\n    content: \"\\f684\";\n  }\n  \n  .bi-boxes::before {\n    content: \"\\f685\";\n  }\n  \n  .bi-dpad-fill::before {\n    content: \"\\f686\";\n  }\n  \n  .bi-dpad::before {\n    content: \"\\f687\";\n  }\n  \n  .bi-ear-fill::before {\n    content: \"\\f688\";\n  }\n  \n  .bi-ear::before {\n    content: \"\\f689\";\n  }\n  \n  .bi-envelope-check-1::before {\n    content: \"\\f68a\";\n  }\n  \n  .bi-envelope-check-fill::before {\n    content: \"\\f68b\";\n  }\n  \n  .bi-envelope-check::before {\n    content: \"\\f68c\";\n  }\n  \n  .bi-envelope-dash-1::before {\n    content: \"\\f68d\";\n  }\n  \n  .bi-envelope-dash-fill::before {\n    content: \"\\f68e\";\n  }\n  \n  .bi-envelope-dash::before {\n    content: \"\\f68f\";\n  }\n  \n  .bi-envelope-exclamation-1::before {\n    content: \"\\f690\";\n  }\n  \n  .bi-envelope-exclamation-fill::before {\n    content: \"\\f691\";\n  }\n  \n  .bi-envelope-exclamation::before {\n    content: \"\\f692\";\n  }\n  \n  .bi-envelope-plus-fill::before {\n    content: \"\\f693\";\n  }\n  \n  .bi-envelope-plus::before {\n    content: \"\\f694\";\n  }\n  \n  .bi-envelope-slash-1::before {\n    content: \"\\f695\";\n  }\n  \n  .bi-envelope-slash-fill::before {\n    content: \"\\f696\";\n  }\n  \n  .bi-envelope-slash::before {\n    content: \"\\f697\";\n  }\n  \n  .bi-envelope-x-1::before {\n    content: \"\\f698\";\n  }\n  \n  .bi-envelope-x-fill::before {\n    content: \"\\f699\";\n  }\n  \n  .bi-envelope-x::before {\n    content: \"\\f69a\";\n  }\n  \n  .bi-explicit-fill::before {\n    content: \"\\f69b\";\n  }\n  \n  .bi-explicit::before {\n    content: \"\\f69c\";\n  }\n  \n  .bi-git::before {\n    content: \"\\f69d\";\n  }\n  \n  .bi-infinity::before {\n    content: \"\\f69e\";\n  }\n  \n  .bi-list-columns-reverse::before {\n    content: \"\\f69f\";\n  }\n  \n  .bi-list-columns::before {\n    content: \"\\f6a0\";\n  }\n  \n  .bi-meta::before {\n    content: \"\\f6a1\";\n  }\n  \n  .bi-mortorboard-fill::before {\n    content: \"\\f6a2\";\n  }\n  \n  .bi-mortorboard::before {\n    content: \"\\f6a3\";\n  }\n  \n  .bi-nintendo-switch::before {\n    content: \"\\f6a4\";\n  }\n  \n  .bi-pc-display-horizontal::before {\n    content: \"\\f6a5\";\n  }\n  \n  .bi-pc-display::before {\n    content: \"\\f6a6\";\n  }\n  \n  .bi-pc-horizontal::before {\n    content: \"\\f6a7\";\n  }\n  \n  .bi-pc::before {\n    content: \"\\f6a8\";\n  }\n  \n  .bi-playstation::before {\n    content: \"\\f6a9\";\n  }\n  \n  .bi-plus-slash-minus::before {\n    content: \"\\f6aa\";\n  }\n  \n  .bi-projector-fill::before {\n    content: \"\\f6ab\";\n  }\n  \n  .bi-projector::before {\n    content: \"\\f6ac\";\n  }\n  \n  .bi-qr-code-scan::before {\n    content: \"\\f6ad\";\n  }\n  \n  .bi-qr-code::before {\n    content: \"\\f6ae\";\n  }\n  \n  .bi-quora::before {\n    content: \"\\f6af\";\n  }\n  \n  .bi-quote::before {\n    content: \"\\f6b0\";\n  }\n  \n  .bi-robot::before {\n    content: \"\\f6b1\";\n  }\n  \n  .bi-send-check-fill::before {\n    content: \"\\f6b2\";\n  }\n  \n  .bi-send-check::before {\n    content: \"\\f6b3\";\n  }\n  \n  .bi-send-dash-fill::before {\n    content: \"\\f6b4\";\n  }\n  \n  .bi-send-dash::before {\n    content: \"\\f6b5\";\n  }\n  \n  .bi-send-exclamation-1::before {\n    content: \"\\f6b6\";\n  }\n  \n  .bi-send-exclamation-fill::before {\n    content: \"\\f6b7\";\n  }\n  \n  .bi-send-exclamation::before {\n    content: \"\\f6b8\";\n  }\n  \n  .bi-send-fill::before {\n    content: \"\\f6b9\";\n  }\n  \n  .bi-send-plus-fill::before {\n    content: \"\\f6ba\";\n  }\n  \n  .bi-send-plus::before {\n    content: \"\\f6bb\";\n  }\n  \n  .bi-send-slash-fill::before {\n    content: \"\\f6bc\";\n  }\n  \n  .bi-send-slash::before {\n    content: \"\\f6bd\";\n  }\n  \n  .bi-send-x-fill::before {\n    content: \"\\f6be\";\n  }\n  \n  .bi-send-x::before {\n    content: \"\\f6bf\";\n  }\n  \n  .bi-send::before {\n    content: \"\\f6c0\";\n  }\n  \n  .bi-steam::before {\n    content: \"\\f6c1\";\n  }\n  \n  .bi-terminal-dash-1::before {\n    content: \"\\f6c2\";\n  }\n  \n  .bi-terminal-dash::before {\n    content: \"\\f6c3\";\n  }\n  \n  .bi-terminal-plus::before {\n    content: \"\\f6c4\";\n  }\n  \n  .bi-terminal-split::before {\n    content: \"\\f6c5\";\n  }\n  \n  .bi-ticket-detailed-fill::before {\n    content: \"\\f6c6\";\n  }\n  \n  .bi-ticket-detailed::before {\n    content: \"\\f6c7\";\n  }\n  \n  .bi-ticket-fill::before {\n    content: \"\\f6c8\";\n  }\n  \n  .bi-ticket-perforated-fill::before {\n    content: \"\\f6c9\";\n  }\n  \n  .bi-ticket-perforated::before {\n    content: \"\\f6ca\";\n  }\n  \n  .bi-ticket::before {\n    content: \"\\f6cb\";\n  }\n  \n  .bi-tiktok::before {\n    content: \"\\f6cc\";\n  }\n  \n  .bi-window-dash::before {\n    content: \"\\f6cd\";\n  }\n  \n  .bi-window-desktop::before {\n    content: \"\\f6ce\";\n  }\n  \n  .bi-window-fullscreen::before {\n    content: \"\\f6cf\";\n  }\n  \n  .bi-window-plus::before {\n    content: \"\\f6d0\";\n  }\n  \n  .bi-window-split::before {\n    content: \"\\f6d1\";\n  }\n  \n  .bi-window-stack::before {\n    content: \"\\f6d2\";\n  }\n  \n  .bi-window-x::before {\n    content: \"\\f6d3\";\n  }\n  \n  .bi-xbox::before {\n    content: \"\\f6d4\";\n  }\n  \n  .bi-ethernet::before {\n    content: \"\\f6d5\";\n  }\n  \n  .bi-hdmi-fill::before {\n    content: \"\\f6d6\";\n  }\n  \n  .bi-hdmi::before {\n    content: \"\\f6d7\";\n  }\n  \n  .bi-usb-c-fill::before {\n    content: \"\\f6d8\";\n  }\n  \n  .bi-usb-c::before {\n    content: \"\\f6d9\";\n  }\n  \n  .bi-usb-fill::before {\n    content: \"\\f6da\";\n  }\n  \n  .bi-usb-plug-fill::before {\n    content: \"\\f6db\";\n  }\n  \n  .bi-usb-plug::before {\n    content: \"\\f6dc\";\n  }\n  \n  .bi-usb-symbol::before {\n    content: \"\\f6dd\";\n  }\n  \n  .bi-usb::before {\n    content: \"\\f6de\";\n  }\n  \n  .bi-boombox-fill::before {\n    content: \"\\f6df\";\n  }\n  \n  .bi-displayport-1::before {\n    content: \"\\f6e0\";\n  }\n  \n  .bi-displayport::before {\n    content: \"\\f6e1\";\n  }\n  \n  .bi-gpu-card::before {\n    content: \"\\f6e2\";\n  }\n  \n  .bi-memory::before {\n    content: \"\\f6e3\";\n  }\n  \n  .bi-modem-fill::before {\n    content: \"\\f6e4\";\n  }\n  \n  .bi-modem::before {\n    content: \"\\f6e5\";\n  }\n  \n  .bi-motherboard-fill::before {\n    content: \"\\f6e6\";\n  }\n  \n  .bi-motherboard::before {\n    content: \"\\f6e7\";\n  }\n  \n  .bi-optical-audio-fill::before {\n    content: \"\\f6e8\";\n  }\n  \n  .bi-optical-audio::before {\n    content: \"\\f6e9\";\n  }\n  \n  .bi-pci-card::before {\n    content: \"\\f6ea\";\n  }\n  \n  .bi-router-fill::before {\n    content: \"\\f6eb\";\n  }\n  \n  .bi-router::before {\n    content: \"\\f6ec\";\n  }\n  \n  .bi-ssd-fill::before {\n    content: \"\\f6ed\";\n  }\n  \n  .bi-ssd::before {\n    content: \"\\f6ee\";\n  }\n  \n  .bi-thunderbolt-fill::before {\n    content: \"\\f6ef\";\n  }\n  \n  .bi-thunderbolt::before {\n    content: \"\\f6f0\";\n  }\n  \n  .bi-usb-drive-fill::before {\n    content: \"\\f6f1\";\n  }\n  \n  .bi-usb-drive::before {\n    content: \"\\f6f2\";\n  }\n  \n  .bi-usb-micro-fill::before {\n    content: \"\\f6f3\";\n  }\n  \n  .bi-usb-micro::before {\n    content: \"\\f6f4\";\n  }\n  \n  .bi-usb-mini-fill::before {\n    content: \"\\f6f5\";\n  }\n  \n  .bi-usb-mini::before {\n    content: \"\\f6f6\";\n  }\n  \n  .bi-cloud-haze2::before {\n    content: \"\\f6f7\";\n  }\n  \n  .bi-device-hdd-fill::before {\n    content: \"\\f6f8\";\n  }\n  \n  .bi-device-hdd::before {\n    content: \"\\f6f9\";\n  }\n  \n  .bi-device-ssd-fill::before {\n    content: \"\\f6fa\";\n  }\n  \n  .bi-device-ssd::before {\n    content: \"\\f6fb\";\n  }\n  \n  .bi-displayport-fill::before {\n    content: \"\\f6fc\";\n  }\n  \n  .bi-mortarboard-fill::before {\n    content: \"\\f6fd\";\n  }\n  \n  .bi-mortarboard::before {\n    content: \"\\f6fe\";\n  }\n  \n  .bi-terminal-x::before {\n    content: \"\\f6ff\";\n  }\n  \n  .bi-arrow-through-heart-fill::before {\n    content: \"\\f700\";\n  }\n  \n  .bi-arrow-through-heart::before {\n    content: \"\\f701\";\n  }\n  \n  .bi-badge-sd-fill::before {\n    content: \"\\f702\";\n  }\n  \n  .bi-badge-sd::before {\n    content: \"\\f703\";\n  }\n  \n  .bi-bag-heart-fill::before {\n    content: \"\\f704\";\n  }\n  \n  .bi-bag-heart::before {\n    content: \"\\f705\";\n  }\n  \n  .bi-balloon-fill::before {\n    content: \"\\f706\";\n  }\n  \n  .bi-balloon-heart-fill::before {\n    content: \"\\f707\";\n  }\n  \n  .bi-balloon-heart::before {\n    content: \"\\f708\";\n  }\n  \n  .bi-balloon::before {\n    content: \"\\f709\";\n  }\n  \n  .bi-box2-fill::before {\n    content: \"\\f70a\";\n  }\n  \n  .bi-box2-heart-fill::before {\n    content: \"\\f70b\";\n  }\n  \n  .bi-box2-heart::before {\n    content: \"\\f70c\";\n  }\n  \n  .bi-box2::before {\n    content: \"\\f70d\";\n  }\n  \n  .bi-braces-asterisk::before {\n    content: \"\\f70e\";\n  }\n  \n  .bi-calendar-heart-fill::before {\n    content: \"\\f70f\";\n  }\n  \n  .bi-calendar-heart::before {\n    content: \"\\f710\";\n  }\n  \n  .bi-calendar2-heart-fill::before {\n    content: \"\\f711\";\n  }\n  \n  .bi-calendar2-heart::before {\n    content: \"\\f712\";\n  }\n  \n  .bi-chat-heart-fill::before {\n    content: \"\\f713\";\n  }\n  \n  .bi-chat-heart::before {\n    content: \"\\f714\";\n  }\n  \n  .bi-chat-left-heart-fill::before {\n    content: \"\\f715\";\n  }\n  \n  .bi-chat-left-heart::before {\n    content: \"\\f716\";\n  }\n  \n  .bi-chat-right-heart-fill::before {\n    content: \"\\f717\";\n  }\n  \n  .bi-chat-right-heart::before {\n    content: \"\\f718\";\n  }\n  \n  .bi-chat-square-heart-fill::before {\n    content: \"\\f719\";\n  }\n  \n  .bi-chat-square-heart::before {\n    content: \"\\f71a\";\n  }\n  \n  .bi-clipboard-check-fill::before {\n    content: \"\\f71b\";\n  }\n  \n  .bi-clipboard-data-fill::before {\n    content: \"\\f71c\";\n  }\n  \n  .bi-clipboard-fill::before {\n    content: \"\\f71d\";\n  }\n  \n  .bi-clipboard-heart-fill::before {\n    content: \"\\f71e\";\n  }\n  \n  .bi-clipboard-heart::before {\n    content: \"\\f71f\";\n  }\n  \n  .bi-clipboard-minus-fill::before {\n    content: \"\\f720\";\n  }\n  \n  .bi-clipboard-plus-fill::before {\n    content: \"\\f721\";\n  }\n  \n  .bi-clipboard-pulse::before {\n    content: \"\\f722\";\n  }\n  \n  .bi-clipboard-x-fill::before {\n    content: \"\\f723\";\n  }\n  \n  .bi-clipboard2-check-fill::before {\n    content: \"\\f724\";\n  }\n  \n  .bi-clipboard2-check::before {\n    content: \"\\f725\";\n  }\n  \n  .bi-clipboard2-data-fill::before {\n    content: \"\\f726\";\n  }\n  \n  .bi-clipboard2-data::before {\n    content: \"\\f727\";\n  }\n  \n  .bi-clipboard2-fill::before {\n    content: \"\\f728\";\n  }\n  \n  .bi-clipboard2-heart-fill::before {\n    content: \"\\f729\";\n  }\n  \n  .bi-clipboard2-heart::before {\n    content: \"\\f72a\";\n  }\n  \n  .bi-clipboard2-minus-fill::before {\n    content: \"\\f72b\";\n  }\n  \n  .bi-clipboard2-minus::before {\n    content: \"\\f72c\";\n  }\n  \n  .bi-clipboard2-plus-fill::before {\n    content: \"\\f72d\";\n  }\n  \n  .bi-clipboard2-plus::before {\n    content: \"\\f72e\";\n  }\n  \n  .bi-clipboard2-pulse-fill::before {\n    content: \"\\f72f\";\n  }\n  \n  .bi-clipboard2-pulse::before {\n    content: \"\\f730\";\n  }\n  \n  .bi-clipboard2-x-fill::before {\n    content: \"\\f731\";\n  }\n  \n  .bi-clipboard2-x::before {\n    content: \"\\f732\";\n  }\n  \n  .bi-clipboard2::before {\n    content: \"\\f733\";\n  }\n  \n  .bi-emoji-kiss-fill::before {\n    content: \"\\f734\";\n  }\n  \n  .bi-emoji-kiss::before {\n    content: \"\\f735\";\n  }\n  \n  .bi-envelope-heart-fill::before {\n    content: \"\\f736\";\n  }\n  \n  .bi-envelope-heart::before {\n    content: \"\\f737\";\n  }\n  \n  .bi-envelope-open-heart-fill::before {\n    content: \"\\f738\";\n  }\n  \n  .bi-envelope-open-heart::before {\n    content: \"\\f739\";\n  }\n  \n  .bi-envelope-paper-fill::before {\n    content: \"\\f73a\";\n  }\n  \n  .bi-envelope-paper-heart-fill::before {\n    content: \"\\f73b\";\n  }\n  \n  .bi-envelope-paper-heart::before {\n    content: \"\\f73c\";\n  }\n  \n  .bi-envelope-paper::before {\n    content: \"\\f73d\";\n  }\n  \n  .bi-filetype-aac::before {\n    content: \"\\f73e\";\n  }\n  \n  .bi-filetype-ai::before {\n    content: \"\\f73f\";\n  }\n  \n  .bi-filetype-bmp::before {\n    content: \"\\f740\";\n  }\n  \n  .bi-filetype-cs::before {\n    content: \"\\f741\";\n  }\n  \n  .bi-filetype-css::before {\n    content: \"\\f742\";\n  }\n  \n  .bi-filetype-csv::before {\n    content: \"\\f743\";\n  }\n  \n  .bi-filetype-doc::before {\n    content: \"\\f744\";\n  }\n  \n  .bi-filetype-docx::before {\n    content: \"\\f745\";\n  }\n  \n  .bi-filetype-exe::before {\n    content: \"\\f746\";\n  }\n  \n  .bi-filetype-gif::before {\n    content: \"\\f747\";\n  }\n  \n  .bi-filetype-heic::before {\n    content: \"\\f748\";\n  }\n  \n  .bi-filetype-html::before {\n    content: \"\\f749\";\n  }\n  \n  .bi-filetype-java::before {\n    content: \"\\f74a\";\n  }\n  \n  .bi-filetype-jpg::before {\n    content: \"\\f74b\";\n  }\n  \n  .bi-filetype-js::before {\n    content: \"\\f74c\";\n  }\n  \n  .bi-filetype-jsx::before {\n    content: \"\\f74d\";\n  }\n  \n  .bi-filetype-key::before {\n    content: \"\\f74e\";\n  }\n  \n  .bi-filetype-m4p::before {\n    content: \"\\f74f\";\n  }\n  \n  .bi-filetype-md::before {\n    content: \"\\f750\";\n  }\n  \n  .bi-filetype-mdx::before {\n    content: \"\\f751\";\n  }\n  \n  .bi-filetype-mov::before {\n    content: \"\\f752\";\n  }\n  \n  .bi-filetype-mp3::before {\n    content: \"\\f753\";\n  }\n  \n  .bi-filetype-mp4::before {\n    content: \"\\f754\";\n  }\n  \n  .bi-filetype-otf::before {\n    content: \"\\f755\";\n  }\n  \n  .bi-filetype-pdf::before {\n    content: \"\\f756\";\n  }\n  \n  .bi-filetype-php::before {\n    content: \"\\f757\";\n  }\n  \n  .bi-filetype-png::before {\n    content: \"\\f758\";\n  }\n  \n  .bi-filetype-ppt-1::before {\n    content: \"\\f759\";\n  }\n  \n  .bi-filetype-ppt::before {\n    content: \"\\f75a\";\n  }\n  \n  .bi-filetype-psd::before {\n    content: \"\\f75b\";\n  }\n  \n  .bi-filetype-py::before {\n    content: \"\\f75c\";\n  }\n  \n  .bi-filetype-raw::before {\n    content: \"\\f75d\";\n  }\n  \n  .bi-filetype-rb::before {\n    content: \"\\f75e\";\n  }\n  \n  .bi-filetype-sass::before {\n    content: \"\\f75f\";\n  }\n  \n  .bi-filetype-scss::before {\n    content: \"\\f760\";\n  }\n  \n  .bi-filetype-sh::before {\n    content: \"\\f761\";\n  }\n  \n  .bi-filetype-svg::before {\n    content: \"\\f762\";\n  }\n  \n  .bi-filetype-tiff::before {\n    content: \"\\f763\";\n  }\n  \n  .bi-filetype-tsx::before {\n    content: \"\\f764\";\n  }\n  \n  .bi-filetype-ttf::before {\n    content: \"\\f765\";\n  }\n  \n  .bi-filetype-txt::before {\n    content: \"\\f766\";\n  }\n  \n  .bi-filetype-wav::before {\n    content: \"\\f767\";\n  }\n  \n  .bi-filetype-woff::before {\n    content: \"\\f768\";\n  }\n  \n  .bi-filetype-xls-1::before {\n    content: \"\\f769\";\n  }\n  \n  .bi-filetype-xls::before {\n    content: \"\\f76a\";\n  }\n  \n  .bi-filetype-xml::before {\n    content: \"\\f76b\";\n  }\n  \n  .bi-filetype-yml::before {\n    content: \"\\f76c\";\n  }\n  \n  .bi-heart-arrow::before {\n    content: \"\\f76d\";\n  }\n  \n  .bi-heart-pulse-fill::before {\n    content: \"\\f76e\";\n  }\n  \n  .bi-heart-pulse::before {\n    content: \"\\f76f\";\n  }\n  \n  .bi-heartbreak-fill::before {\n    content: \"\\f770\";\n  }\n  \n  .bi-heartbreak::before {\n    content: \"\\f771\";\n  }\n  \n  .bi-hearts::before {\n    content: \"\\f772\";\n  }\n  \n  .bi-hospital-fill::before {\n    content: \"\\f773\";\n  }\n  \n  .bi-hospital::before {\n    content: \"\\f774\";\n  }\n  \n  .bi-house-heart-fill::before {\n    content: \"\\f775\";\n  }\n  \n  .bi-house-heart::before {\n    content: \"\\f776\";\n  }\n  \n  .bi-incognito::before {\n    content: \"\\f777\";\n  }\n  \n  .bi-magnet-fill::before {\n    content: \"\\f778\";\n  }\n  \n  .bi-magnet::before {\n    content: \"\\f779\";\n  }\n  \n  .bi-person-heart::before {\n    content: \"\\f77a\";\n  }\n  \n  .bi-person-hearts::before {\n    content: \"\\f77b\";\n  }\n  \n  .bi-phone-flip::before {\n    content: \"\\f77c\";\n  }\n  \n  .bi-plugin::before {\n    content: \"\\f77d\";\n  }\n  \n  .bi-postage-fill::before {\n    content: \"\\f77e\";\n  }\n  \n  .bi-postage-heart-fill::before {\n    content: \"\\f77f\";\n  }\n  \n  .bi-postage-heart::before {\n    content: \"\\f780\";\n  }\n  \n  .bi-postage::before {\n    content: \"\\f781\";\n  }\n  \n  .bi-postcard-fill::before {\n    content: \"\\f782\";\n  }\n  \n  .bi-postcard-heart-fill::before {\n    content: \"\\f783\";\n  }\n  \n  .bi-postcard-heart::before {\n    content: \"\\f784\";\n  }\n  \n  .bi-postcard::before {\n    content: \"\\f785\";\n  }\n  \n  .bi-search-heart-fill::before {\n    content: \"\\f786\";\n  }\n  \n  .bi-search-heart::before {\n    content: \"\\f787\";\n  }\n  \n  .bi-sliders2-vertical::before {\n    content: \"\\f788\";\n  }\n  \n  .bi-sliders2::before {\n    content: \"\\f789\";\n  }\n  \n  .bi-trash3-fill::before {\n    content: \"\\f78a\";\n  }\n  \n  .bi-trash3::before {\n    content: \"\\f78b\";\n  }\n  \n  .bi-valentine::before {\n    content: \"\\f78c\";\n  }\n  \n  .bi-valentine2::before {\n    content: \"\\f78d\";\n  }\n  \n  .bi-wrench-adjustable-circle-fill::before {\n    content: \"\\f78e\";\n  }\n  \n  .bi-wrench-adjustable-circle::before {\n    content: \"\\f78f\";\n  }\n  \n  .bi-wrench-adjustable::before {\n    content: \"\\f790\";\n  }\n  \n  .bi-filetype-json::before {\n    content: \"\\f791\";\n  }\n  \n  .bi-filetype-pptx::before {\n    content: \"\\f792\";\n  }\n  \n  .bi-filetype-xlsx::before {\n    content: \"\\f793\";\n  }\n  \n  .bi-1-circle-1::before {\n    content: \"\\f794\";\n  }\n  \n  .bi-1-circle-fill-1::before {\n    content: \"\\f795\";\n  }\n  \n  .bi-1-circle-fill::before {\n    content: \"\\f796\";\n  }\n  \n  .bi-1-circle::before {\n    content: \"\\f797\";\n  }\n  \n  .bi-1-square-fill::before {\n    content: \"\\f798\";\n  }\n  \n  .bi-1-square::before {\n    content: \"\\f799\";\n  }\n  \n  .bi-2-circle-1::before {\n    content: \"\\f79a\";\n  }\n  \n  .bi-2-circle-fill-1::before {\n    content: \"\\f79b\";\n  }\n  \n  .bi-2-circle-fill::before {\n    content: \"\\f79c\";\n  }\n  \n  .bi-2-circle::before {\n    content: \"\\f79d\";\n  }\n  \n  .bi-2-square-fill::before {\n    content: \"\\f79e\";\n  }\n  \n  .bi-2-square::before {\n    content: \"\\f79f\";\n  }\n  \n  .bi-3-circle-1::before {\n    content: \"\\f7a0\";\n  }\n  \n  .bi-3-circle-fill-1::before {\n    content: \"\\f7a1\";\n  }\n  \n  .bi-3-circle-fill::before {\n    content: \"\\f7a2\";\n  }\n  \n  .bi-3-circle::before {\n    content: \"\\f7a3\";\n  }\n  \n  .bi-3-square-fill::before {\n    content: \"\\f7a4\";\n  }\n  \n  .bi-3-square::before {\n    content: \"\\f7a5\";\n  }\n  \n  .bi-4-circle-1::before {\n    content: \"\\f7a6\";\n  }\n  \n  .bi-4-circle-fill-1::before {\n    content: \"\\f7a7\";\n  }\n  \n  .bi-4-circle-fill::before {\n    content: \"\\f7a8\";\n  }\n  \n  .bi-4-circle::before {\n    content: \"\\f7a9\";\n  }\n  \n  .bi-4-square-fill::before {\n    content: \"\\f7aa\";\n  }\n  \n  .bi-4-square::before {\n    content: \"\\f7ab\";\n  }\n  \n  .bi-5-circle-1::before {\n    content: \"\\f7ac\";\n  }\n  \n  .bi-5-circle-fill-1::before {\n    content: \"\\f7ad\";\n  }\n  \n  .bi-5-circle-fill::before {\n    content: \"\\f7ae\";\n  }\n  \n  .bi-5-circle::before {\n    content: \"\\f7af\";\n  }\n  \n  .bi-5-square-fill::before {\n    content: \"\\f7b0\";\n  }\n  \n  .bi-5-square::before {\n    content: \"\\f7b1\";\n  }\n  \n  .bi-6-circle-1::before {\n    content: \"\\f7b2\";\n  }\n  \n  .bi-6-circle-fill-1::before {\n    content: \"\\f7b3\";\n  }\n  \n  .bi-6-circle-fill::before {\n    content: \"\\f7b4\";\n  }\n  \n  .bi-6-circle::before {\n    content: \"\\f7b5\";\n  }\n  \n  .bi-6-square-fill::before {\n    content: \"\\f7b6\";\n  }\n  \n  .bi-6-square::before {\n    content: \"\\f7b7\";\n  }\n  \n  .bi-7-circle-1::before {\n    content: \"\\f7b8\";\n  }\n  \n  .bi-7-circle-fill-1::before {\n    content: \"\\f7b9\";\n  }\n  \n  .bi-7-circle-fill::before {\n    content: \"\\f7ba\";\n  }\n  \n  .bi-7-circle::before {\n    content: \"\\f7bb\";\n  }\n  \n  .bi-7-square-fill::before {\n    content: \"\\f7bc\";\n  }\n  \n  .bi-7-square::before {\n    content: \"\\f7bd\";\n  }\n  \n  .bi-8-circle-1::before {\n    content: \"\\f7be\";\n  }\n  \n  .bi-8-circle-fill-1::before {\n    content: \"\\f7bf\";\n  }\n  \n  .bi-8-circle-fill::before {\n    content: \"\\f7c0\";\n  }\n  \n  .bi-8-circle::before {\n    content: \"\\f7c1\";\n  }\n  \n  .bi-8-square-fill::before {\n    content: \"\\f7c2\";\n  }\n  \n  .bi-8-square::before {\n    content: \"\\f7c3\";\n  }\n  \n  .bi-9-circle-1::before {\n    content: \"\\f7c4\";\n  }\n  \n  .bi-9-circle-fill-1::before {\n    content: \"\\f7c5\";\n  }\n  \n  .bi-9-circle-fill::before {\n    content: \"\\f7c6\";\n  }\n  \n  .bi-9-circle::before {\n    content: \"\\f7c7\";\n  }\n  \n  .bi-9-square-fill::before {\n    content: \"\\f7c8\";\n  }\n  \n  .bi-9-square::before {\n    content: \"\\f7c9\";\n  }\n  \n  .bi-airplane-engines-fill::before {\n    content: \"\\f7ca\";\n  }\n  \n  .bi-airplane-engines::before {\n    content: \"\\f7cb\";\n  }\n  \n  .bi-airplane-fill::before {\n    content: \"\\f7cc\";\n  }\n  \n  .bi-airplane::before {\n    content: \"\\f7cd\";\n  }\n  \n  .bi-alexa::before {\n    content: \"\\f7ce\";\n  }\n  \n  .bi-alipay::before {\n    content: \"\\f7cf\";\n  }\n  \n  .bi-android::before {\n    content: \"\\f7d0\";\n  }\n  \n  .bi-android2::before {\n    content: \"\\f7d1\";\n  }\n  \n  .bi-box-fill::before {\n    content: \"\\f7d2\";\n  }\n  \n  .bi-box-seam-fill::before {\n    content: \"\\f7d3\";\n  }\n  \n  .bi-browser-chrome::before {\n    content: \"\\f7d4\";\n  }\n  \n  .bi-browser-edge::before {\n    content: \"\\f7d5\";\n  }\n  \n  .bi-browser-firefox::before {\n    content: \"\\f7d6\";\n  }\n  \n  .bi-browser-safari::before {\n    content: \"\\f7d7\";\n  }\n  \n  .bi-c-circle-1::before {\n    content: \"\\f7d8\";\n  }\n  \n  .bi-c-circle-fill-1::before {\n    content: \"\\f7d9\";\n  }\n  \n  .bi-c-circle-fill::before {\n    content: \"\\f7da\";\n  }\n  \n  .bi-c-circle::before {\n    content: \"\\f7db\";\n  }\n  \n  .bi-c-square-fill::before {\n    content: \"\\f7dc\";\n  }\n  \n  .bi-c-square::before {\n    content: \"\\f7dd\";\n  }\n  \n  .bi-capsule-pill::before {\n    content: \"\\f7de\";\n  }\n  \n  .bi-capsule::before {\n    content: \"\\f7df\";\n  }\n  \n  .bi-car-front-fill::before {\n    content: \"\\f7e0\";\n  }\n  \n  .bi-car-front::before {\n    content: \"\\f7e1\";\n  }\n  \n  .bi-cassette-fill::before {\n    content: \"\\f7e2\";\n  }\n  \n  .bi-cassette::before {\n    content: \"\\f7e3\";\n  }\n  \n  .bi-cc-circle-1::before {\n    content: \"\\f7e4\";\n  }\n  \n  .bi-cc-circle-fill-1::before {\n    content: \"\\f7e5\";\n  }\n  \n  .bi-cc-circle-fill::before {\n    content: \"\\f7e6\";\n  }\n  \n  .bi-cc-circle::before {\n    content: \"\\f7e7\";\n  }\n  \n  .bi-cc-square-fill::before {\n    content: \"\\f7e8\";\n  }\n  \n  .bi-cc-square::before {\n    content: \"\\f7e9\";\n  }\n  \n  .bi-cup-hot-fill::before {\n    content: \"\\f7ea\";\n  }\n  \n  .bi-cup-hot::before {\n    content: \"\\f7eb\";\n  }\n  \n  .bi-currency-rupee::before {\n    content: \"\\f7ec\";\n  }\n  \n  .bi-dropbox::before {\n    content: \"\\f7ed\";\n  }\n  \n  .bi-escape::before {\n    content: \"\\f7ee\";\n  }\n  \n  .bi-fast-forward-btn-fill::before {\n    content: \"\\f7ef\";\n  }\n  \n  .bi-fast-forward-btn::before {\n    content: \"\\f7f0\";\n  }\n  \n  .bi-fast-forward-circle-fill::before {\n    content: \"\\f7f1\";\n  }\n  \n  .bi-fast-forward-circle::before {\n    content: \"\\f7f2\";\n  }\n  \n  .bi-fast-forward-fill::before {\n    content: \"\\f7f3\";\n  }\n  \n  .bi-fast-forward::before {\n    content: \"\\f7f4\";\n  }\n  \n  .bi-filetype-sql::before {\n    content: \"\\f7f5\";\n  }\n  \n  .bi-fire::before {\n    content: \"\\f7f6\";\n  }\n  \n  .bi-google-play::before {\n    content: \"\\f7f7\";\n  }\n  \n  .bi-h-circle-1::before {\n    content: \"\\f7f8\";\n  }\n  \n  .bi-h-circle-fill-1::before {\n    content: \"\\f7f9\";\n  }\n  \n  .bi-h-circle-fill::before {\n    content: \"\\f7fa\";\n  }\n  \n  .bi-h-circle::before {\n    content: \"\\f7fb\";\n  }\n  \n  .bi-h-square-fill::before {\n    content: \"\\f7fc\";\n  }\n  \n  .bi-h-square::before {\n    content: \"\\f7fd\";\n  }\n  \n  .bi-indent::before {\n    content: \"\\f7fe\";\n  }\n  \n  .bi-lungs-fill::before {\n    content: \"\\f7ff\";\n  }\n  \n  .bi-lungs::before {\n    content: \"\\f800\";\n  }\n  \n  .bi-microsoft-teams::before {\n    content: \"\\f801\";\n  }\n  \n  .bi-p-circle-1::before {\n    content: \"\\f802\";\n  }\n  \n  .bi-p-circle-fill-1::before {\n    content: \"\\f803\";\n  }\n  \n  .bi-p-circle-fill::before {\n    content: \"\\f804\";\n  }\n  \n  .bi-p-circle::before {\n    content: \"\\f805\";\n  }\n  \n  .bi-p-square-fill::before {\n    content: \"\\f806\";\n  }\n  \n  .bi-p-square::before {\n    content: \"\\f807\";\n  }\n  \n  .bi-pass-fill::before {\n    content: \"\\f808\";\n  }\n  \n  .bi-pass::before {\n    content: \"\\f809\";\n  }\n  \n  .bi-prescription::before {\n    content: \"\\f80a\";\n  }\n  \n  .bi-prescription2::before {\n    content: \"\\f80b\";\n  }\n  \n  .bi-r-circle-1::before {\n    content: \"\\f80c\";\n  }\n  \n  .bi-r-circle-fill-1::before {\n    content: \"\\f80d\";\n  }\n  \n  .bi-r-circle-fill::before {\n    content: \"\\f80e\";\n  }\n  \n  .bi-r-circle::before {\n    content: \"\\f80f\";\n  }\n  \n  .bi-r-square-fill::before {\n    content: \"\\f810\";\n  }\n  \n  .bi-r-square::before {\n    content: \"\\f811\";\n  }\n  \n  .bi-repeat-1::before {\n    content: \"\\f812\";\n  }\n  \n  .bi-repeat::before {\n    content: \"\\f813\";\n  }\n  \n  .bi-rewind-btn-fill::before {\n    content: \"\\f814\";\n  }\n  \n  .bi-rewind-btn::before {\n    content: \"\\f815\";\n  }\n  \n  .bi-rewind-circle-fill::before {\n    content: \"\\f816\";\n  }\n  \n  .bi-rewind-circle::before {\n    content: \"\\f817\";\n  }\n  \n  .bi-rewind-fill::before {\n    content: \"\\f818\";\n  }\n  \n  .bi-rewind::before {\n    content: \"\\f819\";\n  }\n  \n  .bi-train-freight-front-fill::before {\n    content: \"\\f81a\";\n  }\n  \n  .bi-train-freight-front::before {\n    content: \"\\f81b\";\n  }\n  \n  .bi-train-front-fill::before {\n    content: \"\\f81c\";\n  }\n  \n  .bi-train-front::before {\n    content: \"\\f81d\";\n  }\n  \n  .bi-train-lightrail-front-fill::before {\n    content: \"\\f81e\";\n  }\n  \n  .bi-train-lightrail-front::before {\n    content: \"\\f81f\";\n  }\n  \n  .bi-truck-front-fill::before {\n    content: \"\\f820\";\n  }\n  \n  .bi-truck-front::before {\n    content: \"\\f821\";\n  }\n  \n  .bi-ubuntu::before {\n    content: \"\\f822\";\n  }\n  \n  .bi-unindent::before {\n    content: \"\\f823\";\n  }\n  \n  .bi-unity::before {\n    content: \"\\f824\";\n  }\n  \n  .bi-universal-access-circle::before {\n    content: \"\\f825\";\n  }\n  \n  .bi-universal-access::before {\n    content: \"\\f826\";\n  }\n  \n  .bi-virus::before {\n    content: \"\\f827\";\n  }\n  \n  .bi-virus2::before {\n    content: \"\\f828\";\n  }\n  \n  .bi-wechat::before {\n    content: \"\\f829\";\n  }\n  \n  .bi-yelp::before {\n    content: \"\\f82a\";\n  }\n  \n  .bi-sign-stop-fill::before {\n    content: \"\\f82b\";\n  }\n  \n  .bi-sign-stop-lights-fill::before {\n    content: \"\\f82c\";\n  }\n  \n  .bi-sign-stop-lights::before {\n    content: \"\\f82d\";\n  }\n  \n  .bi-sign-stop::before {\n    content: \"\\f82e\";\n  }\n  \n  .bi-sign-turn-left-fill::before {\n    content: \"\\f82f\";\n  }\n  \n  .bi-sign-turn-left::before {\n    content: \"\\f830\";\n  }\n  \n  .bi-sign-turn-right-fill::before {\n    content: \"\\f831\";\n  }\n  \n  .bi-sign-turn-right::before {\n    content: \"\\f832\";\n  }\n  \n  .bi-sign-turn-slight-left-fill::before {\n    content: \"\\f833\";\n  }\n  \n  .bi-sign-turn-slight-left::before {\n    content: \"\\f834\";\n  }\n  \n  .bi-sign-turn-slight-right-fill::before {\n    content: \"\\f835\";\n  }\n  \n  .bi-sign-turn-slight-right::before {\n    content: \"\\f836\";\n  }\n  \n  .bi-sign-yield-fill::before {\n    content: \"\\f837\";\n  }\n  \n  .bi-sign-yield::before {\n    content: \"\\f838\";\n  }\n  \n  .bi-ev-station-fill::before {\n    content: \"\\f839\";\n  }\n  \n  .bi-ev-station::before {\n    content: \"\\f83a\";\n  }\n  \n  .bi-fuel-pump-diesel-fill::before {\n    content: \"\\f83b\";\n  }\n  \n  .bi-fuel-pump-diesel::before {\n    content: \"\\f83c\";\n  }\n  \n  .bi-fuel-pump-fill::before {\n    content: \"\\f83d\";\n  }\n  \n  .bi-fuel-pump::before {\n    content: \"\\f83e\";\n  }", "/*\n################\n* === WP - Forms style  ===\n################\n*/\n@media (min-width: 768px){\n    .glowess-contact-form .w-50 {\n        width: 48.3%!important;\n        padding-bottom:15px !important;\n    }\n}\n\n\n.glowess-contact-form .wpforms-submit-container{\n    margin-top:0px !important;\n    padding-top:15px !important;\n\n}\n\n.glowess-contact-form .wpforms-submit-container button[type=submit] {\n    width:100%;\n    background-color: var(--wp--preset--color--primary) !important;\n    font-weight: 400;\n    padding: 20px 50px !important;\n    font-size: 15px;\n    text-align: center;\n    box-shadow: none;\n    border:transparent;\n    border-radius: 0px;\n    text-transform: uppercase;\n    color:var(--wp--preset--color--base) !important;\n    height:54px !important;\n    cursor: pointer;\n        &:focus:after {\n            border:none !important;\n        }\n    \n    &:hover {\n                background-color: var(--wp--preset--color--contrast) !important;\n                color: var(--wp--preset--color--base) !important;\n            }\n}\n\n@media (min-width: 768px){\n    .glowess-contact-form .wpforms-field:nth-child(2),   .glowess-contact-form .wpforms-field:nth-child(4) {\n        margin-left: 3.4%;\n    }\n}\n\n@media (min-width: 768px){\n    .glowess-contact-form .wpforms-field-container {\n        display: flex!important;\n        flex-wrap: wrap!important;\n    }\n}\n\n@media (min-width: 768px){\n    .glowess-contact-form .w-100 {\n        width: 100%!important;\n        padding-bottom:15px !important;         \n    }\n}\n\n.glowess-contact-form .wpforms-field-container textarea{\n min-height: 229px !important;\n }\n\n/* Glowess Sign-Up Form */\n\n.glowess-sign-up-form .wpforms-form {\n    position: relative !important;\n       }\n\n.glowess-sign-up-form .wpforms-field-container .wpforms-field {\n    padding: 0px !important;\n}\n\n.glowess-sign-up-form .wpforms-field-container input[type=text] {\n    min-height: 59.05px !important;\n    padding-left: 20px !important;\n    border: 1px solid transparent !important;\n    font-size: 15px;\n    font-weight: 300;\n    font-family: inherit;\n    line-height: 35px;\n    color: var(--wp--preset--color--contrast)!important;\n    background-color: var(--wp--preset--color--white)!important;\n       &:focus {\n        border: 1px solid transparent;\n        outline: 0;\n        box-shadow: unset;\n    }\n}\n\n.glowess-sign-up-form .wpforms-field-large {\n    padding: 5px !important;\n }\n\n@media (min-width:320px) {\n       .glowess-sign-up-form .wpforms-submit-container {\n                    padding: 5px !important;\n                    margin: 0px !important;\n       }        \n}\n\n.glowess-sign-up-form .wpforms-field-container input[type=text]::placeholder {\n      color: var(--wp--preset--color--contrast)!important;\n}\n\n.glowess-sign-up-form.wpforms-container .wpforms-form .wpforms-submit{\n       font-family: inherit;\n}\n\n.glowess-sign-up-form .wpforms-submit-container button[type=submit] {\n       background-color: #FFFFFF !important;\n       font-size: 0; /* Hide the text */\n       color: var(--wp--preset--color--contrast) !important;\n       border:transparent;\n       cursor:pointer;\n       margin-right: 3px !important;\n         &:hover,\n                &:focus {\n                 background: var(--wp--preset--color--white)!important;\n                 border: 1px solid transparent !important;\n                }\n}\n\n@media (min-width:320px) {\n       .glowess-sign-up-form .wpforms-submit-container{\n              text-align: center;\n              position: absolute !important;\n              right: 0px !important;\n              top: 5px !important;\n       }\n}\n\n/* Add an arrow icon using Unicode or Font Awesome */\n.glowess-sign-up-form .wpforms-submit::after {\n    content: \" \\F138\"; /* Unicode for right arrow */\n    font-size: 16px; /* Adjust size as needed */\n    font-family: \"bootstrap-icons\";\n    display: inline-block;\n     &:hover,\n                &:focus {\n                 background: var(--wp--preset--color--contrast)!important;\n                 border: 1px solid transparent !important;\n                }\n}\n\n/* Home V3 subscribe form */\n\n.footer-v3 .wpforms-field-container input[type=text] {\n    border-radius: 12px;\n    border: 1px solid  var(--wp--preset--color--gray-100) !important;\n}\n\n/* Home V4 newsletter subscribe form */\n.home-v4-newsletter .wpforms-field-container input[type=text] {\n    border-radius: 16px !important;\n    border: 1px solid  var(--wp--preset--color--gray-100) !important;\n}", "/*\n################\n* === Slick Slider Style ===\n################\n*/\n.slick-slider {\n    position: relative;\n    display: block !important;\n    box-sizing: border-box;\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    -ms-touch-action: pan-y;\n    touch-action: pan-y;\n    -webkit-tap-highlight-color: transparent;\n}\n\n.slick-list {\n    position: relative;\n    overflow: hidden;\n    display: block;\n    margin: 0;\n    padding: 0;\n\n    @media (min-width:600px) {\n        margin: 0 -20px;\n    }\n\n    &:focus {\n        outline: none;\n    }\n\n    &.dragging {\n        cursor: pointer;\n        cursor: hand;\n    }\n}\n.slick-slider .slick-track,\n.slick-slider .slick-list {\n    -webkit-transform: translate3d(0, 0, 0);\n    -moz-transform: translate3d(0, 0, 0);\n    -ms-transform: translate3d(0, 0, 0);\n    -o-transform: translate3d(0, 0, 0);\n    transform: translate3d(0, 0, 0);\n}\n\n.slick-track {\n    position: relative;\n    left: 0;\n    top: 0;\n    display: block;\n    margin-left: auto;\n    margin-right: auto;\n\n    &:before,\n    &:after {\n        content: \"\";\n        display: table;\n    }\n\n    &:after {\n        clear: both;\n    }\n\n    .slick-loading & {\n        visibility: hidden;\n    }\n}\n\n.slick-slide {\n    float: left;\n    height: auto;\n    min-height: 1px;\n\n    [dir=\"rtl\"] & {\n        float: right;\n    }\n\n    img {\n        display: block;\n    }\n\n    &.slick-loading img {\n        display: none;\n    }\n\n    &.dragging img {\n        pointer-events: none;\n    }\n\n    .slick-initialized & {\n        display: block;\n    }\n\n    .slick-loading & {\n        visibility: hidden;\n    }\n\n    .slick-vertical & {\n        display: block;\n        height: auto;\n        border: 1px solid transparent;\n    }\n}\n\n.slick-arrow.slick-hidden {\n    display: none;\n}\n\n.home-v1-hero,\n.v2-hero,\n.home-v4-hero {\n    .slick-prev, .slick-next {\n        top: auto;\n        transform: none;\n        margin: 0;\n        bottom: 60px;\n\n        &::before {\n            width: 42px;\n            height: 40px;\n        }\n    }\n\n    .slick-list {\n        margin: 0;\n\n        .slick-slide > div {\n            padding: 0;\n        }\n    }\n\n    .slick-next {\n        right: 60px;\n\n        &::before {\n            background-image: url('data:image/svg+xml,<svg width=\"42\" height=\"40\" viewBox=\"0 0 42 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 20H39.5675\" stroke=\"white\"/><path d=\"M31.2383 29.3714C33.565 24.2507 35.4083 22.101 40.0008 20.0142C35.3058 17.7033 33.4834 15.5457 31.2383 10.6289\" stroke=\"white\"/></svg>');\n        }\n    }\n\n    .slick-prev {\n        right: 122px;\n        left: auto;\n\n        &::before {\n            background-image: url('data:image/svg+xml,<svg width=\"42\" height=\"40\" viewBox=\"0 0 42 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M42 20H2.4325\" stroke=\"white\"/><path d=\"M10.7617 29.3714C8.43502 24.2507 6.59172 22.101 1.99922 20.0142C6.69422 17.7033 8.51662 15.5457 10.7617 10.6289\" stroke=\"white\"/></svg>');\n        }\n    }\n}\n\n\n// Default Variables\n\n// Slick icon entity codes outputs the following\n// \"\\2190\" outputs ascii character \"←\"\n// \"\\2192\" outputs ascii character \"→\"\n// \"\\2022\" outputs ascii character \"•\"\n\n$slick-font-path: \"./assets/fonts/bootstrap-icons/\" !default;\n$slick-font-family: \"bootstrap-icons\" !default;\n$slick-loader-path: \"./assets/images/\" !default;\n$slick-arrow-color: var(--wp--preset--color--gray-300) !default;\n$slick-dot-color: black !default;\n$slick-dot-color-active: $slick-dot-color !default;\n$slick-prev-character: \"\\F284\" !default;\n$slick-next-character: \"\\F285\" !default;\n$slick-dot-character: \"\\F309\" !default;\n$slick-dot-size: 6px !default;\n$slick-opacity-default: 1 !default;\n$slick-opacity-on-hover: 1 !default;\n$slick-opacity-not-active: 1 !default;\n\n@function slick-image-url($url) {\n    @if function-exists(image-url) {\n        @return image-url($url);\n    }\n    @else {\n        @return url($slick-loader-path + $url);\n    }\n}\n\n@function slick-font-url($url) {\n    @if function-exists(font-url) {\n        @return font-url($url);\n    }\n    @else {\n        @return url($slick-font-path + $url);\n    }\n}\n\n/* Slider */\n\n.slick-list {\n    .slick-loading & {\n        background: var(--wp--preset--color--base) slick-image-url(\"ajax-loader.gif\") center center no-repeat;\n    }\n}\n\n/* Icons */\n@if $slick-font-family == \"bootstrap-icons\" {\n    @font-face {\n        font-family: \"bootstrap-icons\";\n        src: url(\"assets/fonts/bootstrap-icons/bootstrap-icons.woff2?8d200481aa7f02a2d63a331fc782cfaf\") format(\"woff2\"), url(\"assets/fonts/bootstrap-icons/bootstrap-icons.woff?8d200481aa7f02a2d63a331fc782cfaf\") format(\"woff\");\n        font-weight: normal;\n        font-style: normal;\n    }\n}\n\n/* Arrows */\n\n.slick-prev,\n.slick-next {\n    position: absolute;\n    display: block;\n    line-height: 0px;\n    font-size: 0px;\n    cursor: pointer;\n    top: 50%;\n    -webkit-transform: translate(0, -50%);\n    -ms-transform: translate(0, -50%);\n    transform: translate(0, -50%);\n    padding: 0;\n    outline: none;\n    z-index: 1;\n    background-color: transparent;\n    border-width: 0;\n    \n    &:hover, &:focus {\n        outline: none;\n    }\n\n    &.slick-disabled {\n        opacity: 0.5;\n        pointer-events: none;\n    }\n    \n    &:before {\n        line-height: 1;\n        opacity: $slick-opacity-default;\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n        content: \" \";\n        width: 26px;\n        height: 25px;\n        display: block;\n        background-repeat: no-repeat;\n    }\n}\n\n.slick-prev {\n    left: -25px;\n\n    @media (min-width:1200px) {\n        left: 25px;\n    }\n\n    @media (min-width:1400px) {\n        left: -45px;\n    }\n\n    [dir=\"rtl\"] & {\n        left: auto;\n        right: -25px;\n\n        @media (min-width:1200px) {\n            right: 25px;\n        }\n    \n        @media (min-width:1400px) {\n            right: -45px;\n        }\n    }\n    \n    &:before {\n        //content: $slick-prev-character;\n        background-image: url('data:image/svg+xml,<svg width=\"24\" height=\"22\" viewBox=\"0 0 24 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M25 11H0.505832\" stroke=\"%23131C19\"/><path d=\"M5.662 16.8012C4.22166 13.6313 3.08057 12.3005 0.237598 11.0087C3.14403 9.57812 4.27218 8.24246 5.662 5.19873\" stroke=\"%23131C19\"/></svg>');\n\n        [dir=\"rtl\"] & {\n            background-image: url('data:image/svg+xml,<svg width=\"24\" height=\"22\" viewBox=\"0 0 24 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M-1 11H23.4942\" stroke=\"%23131C19\"/><path d=\"M18.338 16.8012C19.7783 13.6313 20.9194 12.3005 23.7624 11.0087C20.856 9.57812 19.7278 8.24246 18.338 5.19873\" stroke=\"%23131C19\"/></svg>');\n        }\n    }\n}\n\n.slick-next {\n    right: -25px;\n\n    @media (min-width:1200px) {\n        right: 25px;\n    }\n\n    @media (min-width:1400px) {\n        right: -45px;\n    }\n\n    [dir=\"rtl\"] & {\n        left: -25px;\n        right: auto;\n\n        @media (min-width:1200px) {\n            left: 25px;\n        }\n    \n        @media (min-width:1400px) {\n            left: -45px;\n        }\n    }\n\n    &:before {\n        //content: $slick-next-character;\n        background-image: url('data:image/svg+xml,<svg width=\"24\" height=\"22\" viewBox=\"0 0 24 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M-1 11H23.4942\" stroke=\"%23131C19\"/><path d=\"M18.338 16.8012C19.7783 13.6313 20.9194 12.3005 23.7624 11.0087C20.856 9.57812 19.7278 8.24246 18.338 5.19873\" stroke=\"%23131C19\"/></svg>');\n\n        [dir=\"rtl\"] & {\n            background-image: url('data:image/svg+xml,<svg width=\"24\" height=\"22\" viewBox=\"0 0 24 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M25 11H0.505832\" stroke=\"%23131C19\"/><path d=\"M5.662 16.8012C4.22166 13.6313 3.08057 12.3005 0.237598 11.0087C3.14403 9.57812 4.27218 8.24246 5.662 5.19873\" stroke=\"%23131C19\"/></svg>');\n        }\n    }\n}\n\n.slick-nav-wrap {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 23px;\n    margin-top: clamp(1.25rem, 2.237vw - 0.185rem, 2rem);\n\n    .slick-next {\n        order: 1;\n    }\n\n    .slick-prev,\n    .slick-next {\n        position: relative;\n        left: 0;\n        right: 0;\n        transform: none;\n    }\n}\n\n.slick-nav-group {\n    .slick-slider {\n        > .slick-prev,\n        > .slick-next,\n        > .slick-dots {\n            opacity: 0;\n        }\n    }\n}\n\n/* Dots */\n.slick-dotted {\n    text-align: center;\n}\n\n.slick-dots {\n    list-style: none;\n    display: inline-flex;\n    text-align: center;\n    padding: 0;\n    margin: 0;\n    background-color: var(--wp--preset--color--bg-3);\n    border-radius: 75px;\n\n    li {\n        display: inline-block;\n        cursor: pointer;\n\n        button {\n            border: 0;\n            display: block;\n            height: 4px;\n            width: 25px;\n            outline: none;\n            line-height: 0px;\n            font-size: 0px;\n            cursor: pointer;\n            padding: 0;  \n\n            &:hover, &:focus {\n                outline: none;\n            }\n        }\n\n        &.slick-active {            \n            button {\n                background-color: var(--wp--preset--color--secondary);\n                border-radius: 12px;\n            }\n        }\n    }\n}\n\n@keyframes fillRotateBorder {\n    0% {\n        border-top-color: transparent;\n        border-right-color: transparent;\n        border-bottom-color: transparent;\n        border-left-color: transparent;\n        transform: translate(-50%, -50%) rotate(0deg);\n    }\n    25% {\n        border-top-color: var(--wp--preset--color--base);\n        border-right-color: transparent;\n        border-bottom-color: transparent;\n        border-left-color: transparent;\n        transform: translate(-50%, -50%) rotate(90deg);\n    }\n    50% {\n        border-top-color: var(--wp--preset--color--base);\n        border-right-color: var(--wp--preset--color--base);\n        border-bottom-color: transparent;\n        border-left-color: transparent;\n        transform: translate(-50%, -50%) rotate(180deg);\n    }\n    75% {\n        border-top-color: var(--wp--preset--color--base);\n        border-right-color: var(--wp--preset--color--base);\n        border-bottom-color: var(--wp--preset--color--base);\n        border-left-color: transparent;\n        transform: translate(-50%, -50%) rotate(270deg);\n    }\n    100% {\n        border-top-color: var(--wp--preset--color--base);\n        border-right-color: var(--wp--preset--color--base);\n        border-bottom-color: var(--wp--preset--color--base);\n        border-left-color: var(--wp--preset--color--base);\n        transform: translate(-50%, -50%) rotate(360deg);\n    }\n}\n\n.dark-dot .slick-dots {\n    li button {\n        background-color: var(--wp--preset--color--contrast);\n    }\n\n    li.slick-active button:before {\n        border:1px solid var(--wp--preset--color--contrast);\n    }\n}\n\n.slick-dots-vertical {\n    .slick-dots {\n        bottom: 30px;\n        \n        @media (min-width:1024px) {\n            display: flex;\n            flex-direction: column;\n            gap: 25px;\n            width: auto;\n            top: 50%;\n            transform: translateY(-50%);\n            bottom: auto;\n            right: calc(100% - 66px);\n        }\n    }\n}\n\n.wc-block-grid__products {\n    .slick-list {\n        margin: 0 -10px;\n\n        @media (min-width:600px) {\n            margin: 0 -14px;\n        }\n    }\n}\n\n.slick-slide {\n    > div {\n        padding: 20px 10px;\n\n        @media (min-width:600px) {\n            padding: 20px 14px;\n        }\n\n        .wc-block-grid__product-image {\n            img {\n               width: 100%;\n            }\n        }\n    }\n}\n"]}