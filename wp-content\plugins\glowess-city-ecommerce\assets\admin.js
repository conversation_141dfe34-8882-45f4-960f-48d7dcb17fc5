jQuery(document).ready(function($) {
    var mediaUploader;

    // Görsel yükleme butonu
    $(document).on('click', '.city-upload-button', function(e) {
        e.preventDefault();
        
        var button = $(this);
        var targetInput = button.data('target');
        var preview = button.siblings('.image-preview');
        
        // Medya uploader zaten varsa aç
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        // Yeni medya uploader oluştur
        mediaUploader = wp.media({
            title: 'Şehir Görseli Seç',
            button: {
                text: 'Seç ve Kullan'
            },
            multiple: false
        });

        // Görsel seçildiğinde
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            
            // Input'a ID'yi yaz
            $('#' + targetInput).val(attachment.id);
            
            // Önizlemeyi güncelle
            var previewHtml = '<img src="' + attachment.sizes.medium.url + '" style="max-width: 200px; height: auto; border: 1px solid #ddd;" />';
            preview.html(previewHtml);
            
            // Buton metinlerini güncelle
            button.text('Görseli Değiştir');
            
            // Kaldır butonunu göster/ekle
            var removeButton = button.siblings('.city-remove-button');
            if (removeButton.length === 0) {
                button.after('<button type="button" class="button city-remove-button" data-target="' + targetInput + '" style="margin-left: 10px;">Görseli Kaldır</button>');
            }
        });

        mediaUploader.open();
    });

    // Görsel kaldırma butonu
    $(document).on('click', '.city-remove-button', function(e) {
        e.preventDefault();
        
        var button = $(this);
        var targetInput = button.data('target');
        var uploadButton = button.siblings('.city-upload-button');
        var preview = button.siblings('.image-preview');
        
        // Input'u temizle
        $('#' + targetInput).val('');
        
        // Önizlemeyi temizle
        var emptyPreview = '<div style="width: 200px; height: 150px; background: #f0f0f0; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; color: #666;">Görsel Seçilmedi</div>';
        preview.html(emptyPreview);
        
        // Buton metinlerini güncelle
        uploadButton.text('Görsel Seç');
        
        // Kaldır butonunu gizle
        button.remove();
    });
});