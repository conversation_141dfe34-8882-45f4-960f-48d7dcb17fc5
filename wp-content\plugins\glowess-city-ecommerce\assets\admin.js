/**
 * Glowess City Based E-Commerce - Admin JavaScript
 * WordPress Media Uploader Integration
 */

jQuery(document).ready(function($) {
    
    // Media uploader instance
    var mediaUploader;
    
    // Upload button click handler
    $(document).on('click', '.city-upload-button', function(e) {
        e.preventDefault();
        
        var button = $(this);
        var targetInput = button.data('target');
        var container = button.closest('.city-image-upload');
        var preview = container.find('.image-preview');
        
        // Create media uploader if not exists
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }
        
        mediaUploader = wp.media({
            title: 'Görse<PERSON>ç',
            button: {
                text: 'Seç'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });
        
        // When image is selected
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            
            // Update hidden input with attachment ID
            $('#' + targetInput).val(attachment.id);
            
            // Update preview image
            var imageUrl = attachment.sizes && attachment.sizes.medium ? 
                          attachment.sizes.medium.url : attachment.url;
            
            preview.html('<img src="' + imageUrl + '" style="max-width: 200px; height: auto; border: 1px solid #ddd;" />');
            
            // Update button text
            button.text('Görseli Değiştir');
            
            // Add remove button if not exists
            if (container.find('.city-remove-button').length === 0) {
                button.after('<button type="button" class="button city-remove-button" data-target="' + targetInput + '" style="margin-left: 10px;">Görseli Kaldır</button>');
            }
            
            // Show remove button
            container.find('.city-remove-button').show();
        });
        
        mediaUploader.open();
    });
    
    // Remove button click handler
    $(document).on('click', '.city-remove-button', function(e) {
        e.preventDefault();
        
        var button = $(this);
        var targetInput = button.data('target');
        var container = button.closest('.city-image-upload');
        var preview = container.find('.image-preview');
        var uploadButton = container.find('.city-upload-button');
        
        // Clear hidden input
        $('#' + targetInput).val('');
        
        // Reset preview
        var placeholderText = targetInput.includes('hero') ? 'Hero Görseli Seçilmedi' : 'Görsel Seçilmedi';
        preview.html('<div style="width: 200px; height: 150px; background: #f0f0f0; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; color: #666;">' + placeholderText + '</div>');
        
        // Update button text
        var buttonText = targetInput.includes('hero') ? 'Hero Görseli Seç' : 'Görsel Seç';
        uploadButton.text(buttonText);
        
        // Hide remove button
        button.hide();
    });
    
    // Form validation
    $('form').on('submit', function() {
        var hasError = false;
        var errorMessages = [];
        
        // Check if city slug is filled
        var citySlug = $('#city_slug').val().trim();
        if (!citySlug) {
            hasError = true;
            errorMessages.push('Şehir kodu zorunludur.');
            $('#city_slug').css('border-color', '#dc3545');
        } else {
            $('#city_slug').css('border-color', '');
        }
        
        // Validate city slug format (only lowercase letters, numbers, and hyphens)
        if (citySlug && !/^[a-z0-9-]+$/.test(citySlug)) {
            hasError = true;
            errorMessages.push('Şehir kodu sadece küçük harf, rakam ve tire (-) içerebilir.');
            $('#city_slug').css('border-color', '#dc3545');
        }
        
        // Check if at least one slider image is selected
        var hasSliderImage = false;
        $('input[name^="city_slider_images"]').each(function() {
            if ($(this).val()) {
                hasSliderImage = true;
                return false; // break loop
            }
        });
        
        if (!hasSliderImage) {
            hasError = true;
            errorMessages.push('En az bir slider görseli seçmelisiniz.');
        }
        
        // Show errors if any
        if (hasError) {
            alert('Lütfen aşağıdaki hataları düzeltin:\n\n' + errorMessages.join('\n'));
            return false;
        }
        
        return true;
    });
    
    // City slug auto-generation from title
    $('#title').on('input', function() {
        var title = $(this).val();
        var slug = generateSlug(title);
        
        // Only auto-fill if slug field is empty
        if (!$('#city_slug').val()) {
            $('#city_slug').val(slug);
        }
    });
    
    // Slug generation function
    function generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/ğ/g, 'g')
            .replace(/ü/g, 'u')
            .replace(/ş/g, 's')
            .replace(/ı/g, 'i')
            .replace(/ö/g, 'o')
            .replace(/ç/g, 'c')
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    }
    
    // Real-time slug validation
    $('#city_slug').on('input', function() {
        var slug = $(this).val();
        var validSlug = generateSlug(slug);
        
        if (slug !== validSlug) {
            $(this).css('border-color', '#ffc107');
            $(this).next('.description').html('Önerilen format: <strong>' + validSlug + '</strong>');
        } else {
            $(this).css('border-color', '#28a745');
            $(this).next('.description').html('URL için kullanılacak (örn: istanbul, ankara)');
        }
    });
    
    // Image size recommendations
    $('.city-upload-button').on('click', function() {
        var target = $(this).data('target');
        var recommendation = '';
        
        if (target.includes('hero')) {
            recommendation = 'Hero görseli için önerilen boyut: 1920x1080px';
        } else {
            recommendation = 'Slider görseli için önerilen boyut: 1920x700px';
        }
        
        // Show recommendation in console for developers
        console.log('Glowess City: ' + recommendation);
    });
    
    // Auto-save draft functionality
    var autoSaveTimer;
    $('input, textarea, select').on('change input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            // Trigger WordPress auto-save if available
            if (typeof wp !== 'undefined' && wp.autosave) {
                wp.autosave.server.triggerSave();
            }
        }, 5000); // Auto-save after 5 seconds of inactivity
    });
    
    // Preview functionality
    $('#preview-city-banner').on('click', function(e) {
        e.preventDefault();
        
        var previewData = {
            title: $('#city_slider_title').val() || $('#title').val() + ' için özel ürünler',
            subtitle: $('#city_slider_subtitle').val() || $('#title').val() + ' şehrindeki en kaliteli ürünleri keşfedin.',
            button_text: $('#city_slider_button_text').val() || 'Alışverişe Başla',
            button_url: $('#city_slider_button_url').val() || '/shop',
            image: $('#city_slider_image_0').val() ? wp.media.attachment($('#city_slider_image_0').val()).get('url') : ''
        };
        
        // Open preview in new window
        var previewWindow = window.open('', 'preview', 'width=1200,height=800');
        previewWindow.document.write(generatePreviewHTML(previewData));
    });
    
    // Generate preview HTML
    function generatePreviewHTML(data) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Banner Önizleme</title>
                <style>
                    body { margin: 0; font-family: Arial, sans-serif; }
                    .banner { 
                        position: relative; 
                        height: 700px; 
                        background: url('${data.image}') center/cover; 
                        display: flex; 
                        align-items: center; 
                        justify-content: center;
                        color: white;
                        text-align: center;
                    }
                    .banner::before {
                        content: '';
                        position: absolute;
                        top: 0; left: 0; right: 0; bottom: 0;
                        background: rgba(0,0,0,0.4);
                    }
                    .content { position: relative; z-index: 2; max-width: 600px; padding: 0 20px; }
                    h1 { font-size: 60px; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
                    p { font-size: 16px; line-height: 1.9; margin-bottom: 40px; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }
                    .btn { 
                        display: inline-block; 
                        padding: 12px 50px; 
                        background: white; 
                        color: #333; 
                        text-decoration: none; 
                        font-weight: 500;
                        border: 1px solid white;
                    }
                </style>
            </head>
            <body>
                <div class="banner">
                    <div class="content">
                        <h1>${data.title}</h1>
                        <p>${data.subtitle}</p>
                        <a href="${data.button_url}" class="btn">${data.button_text}</a>
                    </div>
                </div>
            </body>
            </html>
        `;
    }
    
    // Add preview button to form
    if ($('#city_slider_title').length) {
        $('#city_slider_title').closest('tr').after(
            '<tr><td colspan="2" style="text-align: center; padding: 20px;">' +
            '<button type="button" id="preview-city-banner" class="button button-secondary">' +
            '🔍 Banner Önizleme' +
            '</button></td></tr>'
        );
    }
});
