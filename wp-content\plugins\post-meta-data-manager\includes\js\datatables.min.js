/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#dt/dt-1.10.25/b-1.7.1/b-colvis-1.7.1/r-2.2.9/sc-2.0.4/sp-1.3.0
 *
 * Included libraries:
 *   DataTables 1.10.25, Buttons 1.7.1, Column visibility 1.7.1, Responsive 2.2.9, Scroller 2.0.4, SearchPanes 1.3.0
 */

/*!
   Copyright 2008-2021 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 DataTables 1.10.25
 ©2008-2021 SpryMedia Ltd - datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(k,y,z){k instanceof String&&(k=String(k));for(var q=k.length,G=0;G<q;G++){var O=k[G];if(y.call(z,O,G,k))return{i:G,v:O}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(k,y,z){if(k==Array.prototype||k==Object.prototype)return k;k[y]=z.value;return k};$jscomp.getGlobal=function(k){k=["object"==typeof globalThis&&globalThis,k,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var y=0;y<k.length;++y){var z=k[y];if(z&&z.Math==Math)return z}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(k,y){var z=$jscomp.propertyToPolyfillSymbol[y];if(null==z)return k[y];z=k[z];return void 0!==z?z:k[y]};
$jscomp.polyfill=function(k,y,z,q){y&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(k,y,z,q):$jscomp.polyfillUnisolated(k,y,z,q))};$jscomp.polyfillUnisolated=function(k,y,z,q){z=$jscomp.global;k=k.split(".");for(q=0;q<k.length-1;q++){var G=k[q];if(!(G in z))return;z=z[G]}k=k[k.length-1];q=z[k];y=y(q);y!=q&&null!=y&&$jscomp.defineProperty(z,k,{configurable:!0,writable:!0,value:y})};
$jscomp.polyfillIsolated=function(k,y,z,q){var G=k.split(".");k=1===G.length;q=G[0];q=!k&&q in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var O=0;O<G.length-1;O++){var ma=G[O];if(!(ma in q))return;q=q[ma]}G=G[G.length-1];z=$jscomp.IS_SYMBOL_NATIVE&&"es6"===z?q[G]:null;y=y(z);null!=y&&(k?$jscomp.defineProperty($jscomp.polyfills,G,{configurable:!0,writable:!0,value:y}):y!==z&&($jscomp.propertyToPolyfillSymbol[G]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(G):$jscomp.POLYFILL_PREFIX+G,
G=$jscomp.propertyToPolyfillSymbol[G],$jscomp.defineProperty(q,G,{configurable:!0,writable:!0,value:y})))};$jscomp.polyfill("Array.prototype.find",function(k){return k?k:function(y,z){return $jscomp.findInternal(this,y,z).v}},"es6","es3");
(function(k){"function"===typeof define&&define.amd?define(["jquery"],function(y){return k(y,window,document)}):"object"===typeof exports?module.exports=function(y,z){y||(y=window);z||(z="undefined"!==typeof window?require("jquery"):require("jquery")(y));return k(z,y,y.document)}:k(jQuery,window,document)})(function(k,y,z,q){function G(a){var b,c,d={};k.each(a,function(e,f){(b=e.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(b[1]+" ")&&(c=e.replace(b[0],b[2].toLowerCase()),
d[c]=e,"o"===b[1]&&G(a[e]))});a._hungarianMap=d}function O(a,b,c){a._hungarianMap||G(a);var d;k.each(b,function(e,f){d=a._hungarianMap[e];d===q||!c&&b[d]!==q||("o"===d.charAt(0)?(b[d]||(b[d]={}),k.extend(!0,b[d],b[e]),O(a[d],b[d],c)):b[d]=b[e])})}function ma(a){var b=u.defaults.oLanguage,c=b.sDecimal;c&&Wa(c);if(a){var d=a.sZeroRecords;!a.sEmptyTable&&d&&"No data available in table"===b.sEmptyTable&&V(a,a,"sZeroRecords","sEmptyTable");!a.sLoadingRecords&&d&&"Loading..."===b.sLoadingRecords&&V(a,a,
"sZeroRecords","sLoadingRecords");a.sInfoThousands&&(a.sThousands=a.sInfoThousands);(a=a.sDecimal)&&c!==a&&Wa(a)}}function zb(a){R(a,"ordering","bSort");R(a,"orderMulti","bSortMulti");R(a,"orderClasses","bSortClasses");R(a,"orderCellsTop","bSortCellsTop");R(a,"order","aaSorting");R(a,"orderFixed","aaSortingFixed");R(a,"paging","bPaginate");R(a,"pagingType","sPaginationType");R(a,"pageLength","iDisplayLength");R(a,"searching","bFilter");"boolean"===typeof a.sScrollX&&(a.sScrollX=a.sScrollX?"100%":
"");"boolean"===typeof a.scrollX&&(a.scrollX=a.scrollX?"100%":"");if(a=a.aoSearchCols)for(var b=0,c=a.length;b<c;b++)a[b]&&O(u.models.oSearch,a[b])}function Ab(a){R(a,"orderable","bSortable");R(a,"orderData","aDataSort");R(a,"orderSequence","asSorting");R(a,"orderDataType","sortDataType");var b=a.aDataSort;"number"!==typeof b||Array.isArray(b)||(a.aDataSort=[b])}function Bb(a){if(!u.__browser){var b={};u.__browser=b;var c=k("<div/>").css({position:"fixed",top:0,left:-1*k(y).scrollLeft(),height:1,
width:1,overflow:"hidden"}).append(k("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(k("<div/>").css({width:"100%",height:10}))).appendTo("body"),d=c.children(),e=d.children();b.barWidth=d[0].offsetWidth-d[0].clientWidth;b.bScrollOversize=100===e[0].offsetWidth&&100!==d[0].clientWidth;b.bScrollbarLeft=1!==Math.round(e.offset().left);b.bBounding=c[0].getBoundingClientRect().width?!0:!1;c.remove()}k.extend(a.oBrowser,u.__browser);a.oScroll.iBarWidth=u.__browser.barWidth}
function Cb(a,b,c,d,e,f){var g=!1;if(c!==q){var h=c;g=!0}for(;d!==e;)a.hasOwnProperty(d)&&(h=g?b(h,a[d],d,a):a[d],g=!0,d+=f);return h}function Xa(a,b){var c=u.defaults.column,d=a.aoColumns.length;c=k.extend({},u.models.oColumn,c,{nTh:b?b:z.createElement("th"),sTitle:c.sTitle?c.sTitle:b?b.innerHTML:"",aDataSort:c.aDataSort?c.aDataSort:[d],mData:c.mData?c.mData:d,idx:d});a.aoColumns.push(c);c=a.aoPreSearchCols;c[d]=k.extend({},u.models.oSearch,c[d]);Ea(a,d,k(b).data())}function Ea(a,b,c){b=a.aoColumns[b];
var d=a.oClasses,e=k(b.nTh);if(!b.sWidthOrig){b.sWidthOrig=e.attr("width")||null;var f=(e.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);f&&(b.sWidthOrig=f[1])}c!==q&&null!==c&&(Ab(c),O(u.defaults.column,c,!0),c.mDataProp===q||c.mData||(c.mData=c.mDataProp),c.sType&&(b._sManualType=c.sType),c.className&&!c.sClass&&(c.sClass=c.className),c.sClass&&e.addClass(c.sClass),k.extend(b,c),V(b,c,"sWidth","sWidthOrig"),c.iDataSort!==q&&(b.aDataSort=[c.iDataSort]),V(b,c,"aDataSort"));var g=b.mData,h=ia(g),
l=b.mRender?ia(b.mRender):null;c=function(n){return"string"===typeof n&&-1!==n.indexOf("@")};b._bAttrSrc=k.isPlainObject(g)&&(c(g.sort)||c(g.type)||c(g.filter));b._setter=null;b.fnGetData=function(n,m,p){var t=h(n,m,q,p);return l&&m?l(t,m,n,p):t};b.fnSetData=function(n,m,p){return da(g)(n,m,p)};"number"!==typeof g&&(a._rowReadObject=!0);a.oFeatures.bSort||(b.bSortable=!1,e.addClass(d.sSortableNone));a=-1!==k.inArray("asc",b.asSorting);c=-1!==k.inArray("desc",b.asSorting);b.bSortable&&(a||c)?a&&!c?
(b.sSortingClass=d.sSortableAsc,b.sSortingClassJUI=d.sSortJUIAscAllowed):!a&&c?(b.sSortingClass=d.sSortableDesc,b.sSortingClassJUI=d.sSortJUIDescAllowed):(b.sSortingClass=d.sSortable,b.sSortingClassJUI=d.sSortJUI):(b.sSortingClass=d.sSortableNone,b.sSortingClassJUI="")}function ra(a){if(!1!==a.oFeatures.bAutoWidth){var b=a.aoColumns;Ya(a);for(var c=0,d=b.length;c<d;c++)b[c].nTh.style.width=b[c].sWidth}b=a.oScroll;""===b.sY&&""===b.sX||Fa(a);H(a,null,"column-sizing",[a])}function sa(a,b){a=Ga(a,"bVisible");
return"number"===typeof a[b]?a[b]:null}function ta(a,b){a=Ga(a,"bVisible");b=k.inArray(b,a);return-1!==b?b:null}function na(a){var b=0;k.each(a.aoColumns,function(c,d){d.bVisible&&"none"!==k(d.nTh).css("display")&&b++});return b}function Ga(a,b){var c=[];k.map(a.aoColumns,function(d,e){d[b]&&c.push(e)});return c}function Za(a){var b=a.aoColumns,c=a.aoData,d=u.ext.type.detect,e,f,g;var h=0;for(e=b.length;h<e;h++){var l=b[h];var n=[];if(!l.sType&&l._sManualType)l.sType=l._sManualType;else if(!l.sType){var m=
0;for(f=d.length;m<f;m++){var p=0;for(g=c.length;p<g;p++){n[p]===q&&(n[p]=S(a,p,h,"type"));var t=d[m](n[p],a);if(!t&&m!==d.length-1)break;if("html"===t&&!Z(n[p]))break}if(t){l.sType=t;break}}l.sType||(l.sType="string")}}}function Db(a,b,c,d){var e,f,g,h=a.aoColumns;if(b)for(e=b.length-1;0<=e;e--){var l=b[e];var n=l.targets!==q?l.targets:l.aTargets;Array.isArray(n)||(n=[n]);var m=0;for(f=n.length;m<f;m++)if("number"===typeof n[m]&&0<=n[m]){for(;h.length<=n[m];)Xa(a);d(n[m],l)}else if("number"===typeof n[m]&&
0>n[m])d(h.length+n[m],l);else if("string"===typeof n[m]){var p=0;for(g=h.length;p<g;p++)("_all"==n[m]||k(h[p].nTh).hasClass(n[m]))&&d(p,l)}}if(c)for(e=0,a=c.length;e<a;e++)d(e,c[e])}function ea(a,b,c,d){var e=a.aoData.length,f=k.extend(!0,{},u.models.oRow,{src:c?"dom":"data",idx:e});f._aData=b;a.aoData.push(f);for(var g=a.aoColumns,h=0,l=g.length;h<l;h++)g[h].sType=null;a.aiDisplayMaster.push(e);b=a.rowIdFn(b);b!==q&&(a.aIds[b]=f);!c&&a.oFeatures.bDeferRender||$a(a,e,c,d);return e}function Ha(a,
b){var c;b instanceof k||(b=k(b));return b.map(function(d,e){c=ab(a,e);return ea(a,c.data,e,c.cells)})}function S(a,b,c,d){var e=a.iDraw,f=a.aoColumns[c],g=a.aoData[b]._aData,h=f.sDefaultContent,l=f.fnGetData(g,d,{settings:a,row:b,col:c});if(l===q)return a.iDrawError!=e&&null===h&&(ca(a,0,"Requested unknown parameter "+("function"==typeof f.mData?"{function}":"'"+f.mData+"'")+" for row "+b+", column "+c,4),a.iDrawError=e),h;if((l===g||null===l)&&null!==h&&d!==q)l=h;else if("function"===typeof l)return l.call(g);
return null===l&&"display"==d?"":l}function Eb(a,b,c,d){a.aoColumns[c].fnSetData(a.aoData[b]._aData,d,{settings:a,row:b,col:c})}function bb(a){return k.map(a.match(/(\\.|[^\.])+/g)||[""],function(b){return b.replace(/\\\./g,".")})}function ia(a){if(k.isPlainObject(a)){var b={};k.each(a,function(d,e){e&&(b[d]=ia(e))});return function(d,e,f,g){var h=b[e]||b._;return h!==q?h(d,e,f,g):d}}if(null===a)return function(d){return d};if("function"===typeof a)return function(d,e,f,g){return a(d,e,f,g)};if("string"!==
typeof a||-1===a.indexOf(".")&&-1===a.indexOf("[")&&-1===a.indexOf("("))return function(d,e){return d[a]};var c=function(d,e,f){if(""!==f){var g=bb(f);for(var h=0,l=g.length;h<l;h++){f=g[h].match(ua);var n=g[h].match(oa);if(f){g[h]=g[h].replace(ua,"");""!==g[h]&&(d=d[g[h]]);n=[];g.splice(0,h+1);g=g.join(".");if(Array.isArray(d))for(h=0,l=d.length;h<l;h++)n.push(c(d[h],e,g));d=f[0].substring(1,f[0].length-1);d=""===d?n:n.join(d);break}else if(n){g[h]=g[h].replace(oa,"");d=d[g[h]]();continue}if(null===
d||d[g[h]]===q)return q;d=d[g[h]]}}return d};return function(d,e){return c(d,e,a)}}function da(a){if(k.isPlainObject(a))return da(a._);if(null===a)return function(){};if("function"===typeof a)return function(c,d,e){a(c,"set",d,e)};if("string"!==typeof a||-1===a.indexOf(".")&&-1===a.indexOf("[")&&-1===a.indexOf("("))return function(c,d){c[a]=d};var b=function(c,d,e){e=bb(e);var f=e[e.length-1];for(var g,h,l=0,n=e.length-1;l<n;l++){if("__proto__"===e[l]||"constructor"===e[l])throw Error("Cannot set prototype values");
g=e[l].match(ua);h=e[l].match(oa);if(g){e[l]=e[l].replace(ua,"");c[e[l]]=[];f=e.slice();f.splice(0,l+1);g=f.join(".");if(Array.isArray(d))for(h=0,n=d.length;h<n;h++)f={},b(f,d[h],g),c[e[l]].push(f);else c[e[l]]=d;return}h&&(e[l]=e[l].replace(oa,""),c=c[e[l]](d));if(null===c[e[l]]||c[e[l]]===q)c[e[l]]={};c=c[e[l]]}if(f.match(oa))c[f.replace(oa,"")](d);else c[f.replace(ua,"")]=d};return function(c,d){return b(c,d,a)}}function cb(a){return T(a.aoData,"_aData")}function Ia(a){a.aoData.length=0;a.aiDisplayMaster.length=
0;a.aiDisplay.length=0;a.aIds={}}function Ja(a,b,c){for(var d=-1,e=0,f=a.length;e<f;e++)a[e]==b?d=e:a[e]>b&&a[e]--; -1!=d&&c===q&&a.splice(d,1)}function va(a,b,c,d){var e=a.aoData[b],f,g=function(l,n){for(;l.childNodes.length;)l.removeChild(l.firstChild);l.innerHTML=S(a,b,n,"display")};if("dom"!==c&&(c&&"auto"!==c||"dom"!==e.src)){var h=e.anCells;if(h)if(d!==q)g(h[d],d);else for(c=0,f=h.length;c<f;c++)g(h[c],c)}else e._aData=ab(a,e,d,d===q?q:e._aData).data;e._aSortData=null;e._aFilterData=null;g=
a.aoColumns;if(d!==q)g[d].sType=null;else{c=0;for(f=g.length;c<f;c++)g[c].sType=null;db(a,e)}}function ab(a,b,c,d){var e=[],f=b.firstChild,g,h=0,l,n=a.aoColumns,m=a._rowReadObject;d=d!==q?d:m?{}:[];var p=function(x,r){if("string"===typeof x){var A=x.indexOf("@");-1!==A&&(A=x.substring(A+1),da(x)(d,r.getAttribute(A)))}},t=function(x){if(c===q||c===h)g=n[h],l=x.innerHTML.trim(),g&&g._bAttrSrc?(da(g.mData._)(d,l),p(g.mData.sort,x),p(g.mData.type,x),p(g.mData.filter,x)):m?(g._setter||(g._setter=da(g.mData)),
g._setter(d,l)):d[h]=l;h++};if(f)for(;f;){var v=f.nodeName.toUpperCase();if("TD"==v||"TH"==v)t(f),e.push(f);f=f.nextSibling}else for(e=b.anCells,f=0,v=e.length;f<v;f++)t(e[f]);(b=b.firstChild?b:b.nTr)&&(b=b.getAttribute("id"))&&da(a.rowId)(d,b);return{data:d,cells:e}}function $a(a,b,c,d){var e=a.aoData[b],f=e._aData,g=[],h,l;if(null===e.nTr){var n=c||z.createElement("tr");e.nTr=n;e.anCells=g;n._DT_RowIndex=b;db(a,e);var m=0;for(h=a.aoColumns.length;m<h;m++){var p=a.aoColumns[m];e=(l=c?!1:!0)?z.createElement(p.sCellType):
d[m];e._DT_CellIndex={row:b,column:m};g.push(e);if(l||!(!p.mRender&&p.mData===m||k.isPlainObject(p.mData)&&p.mData._===m+".display"))e.innerHTML=S(a,b,m,"display");p.sClass&&(e.className+=" "+p.sClass);p.bVisible&&!c?n.appendChild(e):!p.bVisible&&c&&e.parentNode.removeChild(e);p.fnCreatedCell&&p.fnCreatedCell.call(a.oInstance,e,S(a,b,m),f,b,m)}H(a,"aoRowCreatedCallback",null,[n,f,b,g])}}function db(a,b){var c=b.nTr,d=b._aData;if(c){if(a=a.rowIdFn(d))c.id=a;d.DT_RowClass&&(a=d.DT_RowClass.split(" "),
b.__rowc=b.__rowc?Ka(b.__rowc.concat(a)):a,k(c).removeClass(b.__rowc.join(" ")).addClass(d.DT_RowClass));d.DT_RowAttr&&k(c).attr(d.DT_RowAttr);d.DT_RowData&&k(c).data(d.DT_RowData)}}function Fb(a){var b,c,d=a.nTHead,e=a.nTFoot,f=0===k("th, td",d).length,g=a.oClasses,h=a.aoColumns;f&&(c=k("<tr/>").appendTo(d));var l=0;for(b=h.length;l<b;l++){var n=h[l];var m=k(n.nTh).addClass(n.sClass);f&&m.appendTo(c);a.oFeatures.bSort&&(m.addClass(n.sSortingClass),!1!==n.bSortable&&(m.attr("tabindex",a.iTabIndex).attr("aria-controls",
a.sTableId),eb(a,n.nTh,l)));n.sTitle!=m[0].innerHTML&&m.html(n.sTitle);fb(a,"header")(a,m,n,g)}f&&wa(a.aoHeader,d);k(d).children("tr").attr("role","row");k(d).children("tr").children("th, td").addClass(g.sHeaderTH);k(e).children("tr").children("th, td").addClass(g.sFooterTH);if(null!==e)for(a=a.aoFooter[0],l=0,b=a.length;l<b;l++)n=h[l],n.nTf=a[l].cell,n.sClass&&k(n.nTf).addClass(n.sClass)}function xa(a,b,c){var d,e,f=[],g=[],h=a.aoColumns.length;if(b){c===q&&(c=!1);var l=0;for(d=b.length;l<d;l++){f[l]=
b[l].slice();f[l].nTr=b[l].nTr;for(e=h-1;0<=e;e--)a.aoColumns[e].bVisible||c||f[l].splice(e,1);g.push([])}l=0;for(d=f.length;l<d;l++){if(a=f[l].nTr)for(;e=a.firstChild;)a.removeChild(e);e=0;for(b=f[l].length;e<b;e++){var n=h=1;if(g[l][e]===q){a.appendChild(f[l][e].cell);for(g[l][e]=1;f[l+h]!==q&&f[l][e].cell==f[l+h][e].cell;)g[l+h][e]=1,h++;for(;f[l][e+n]!==q&&f[l][e].cell==f[l][e+n].cell;){for(c=0;c<h;c++)g[l+c][e+n]=1;n++}k(f[l][e].cell).attr("rowspan",h).attr("colspan",n)}}}}}function fa(a,b){var c=
H(a,"aoPreDrawCallback","preDraw",[a]);if(-1!==k.inArray(!1,c))U(a,!1);else{c=[];var d=0,e=a.asStripeClasses,f=e.length,g=a.oLanguage,h=a.iInitDisplayStart,l="ssp"==P(a),n=a.aiDisplay;a.bDrawing=!0;h!==q&&-1!==h&&(a._iDisplayStart=l?h:h>=a.fnRecordsDisplay()?0:h,a.iInitDisplayStart=-1);h=a._iDisplayStart;var m=a.fnDisplayEnd();if(a.bDeferLoading)a.bDeferLoading=!1,a.iDraw++,U(a,!1);else if(!l)a.iDraw++;else if(!a.bDestroying&&!b){Gb(a);return}if(0!==n.length)for(b=l?a.aoData.length:m,g=l?0:h;g<b;g++){l=
n[g];var p=a.aoData[l];null===p.nTr&&$a(a,l);var t=p.nTr;if(0!==f){var v=e[d%f];p._sRowStripe!=v&&(k(t).removeClass(p._sRowStripe).addClass(v),p._sRowStripe=v)}H(a,"aoRowCallback",null,[t,p._aData,d,g,l]);c.push(t);d++}else d=g.sZeroRecords,1==a.iDraw&&"ajax"==P(a)?d=g.sLoadingRecords:g.sEmptyTable&&0===a.fnRecordsTotal()&&(d=g.sEmptyTable),c[0]=k("<tr/>",{"class":f?e[0]:""}).append(k("<td />",{valign:"top",colSpan:na(a),"class":a.oClasses.sRowEmpty}).html(d))[0];H(a,"aoHeaderCallback","header",[k(a.nTHead).children("tr")[0],
cb(a),h,m,n]);H(a,"aoFooterCallback","footer",[k(a.nTFoot).children("tr")[0],cb(a),h,m,n]);e=k(a.nTBody);e.children().detach();e.append(k(c));H(a,"aoDrawCallback","draw",[a]);a.bSorted=!1;a.bFiltered=!1;a.bDrawing=!1}}function ja(a,b){var c=a.oFeatures,d=c.bFilter;c.bSort&&Hb(a);d?ya(a,a.oPreviousSearch):a.aiDisplay=a.aiDisplayMaster.slice();!0!==b&&(a._iDisplayStart=0);a._drawHold=b;fa(a);a._drawHold=!1}function Ib(a){var b=a.oClasses,c=k(a.nTable);c=k("<div/>").insertBefore(c);var d=a.oFeatures,
e=k("<div/>",{id:a.sTableId+"_wrapper","class":b.sWrapper+(a.nTFoot?"":" "+b.sNoFooter)});a.nHolding=c[0];a.nTableWrapper=e[0];a.nTableReinsertBefore=a.nTable.nextSibling;for(var f=a.sDom.split(""),g,h,l,n,m,p,t=0;t<f.length;t++){g=null;h=f[t];if("<"==h){l=k("<div/>")[0];n=f[t+1];if("'"==n||'"'==n){m="";for(p=2;f[t+p]!=n;)m+=f[t+p],p++;"H"==m?m=b.sJUIHeader:"F"==m&&(m=b.sJUIFooter);-1!=m.indexOf(".")?(n=m.split("."),l.id=n[0].substr(1,n[0].length-1),l.className=n[1]):"#"==m.charAt(0)?l.id=m.substr(1,
m.length-1):l.className=m;t+=p}e.append(l);e=k(l)}else if(">"==h)e=e.parent();else if("l"==h&&d.bPaginate&&d.bLengthChange)g=Jb(a);else if("f"==h&&d.bFilter)g=Kb(a);else if("r"==h&&d.bProcessing)g=Lb(a);else if("t"==h)g=Mb(a);else if("i"==h&&d.bInfo)g=Nb(a);else if("p"==h&&d.bPaginate)g=Ob(a);else if(0!==u.ext.feature.length)for(l=u.ext.feature,p=0,n=l.length;p<n;p++)if(h==l[p].cFeature){g=l[p].fnInit(a);break}g&&(l=a.aanFeatures,l[h]||(l[h]=[]),l[h].push(g),e.append(g))}c.replaceWith(e);a.nHolding=
null}function wa(a,b){b=k(b).children("tr");var c,d,e;a.splice(0,a.length);var f=0;for(e=b.length;f<e;f++)a.push([]);f=0;for(e=b.length;f<e;f++){var g=b[f];for(c=g.firstChild;c;){if("TD"==c.nodeName.toUpperCase()||"TH"==c.nodeName.toUpperCase()){var h=1*c.getAttribute("colspan");var l=1*c.getAttribute("rowspan");h=h&&0!==h&&1!==h?h:1;l=l&&0!==l&&1!==l?l:1;var n=0;for(d=a[f];d[n];)n++;var m=n;var p=1===h?!0:!1;for(d=0;d<h;d++)for(n=0;n<l;n++)a[f+n][m+d]={cell:c,unique:p},a[f+n].nTr=g}c=c.nextSibling}}}
function La(a,b,c){var d=[];c||(c=a.aoHeader,b&&(c=[],wa(c,b)));b=0;for(var e=c.length;b<e;b++)for(var f=0,g=c[b].length;f<g;f++)!c[b][f].unique||d[f]&&a.bSortCellsTop||(d[f]=c[b][f].cell);return d}function Ma(a,b,c){H(a,"aoServerParams","serverParams",[b]);if(b&&Array.isArray(b)){var d={},e=/(.*?)\[\]$/;k.each(b,function(m,p){(m=p.name.match(e))?(m=m[0],d[m]||(d[m]=[]),d[m].push(p.value)):d[p.name]=p.value});b=d}var f=a.ajax,g=a.oInstance,h=function(m){H(a,null,"xhr",[a,m,a.jqXHR]);c(m)};if(k.isPlainObject(f)&&
f.data){var l=f.data;var n="function"===typeof l?l(b,a):l;b="function"===typeof l&&n?n:k.extend(!0,b,n);delete f.data}n={data:b,success:function(m){var p=m.error||m.sError;p&&ca(a,0,p);a.json=m;h(m)},dataType:"json",cache:!1,type:a.sServerMethod,error:function(m,p,t){t=H(a,null,"xhr",[a,null,a.jqXHR]);-1===k.inArray(!0,t)&&("parsererror"==p?ca(a,0,"Invalid JSON response",1):4===m.readyState&&ca(a,0,"Ajax error",7));U(a,!1)}};a.oAjaxData=b;H(a,null,"preXhr",[a,b]);a.fnServerData?a.fnServerData.call(g,
a.sAjaxSource,k.map(b,function(m,p){return{name:p,value:m}}),h,a):a.sAjaxSource||"string"===typeof f?a.jqXHR=k.ajax(k.extend(n,{url:f||a.sAjaxSource})):"function"===typeof f?a.jqXHR=f.call(g,b,h,a):(a.jqXHR=k.ajax(k.extend(n,f)),f.data=l)}function Gb(a){a.iDraw++;U(a,!0);Ma(a,Pb(a),function(b){Qb(a,b)})}function Pb(a){var b=a.aoColumns,c=b.length,d=a.oFeatures,e=a.oPreviousSearch,f=a.aoPreSearchCols,g=[],h=pa(a);var l=a._iDisplayStart;var n=!1!==d.bPaginate?a._iDisplayLength:-1;var m=function(x,r){g.push({name:x,
value:r})};m("sEcho",a.iDraw);m("iColumns",c);m("sColumns",T(b,"sName").join(","));m("iDisplayStart",l);m("iDisplayLength",n);var p={draw:a.iDraw,columns:[],order:[],start:l,length:n,search:{value:e.sSearch,regex:e.bRegex}};for(l=0;l<c;l++){var t=b[l];var v=f[l];n="function"==typeof t.mData?"function":t.mData;p.columns.push({data:n,name:t.sName,searchable:t.bSearchable,orderable:t.bSortable,search:{value:v.sSearch,regex:v.bRegex}});m("mDataProp_"+l,n);d.bFilter&&(m("sSearch_"+l,v.sSearch),m("bRegex_"+
l,v.bRegex),m("bSearchable_"+l,t.bSearchable));d.bSort&&m("bSortable_"+l,t.bSortable)}d.bFilter&&(m("sSearch",e.sSearch),m("bRegex",e.bRegex));d.bSort&&(k.each(h,function(x,r){p.order.push({column:r.col,dir:r.dir});m("iSortCol_"+x,r.col);m("sSortDir_"+x,r.dir)}),m("iSortingCols",h.length));b=u.ext.legacy.ajax;return null===b?a.sAjaxSource?g:p:b?g:p}function Qb(a,b){var c=function(g,h){return b[g]!==q?b[g]:b[h]},d=Na(a,b),e=c("sEcho","draw"),f=c("iTotalRecords","recordsTotal");c=c("iTotalDisplayRecords",
"recordsFiltered");if(e!==q){if(1*e<a.iDraw)return;a.iDraw=1*e}Ia(a);a._iRecordsTotal=parseInt(f,10);a._iRecordsDisplay=parseInt(c,10);e=0;for(f=d.length;e<f;e++)ea(a,d[e]);a.aiDisplay=a.aiDisplayMaster.slice();fa(a,!0);a._bInitComplete||Oa(a,b);U(a,!1)}function Na(a,b){a=k.isPlainObject(a.ajax)&&a.ajax.dataSrc!==q?a.ajax.dataSrc:a.sAjaxDataProp;return"data"===a?b.aaData||b[a]:""!==a?ia(a)(b):b}function Kb(a){var b=a.oClasses,c=a.sTableId,d=a.oLanguage,e=a.oPreviousSearch,f=a.aanFeatures,g='<input type="search" class="'+
b.sFilterInput+'"/>',h=d.sSearch;h=h.match(/_INPUT_/)?h.replace("_INPUT_",g):h+g;b=k("<div/>",{id:f.f?null:c+"_filter","class":b.sFilter}).append(k("<label/>").append(h));var l=function(){var m=this.value?this.value:"";m!=e.sSearch&&(ya(a,{sSearch:m,bRegex:e.bRegex,bSmart:e.bSmart,bCaseInsensitive:e.bCaseInsensitive}),a._iDisplayStart=0,fa(a))};f=null!==a.searchDelay?a.searchDelay:"ssp"===P(a)?400:0;var n=k("input",b).val(e.sSearch).attr("placeholder",d.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",
f?gb(l,f):l).on("mouseup",function(m){setTimeout(function(){l.call(n[0])},10)}).on("keypress.DT",function(m){if(13==m.keyCode)return!1}).attr("aria-controls",c);k(a.nTable).on("search.dt.DT",function(m,p){if(a===p)try{n[0]!==z.activeElement&&n.val(e.sSearch)}catch(t){}});return b[0]}function ya(a,b,c){var d=a.oPreviousSearch,e=a.aoPreSearchCols,f=function(h){d.sSearch=h.sSearch;d.bRegex=h.bRegex;d.bSmart=h.bSmart;d.bCaseInsensitive=h.bCaseInsensitive},g=function(h){return h.bEscapeRegex!==q?!h.bEscapeRegex:
h.bRegex};Za(a);if("ssp"!=P(a)){Rb(a,b.sSearch,c,g(b),b.bSmart,b.bCaseInsensitive);f(b);for(b=0;b<e.length;b++)Sb(a,e[b].sSearch,b,g(e[b]),e[b].bSmart,e[b].bCaseInsensitive);Tb(a)}else f(b);a.bFiltered=!0;H(a,null,"search",[a])}function Tb(a){for(var b=u.ext.search,c=a.aiDisplay,d,e,f=0,g=b.length;f<g;f++){for(var h=[],l=0,n=c.length;l<n;l++)e=c[l],d=a.aoData[e],b[f](a,d._aFilterData,e,d._aData,l)&&h.push(e);c.length=0;k.merge(c,h)}}function Sb(a,b,c,d,e,f){if(""!==b){var g=[],h=a.aiDisplay;d=hb(b,
d,e,f);for(e=0;e<h.length;e++)b=a.aoData[h[e]]._aFilterData[c],d.test(b)&&g.push(h[e]);a.aiDisplay=g}}function Rb(a,b,c,d,e,f){e=hb(b,d,e,f);var g=a.oPreviousSearch.sSearch,h=a.aiDisplayMaster;f=[];0!==u.ext.search.length&&(c=!0);var l=Ub(a);if(0>=b.length)a.aiDisplay=h.slice();else{if(l||c||d||g.length>b.length||0!==b.indexOf(g)||a.bSorted)a.aiDisplay=h.slice();b=a.aiDisplay;for(c=0;c<b.length;c++)e.test(a.aoData[b[c]]._sFilterRow)&&f.push(b[c]);a.aiDisplay=f}}function hb(a,b,c,d){a=b?a:ib(a);c&&
(a="^(?=.*?"+k.map(a.match(/"[^"]+"|[^ ]+/g)||[""],function(e){if('"'===e.charAt(0)){var f=e.match(/^"(.*)"$/);e=f?f[1]:e}return e.replace('"',"")}).join(")(?=.*?")+").*$");return new RegExp(a,d?"i":"")}function Ub(a){var b=a.aoColumns,c,d,e=u.ext.type.search;var f=!1;var g=0;for(c=a.aoData.length;g<c;g++){var h=a.aoData[g];if(!h._aFilterData){var l=[];var n=0;for(d=b.length;n<d;n++){f=b[n];if(f.bSearchable){var m=S(a,g,n,"filter");e[f.sType]&&(m=e[f.sType](m));null===m&&(m="");"string"!==typeof m&&
m.toString&&(m=m.toString())}else m="";m.indexOf&&-1!==m.indexOf("&")&&(Pa.innerHTML=m,m=rc?Pa.textContent:Pa.innerText);m.replace&&(m=m.replace(/[\r\n\u2028]/g,""));l.push(m)}h._aFilterData=l;h._sFilterRow=l.join("  ");f=!0}}return f}function Vb(a){return{search:a.sSearch,smart:a.bSmart,regex:a.bRegex,caseInsensitive:a.bCaseInsensitive}}function Wb(a){return{sSearch:a.search,bSmart:a.smart,bRegex:a.regex,bCaseInsensitive:a.caseInsensitive}}function Nb(a){var b=a.sTableId,c=a.aanFeatures.i,d=k("<div/>",
{"class":a.oClasses.sInfo,id:c?null:b+"_info"});c||(a.aoDrawCallback.push({fn:Xb,sName:"information"}),d.attr("role","status").attr("aria-live","polite"),k(a.nTable).attr("aria-describedby",b+"_info"));return d[0]}function Xb(a){var b=a.aanFeatures.i;if(0!==b.length){var c=a.oLanguage,d=a._iDisplayStart+1,e=a.fnDisplayEnd(),f=a.fnRecordsTotal(),g=a.fnRecordsDisplay(),h=g?c.sInfo:c.sInfoEmpty;g!==f&&(h+=" "+c.sInfoFiltered);h+=c.sInfoPostFix;h=Yb(a,h);c=c.fnInfoCallback;null!==c&&(h=c.call(a.oInstance,
a,d,e,f,g,h));k(b).html(h)}}function Yb(a,b){var c=a.fnFormatNumber,d=a._iDisplayStart+1,e=a._iDisplayLength,f=a.fnRecordsDisplay(),g=-1===e;return b.replace(/_START_/g,c.call(a,d)).replace(/_END_/g,c.call(a,a.fnDisplayEnd())).replace(/_MAX_/g,c.call(a,a.fnRecordsTotal())).replace(/_TOTAL_/g,c.call(a,f)).replace(/_PAGE_/g,c.call(a,g?1:Math.ceil(d/e))).replace(/_PAGES_/g,c.call(a,g?1:Math.ceil(f/e)))}function za(a){var b=a.iInitDisplayStart,c=a.aoColumns;var d=a.oFeatures;var e=a.bDeferLoading;if(a.bInitialised){Ib(a);
Fb(a);xa(a,a.aoHeader);xa(a,a.aoFooter);U(a,!0);d.bAutoWidth&&Ya(a);var f=0;for(d=c.length;f<d;f++){var g=c[f];g.sWidth&&(g.nTh.style.width=K(g.sWidth))}H(a,null,"preInit",[a]);ja(a);c=P(a);if("ssp"!=c||e)"ajax"==c?Ma(a,[],function(h){var l=Na(a,h);for(f=0;f<l.length;f++)ea(a,l[f]);a.iInitDisplayStart=b;ja(a);U(a,!1);Oa(a,h)},a):(U(a,!1),Oa(a))}else setTimeout(function(){za(a)},200)}function Oa(a,b){a._bInitComplete=!0;(b||a.oInit.aaData)&&ra(a);H(a,null,"plugin-init",[a,b]);H(a,"aoInitComplete",
"init",[a,b])}function jb(a,b){b=parseInt(b,10);a._iDisplayLength=b;kb(a);H(a,null,"length",[a,b])}function Jb(a){var b=a.oClasses,c=a.sTableId,d=a.aLengthMenu,e=Array.isArray(d[0]),f=e?d[0]:d;d=e?d[1]:d;e=k("<select/>",{name:c+"_length","aria-controls":c,"class":b.sLengthSelect});for(var g=0,h=f.length;g<h;g++)e[0][g]=new Option("number"===typeof d[g]?a.fnFormatNumber(d[g]):d[g],f[g]);var l=k("<div><label/></div>").addClass(b.sLength);a.aanFeatures.l||(l[0].id=c+"_length");l.children().append(a.oLanguage.sLengthMenu.replace("_MENU_",
e[0].outerHTML));k("select",l).val(a._iDisplayLength).on("change.DT",function(n){jb(a,k(this).val());fa(a)});k(a.nTable).on("length.dt.DT",function(n,m,p){a===m&&k("select",l).val(p)});return l[0]}function Ob(a){var b=a.sPaginationType,c=u.ext.pager[b],d="function"===typeof c,e=function(g){fa(g)};b=k("<div/>").addClass(a.oClasses.sPaging+b)[0];var f=a.aanFeatures;d||c.fnInit(a,b,e);f.p||(b.id=a.sTableId+"_paginate",a.aoDrawCallback.push({fn:function(g){if(d){var h=g._iDisplayStart,l=g._iDisplayLength,
n=g.fnRecordsDisplay(),m=-1===l;h=m?0:Math.ceil(h/l);l=m?1:Math.ceil(n/l);n=c(h,l);var p;m=0;for(p=f.p.length;m<p;m++)fb(g,"pageButton")(g,f.p[m],m,n,h,l)}else c.fnUpdate(g,e)},sName:"pagination"}));return b}function lb(a,b,c){var d=a._iDisplayStart,e=a._iDisplayLength,f=a.fnRecordsDisplay();0===f||-1===e?d=0:"number"===typeof b?(d=b*e,d>f&&(d=0)):"first"==b?d=0:"previous"==b?(d=0<=e?d-e:0,0>d&&(d=0)):"next"==b?d+e<f&&(d+=e):"last"==b?d=Math.floor((f-1)/e)*e:ca(a,0,"Unknown paging action: "+b,5);
b=a._iDisplayStart!==d;a._iDisplayStart=d;b&&(H(a,null,"page",[a]),c&&fa(a));return b}function Lb(a){return k("<div/>",{id:a.aanFeatures.r?null:a.sTableId+"_processing","class":a.oClasses.sProcessing}).html(a.oLanguage.sProcessing).insertBefore(a.nTable)[0]}function U(a,b){a.oFeatures.bProcessing&&k(a.aanFeatures.r).css("display",b?"block":"none");H(a,null,"processing",[a,b])}function Mb(a){var b=k(a.nTable);b.attr("role","grid");var c=a.oScroll;if(""===c.sX&&""===c.sY)return a.nTable;var d=c.sX,
e=c.sY,f=a.oClasses,g=b.children("caption"),h=g.length?g[0]._captionSide:null,l=k(b[0].cloneNode(!1)),n=k(b[0].cloneNode(!1)),m=b.children("tfoot");m.length||(m=null);l=k("<div/>",{"class":f.sScrollWrapper}).append(k("<div/>",{"class":f.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:d?d?K(d):null:"100%"}).append(k("<div/>",{"class":f.sScrollHeadInner}).css({"box-sizing":"content-box",width:c.sXInner||"100%"}).append(l.removeAttr("id").css("margin-left",0).append("top"===h?
g:null).append(b.children("thead"))))).append(k("<div/>",{"class":f.sScrollBody}).css({position:"relative",overflow:"auto",width:d?K(d):null}).append(b));m&&l.append(k("<div/>",{"class":f.sScrollFoot}).css({overflow:"hidden",border:0,width:d?d?K(d):null:"100%"}).append(k("<div/>",{"class":f.sScrollFootInner}).append(n.removeAttr("id").css("margin-left",0).append("bottom"===h?g:null).append(b.children("tfoot")))));b=l.children();var p=b[0];f=b[1];var t=m?b[2]:null;if(d)k(f).on("scroll.DT",function(v){v=
this.scrollLeft;p.scrollLeft=v;m&&(t.scrollLeft=v)});k(f).css("max-height",e);c.bCollapse||k(f).css("height",e);a.nScrollHead=p;a.nScrollBody=f;a.nScrollFoot=t;a.aoDrawCallback.push({fn:Fa,sName:"scrolling"});return l[0]}function Fa(a){var b=a.oScroll,c=b.sX,d=b.sXInner,e=b.sY;b=b.iBarWidth;var f=k(a.nScrollHead),g=f[0].style,h=f.children("div"),l=h[0].style,n=h.children("table");h=a.nScrollBody;var m=k(h),p=h.style,t=k(a.nScrollFoot).children("div"),v=t.children("table"),x=k(a.nTHead),r=k(a.nTable),
A=r[0],D=A.style,I=a.nTFoot?k(a.nTFoot):null,W=a.oBrowser,M=W.bScrollOversize,B=T(a.aoColumns,"nTh"),E=[],aa=[],X=[],Aa=[],mb,Ba=function(F){F=F.style;F.paddingTop="0";F.paddingBottom="0";F.borderTopWidth="0";F.borderBottomWidth="0";F.height=0};var ha=h.scrollHeight>h.clientHeight;if(a.scrollBarVis!==ha&&a.scrollBarVis!==q)a.scrollBarVis=ha,ra(a);else{a.scrollBarVis=ha;r.children("thead, tfoot").remove();if(I){var ka=I.clone().prependTo(r);var la=I.find("tr");ka=ka.find("tr")}var nb=x.clone().prependTo(r);
x=x.find("tr");ha=nb.find("tr");nb.find("th, td").removeAttr("tabindex");c||(p.width="100%",f[0].style.width="100%");k.each(La(a,nb),function(F,Y){mb=sa(a,F);Y.style.width=a.aoColumns[mb].sWidth});I&&ba(function(F){F.style.width=""},ka);f=r.outerWidth();""===c?(D.width="100%",M&&(r.find("tbody").height()>h.offsetHeight||"scroll"==m.css("overflow-y"))&&(D.width=K(r.outerWidth()-b)),f=r.outerWidth()):""!==d&&(D.width=K(d),f=r.outerWidth());ba(Ba,ha);ba(function(F){X.push(F.innerHTML);E.push(K(k(F).css("width")))},
ha);ba(function(F,Y){-1!==k.inArray(F,B)&&(F.style.width=E[Y])},x);k(ha).height(0);I&&(ba(Ba,ka),ba(function(F){Aa.push(F.innerHTML);aa.push(K(k(F).css("width")))},ka),ba(function(F,Y){F.style.width=aa[Y]},la),k(ka).height(0));ba(function(F,Y){F.innerHTML='<div class="dataTables_sizing">'+X[Y]+"</div>";F.childNodes[0].style.height="0";F.childNodes[0].style.overflow="hidden";F.style.width=E[Y]},ha);I&&ba(function(F,Y){F.innerHTML='<div class="dataTables_sizing">'+Aa[Y]+"</div>";F.childNodes[0].style.height=
"0";F.childNodes[0].style.overflow="hidden";F.style.width=aa[Y]},ka);r.outerWidth()<f?(la=h.scrollHeight>h.offsetHeight||"scroll"==m.css("overflow-y")?f+b:f,M&&(h.scrollHeight>h.offsetHeight||"scroll"==m.css("overflow-y"))&&(D.width=K(la-b)),""!==c&&""===d||ca(a,1,"Possible column misalignment",6)):la="100%";p.width=K(la);g.width=K(la);I&&(a.nScrollFoot.style.width=K(la));!e&&M&&(p.height=K(A.offsetHeight+b));c=r.outerWidth();n[0].style.width=K(c);l.width=K(c);d=r.height()>h.clientHeight||"scroll"==
m.css("overflow-y");e="padding"+(W.bScrollbarLeft?"Left":"Right");l[e]=d?b+"px":"0px";I&&(v[0].style.width=K(c),t[0].style.width=K(c),t[0].style[e]=d?b+"px":"0px");r.children("colgroup").insertBefore(r.children("thead"));m.trigger("scroll");!a.bSorted&&!a.bFiltered||a._drawHold||(h.scrollTop=0)}}function ba(a,b,c){for(var d=0,e=0,f=b.length,g,h;e<f;){g=b[e].firstChild;for(h=c?c[e].firstChild:null;g;)1===g.nodeType&&(c?a(g,h,d):a(g,d),d++),g=g.nextSibling,h=c?h.nextSibling:null;e++}}function Ya(a){var b=
a.nTable,c=a.aoColumns,d=a.oScroll,e=d.sY,f=d.sX,g=d.sXInner,h=c.length,l=Ga(a,"bVisible"),n=k("th",a.nTHead),m=b.getAttribute("width"),p=b.parentNode,t=!1,v,x=a.oBrowser;d=x.bScrollOversize;(v=b.style.width)&&-1!==v.indexOf("%")&&(m=v);for(v=0;v<l.length;v++){var r=c[l[v]];null!==r.sWidth&&(r.sWidth=Zb(r.sWidthOrig,p),t=!0)}if(d||!t&&!f&&!e&&h==na(a)&&h==n.length)for(v=0;v<h;v++)l=sa(a,v),null!==l&&(c[l].sWidth=K(n.eq(v).width()));else{h=k(b).clone().css("visibility","hidden").removeAttr("id");h.find("tbody tr").remove();
var A=k("<tr/>").appendTo(h.find("tbody"));h.find("thead, tfoot").remove();h.append(k(a.nTHead).clone()).append(k(a.nTFoot).clone());h.find("tfoot th, tfoot td").css("width","");n=La(a,h.find("thead")[0]);for(v=0;v<l.length;v++)r=c[l[v]],n[v].style.width=null!==r.sWidthOrig&&""!==r.sWidthOrig?K(r.sWidthOrig):"",r.sWidthOrig&&f&&k(n[v]).append(k("<div/>").css({width:r.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(a.aoData.length)for(v=0;v<l.length;v++)t=l[v],r=c[t],k($b(a,t)).clone(!1).append(r.sContentPadding).appendTo(A);
k("[name]",h).removeAttr("name");r=k("<div/>").css(f||e?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(h).appendTo(p);f&&g?h.width(g):f?(h.css("width","auto"),h.removeAttr("width"),h.width()<p.clientWidth&&m&&h.width(p.clientWidth)):e?h.width(p.clientWidth):m&&h.width(m);for(v=e=0;v<l.length;v++)p=k(n[v]),g=p.outerWidth()-p.width(),p=x.bBounding?Math.ceil(n[v].getBoundingClientRect().width):p.outerWidth(),e+=p,c[l[v]].sWidth=K(p-g);b.style.width=K(e);r.remove()}m&&
(b.style.width=K(m));!m&&!f||a._reszEvt||(b=function(){k(y).on("resize.DT-"+a.sInstance,gb(function(){ra(a)}))},d?setTimeout(b,1E3):b(),a._reszEvt=!0)}function Zb(a,b){if(!a)return 0;a=k("<div/>").css("width",K(a)).appendTo(b||z.body);b=a[0].offsetWidth;a.remove();return b}function $b(a,b){var c=ac(a,b);if(0>c)return null;var d=a.aoData[c];return d.nTr?d.anCells[b]:k("<td/>").html(S(a,c,b,"display"))[0]}function ac(a,b){for(var c,d=-1,e=-1,f=0,g=a.aoData.length;f<g;f++)c=S(a,f,b,"display")+"",c=c.replace(sc,
""),c=c.replace(/&nbsp;/g," "),c.length>d&&(d=c.length,e=f);return e}function K(a){return null===a?"0px":"number"==typeof a?0>a?"0px":a+"px":a.match(/\d$/)?a+"px":a}function pa(a){var b=[],c=a.aoColumns;var d=a.aaSortingFixed;var e=k.isPlainObject(d);var f=[];var g=function(m){m.length&&!Array.isArray(m[0])?f.push(m):k.merge(f,m)};Array.isArray(d)&&g(d);e&&d.pre&&g(d.pre);g(a.aaSorting);e&&d.post&&g(d.post);for(a=0;a<f.length;a++){var h=f[a][0];g=c[h].aDataSort;d=0;for(e=g.length;d<e;d++){var l=g[d];
var n=c[l].sType||"string";f[a]._idx===q&&(f[a]._idx=k.inArray(f[a][1],c[l].asSorting));b.push({src:h,col:l,dir:f[a][1],index:f[a]._idx,type:n,formatter:u.ext.type.order[n+"-pre"]})}}return b}function Hb(a){var b,c=[],d=u.ext.type.order,e=a.aoData,f=0,g=a.aiDisplayMaster;Za(a);var h=pa(a);var l=0;for(b=h.length;l<b;l++){var n=h[l];n.formatter&&f++;bc(a,n.col)}if("ssp"!=P(a)&&0!==h.length){l=0;for(b=g.length;l<b;l++)c[g[l]]=l;f===h.length?g.sort(function(m,p){var t,v=h.length,x=e[m]._aSortData,r=e[p]._aSortData;
for(t=0;t<v;t++){var A=h[t];var D=x[A.col];var I=r[A.col];D=D<I?-1:D>I?1:0;if(0!==D)return"asc"===A.dir?D:-D}D=c[m];I=c[p];return D<I?-1:D>I?1:0}):g.sort(function(m,p){var t,v=h.length,x=e[m]._aSortData,r=e[p]._aSortData;for(t=0;t<v;t++){var A=h[t];var D=x[A.col];var I=r[A.col];A=d[A.type+"-"+A.dir]||d["string-"+A.dir];D=A(D,I);if(0!==D)return D}D=c[m];I=c[p];return D<I?-1:D>I?1:0})}a.bSorted=!0}function cc(a){var b=a.aoColumns,c=pa(a);a=a.oLanguage.oAria;for(var d=0,e=b.length;d<e;d++){var f=b[d];
var g=f.asSorting;var h=f.ariaTitle||f.sTitle.replace(/<.*?>/g,"");var l=f.nTh;l.removeAttribute("aria-sort");f.bSortable&&(0<c.length&&c[0].col==d?(l.setAttribute("aria-sort","asc"==c[0].dir?"ascending":"descending"),f=g[c[0].index+1]||g[0]):f=g[0],h+="asc"===f?a.sSortAscending:a.sSortDescending);l.setAttribute("aria-label",h)}}function ob(a,b,c,d){var e=a.aaSorting,f=a.aoColumns[b].asSorting,g=function(h,l){var n=h._idx;n===q&&(n=k.inArray(h[1],f));return n+1<f.length?n+1:l?null:0};"number"===typeof e[0]&&
(e=a.aaSorting=[e]);c&&a.oFeatures.bSortMulti?(c=k.inArray(b,T(e,"0")),-1!==c?(b=g(e[c],!0),null===b&&1===e.length&&(b=0),null===b?e.splice(c,1):(e[c][1]=f[b],e[c]._idx=b)):(e.push([b,f[0],0]),e[e.length-1]._idx=0)):e.length&&e[0][0]==b?(b=g(e[0]),e.length=1,e[0][1]=f[b],e[0]._idx=b):(e.length=0,e.push([b,f[0]]),e[0]._idx=0);ja(a);"function"==typeof d&&d(a)}function eb(a,b,c,d){var e=a.aoColumns[c];pb(b,{},function(f){!1!==e.bSortable&&(a.oFeatures.bProcessing?(U(a,!0),setTimeout(function(){ob(a,
c,f.shiftKey,d);"ssp"!==P(a)&&U(a,!1)},0)):ob(a,c,f.shiftKey,d))})}function Qa(a){var b=a.aLastSort,c=a.oClasses.sSortColumn,d=pa(a),e=a.oFeatures,f;if(e.bSort&&e.bSortClasses){e=0;for(f=b.length;e<f;e++){var g=b[e].src;k(T(a.aoData,"anCells",g)).removeClass(c+(2>e?e+1:3))}e=0;for(f=d.length;e<f;e++)g=d[e].src,k(T(a.aoData,"anCells",g)).addClass(c+(2>e?e+1:3))}a.aLastSort=d}function bc(a,b){var c=a.aoColumns[b],d=u.ext.order[c.sSortDataType],e;d&&(e=d.call(a.oInstance,a,b,ta(a,b)));for(var f,g=u.ext.type.order[c.sType+
"-pre"],h=0,l=a.aoData.length;h<l;h++)if(c=a.aoData[h],c._aSortData||(c._aSortData=[]),!c._aSortData[b]||d)f=d?e[h]:S(a,h,b,"sort"),c._aSortData[b]=g?g(f):f}function Ra(a){if(a.oFeatures.bStateSave&&!a.bDestroying){var b={time:+new Date,start:a._iDisplayStart,length:a._iDisplayLength,order:k.extend(!0,[],a.aaSorting),search:Vb(a.oPreviousSearch),columns:k.map(a.aoColumns,function(c,d){return{visible:c.bVisible,search:Vb(a.aoPreSearchCols[d])}})};H(a,"aoStateSaveParams","stateSaveParams",[a,b]);a.oSavedState=
b;a.fnStateSaveCallback.call(a.oInstance,a,b)}}function dc(a,b,c){var d,e,f=a.aoColumns;b=function(h){if(h&&h.time){var l=H(a,"aoStateLoadParams","stateLoadParams",[a,h]);if(-1===k.inArray(!1,l)&&(l=a.iStateDuration,!(0<l&&h.time<+new Date-1E3*l||h.columns&&f.length!==h.columns.length))){a.oLoadedState=k.extend(!0,{},h);h.start!==q&&(a._iDisplayStart=h.start,a.iInitDisplayStart=h.start);h.length!==q&&(a._iDisplayLength=h.length);h.order!==q&&(a.aaSorting=[],k.each(h.order,function(n,m){a.aaSorting.push(m[0]>=
f.length?[0,m[1]]:m)}));h.search!==q&&k.extend(a.oPreviousSearch,Wb(h.search));if(h.columns)for(d=0,e=h.columns.length;d<e;d++)l=h.columns[d],l.visible!==q&&(f[d].bVisible=l.visible),l.search!==q&&k.extend(a.aoPreSearchCols[d],Wb(l.search));H(a,"aoStateLoaded","stateLoaded",[a,h])}}c()};if(a.oFeatures.bStateSave){var g=a.fnStateLoadCallback.call(a.oInstance,a,b);g!==q&&b(g)}else c()}function Sa(a){var b=u.settings;a=k.inArray(a,T(b,"nTable"));return-1!==a?b[a]:null}function ca(a,b,c,d){c="DataTables warning: "+
(a?"table id="+a.sTableId+" - ":"")+c;d&&(c+=". For more information about this error, please see http://datatables.net/tn/"+d);if(b)y.console&&console.log&&console.log(c);else if(b=u.ext,b=b.sErrMode||b.errMode,a&&H(a,null,"error",[a,d,c]),"alert"==b)alert(c);else{if("throw"==b)throw Error(c);"function"==typeof b&&b(a,d,c)}}function V(a,b,c,d){Array.isArray(c)?k.each(c,function(e,f){Array.isArray(f)?V(a,b,f[0],f[1]):V(a,b,f)}):(d===q&&(d=c),b[c]!==q&&(a[d]=b[c]))}function qb(a,b,c){var d;for(d in b)if(b.hasOwnProperty(d)){var e=
b[d];k.isPlainObject(e)?(k.isPlainObject(a[d])||(a[d]={}),k.extend(!0,a[d],e)):c&&"data"!==d&&"aaData"!==d&&Array.isArray(e)?a[d]=e.slice():a[d]=e}return a}function pb(a,b,c){k(a).on("click.DT",b,function(d){k(a).trigger("blur");c(d)}).on("keypress.DT",b,function(d){13===d.which&&(d.preventDefault(),c(d))}).on("selectstart.DT",function(){return!1})}function Q(a,b,c,d){c&&a[b].push({fn:c,sName:d})}function H(a,b,c,d){var e=[];b&&(e=k.map(a[b].slice().reverse(),function(f,g){return f.fn.apply(a.oInstance,
d)}));null!==c&&(b=k.Event(c+".dt"),k(a.nTable).trigger(b,d),e.push(b.result));return e}function kb(a){var b=a._iDisplayStart,c=a.fnDisplayEnd(),d=a._iDisplayLength;b>=c&&(b=c-d);b-=b%d;if(-1===d||0>b)b=0;a._iDisplayStart=b}function fb(a,b){a=a.renderer;var c=u.ext.renderer[b];return k.isPlainObject(a)&&a[b]?c[a[b]]||c._:"string"===typeof a?c[a]||c._:c._}function P(a){return a.oFeatures.bServerSide?"ssp":a.ajax||a.sAjaxSource?"ajax":"dom"}function Ca(a,b){var c=ec.numbers_length,d=Math.floor(c/2);
b<=c?a=qa(0,b):a<=d?(a=qa(0,c-2),a.push("ellipsis"),a.push(b-1)):(a>=b-1-d?a=qa(b-(c-2),b):(a=qa(a-d+2,a+d-1),a.push("ellipsis"),a.push(b-1)),a.splice(0,0,"ellipsis"),a.splice(0,0,0));a.DT_el="span";return a}function Wa(a){k.each({num:function(b){return Ta(b,a)},"num-fmt":function(b){return Ta(b,a,rb)},"html-num":function(b){return Ta(b,a,Ua)},"html-num-fmt":function(b){return Ta(b,a,Ua,rb)}},function(b,c){L.type.order[b+a+"-pre"]=c;b.match(/^html\-/)&&(L.type.search[b+a]=L.type.search.html)})}function fc(a){return function(){var b=
[Sa(this[u.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return u.ext.internal[a].apply(this,b)}}var u=function(a){this.$=function(f,g){return this.api(!0).$(f,g)};this._=function(f,g){return this.api(!0).rows(f,g).data()};this.api=function(f){return f?new C(Sa(this[L.iApiIndex])):new C(this)};this.fnAddData=function(f,g){var h=this.api(!0);f=Array.isArray(f)&&(Array.isArray(f[0])||k.isPlainObject(f[0]))?h.rows.add(f):h.row.add(f);(g===q||g)&&h.draw();return f.flatten().toArray()};
this.fnAdjustColumnSizing=function(f){var g=this.api(!0).columns.adjust(),h=g.settings()[0],l=h.oScroll;f===q||f?g.draw(!1):(""!==l.sX||""!==l.sY)&&Fa(h)};this.fnClearTable=function(f){var g=this.api(!0).clear();(f===q||f)&&g.draw()};this.fnClose=function(f){this.api(!0).row(f).child.hide()};this.fnDeleteRow=function(f,g,h){var l=this.api(!0);f=l.rows(f);var n=f.settings()[0],m=n.aoData[f[0][0]];f.remove();g&&g.call(this,n,m);(h===q||h)&&l.draw();return m};this.fnDestroy=function(f){this.api(!0).destroy(f)};
this.fnDraw=function(f){this.api(!0).draw(f)};this.fnFilter=function(f,g,h,l,n,m){n=this.api(!0);null===g||g===q?n.search(f,h,l,m):n.column(g).search(f,h,l,m);n.draw()};this.fnGetData=function(f,g){var h=this.api(!0);if(f!==q){var l=f.nodeName?f.nodeName.toLowerCase():"";return g!==q||"td"==l||"th"==l?h.cell(f,g).data():h.row(f).data()||null}return h.data().toArray()};this.fnGetNodes=function(f){var g=this.api(!0);return f!==q?g.row(f).node():g.rows().nodes().flatten().toArray()};this.fnGetPosition=
function(f){var g=this.api(!0),h=f.nodeName.toUpperCase();return"TR"==h?g.row(f).index():"TD"==h||"TH"==h?(f=g.cell(f).index(),[f.row,f.columnVisible,f.column]):null};this.fnIsOpen=function(f){return this.api(!0).row(f).child.isShown()};this.fnOpen=function(f,g,h){return this.api(!0).row(f).child(g,h).show().child()[0]};this.fnPageChange=function(f,g){f=this.api(!0).page(f);(g===q||g)&&f.draw(!1)};this.fnSetColumnVis=function(f,g,h){f=this.api(!0).column(f).visible(g);(h===q||h)&&f.columns.adjust().draw()};
this.fnSettings=function(){return Sa(this[L.iApiIndex])};this.fnSort=function(f){this.api(!0).order(f).draw()};this.fnSortListener=function(f,g,h){this.api(!0).order.listener(f,g,h)};this.fnUpdate=function(f,g,h,l,n){var m=this.api(!0);h===q||null===h?m.row(g).data(f):m.cell(g,h).data(f);(n===q||n)&&m.columns.adjust();(l===q||l)&&m.draw();return 0};this.fnVersionCheck=L.fnVersionCheck;var b=this,c=a===q,d=this.length;c&&(a={});this.oApi=this.internal=L.internal;for(var e in u.ext.internal)e&&(this[e]=
fc(e));this.each(function(){var f={},g=1<d?qb(f,a,!0):a,h=0,l;f=this.getAttribute("id");var n=!1,m=u.defaults,p=k(this);if("table"!=this.nodeName.toLowerCase())ca(null,0,"Non-table node initialisation ("+this.nodeName+")",2);else{zb(m);Ab(m.column);O(m,m,!0);O(m.column,m.column,!0);O(m,k.extend(g,p.data()),!0);var t=u.settings;h=0;for(l=t.length;h<l;h++){var v=t[h];if(v.nTable==this||v.nTHead&&v.nTHead.parentNode==this||v.nTFoot&&v.nTFoot.parentNode==this){var x=g.bRetrieve!==q?g.bRetrieve:m.bRetrieve;
if(c||x)return v.oInstance;if(g.bDestroy!==q?g.bDestroy:m.bDestroy){v.oInstance.fnDestroy();break}else{ca(v,0,"Cannot reinitialise DataTable",3);return}}if(v.sTableId==this.id){t.splice(h,1);break}}if(null===f||""===f)this.id=f="DataTables_Table_"+u.ext._unique++;var r=k.extend(!0,{},u.models.oSettings,{sDestroyWidth:p[0].style.width,sInstance:f,sTableId:f});r.nTable=this;r.oApi=b.internal;r.oInit=g;t.push(r);r.oInstance=1===b.length?b:p.dataTable();zb(g);ma(g.oLanguage);g.aLengthMenu&&!g.iDisplayLength&&
(g.iDisplayLength=Array.isArray(g.aLengthMenu[0])?g.aLengthMenu[0][0]:g.aLengthMenu[0]);g=qb(k.extend(!0,{},m),g);V(r.oFeatures,g,"bPaginate bLengthChange bFilter bSort bSortMulti bInfo bProcessing bAutoWidth bSortClasses bServerSide bDeferRender".split(" "));V(r,g,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback",
"fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]);V(r.oScroll,g,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]);V(r.oLanguage,g,"fnInfoCallback");Q(r,"aoDrawCallback",g.fnDrawCallback,"user");Q(r,"aoServerParams",g.fnServerParams,"user");Q(r,"aoStateSaveParams",g.fnStateSaveParams,"user");Q(r,"aoStateLoadParams",
g.fnStateLoadParams,"user");Q(r,"aoStateLoaded",g.fnStateLoaded,"user");Q(r,"aoRowCallback",g.fnRowCallback,"user");Q(r,"aoRowCreatedCallback",g.fnCreatedRow,"user");Q(r,"aoHeaderCallback",g.fnHeaderCallback,"user");Q(r,"aoFooterCallback",g.fnFooterCallback,"user");Q(r,"aoInitComplete",g.fnInitComplete,"user");Q(r,"aoPreDrawCallback",g.fnPreDrawCallback,"user");r.rowIdFn=ia(g.rowId);Bb(r);var A=r.oClasses;k.extend(A,u.ext.classes,g.oClasses);p.addClass(A.sTable);r.iInitDisplayStart===q&&(r.iInitDisplayStart=
g.iDisplayStart,r._iDisplayStart=g.iDisplayStart);null!==g.iDeferLoading&&(r.bDeferLoading=!0,f=Array.isArray(g.iDeferLoading),r._iRecordsDisplay=f?g.iDeferLoading[0]:g.iDeferLoading,r._iRecordsTotal=f?g.iDeferLoading[1]:g.iDeferLoading);var D=r.oLanguage;k.extend(!0,D,g.oLanguage);D.sUrl?(k.ajax({dataType:"json",url:D.sUrl,success:function(B){ma(B);O(m.oLanguage,B);k.extend(!0,D,B);H(r,null,"i18n",[r]);za(r)},error:function(){za(r)}}),n=!0):H(r,null,"i18n",[r]);null===g.asStripeClasses&&(r.asStripeClasses=
[A.sStripeOdd,A.sStripeEven]);f=r.asStripeClasses;var I=p.children("tbody").find("tr").eq(0);-1!==k.inArray(!0,k.map(f,function(B,E){return I.hasClass(B)}))&&(k("tbody tr",this).removeClass(f.join(" ")),r.asDestroyStripes=f.slice());f=[];t=this.getElementsByTagName("thead");0!==t.length&&(wa(r.aoHeader,t[0]),f=La(r));if(null===g.aoColumns)for(t=[],h=0,l=f.length;h<l;h++)t.push(null);else t=g.aoColumns;h=0;for(l=t.length;h<l;h++)Xa(r,f?f[h]:null);Db(r,g.aoColumnDefs,t,function(B,E){Ea(r,B,E)});if(I.length){var W=
function(B,E){return null!==B.getAttribute("data-"+E)?E:null};k(I[0]).children("th, td").each(function(B,E){var aa=r.aoColumns[B];if(aa.mData===B){var X=W(E,"sort")||W(E,"order");E=W(E,"filter")||W(E,"search");if(null!==X||null!==E)aa.mData={_:B+".display",sort:null!==X?B+".@data-"+X:q,type:null!==X?B+".@data-"+X:q,filter:null!==E?B+".@data-"+E:q},Ea(r,B)}})}var M=r.oFeatures;f=function(){if(g.aaSorting===q){var B=r.aaSorting;h=0;for(l=B.length;h<l;h++)B[h][1]=r.aoColumns[h].asSorting[0]}Qa(r);M.bSort&&
Q(r,"aoDrawCallback",function(){if(r.bSorted){var X=pa(r),Aa={};k.each(X,function(mb,Ba){Aa[Ba.src]=Ba.dir});H(r,null,"order",[r,X,Aa]);cc(r)}});Q(r,"aoDrawCallback",function(){(r.bSorted||"ssp"===P(r)||M.bDeferRender)&&Qa(r)},"sc");B=p.children("caption").each(function(){this._captionSide=k(this).css("caption-side")});var E=p.children("thead");0===E.length&&(E=k("<thead/>").appendTo(p));r.nTHead=E[0];var aa=p.children("tbody");0===aa.length&&(aa=k("<tbody/>").insertAfter(E));r.nTBody=aa[0];E=p.children("tfoot");
0===E.length&&0<B.length&&(""!==r.oScroll.sX||""!==r.oScroll.sY)&&(E=k("<tfoot/>").appendTo(p));0===E.length||0===E.children().length?p.addClass(A.sNoFooter):0<E.length&&(r.nTFoot=E[0],wa(r.aoFooter,r.nTFoot));if(g.aaData)for(h=0;h<g.aaData.length;h++)ea(r,g.aaData[h]);else(r.bDeferLoading||"dom"==P(r))&&Ha(r,k(r.nTBody).children("tr"));r.aiDisplay=r.aiDisplayMaster.slice();r.bInitialised=!0;!1===n&&za(r)};g.bStateSave?(M.bStateSave=!0,Q(r,"aoDrawCallback",Ra,"state_save"),dc(r,g,f)):f()}});b=null;
return this},L,w,J,sb={},gc=/[\r\n\u2028]/g,Ua=/<.*?>/g,tc=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,uc=/(\/|\.|\*|\+|\?|\||\(|\)|\[|\]|\{|\}|\\|\$|\^|\-)/g,rb=/['\u00A0,$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,Z=function(a){return a&&!0!==a&&"-"!==a?!1:!0},hc=function(a){var b=parseInt(a,10);return!isNaN(b)&&isFinite(a)?b:null},ic=function(a,b){sb[b]||(sb[b]=new RegExp(ib(b),"g"));return"string"===typeof a&&"."!==b?a.replace(/\./g,"").replace(sb[b],"."):
a},tb=function(a,b,c){var d="string"===typeof a;if(Z(a))return!0;b&&d&&(a=ic(a,b));c&&d&&(a=a.replace(rb,""));return!isNaN(parseFloat(a))&&isFinite(a)},jc=function(a,b,c){return Z(a)?!0:Z(a)||"string"===typeof a?tb(a.replace(Ua,""),b,c)?!0:null:null},T=function(a,b,c){var d=[],e=0,f=a.length;if(c!==q)for(;e<f;e++)a[e]&&a[e][b]&&d.push(a[e][b][c]);else for(;e<f;e++)a[e]&&d.push(a[e][b]);return d},Da=function(a,b,c,d){var e=[],f=0,g=b.length;if(d!==q)for(;f<g;f++)a[b[f]][c]&&e.push(a[b[f]][c][d]);else for(;f<
g;f++)e.push(a[b[f]][c]);return e},qa=function(a,b){var c=[];if(b===q){b=0;var d=a}else d=b,b=a;for(a=b;a<d;a++)c.push(a);return c},kc=function(a){for(var b=[],c=0,d=a.length;c<d;c++)a[c]&&b.push(a[c]);return b},Ka=function(a){a:{if(!(2>a.length)){var b=a.slice().sort();for(var c=b[0],d=1,e=b.length;d<e;d++){if(b[d]===c){b=!1;break a}c=b[d]}}b=!0}if(b)return a.slice();b=[];e=a.length;var f,g=0;d=0;a:for(;d<e;d++){c=a[d];for(f=0;f<g;f++)if(b[f]===c)continue a;b.push(c);g++}return b},lc=function(a,
b){if(Array.isArray(b))for(var c=0;c<b.length;c++)lc(a,b[c]);else a.push(b);return a};Array.isArray||(Array.isArray=function(a){return"[object Array]"===Object.prototype.toString.call(a)});String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")});u.util={throttle:function(a,b){var c=b!==q?b:200,d,e;return function(){var f=this,g=+new Date,h=arguments;d&&g<d+c?(clearTimeout(e),e=setTimeout(function(){d=q;a.apply(f,h)},c)):(d=g,a.apply(f,
h))}},escapeRegex:function(a){return a.replace(uc,"\\$1")}};var R=function(a,b,c){a[b]!==q&&(a[c]=a[b])},ua=/\[.*?\]$/,oa=/\(\)$/,ib=u.util.escapeRegex,Pa=k("<div>")[0],rc=Pa.textContent!==q,sc=/<.*?>/g,gb=u.util.throttle,mc=[],N=Array.prototype,vc=function(a){var b,c=u.settings,d=k.map(c,function(f,g){return f.nTable});if(a){if(a.nTable&&a.oApi)return[a];if(a.nodeName&&"table"===a.nodeName.toLowerCase()){var e=k.inArray(a,d);return-1!==e?[c[e]]:null}if(a&&"function"===typeof a.settings)return a.settings().toArray();
"string"===typeof a?b=k(a):a instanceof k&&(b=a)}else return[];if(b)return b.map(function(f){e=k.inArray(this,d);return-1!==e?c[e]:null}).toArray()};var C=function(a,b){if(!(this instanceof C))return new C(a,b);var c=[],d=function(g){(g=vc(g))&&c.push.apply(c,g)};if(Array.isArray(a))for(var e=0,f=a.length;e<f;e++)d(a[e]);else d(a);this.context=Ka(c);b&&k.merge(this,b);this.selector={rows:null,cols:null,opts:null};C.extend(this,this,mc)};u.Api=C;k.extend(C.prototype,{any:function(){return 0!==this.count()},
concat:N.concat,context:[],count:function(){return this.flatten().length},each:function(a){for(var b=0,c=this.length;b<c;b++)a.call(this,this[b],b,this);return this},eq:function(a){var b=this.context;return b.length>a?new C(b[a],this[a]):null},filter:function(a){var b=[];if(N.filter)b=N.filter.call(this,a,this);else for(var c=0,d=this.length;c<d;c++)a.call(this,this[c],c,this)&&b.push(this[c]);return new C(this.context,b)},flatten:function(){var a=[];return new C(this.context,a.concat.apply(a,this.toArray()))},
join:N.join,indexOf:N.indexOf||function(a,b){b=b||0;for(var c=this.length;b<c;b++)if(this[b]===a)return b;return-1},iterator:function(a,b,c,d){var e=[],f,g,h=this.context,l,n=this.selector;"string"===typeof a&&(d=c,c=b,b=a,a=!1);var m=0;for(f=h.length;m<f;m++){var p=new C(h[m]);if("table"===b){var t=c.call(p,h[m],m);t!==q&&e.push(t)}else if("columns"===b||"rows"===b)t=c.call(p,h[m],this[m],m),t!==q&&e.push(t);else if("column"===b||"column-rows"===b||"row"===b||"cell"===b){var v=this[m];"column-rows"===
b&&(l=Va(h[m],n.opts));var x=0;for(g=v.length;x<g;x++)t=v[x],t="cell"===b?c.call(p,h[m],t.row,t.column,m,x):c.call(p,h[m],t,m,x,l),t!==q&&e.push(t)}}return e.length||d?(a=new C(h,a?e.concat.apply([],e):e),b=a.selector,b.rows=n.rows,b.cols=n.cols,b.opts=n.opts,a):this},lastIndexOf:N.lastIndexOf||function(a,b){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(a){var b=[];if(N.map)b=N.map.call(this,a,this);else for(var c=0,d=this.length;c<d;c++)b.push(a.call(this,this[c],
c));return new C(this.context,b)},pluck:function(a){return this.map(function(b){return b[a]})},pop:N.pop,push:N.push,reduce:N.reduce||function(a,b){return Cb(this,a,b,0,this.length,1)},reduceRight:N.reduceRight||function(a,b){return Cb(this,a,b,this.length-1,-1,-1)},reverse:N.reverse,selector:null,shift:N.shift,slice:function(){return new C(this.context,this)},sort:N.sort,splice:N.splice,toArray:function(){return N.slice.call(this)},to$:function(){return k(this)},toJQuery:function(){return k(this)},
unique:function(){return new C(this.context,Ka(this))},unshift:N.unshift});C.extend=function(a,b,c){if(c.length&&b&&(b instanceof C||b.__dt_wrapper)){var d,e=function(h,l,n){return function(){var m=l.apply(h,arguments);C.extend(m,m,n.methodExt);return m}};var f=0;for(d=c.length;f<d;f++){var g=c[f];b[g.name]="function"===g.type?e(a,g.val,g):"object"===g.type?{}:g.val;b[g.name].__dt_wrapper=!0;C.extend(a,b[g.name],g.propExt)}}};C.register=w=function(a,b){if(Array.isArray(a))for(var c=0,d=a.length;c<
d;c++)C.register(a[c],b);else{d=a.split(".");var e=mc,f;a=0;for(c=d.length;a<c;a++){var g=(f=-1!==d[a].indexOf("()"))?d[a].replace("()",""):d[a];a:{var h=0;for(var l=e.length;h<l;h++)if(e[h].name===g){h=e[h];break a}h=null}h||(h={name:g,val:{},methodExt:[],propExt:[],type:"object"},e.push(h));a===c-1?(h.val=b,h.type="function"===typeof b?"function":k.isPlainObject(b)?"object":"other"):e=f?h.methodExt:h.propExt}}};C.registerPlural=J=function(a,b,c){C.register(a,c);C.register(b,function(){var d=c.apply(this,
arguments);return d===this?this:d instanceof C?d.length?Array.isArray(d[0])?new C(d.context,d[0]):d[0]:q:d})};var nc=function(a,b){if(Array.isArray(a))return k.map(a,function(d){return nc(d,b)});if("number"===typeof a)return[b[a]];var c=k.map(b,function(d,e){return d.nTable});return k(c).filter(a).map(function(d){d=k.inArray(this,c);return b[d]}).toArray()};w("tables()",function(a){return a!==q&&null!==a?new C(nc(a,this.context)):this});w("table()",function(a){a=this.tables(a);var b=a.context;return b.length?
new C(b[0]):a});J("tables().nodes()","table().node()",function(){return this.iterator("table",function(a){return a.nTable},1)});J("tables().body()","table().body()",function(){return this.iterator("table",function(a){return a.nTBody},1)});J("tables().header()","table().header()",function(){return this.iterator("table",function(a){return a.nTHead},1)});J("tables().footer()","table().footer()",function(){return this.iterator("table",function(a){return a.nTFoot},1)});J("tables().containers()","table().container()",
function(){return this.iterator("table",function(a){return a.nTableWrapper},1)});w("draw()",function(a){return this.iterator("table",function(b){"page"===a?fa(b):("string"===typeof a&&(a="full-hold"===a?!1:!0),ja(b,!1===a))})});w("page()",function(a){return a===q?this.page.info().page:this.iterator("table",function(b){lb(b,a)})});w("page.info()",function(a){if(0===this.context.length)return q;a=this.context[0];var b=a._iDisplayStart,c=a.oFeatures.bPaginate?a._iDisplayLength:-1,d=a.fnRecordsDisplay(),
e=-1===c;return{page:e?0:Math.floor(b/c),pages:e?1:Math.ceil(d/c),start:b,end:a.fnDisplayEnd(),length:c,recordsTotal:a.fnRecordsTotal(),recordsDisplay:d,serverSide:"ssp"===P(a)}});w("page.len()",function(a){return a===q?0!==this.context.length?this.context[0]._iDisplayLength:q:this.iterator("table",function(b){jb(b,a)})});var oc=function(a,b,c){if(c){var d=new C(a);d.one("draw",function(){c(d.ajax.json())})}if("ssp"==P(a))ja(a,b);else{U(a,!0);var e=a.jqXHR;e&&4!==e.readyState&&e.abort();Ma(a,[],function(f){Ia(a);
f=Na(a,f);for(var g=0,h=f.length;g<h;g++)ea(a,f[g]);ja(a,b);U(a,!1)})}};w("ajax.json()",function(){var a=this.context;if(0<a.length)return a[0].json});w("ajax.params()",function(){var a=this.context;if(0<a.length)return a[0].oAjaxData});w("ajax.reload()",function(a,b){return this.iterator("table",function(c){oc(c,!1===b,a)})});w("ajax.url()",function(a){var b=this.context;if(a===q){if(0===b.length)return q;b=b[0];return b.ajax?k.isPlainObject(b.ajax)?b.ajax.url:b.ajax:b.sAjaxSource}return this.iterator("table",
function(c){k.isPlainObject(c.ajax)?c.ajax.url=a:c.ajax=a})});w("ajax.url().load()",function(a,b){return this.iterator("table",function(c){oc(c,!1===b,a)})});var ub=function(a,b,c,d,e){var f=[],g,h,l;var n=typeof b;b&&"string"!==n&&"function"!==n&&b.length!==q||(b=[b]);n=0;for(h=b.length;n<h;n++){var m=b[n]&&b[n].split&&!b[n].match(/[\[\(:]/)?b[n].split(","):[b[n]];var p=0;for(l=m.length;p<l;p++)(g=c("string"===typeof m[p]?m[p].trim():m[p]))&&g.length&&(f=f.concat(g))}a=L.selector[a];if(a.length)for(n=
0,h=a.length;n<h;n++)f=a[n](d,e,f);return Ka(f)},vb=function(a){a||(a={});a.filter&&a.search===q&&(a.search=a.filter);return k.extend({search:"none",order:"current",page:"all"},a)},wb=function(a){for(var b=0,c=a.length;b<c;b++)if(0<a[b].length)return a[0]=a[b],a[0].length=1,a.length=1,a.context=[a.context[b]],a;a.length=0;return a},Va=function(a,b){var c=[],d=a.aiDisplay;var e=a.aiDisplayMaster;var f=b.search;var g=b.order;b=b.page;if("ssp"==P(a))return"removed"===f?[]:qa(0,e.length);if("current"==
b)for(g=a._iDisplayStart,a=a.fnDisplayEnd();g<a;g++)c.push(d[g]);else if("current"==g||"applied"==g)if("none"==f)c=e.slice();else if("applied"==f)c=d.slice();else{if("removed"==f){var h={};g=0;for(a=d.length;g<a;g++)h[d[g]]=null;c=k.map(e,function(l){return h.hasOwnProperty(l)?null:l})}}else if("index"==g||"original"==g)for(g=0,a=a.aoData.length;g<a;g++)"none"==f?c.push(g):(e=k.inArray(g,d),(-1===e&&"removed"==f||0<=e&&"applied"==f)&&c.push(g));return c},wc=function(a,b,c){var d;return ub("row",b,
function(e){var f=hc(e),g=a.aoData;if(null!==f&&!c)return[f];d||(d=Va(a,c));if(null!==f&&-1!==k.inArray(f,d))return[f];if(null===e||e===q||""===e)return d;if("function"===typeof e)return k.map(d,function(l){var n=g[l];return e(l,n._aData,n.nTr)?l:null});if(e.nodeName){f=e._DT_RowIndex;var h=e._DT_CellIndex;if(f!==q)return g[f]&&g[f].nTr===e?[f]:[];if(h)return g[h.row]&&g[h.row].nTr===e.parentNode?[h.row]:[];f=k(e).closest("*[data-dt-row]");return f.length?[f.data("dt-row")]:[]}if("string"===typeof e&&
"#"===e.charAt(0)&&(f=a.aIds[e.replace(/^#/,"")],f!==q))return[f.idx];f=kc(Da(a.aoData,d,"nTr"));return k(f).filter(e).map(function(){return this._DT_RowIndex}).toArray()},a,c)};w("rows()",function(a,b){a===q?a="":k.isPlainObject(a)&&(b=a,a="");b=vb(b);var c=this.iterator("table",function(d){return wc(d,a,b)},1);c.selector.rows=a;c.selector.opts=b;return c});w("rows().nodes()",function(){return this.iterator("row",function(a,b){return a.aoData[b].nTr||q},1)});w("rows().data()",function(){return this.iterator(!0,
"rows",function(a,b){return Da(a.aoData,b,"_aData")},1)});J("rows().cache()","row().cache()",function(a){return this.iterator("row",function(b,c){b=b.aoData[c];return"search"===a?b._aFilterData:b._aSortData},1)});J("rows().invalidate()","row().invalidate()",function(a){return this.iterator("row",function(b,c){va(b,c,a)})});J("rows().indexes()","row().index()",function(){return this.iterator("row",function(a,b){return b},1)});J("rows().ids()","row().id()",function(a){for(var b=[],c=this.context,d=
0,e=c.length;d<e;d++)for(var f=0,g=this[d].length;f<g;f++){var h=c[d].rowIdFn(c[d].aoData[this[d][f]]._aData);b.push((!0===a?"#":"")+h)}return new C(c,b)});J("rows().remove()","row().remove()",function(){var a=this;this.iterator("row",function(b,c,d){var e=b.aoData,f=e[c],g,h;e.splice(c,1);var l=0;for(g=e.length;l<g;l++){var n=e[l];var m=n.anCells;null!==n.nTr&&(n.nTr._DT_RowIndex=l);if(null!==m)for(n=0,h=m.length;n<h;n++)m[n]._DT_CellIndex.row=l}Ja(b.aiDisplayMaster,c);Ja(b.aiDisplay,c);Ja(a[d],
c,!1);0<b._iRecordsDisplay&&b._iRecordsDisplay--;kb(b);c=b.rowIdFn(f._aData);c!==q&&delete b.aIds[c]});this.iterator("table",function(b){for(var c=0,d=b.aoData.length;c<d;c++)b.aoData[c].idx=c});return this});w("rows.add()",function(a){var b=this.iterator("table",function(d){var e,f=[];var g=0;for(e=a.length;g<e;g++){var h=a[g];h.nodeName&&"TR"===h.nodeName.toUpperCase()?f.push(Ha(d,h)[0]):f.push(ea(d,h))}return f},1),c=this.rows(-1);c.pop();k.merge(c,b);return c});w("row()",function(a,b){return wb(this.rows(a,
b))});w("row().data()",function(a){var b=this.context;if(a===q)return b.length&&this.length?b[0].aoData[this[0]]._aData:q;var c=b[0].aoData[this[0]];c._aData=a;Array.isArray(a)&&c.nTr&&c.nTr.id&&da(b[0].rowId)(a,c.nTr.id);va(b[0],this[0],"data");return this});w("row().node()",function(){var a=this.context;return a.length&&this.length?a[0].aoData[this[0]].nTr||null:null});w("row.add()",function(a){a instanceof k&&a.length&&(a=a[0]);var b=this.iterator("table",function(c){return a.nodeName&&"TR"===
a.nodeName.toUpperCase()?Ha(c,a)[0]:ea(c,a)});return this.row(b[0])});var xc=function(a,b,c,d){var e=[],f=function(g,h){if(Array.isArray(g)||g instanceof k)for(var l=0,n=g.length;l<n;l++)f(g[l],h);else g.nodeName&&"tr"===g.nodeName.toLowerCase()?e.push(g):(l=k("<tr><td></td></tr>").addClass(h),k("td",l).addClass(h).html(g)[0].colSpan=na(a),e.push(l[0]))};f(c,d);b._details&&b._details.detach();b._details=k(e);b._detailsShow&&b._details.insertAfter(b.nTr)},xb=function(a,b){var c=a.context;c.length&&
(a=c[0].aoData[b!==q?b:a[0]])&&a._details&&(a._details.remove(),a._detailsShow=q,a._details=q)},pc=function(a,b){var c=a.context;c.length&&a.length&&(a=c[0].aoData[a[0]],a._details&&((a._detailsShow=b)?a._details.insertAfter(a.nTr):a._details.detach(),yc(c[0])))},yc=function(a){var b=new C(a),c=a.aoData;b.off("draw.dt.DT_details column-visibility.dt.DT_details destroy.dt.DT_details");0<T(c,"_details").length&&(b.on("draw.dt.DT_details",function(d,e){a===e&&b.rows({page:"current"}).eq(0).each(function(f){f=
c[f];f._detailsShow&&f._details.insertAfter(f.nTr)})}),b.on("column-visibility.dt.DT_details",function(d,e,f,g){if(a===e)for(e=na(e),f=0,g=c.length;f<g;f++)d=c[f],d._details&&d._details.children("td[colspan]").attr("colspan",e)}),b.on("destroy.dt.DT_details",function(d,e){if(a===e)for(d=0,e=c.length;d<e;d++)c[d]._details&&xb(b,d)}))};w("row().child()",function(a,b){var c=this.context;if(a===q)return c.length&&this.length?c[0].aoData[this[0]]._details:q;!0===a?this.child.show():!1===a?xb(this):c.length&&
this.length&&xc(c[0],c[0].aoData[this[0]],a,b);return this});w(["row().child.show()","row().child().show()"],function(a){pc(this,!0);return this});w(["row().child.hide()","row().child().hide()"],function(){pc(this,!1);return this});w(["row().child.remove()","row().child().remove()"],function(){xb(this);return this});w("row().child.isShown()",function(){var a=this.context;return a.length&&this.length?a[0].aoData[this[0]]._detailsShow||!1:!1});var zc=/^([^:]+):(name|visIdx|visible)$/,qc=function(a,
b,c,d,e){c=[];d=0;for(var f=e.length;d<f;d++)c.push(S(a,e[d],b));return c},Ac=function(a,b,c){var d=a.aoColumns,e=T(d,"sName"),f=T(d,"nTh");return ub("column",b,function(g){var h=hc(g);if(""===g)return qa(d.length);if(null!==h)return[0<=h?h:d.length+h];if("function"===typeof g){var l=Va(a,c);return k.map(d,function(p,t){return g(t,qc(a,t,0,0,l),f[t])?t:null})}var n="string"===typeof g?g.match(zc):"";if(n)switch(n[2]){case "visIdx":case "visible":h=parseInt(n[1],10);if(0>h){var m=k.map(d,function(p,
t){return p.bVisible?t:null});return[m[m.length+h]]}return[sa(a,h)];case "name":return k.map(e,function(p,t){return p===n[1]?t:null});default:return[]}if(g.nodeName&&g._DT_CellIndex)return[g._DT_CellIndex.column];h=k(f).filter(g).map(function(){return k.inArray(this,f)}).toArray();if(h.length||!g.nodeName)return h;h=k(g).closest("*[data-dt-column]");return h.length?[h.data("dt-column")]:[]},a,c)};w("columns()",function(a,b){a===q?a="":k.isPlainObject(a)&&(b=a,a="");b=vb(b);var c=this.iterator("table",
function(d){return Ac(d,a,b)},1);c.selector.cols=a;c.selector.opts=b;return c});J("columns().header()","column().header()",function(a,b){return this.iterator("column",function(c,d){return c.aoColumns[d].nTh},1)});J("columns().footer()","column().footer()",function(a,b){return this.iterator("column",function(c,d){return c.aoColumns[d].nTf},1)});J("columns().data()","column().data()",function(){return this.iterator("column-rows",qc,1)});J("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",
function(a,b){return a.aoColumns[b].mData},1)});J("columns().cache()","column().cache()",function(a){return this.iterator("column-rows",function(b,c,d,e,f){return Da(b.aoData,f,"search"===a?"_aFilterData":"_aSortData",c)},1)});J("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(a,b,c,d,e){return Da(a.aoData,e,"anCells",b)},1)});J("columns().visible()","column().visible()",function(a,b){var c=this,d=this.iterator("column",function(e,f){if(a===q)return e.aoColumns[f].bVisible;
var g=e.aoColumns,h=g[f],l=e.aoData,n;if(a!==q&&h.bVisible!==a){if(a){var m=k.inArray(!0,T(g,"bVisible"),f+1);g=0;for(n=l.length;g<n;g++){var p=l[g].nTr;e=l[g].anCells;p&&p.insertBefore(e[f],e[m]||null)}}else k(T(e.aoData,"anCells",f)).detach();h.bVisible=a}});a!==q&&this.iterator("table",function(e){xa(e,e.aoHeader);xa(e,e.aoFooter);e.aiDisplay.length||k(e.nTBody).find("td[colspan]").attr("colspan",na(e));Ra(e);c.iterator("column",function(f,g){H(f,null,"column-visibility",[f,g,a,b])});(b===q||b)&&
c.columns.adjust()});return d});J("columns().indexes()","column().index()",function(a){return this.iterator("column",function(b,c){return"visible"===a?ta(b,c):c},1)});w("columns.adjust()",function(){return this.iterator("table",function(a){ra(a)},1)});w("column.index()",function(a,b){if(0!==this.context.length){var c=this.context[0];if("fromVisible"===a||"toData"===a)return sa(c,b);if("fromData"===a||"toVisible"===a)return ta(c,b)}});w("column()",function(a,b){return wb(this.columns(a,b))});var Bc=
function(a,b,c){var d=a.aoData,e=Va(a,c),f=kc(Da(d,e,"anCells")),g=k(lc([],f)),h,l=a.aoColumns.length,n,m,p,t,v,x;return ub("cell",b,function(r){var A="function"===typeof r;if(null===r||r===q||A){n=[];m=0;for(p=e.length;m<p;m++)for(h=e[m],t=0;t<l;t++)v={row:h,column:t},A?(x=d[h],r(v,S(a,h,t),x.anCells?x.anCells[t]:null)&&n.push(v)):n.push(v);return n}if(k.isPlainObject(r))return r.column!==q&&r.row!==q&&-1!==k.inArray(r.row,e)?[r]:[];A=g.filter(r).map(function(D,I){return{row:I._DT_CellIndex.row,
column:I._DT_CellIndex.column}}).toArray();if(A.length||!r.nodeName)return A;x=k(r).closest("*[data-dt-row]");return x.length?[{row:x.data("dt-row"),column:x.data("dt-column")}]:[]},a,c)};w("cells()",function(a,b,c){k.isPlainObject(a)&&(a.row===q?(c=a,a=null):(c=b,b=null));k.isPlainObject(b)&&(c=b,b=null);if(null===b||b===q)return this.iterator("table",function(m){return Bc(m,a,vb(c))});var d=c?{page:c.page,order:c.order,search:c.search}:{},e=this.columns(b,d),f=this.rows(a,d),g,h,l,n;d=this.iterator("table",
function(m,p){m=[];g=0;for(h=f[p].length;g<h;g++)for(l=0,n=e[p].length;l<n;l++)m.push({row:f[p][g],column:e[p][l]});return m},1);d=c&&c.selected?this.cells(d,c):d;k.extend(d.selector,{cols:b,rows:a,opts:c});return d});J("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(a,b,c){return(a=a.aoData[b])&&a.anCells?a.anCells[c]:q},1)});w("cells().data()",function(){return this.iterator("cell",function(a,b,c){return S(a,b,c)},1)});J("cells().cache()","cell().cache()",function(a){a=
"search"===a?"_aFilterData":"_aSortData";return this.iterator("cell",function(b,c,d){return b.aoData[c][a][d]},1)});J("cells().render()","cell().render()",function(a){return this.iterator("cell",function(b,c,d){return S(b,c,d,a)},1)});J("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(a,b,c){return{row:b,column:c,columnVisible:ta(a,c)}},1)});J("cells().invalidate()","cell().invalidate()",function(a){return this.iterator("cell",function(b,c,d){va(b,c,a,d)})});w("cell()",
function(a,b,c){return wb(this.cells(a,b,c))});w("cell().data()",function(a){var b=this.context,c=this[0];if(a===q)return b.length&&c.length?S(b[0],c[0].row,c[0].column):q;Eb(b[0],c[0].row,c[0].column,a);va(b[0],c[0].row,"data",c[0].column);return this});w("order()",function(a,b){var c=this.context;if(a===q)return 0!==c.length?c[0].aaSorting:q;"number"===typeof a?a=[[a,b]]:a.length&&!Array.isArray(a[0])&&(a=Array.prototype.slice.call(arguments));return this.iterator("table",function(d){d.aaSorting=
a.slice()})});w("order.listener()",function(a,b,c){return this.iterator("table",function(d){eb(d,a,b,c)})});w("order.fixed()",function(a){if(!a){var b=this.context;b=b.length?b[0].aaSortingFixed:q;return Array.isArray(b)?{pre:b}:b}return this.iterator("table",function(c){c.aaSortingFixed=k.extend(!0,{},a)})});w(["columns().order()","column().order()"],function(a){var b=this;return this.iterator("table",function(c,d){var e=[];k.each(b[d],function(f,g){e.push([g,a])});c.aaSorting=e})});w("search()",
function(a,b,c,d){var e=this.context;return a===q?0!==e.length?e[0].oPreviousSearch.sSearch:q:this.iterator("table",function(f){f.oFeatures.bFilter&&ya(f,k.extend({},f.oPreviousSearch,{sSearch:a+"",bRegex:null===b?!1:b,bSmart:null===c?!0:c,bCaseInsensitive:null===d?!0:d}),1)})});J("columns().search()","column().search()",function(a,b,c,d){return this.iterator("column",function(e,f){var g=e.aoPreSearchCols;if(a===q)return g[f].sSearch;e.oFeatures.bFilter&&(k.extend(g[f],{sSearch:a+"",bRegex:null===
b?!1:b,bSmart:null===c?!0:c,bCaseInsensitive:null===d?!0:d}),ya(e,e.oPreviousSearch,1))})});w("state()",function(){return this.context.length?this.context[0].oSavedState:null});w("state.clear()",function(){return this.iterator("table",function(a){a.fnStateSaveCallback.call(a.oInstance,a,{})})});w("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null});w("state.save()",function(){return this.iterator("table",function(a){Ra(a)})});u.versionCheck=u.fnVersionCheck=function(a){var b=
u.version.split(".");a=a.split(".");for(var c,d,e=0,f=a.length;e<f;e++)if(c=parseInt(b[e],10)||0,d=parseInt(a[e],10)||0,c!==d)return c>d;return!0};u.isDataTable=u.fnIsDataTable=function(a){var b=k(a).get(0),c=!1;if(a instanceof u.Api)return!0;k.each(u.settings,function(d,e){d=e.nScrollHead?k("table",e.nScrollHead)[0]:null;var f=e.nScrollFoot?k("table",e.nScrollFoot)[0]:null;if(e.nTable===b||d===b||f===b)c=!0});return c};u.tables=u.fnTables=function(a){var b=!1;k.isPlainObject(a)&&(b=a.api,a=a.visible);
var c=k.map(u.settings,function(d){if(!a||a&&k(d.nTable).is(":visible"))return d.nTable});return b?new C(c):c};u.camelToHungarian=O;w("$()",function(a,b){b=this.rows(b).nodes();b=k(b);return k([].concat(b.filter(a).toArray(),b.find(a).toArray()))});k.each(["on","one","off"],function(a,b){w(b+"()",function(){var c=Array.prototype.slice.call(arguments);c[0]=k.map(c[0].split(/\s/),function(e){return e.match(/\.dt\b/)?e:e+".dt"}).join(" ");var d=k(this.tables().nodes());d[b].apply(d,c);return this})});
w("clear()",function(){return this.iterator("table",function(a){Ia(a)})});w("settings()",function(){return new C(this.context,this.context)});w("init()",function(){var a=this.context;return a.length?a[0].oInit:null});w("data()",function(){return this.iterator("table",function(a){return T(a.aoData,"_aData")}).flatten()});w("destroy()",function(a){a=a||!1;return this.iterator("table",function(b){var c=b.nTableWrapper.parentNode,d=b.oClasses,e=b.nTable,f=b.nTBody,g=b.nTHead,h=b.nTFoot,l=k(e);f=k(f);
var n=k(b.nTableWrapper),m=k.map(b.aoData,function(t){return t.nTr}),p;b.bDestroying=!0;H(b,"aoDestroyCallback","destroy",[b]);a||(new C(b)).columns().visible(!0);n.off(".DT").find(":not(tbody *)").off(".DT");k(y).off(".DT-"+b.sInstance);e!=g.parentNode&&(l.children("thead").detach(),l.append(g));h&&e!=h.parentNode&&(l.children("tfoot").detach(),l.append(h));b.aaSorting=[];b.aaSortingFixed=[];Qa(b);k(m).removeClass(b.asStripeClasses.join(" "));k("th, td",g).removeClass(d.sSortable+" "+d.sSortableAsc+
" "+d.sSortableDesc+" "+d.sSortableNone);f.children().detach();f.append(m);g=a?"remove":"detach";l[g]();n[g]();!a&&c&&(c.insertBefore(e,b.nTableReinsertBefore),l.css("width",b.sDestroyWidth).removeClass(d.sTable),(p=b.asDestroyStripes.length)&&f.children().each(function(t){k(this).addClass(b.asDestroyStripes[t%p])}));c=k.inArray(b,u.settings);-1!==c&&u.settings.splice(c,1)})});k.each(["column","row","cell"],function(a,b){w(b+"s().every()",function(c){var d=this.selector.opts,e=this;return this.iterator(b,
function(f,g,h,l,n){c.call(e[b](g,"cell"===b?h:d,"cell"===b?d:q),g,h,l,n)})})});w("i18n()",function(a,b,c){var d=this.context[0];a=ia(a)(d.oLanguage);a===q&&(a=b);c!==q&&k.isPlainObject(a)&&(a=a[c]!==q?a[c]:a._);return a.replace("%d",c)});u.version="1.10.25";u.settings=[];u.models={};u.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0};u.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1};u.models.oColumn=
{idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null};u.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,
aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(a){return a.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,
fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(a){try{return JSON.parse((-1===a.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+a.sInstance+"_"+location.pathname))}catch(b){return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(a,b){try{(-1===a.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+a.sInstance+"_"+location.pathname,JSON.stringify(b))}catch(c){}},fnStateSaveParams:null,
iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",
sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"Processing...",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:k.extend({},u.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"};G(u.defaults);u.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc",
"desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null};G(u.defaults.column);u.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,
sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],
aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,jqXHR:null,json:q,oAjaxData:q,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,
iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==P(this)?1*this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==P(this)?1*this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var a=this._iDisplayLength,b=this._iDisplayStart,c=b+a,d=this.aiDisplay.length,e=this.oFeatures,f=e.bPaginate;
return e.bServerSide?!1===f||-1===a?b+d:Math.min(b+a,this._iRecordsDisplay):!f||c>d||-1===a?d:c},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null};u.ext=L={buttons:{},classes:{},build:"dt/dt-1.10.25/b-1.7.1/b-colvis-1.7.1/r-2.2.9/sc-2.0.4/sp-1.3.0",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:u.fnVersionCheck,
iApiIndex:0,oJUIClasses:{},sVersion:u.version};k.extend(L,{afnFiltering:L.search,aTypes:L.type.detect,ofnSearch:L.type.search,oSort:L.type.order,afnSortData:L.order,aoFeatures:L.feature,oApi:L.internal,oStdClasses:L.classes,oPagination:L.pager});k.extend(u.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",
sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_desc_disabled",sSortableDesc:"sorting_asc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",
sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""});var ec=u.ext.pager;k.extend(ec,{simple:function(a,b){return["previous","next"]},full:function(a,b){return["first","previous","next","last"]},numbers:function(a,b){return[Ca(a,b)]},simple_numbers:function(a,b){return["previous",Ca(a,b),"next"]},
full_numbers:function(a,b){return["first","previous",Ca(a,b),"next","last"]},first_last_numbers:function(a,b){return["first",Ca(a,b),"last"]},_numbers:Ca,numbers_length:7});k.extend(!0,u.ext.renderer,{pageButton:{_:function(a,b,c,d,e,f){var g=a.oClasses,h=a.oLanguage.oPaginate,l=a.oLanguage.oAria.paginate||{},n,m,p=0,t=function(x,r){var A,D=g.sPageButtonDisabled,I=function(E){lb(a,E.data.action,!0)};var W=0;for(A=r.length;W<A;W++){var M=r[W];if(Array.isArray(M)){var B=k("<"+(M.DT_el||"div")+"/>").appendTo(x);
t(B,M)}else{n=null;m=M;B=a.iTabIndex;switch(M){case "ellipsis":x.append('<span class="ellipsis">&#x2026;</span>');break;case "first":n=h.sFirst;0===e&&(B=-1,m+=" "+D);break;case "previous":n=h.sPrevious;0===e&&(B=-1,m+=" "+D);break;case "next":n=h.sNext;if(0===f||e===f-1)B=-1,m+=" "+D;break;case "last":n=h.sLast;if(0===f||e===f-1)B=-1,m+=" "+D;break;default:n=a.fnFormatNumber(M+1),m=e===M?g.sPageButtonActive:""}null!==n&&(B=k("<a>",{"class":g.sPageButton+" "+m,"aria-controls":a.sTableId,"aria-label":l[M],
"data-dt-idx":p,tabindex:B,id:0===c&&"string"===typeof M?a.sTableId+"_"+M:null}).html(n).appendTo(x),pb(B,{action:M},I),p++)}}};try{var v=k(b).find(z.activeElement).data("dt-idx")}catch(x){}t(k(b).empty(),d);v!==q&&k(b).find("[data-dt-idx="+v+"]").trigger("focus")}}});k.extend(u.ext.type.detect,[function(a,b){b=b.oLanguage.sDecimal;return tb(a,b)?"num"+b:null},function(a,b){if(a&&!(a instanceof Date)&&!tc.test(a))return null;b=Date.parse(a);return null!==b&&!isNaN(b)||Z(a)?"date":null},function(a,
b){b=b.oLanguage.sDecimal;return tb(a,b,!0)?"num-fmt"+b:null},function(a,b){b=b.oLanguage.sDecimal;return jc(a,b)?"html-num"+b:null},function(a,b){b=b.oLanguage.sDecimal;return jc(a,b,!0)?"html-num-fmt"+b:null},function(a,b){return Z(a)||"string"===typeof a&&-1!==a.indexOf("<")?"html":null}]);k.extend(u.ext.type.search,{html:function(a){return Z(a)?a:"string"===typeof a?a.replace(gc," ").replace(Ua,""):""},string:function(a){return Z(a)?a:"string"===typeof a?a.replace(gc," "):a}});var Ta=function(a,
b,c,d){if(0!==a&&(!a||"-"===a))return-Infinity;b&&(a=ic(a,b));a.replace&&(c&&(a=a.replace(c,"")),d&&(a=a.replace(d,"")));return 1*a};k.extend(L.type.order,{"date-pre":function(a){a=Date.parse(a);return isNaN(a)?-Infinity:a},"html-pre":function(a){return Z(a)?"":a.replace?a.replace(/<.*?>/g,"").toLowerCase():a+""},"string-pre":function(a){return Z(a)?"":"string"===typeof a?a.toLowerCase():a.toString?a.toString():""},"string-asc":function(a,b){return a<b?-1:a>b?1:0},"string-desc":function(a,b){return a<
b?1:a>b?-1:0}});Wa("");k.extend(!0,u.ext.renderer,{header:{_:function(a,b,c,d){k(a.nTable).on("order.dt.DT",function(e,f,g,h){a===f&&(e=c.idx,b.removeClass(d.sSortAsc+" "+d.sSortDesc).addClass("asc"==h[e]?d.sSortAsc:"desc"==h[e]?d.sSortDesc:c.sSortingClass))})},jqueryui:function(a,b,c,d){k("<div/>").addClass(d.sSortJUIWrapper).append(b.contents()).append(k("<span/>").addClass(d.sSortIcon+" "+c.sSortingClassJUI)).appendTo(b);k(a.nTable).on("order.dt.DT",function(e,f,g,h){a===f&&(e=c.idx,b.removeClass(d.sSortAsc+
" "+d.sSortDesc).addClass("asc"==h[e]?d.sSortAsc:"desc"==h[e]?d.sSortDesc:c.sSortingClass),b.find("span."+d.sSortIcon).removeClass(d.sSortJUIAsc+" "+d.sSortJUIDesc+" "+d.sSortJUI+" "+d.sSortJUIAscAllowed+" "+d.sSortJUIDescAllowed).addClass("asc"==h[e]?d.sSortJUIAsc:"desc"==h[e]?d.sSortJUIDesc:c.sSortingClassJUI))})}}});var yb=function(a){return"string"===typeof a?a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):a};u.render={number:function(a,b,c,d,e){return{display:function(f){if("number"!==
typeof f&&"string"!==typeof f)return f;var g=0>f?"-":"",h=parseFloat(f);if(isNaN(h))return yb(f);h=h.toFixed(c);f=Math.abs(h);h=parseInt(f,10);f=c?b+(f-h).toFixed(c).substring(2):"";0===h&&0===parseFloat(f)&&(g="");return g+(d||"")+h.toString().replace(/\B(?=(\d{3})+(?!\d))/g,a)+f+(e||"")}}},text:function(){return{display:yb,filter:yb}}};k.extend(u.ext.internal,{_fnExternApiFunc:fc,_fnBuildAjax:Ma,_fnAjaxUpdate:Gb,_fnAjaxParameters:Pb,_fnAjaxUpdateDraw:Qb,_fnAjaxDataSrc:Na,_fnAddColumn:Xa,_fnColumnOptions:Ea,
_fnAdjustColumnSizing:ra,_fnVisibleToColumnIndex:sa,_fnColumnIndexToVisible:ta,_fnVisbleColumns:na,_fnGetColumns:Ga,_fnColumnTypes:Za,_fnApplyColumnDefs:Db,_fnHungarianMap:G,_fnCamelToHungarian:O,_fnLanguageCompat:ma,_fnBrowserDetect:Bb,_fnAddData:ea,_fnAddTr:Ha,_fnNodeToDataIndex:function(a,b){return b._DT_RowIndex!==q?b._DT_RowIndex:null},_fnNodeToColumnIndex:function(a,b,c){return k.inArray(c,a.aoData[b].anCells)},_fnGetCellData:S,_fnSetCellData:Eb,_fnSplitObjNotation:bb,_fnGetObjectDataFn:ia,
_fnSetObjectDataFn:da,_fnGetDataMaster:cb,_fnClearTable:Ia,_fnDeleteIndex:Ja,_fnInvalidate:va,_fnGetRowElements:ab,_fnCreateTr:$a,_fnBuildHead:Fb,_fnDrawHead:xa,_fnDraw:fa,_fnReDraw:ja,_fnAddOptionsHtml:Ib,_fnDetectHeader:wa,_fnGetUniqueThs:La,_fnFeatureHtmlFilter:Kb,_fnFilterComplete:ya,_fnFilterCustom:Tb,_fnFilterColumn:Sb,_fnFilter:Rb,_fnFilterCreateSearch:hb,_fnEscapeRegex:ib,_fnFilterData:Ub,_fnFeatureHtmlInfo:Nb,_fnUpdateInfo:Xb,_fnInfoMacros:Yb,_fnInitialise:za,_fnInitComplete:Oa,_fnLengthChange:jb,
_fnFeatureHtmlLength:Jb,_fnFeatureHtmlPaginate:Ob,_fnPageChange:lb,_fnFeatureHtmlProcessing:Lb,_fnProcessingDisplay:U,_fnFeatureHtmlTable:Mb,_fnScrollDraw:Fa,_fnApplyToChildren:ba,_fnCalculateColumnWidths:Ya,_fnThrottle:gb,_fnConvertToWidth:Zb,_fnGetWidestNode:$b,_fnGetMaxLenString:ac,_fnStringToCss:K,_fnSortFlatten:pa,_fnSort:Hb,_fnSortAria:cc,_fnSortListener:ob,_fnSortAttachListener:eb,_fnSortingClasses:Qa,_fnSortData:bc,_fnSaveState:Ra,_fnLoadState:dc,_fnSettingsFromNode:Sa,_fnLog:ca,_fnMap:V,
_fnBindAction:pb,_fnCallbackReg:Q,_fnCallbackFire:H,_fnLengthOverflow:kb,_fnRenderer:fb,_fnDataSource:P,_fnRowAttributes:db,_fnExtend:qb,_fnCalculateEnd:function(){}});k.fn.dataTable=u;u.$=k;k.fn.dataTableSettings=u.settings;k.fn.dataTableExt=u.ext;k.fn.DataTable=function(a){return k(this).dataTable(a).api()};k.each(u,function(a,b){k.fn.DataTable[a]=b});return k.fn.dataTable});


/*!
 DataTables styling integration
 ©2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net")(a,b).$);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){return c.fn.dataTable});


/*!
 Buttons for DataTables 1.7.1
 ©2016-2021 SpryMedia Ltd - datatables.net/license
*/
(function(e){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(y){return e(y,window,document)}):"object"===typeof exports?module.exports=function(y,w){y||(y=window);w&&w.fn.dataTable||(w=require("datatables.net")(y,w).$);return e(w,y,y.document)}:e(jQuery,window,document)})(function(e,y,w,r){function B(a,b,c){e.fn.animate?a.stop().fadeIn(b,c):(a.css("display","block"),c&&c.call(a))}function C(a,b,c){e.fn.animate?a.stop().fadeOut(b,c):(a.css("display","none"),c&&c.call(a))}
function E(a,b){a=new q.Api(a);b=b?b:a.init().buttons||q.defaults.buttons;return(new t(a,b)).container()}var q=e.fn.dataTable,I=0,J=0,x=q.ext.buttons,t=function(a,b){if(!(this instanceof t))return function(c){return(new t(c,a)).container()};"undefined"===typeof b&&(b={});!0===b&&(b={});Array.isArray(b)&&(b={buttons:b});this.c=e.extend(!0,{},t.defaults,b);b.buttons&&(this.c.buttons=b.buttons);this.s={dt:new q.Api(a),buttons:[],listenKeys:"",namespace:"dtb"+I++};this.dom={container:e("<"+this.c.dom.container.tag+
"/>").addClass(this.c.dom.container.className)};this._constructor()};e.extend(t.prototype,{action:function(a,b){a=this._nodeToButton(a);if(b===r)return a.conf.action;a.conf.action=b;return this},active:function(a,b){var c=this._nodeToButton(a);a=this.c.dom.button.active;c=e(c.node);if(b===r)return c.hasClass(a);c.toggleClass(a,b===r?!0:b);return this},add:function(a,b){var c=this.s.buttons;if("string"===typeof b){b=b.split("-");var d=this.s;c=0;for(var f=b.length-1;c<f;c++)d=d.buttons[1*b[c]];c=d.buttons;
b=1*b[b.length-1]}this._expandButton(c,a,d!==r,b);this._draw();return this},container:function(){return this.dom.container},disable:function(a){a=this._nodeToButton(a);e(a.node).addClass(this.c.dom.button.disabled).attr("disabled",!0);return this},destroy:function(){e("body").off("keyup."+this.s.namespace);var a=this.s.buttons.slice(),b;var c=0;for(b=a.length;c<b;c++)this.remove(a[c].node);this.dom.container.remove();a=this.s.dt.settings()[0];c=0;for(b=a.length;c<b;c++)if(a.inst===this){a.splice(c,
1);break}return this},enable:function(a,b){if(!1===b)return this.disable(a);a=this._nodeToButton(a);e(a.node).removeClass(this.c.dom.button.disabled).removeAttr("disabled");return this},name:function(){return this.c.name},node:function(a){if(!a)return this.dom.container;a=this._nodeToButton(a);return e(a.node)},processing:function(a,b){var c=this.s.dt,d=this._nodeToButton(a);if(b===r)return e(d.node).hasClass("processing");e(d.node).toggleClass("processing",b);e(c.table().node()).triggerHandler("buttons-processing.dt",
[b,c.button(a),c,e(a),d.conf]);return this},remove:function(a){var b=this._nodeToButton(a),c=this._nodeToHost(a),d=this.s.dt;if(b.buttons.length)for(var f=b.buttons.length-1;0<=f;f--)this.remove(b.buttons[f].node);b.conf.destroy&&b.conf.destroy.call(d.button(a),d,e(a),b.conf);this._removeKey(b.conf);e(b.node).remove();a=e.inArray(b,c);c.splice(a,1);return this},text:function(a,b){var c=this._nodeToButton(a);a=this.c.dom.collection.buttonLiner;a=c.inCollection&&a&&a.tag?a.tag:this.c.dom.buttonLiner.tag;
var d=this.s.dt,f=e(c.node),h=function(m){return"function"===typeof m?m(d,f,c.conf):m};if(b===r)return h(c.conf.text);c.conf.text=b;a?f.children(a).html(h(b)):f.html(h(b));return this},_constructor:function(){var a=this,b=this.s.dt,c=b.settings()[0],d=this.c.buttons;c._buttons||(c._buttons=[]);c._buttons.push({inst:this,name:this.c.name});for(var f=0,h=d.length;f<h;f++)this.add(d[f]);b.on("destroy",function(m,g){g===c&&a.destroy()});e("body").on("keyup."+this.s.namespace,function(m){if(!w.activeElement||
w.activeElement===w.body){var g=String.fromCharCode(m.keyCode).toLowerCase();-1!==a.s.listenKeys.toLowerCase().indexOf(g)&&a._keypress(g,m)}})},_addKey:function(a){a.key&&(this.s.listenKeys+=e.isPlainObject(a.key)?a.key.key:a.key)},_draw:function(a,b){a||(a=this.dom.container,b=this.s.buttons);a.children().detach();for(var c=0,d=b.length;c<d;c++)a.append(b[c].inserter),a.append(" "),b[c].buttons&&b[c].buttons.length&&this._draw(b[c].collection,b[c].buttons)},_expandButton:function(a,b,c,d){var f=
this.s.dt,h=0;b=Array.isArray(b)?b:[b];for(var m=0,g=b.length;m<g;m++){var n=this._resolveExtends(b[m]);if(n)if(Array.isArray(n))this._expandButton(a,n,c,d);else{var k=this._buildButton(n,c);k&&(d!==r&&null!==d?(a.splice(d,0,k),d++):a.push(k),k.conf.buttons&&(k.collection=e("<"+this.c.dom.collection.tag+"/>"),k.conf._collection=k.collection,this._expandButton(k.buttons,k.conf.buttons,!0,d)),n.init&&n.init.call(f.button(k.node),f,e(k.node),n),h++)}}},_buildButton:function(a,b){var c=this.c.dom.button,
d=this.c.dom.buttonLiner,f=this.c.dom.collection,h=this.s.dt,m=function(p){return"function"===typeof p?p(h,k,a):p};b&&f.button&&(c=f.button);b&&f.buttonLiner&&(d=f.buttonLiner);if(a.available&&!a.available(h,a))return!1;var g=function(p,l,v,u){u.action.call(l.button(v),p,l,v,u);e(l.table().node()).triggerHandler("buttons-action.dt",[l.button(v),l,v,u])};f=a.tag||c.tag;var n=a.clickBlurs===r?!0:a.clickBlurs,k=e("<"+f+"/>").addClass(c.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",
this.s.dt.table().node().id).on("click.dtb",function(p){p.preventDefault();!k.hasClass(c.disabled)&&a.action&&g(p,h,k,a);n&&k.trigger("blur")}).on("keyup.dtb",function(p){13===p.keyCode&&!k.hasClass(c.disabled)&&a.action&&g(p,h,k,a)});"a"===f.toLowerCase()&&k.attr("href","#");"button"===f.toLowerCase()&&k.attr("type","button");d.tag?(f=e("<"+d.tag+"/>").html(m(a.text)).addClass(d.className),"a"===d.tag.toLowerCase()&&f.attr("href","#"),k.append(f)):k.html(m(a.text));!1===a.enabled&&k.addClass(c.disabled);
a.className&&k.addClass(a.className);a.titleAttr&&k.attr("title",m(a.titleAttr));a.attr&&k.attr(a.attr);a.namespace||(a.namespace=".dt-button-"+J++);d=(d=this.c.dom.buttonContainer)&&d.tag?e("<"+d.tag+"/>").addClass(d.className).append(k):k;this._addKey(a);this.c.buttonCreated&&(d=this.c.buttonCreated(a,d));return{conf:a,node:k.get(0),inserter:d,buttons:[],inCollection:b,collection:null}},_nodeToButton:function(a,b){b||(b=this.s.buttons);for(var c=0,d=b.length;c<d;c++){if(b[c].node===a)return b[c];
if(b[c].buttons.length){var f=this._nodeToButton(a,b[c].buttons);if(f)return f}}},_nodeToHost:function(a,b){b||(b=this.s.buttons);for(var c=0,d=b.length;c<d;c++){if(b[c].node===a)return b;if(b[c].buttons.length){var f=this._nodeToHost(a,b[c].buttons);if(f)return f}}},_keypress:function(a,b){if(!b._buttonsHandled){var c=function(d){for(var f=0,h=d.length;f<h;f++){var m=d[f].conf,g=d[f].node;m.key&&(m.key===a?(b._buttonsHandled=!0,e(g).click()):!e.isPlainObject(m.key)||m.key.key!==a||m.key.shiftKey&&
!b.shiftKey||m.key.altKey&&!b.altKey||m.key.ctrlKey&&!b.ctrlKey||m.key.metaKey&&!b.metaKey||(b._buttonsHandled=!0,e(g).click()));d[f].buttons.length&&c(d[f].buttons)}};c(this.s.buttons)}},_removeKey:function(a){if(a.key){var b=e.isPlainObject(a.key)?a.key.key:a.key;a=this.s.listenKeys.split("");b=e.inArray(b,a);a.splice(b,1);this.s.listenKeys=a.join("")}},_resolveExtends:function(a){var b=this.s.dt,c,d=function(g){for(var n=0;!e.isPlainObject(g)&&!Array.isArray(g);){if(g===r)return;if("function"===
typeof g){if(g=g(b,a),!g)return!1}else if("string"===typeof g){if(!x[g])throw"Unknown button type: "+g;g=x[g]}n++;if(30<n)throw"Buttons: Too many iterations";}return Array.isArray(g)?g:e.extend({},g)};for(a=d(a);a&&a.extend;){if(!x[a.extend])throw"Cannot extend unknown button type: "+a.extend;var f=d(x[a.extend]);if(Array.isArray(f))return f;if(!f)return!1;var h=f.className;a=e.extend({},f,a);h&&a.className!==h&&(a.className=h+" "+a.className);var m=a.postfixButtons;if(m){a.buttons||(a.buttons=[]);
h=0;for(c=m.length;h<c;h++)a.buttons.push(m[h]);a.postfixButtons=null}if(m=a.prefixButtons){a.buttons||(a.buttons=[]);h=0;for(c=m.length;h<c;h++)a.buttons.splice(h,0,m[h]);a.prefixButtons=null}a.extend=f.extend}return a},_popover:function(a,b,c){var d=this.c,f=e.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",contentClassName:d.dom.collection.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,rightAlignClassName:"dt-button-right",
tag:d.dom.collection.tag},c),h=b.node(),m=function(){C(e(".dt-button-collection"),f.fade,function(){e(this).detach()});e(b.buttons('[aria-haspopup="true"][aria-expanded="true"]').nodes()).attr("aria-expanded","false");e("div.dt-button-background").off("click.dtb-collection");t.background(!1,f.backgroundClassName,f.fade,h);e("body").off(".dtb-collection");b.off("buttons-action.b-internal")};!1===a&&m();c=e(b.buttons('[aria-haspopup="true"][aria-expanded="true"]').nodes());c.length&&(h=c.eq(0),m());
c=e("<div/>").addClass("dt-button-collection").addClass(f.collectionLayout).css("display","none");a=e(a).addClass(f.contentClassName).attr("role","menu").appendTo(c);h.attr("aria-expanded","true");h.parents("body")[0]!==w.body&&(h=w.body.lastChild);f.collectionTitle&&c.prepend('<div class="dt-button-collection-title">'+f.collectionTitle+"</div>");B(c.insertAfter(h),f.fade);d=e(b.table().container());var g=c.css("position");"dt-container"===f.align&&(h=h.parent(),c.css("width",d.width()));if("absolute"===
g){var n=h.position();g=e(b.node()).position();c.css({top:g.top+h.outerHeight(),left:n.left});n=c.outerHeight();var k=d.offset().top+d.height();k=g.top+h.outerHeight()+n-k;var p=g.top-n,l=d.offset().top;g=g.top-n-5;(k>l-p||f.dropup)&&-g<l&&c.css("top",g);g=d.offset().left;d=d.width();d=g+d;n=c.offset().left;k=c.width();k=n+k;p=h.offset().left;l=h.outerWidth();var v=p+l;c.hasClass(f.rightAlignClassName)||c.hasClass(f.leftAlignClassName)||"dt-container"===f.align?(l=0,c.hasClass(f.rightAlignClassName)?
(l=v-k,g>n+l&&(g-=n+l,d-=k+l,l=g>d?l+d:l+g)):(l=g-n,d<k+l&&(g-=n+l,d-=k+l,l=g>d?l+d:l+g))):(d=h.offset().top,l=0,l="button-right"===f.align?v-k:p-n);c.css("left",c.position().left+l)}else d=c.height()/2,d>e(y).height()/2&&(d=e(y).height()/2),c.css("marginTop",-1*d);f.background&&t.background(!0,f.backgroundClassName,f.fade,h);e("div.dt-button-background").on("click.dtb-collection",function(){});e("body").on("click.dtb-collection",function(u){var z=e.fn.addBack?"addBack":"andSelf",F=e(u.target).parent()[0];
(!e(u.target).parents()[z]().filter(a).length&&!e(F).hasClass("dt-buttons")||e(u.target).hasClass("dt-button-background"))&&m()}).on("keyup.dtb-collection",function(u){27===u.keyCode&&m()});f.autoClose&&setTimeout(function(){b.on("buttons-action.b-internal",function(u,z,F,K){K[0]!==h[0]&&m()})},0);e(c).trigger("buttons-popover.dt")}});t.background=function(a,b,c,d){c===r&&(c=400);d||(d=w.body);a?B(e("<div/>").addClass(b).css("display","none").insertAfter(d),c):C(e("div."+b),c,function(){e(this).removeClass(b).remove()})};
t.instanceSelector=function(a,b){if(a===r||null===a)return e.map(b,function(h){return h.inst});var c=[],d=e.map(b,function(h){return h.name}),f=function(h){if(Array.isArray(h))for(var m=0,g=h.length;m<g;m++)f(h[m]);else"string"===typeof h?-1!==h.indexOf(",")?f(h.split(",")):(h=e.inArray(h.trim(),d),-1!==h&&c.push(b[h].inst)):"number"===typeof h&&c.push(b[h].inst)};f(a);return c};t.buttonSelector=function(a,b){for(var c=[],d=function(g,n,k){for(var p,l,v=0,u=n.length;v<u;v++)if(p=n[v])l=k!==r?k+v:
v+"",g.push({node:p.node,name:p.conf.name,idx:l}),p.buttons&&d(g,p.buttons,l+"-")},f=function(g,n){var k,p=[];d(p,n.s.buttons);var l=e.map(p,function(v){return v.node});if(Array.isArray(g)||g instanceof e)for(l=0,k=g.length;l<k;l++)f(g[l],n);else if(null===g||g===r||"*"===g)for(l=0,k=p.length;l<k;l++)c.push({inst:n,node:p[l].node});else if("number"===typeof g)c.push({inst:n,node:n.s.buttons[g].node});else if("string"===typeof g)if(-1!==g.indexOf(","))for(p=g.split(","),l=0,k=p.length;l<k;l++)f(p[l].trim(),
n);else if(g.match(/^\d+(\-\d+)*$/))l=e.map(p,function(v){return v.idx}),c.push({inst:n,node:p[e.inArray(g,l)].node});else if(-1!==g.indexOf(":name"))for(g=g.replace(":name",""),l=0,k=p.length;l<k;l++)p[l].name===g&&c.push({inst:n,node:p[l].node});else e(l).filter(g).each(function(){c.push({inst:n,node:this})});else"object"===typeof g&&g.nodeName&&(p=e.inArray(g,l),-1!==p&&c.push({inst:n,node:l[p]}))},h=0,m=a.length;h<m;h++)f(b,a[h]);return c};t.stripData=function(a,b){if("string"!==typeof a)return a;
a=a.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"");a=a.replace(/<!\-\-.*?\-\->/g,"");if(!b||b.stripHtml)a=a.replace(/<[^>]*>/g,"");if(!b||b.trim)a=a.replace(/^\s+|\s+$/g,"");if(!b||b.stripNewlines)a=a.replace(/\n/g," ");if(!b||b.decodeEntities)G.innerHTML=a,a=G.value;return a};t.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{tag:"div",className:""},button:{tag:"button",className:"dt-button",
active:"active",disabled:"disabled"},buttonLiner:{tag:"span",className:""}}};t.version="1.7.1";e.extend(x,{collection:{text:function(a){return a.i18n("buttons.collection","Collection")},className:"buttons-collection",init:function(a,b,c){b.attr("aria-expanded",!1)},action:function(a,b,c,d){a.stopPropagation();d._collection.parents("body").length?this.popover(!1,d):this.popover(d._collection,d)},attr:{"aria-haspopup":!0}},copy:function(a,b){if(x.copyHtml5)return"copyHtml5"},csv:function(a,b){if(x.csvHtml5&&
x.csvHtml5.available(a,b))return"csvHtml5"},excel:function(a,b){if(x.excelHtml5&&x.excelHtml5.available(a,b))return"excelHtml5"},pdf:function(a,b){if(x.pdfHtml5&&x.pdfHtml5.available(a,b))return"pdfHtml5"},pageLength:function(a){a=a.settings()[0].aLengthMenu;var b=[],c=[];if(Array.isArray(a[0]))b=a[0],c=a[1];else for(var d=0;d<a.length;d++){var f=a[d];e.isPlainObject(f)?(b.push(f.value),c.push(f.label)):(b.push(f),c.push(f))}return{extend:"collection",text:function(h){return h.i18n("buttons.pageLength",
{"-1":"Show all rows",_:"Show %d rows"},h.page.len())},className:"buttons-page-length",autoClose:!0,buttons:e.map(b,function(h,m){return{text:c[m],className:"button-page-length",action:function(g,n){n.page.len(h).draw()},init:function(g,n,k){var p=this;n=function(){p.active(g.page.len()===h)};g.on("length.dt"+k.namespace,n);n()},destroy:function(g,n,k){g.off("length.dt"+k.namespace)}}}),init:function(h,m,g){var n=this;h.on("length.dt"+g.namespace,function(){n.text(g.text)})},destroy:function(h,m,
g){h.off("length.dt"+g.namespace)}}}});q.Api.register("buttons()",function(a,b){b===r&&(b=a,a=r);this.selector.buttonGroup=a;var c=this.iterator(!0,"table",function(d){if(d._buttons)return t.buttonSelector(t.instanceSelector(a,d._buttons),b)},!0);c._groupSelector=a;return c});q.Api.register("button()",function(a,b){a=this.buttons(a,b);1<a.length&&a.splice(1,a.length);return a});q.Api.registerPlural("buttons().active()","button().active()",function(a){return a===r?this.map(function(b){return b.inst.active(b.node)}):
this.each(function(b){b.inst.active(b.node,a)})});q.Api.registerPlural("buttons().action()","button().action()",function(a){return a===r?this.map(function(b){return b.inst.action(b.node)}):this.each(function(b){b.inst.action(b.node,a)})});q.Api.register(["buttons().enable()","button().enable()"],function(a){return this.each(function(b){b.inst.enable(b.node,a)})});q.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(a){a.inst.disable(a.node)})});q.Api.registerPlural("buttons().nodes()",
"button().node()",function(){var a=e();e(this.each(function(b){a=a.add(b.inst.node(b.node))}));return a});q.Api.registerPlural("buttons().processing()","button().processing()",function(a){return a===r?this.map(function(b){return b.inst.processing(b.node)}):this.each(function(b){b.inst.processing(b.node,a)})});q.Api.registerPlural("buttons().text()","button().text()",function(a){return a===r?this.map(function(b){return b.inst.text(b.node)}):this.each(function(b){b.inst.text(b.node,a)})});q.Api.registerPlural("buttons().trigger()",
"button().trigger()",function(){return this.each(function(a){a.inst.node(a.node).trigger("click")})});q.Api.register("button().popover()",function(a,b){return this.map(function(c){return c.inst._popover(a,this.button(this[0].node),b)})});q.Api.register("buttons().containers()",function(){var a=e(),b=this._groupSelector;this.iterator(!0,"table",function(c){if(c._buttons){c=t.instanceSelector(b,c._buttons);for(var d=0,f=c.length;d<f;d++)a=a.add(c[d].container())}});return a});q.Api.register("buttons().container()",
function(){return this.containers().eq(0)});q.Api.register("button().add()",function(a,b){var c=this.context;c.length&&(c=t.instanceSelector(this._groupSelector,c[0]._buttons),c.length&&c[0].add(b,a));return this.button(this._groupSelector,a)});q.Api.register("buttons().destroy()",function(){this.pluck("inst").unique().each(function(a){a.destroy()});return this});q.Api.registerPlural("buttons().remove()","buttons().remove()",function(){this.each(function(a){a.inst.remove(a.node)});return this});var A;
q.Api.register("buttons.info()",function(a,b,c){var d=this;if(!1===a)return this.off("destroy.btn-info"),C(e("#datatables_buttons_info"),400,function(){e(this).remove()}),clearTimeout(A),A=null,this;A&&clearTimeout(A);e("#datatables_buttons_info").length&&e("#datatables_buttons_info").remove();a=a?"<h2>"+a+"</h2>":"";B(e('<div id="datatables_buttons_info" class="dt-button-info"/>').html(a).append(e("<div/>")["string"===typeof b?"html":"append"](b)).css("display","none").appendTo("body"));c!==r&&0!==
c&&(A=setTimeout(function(){d.buttons.info(!1)},c));this.on("destroy.btn-info",function(){d.buttons.info(!1)});return this});q.Api.register("buttons.exportData()",function(a){if(this.context.length)return L(new q.Api(this.context[0]),a)});q.Api.register("buttons.exportInfo()",function(a){a||(a={});var b=a;var c="*"===b.filename&&"*"!==b.title&&b.title!==r&&null!==b.title&&""!==b.title?b.title:b.filename;"function"===typeof c&&(c=c());c===r||null===c?c=null:(-1!==c.indexOf("*")&&(c=c.replace("*",e("head > title").text()).trim()),
c=c.replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""),(b=D(b.extension))||(b=""),c+=b);b=D(a.title);b=null===b?null:-1!==b.indexOf("*")?b.replace("*",e("head > title").text()||"Exported data"):b;return{filename:c,title:b,messageTop:H(this,a.message||a.messageTop,"top"),messageBottom:H(this,a.messageBottom,"bottom")}});var D=function(a){return null===a||a===r?null:"function"===typeof a?a():a},H=function(a,b,c){b=D(b);if(null===b)return null;a=e("caption",a.table().container()).eq(0);return"*"===
b?a.css("caption-side")!==c?null:a.length?a.text():"":b},G=e("<textarea/>")[0],L=function(a,b){var c=e.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(u){return t.stripData(u,c)},footer:function(u){return t.stripData(u,c)},body:function(u){return t.stripData(u,c)}},customizeData:null},b);b=a.columns(c.columns).indexes().map(function(u){var z=a.column(u).header();return c.format.header(z.innerHTML,
u,z)}).toArray();var d=a.table().footer()?a.columns(c.columns).indexes().map(function(u){var z=a.column(u).footer();return c.format.footer(z?z.innerHTML:"",u,z)}).toArray():null,f=e.extend({},c.modifier);a.select&&"function"===typeof a.select.info&&f.selected===r&&a.rows(c.rows,e.extend({selected:!0},f)).any()&&e.extend(f,{selected:!0});f=a.rows(c.rows,f).indexes().toArray();var h=a.cells(f,c.columns);f=h.render(c.orthogonal).toArray();h=h.nodes().toArray();for(var m=b.length,g=[],n=0,k=0,p=0<m?f.length/
m:0;k<p;k++){for(var l=[m],v=0;v<m;v++)l[v]=c.format.body(f[n],k,v,h[n]),n++;g[k]=l}b={header:b,footer:d,body:g};c.customizeData&&c.customizeData(b);return b};e.fn.dataTable.Buttons=t;e.fn.DataTable.Buttons=t;e(w).on("init.dt plugin-init.dt",function(a,b){"dt"===a.namespace&&(a=b.oInit.buttons||q.defaults.buttons)&&!b._buttons&&(new t(b,a)).container()});q.ext.feature.push({fnInit:E,cFeature:"B"});q.ext.features&&q.ext.features.register("buttons",E);return t});


/*!
 DataTables styling wrapper for Buttons
 ©2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-dt","datatables.net-buttons"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-dt")(a,b).$);b.fn.dataTable.Buttons||require("datatables.net-buttons")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){return c.fn.dataTable});


/*!
 Column visibility buttons for Buttons and DataTables.
 2016 SpryMedia Ltd - datatables.net/license
*/
(function(g){"function"===typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(e){return g(e,window,document)}):"object"===typeof exports?module.exports=function(e,f){e||(e=window);f&&f.fn.dataTable||(f=require("datatables.net")(e,f).$);f.fn.dataTable.Buttons||require("datatables.net-buttons")(e,f);return g(f,e,e.document)}:g(jQuery,window,document)})(function(g,e,f,l){e=g.fn.dataTable;g.extend(e.ext.buttons,{colvis:function(b,a){return{extend:"collection",
text:function(c){return c.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",buttons:[{extend:"columnsToggle",columns:a.columns,columnText:a.columnText}]}},columnsToggle:function(b,a){return b.columns(a.columns).indexes().map(function(c){return{extend:"columnToggle",columns:c,columnText:a.columnText}}).toArray()},columnToggle:function(b,a){return{extend:"columnVisibility",columns:a.columns,columnText:a.columnText}},columnsVisibility:function(b,a){return b.columns(a.columns).indexes().map(function(c){return{extend:"columnVisibility",
columns:c,visibility:a.visibility,columnText:a.columnText}}).toArray()},columnVisibility:{columns:l,text:function(b,a,c){return c._columnText(b,c)},className:"buttons-columnVisibility",action:function(b,a,c,d){b=a.columns(d.columns);a=b.visible();b.visible(d.visibility!==l?d.visibility:!(a.length&&a[0]))},init:function(b,a,c){var d=this;a.attr("data-cv-idx",c.columns);b.on("column-visibility.dt"+c.namespace,function(h,k){k.bDestroying||k.nTable!=b.settings()[0].nTable||d.active(b.column(c.columns).visible())}).on("column-reorder.dt"+
c.namespace,function(h,k,m){1===b.columns(c.columns).count()&&(d.text(c._columnText(b,c)),d.active(b.column(c.columns).visible()))});this.active(b.column(c.columns).visible())},destroy:function(b,a,c){b.off("column-visibility.dt"+c.namespace).off("column-reorder.dt"+c.namespace)},_columnText:function(b,a){var c=b.column(a.columns).index(),d=b.settings()[0].aoColumns[c].sTitle;d||(d=b.column(c).header().innerHTML);d=d.replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,
"").replace(/<!\-\-.*?\-\->/g,"").replace(/<.*?>/g,"").replace(/^\s+|\s+$/g,"");return a.columnText?a.columnText(b,c,d):d}},colvisRestore:{className:"buttons-colvisRestore",text:function(b){return b.i18n("buttons.colvisRestore","Restore visibility")},init:function(b,a,c){c._visOriginal=b.columns().indexes().map(function(d){return b.column(d).visible()}).toArray()},action:function(b,a,c,d){a.columns().every(function(h){h=a.colReorder&&a.colReorder.transpose?a.colReorder.transpose(h,"toOriginal"):h;
this.visible(d._visOriginal[h])})}},colvisGroup:{className:"buttons-colvisGroup",action:function(b,a,c,d){a.columns(d.show).visible(!0,!1);a.columns(d.hide).visible(!1,!1);a.columns.adjust()},show:[],hide:[]}});return e.Buttons});


/*!
   Copyright 2014-2021 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license/mit

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 Responsive 2.2.9
 2014-2021 SpryMedia Ltd - datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(b,k,m){b instanceof String&&(b=String(b));for(var n=b.length,p=0;p<n;p++){var y=b[p];if(k.call(m,y,p,b))return{i:p,v:y}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(b,k,m){if(b==Array.prototype||b==Object.prototype)return b;b[k]=m.value;return b};$jscomp.getGlobal=function(b){b=["object"==typeof globalThis&&globalThis,b,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var k=0;k<b.length;++k){var m=b[k];if(m&&m.Math==Math)return m}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(b,k){var m=$jscomp.propertyToPolyfillSymbol[k];if(null==m)return b[k];m=b[m];return void 0!==m?m:b[k]};
$jscomp.polyfill=function(b,k,m,n){k&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(b,k,m,n):$jscomp.polyfillUnisolated(b,k,m,n))};$jscomp.polyfillUnisolated=function(b,k,m,n){m=$jscomp.global;b=b.split(".");for(n=0;n<b.length-1;n++){var p=b[n];if(!(p in m))return;m=m[p]}b=b[b.length-1];n=m[b];k=k(n);k!=n&&null!=k&&$jscomp.defineProperty(m,b,{configurable:!0,writable:!0,value:k})};
$jscomp.polyfillIsolated=function(b,k,m,n){var p=b.split(".");b=1===p.length;n=p[0];n=!b&&n in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var y=0;y<p.length-1;y++){var z=p[y];if(!(z in n))return;n=n[z]}p=p[p.length-1];m=$jscomp.IS_SYMBOL_NATIVE&&"es6"===m?n[p]:null;k=k(m);null!=k&&(b?$jscomp.defineProperty($jscomp.polyfills,p,{configurable:!0,writable:!0,value:k}):k!==m&&($jscomp.propertyToPolyfillSymbol[p]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(p):$jscomp.POLYFILL_PREFIX+p,p=
$jscomp.propertyToPolyfillSymbol[p],$jscomp.defineProperty(n,p,{configurable:!0,writable:!0,value:k})))};$jscomp.polyfill("Array.prototype.find",function(b){return b?b:function(k,m){return $jscomp.findInternal(this,k,m).v}},"es6","es3");
(function(b){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(k){return b(k,window,document)}):"object"===typeof exports?module.exports=function(k,m){k||(k=window);m&&m.fn.dataTable||(m=require("datatables.net")(k,m).$);return b(m,k,k.document)}:b(jQuery,window,document)})(function(b,k,m,n){function p(a,c,d){var f=c+"-"+d;if(A[f])return A[f];var g=[];a=a.cell(c,d).node().childNodes;c=0;for(d=a.length;c<d;c++)g.push(a[c]);return A[f]=g}function y(a,c,d){var f=c+"-"+
d;if(A[f]){a=a.cell(c,d).node();d=A[f][0].parentNode.childNodes;c=[];for(var g=0,l=d.length;g<l;g++)c.push(d[g]);d=0;for(g=c.length;d<g;d++)a.appendChild(c[d]);A[f]=n}}var z=b.fn.dataTable,u=function(a,c){if(!z.versionCheck||!z.versionCheck("1.10.10"))throw"DataTables Responsive requires DataTables 1.10.10 or newer";this.s={dt:new z.Api(a),columns:[],current:[]};this.s.dt.settings()[0].responsive||(c&&"string"===typeof c.details?c.details={type:c.details}:c&&!1===c.details?c.details={type:!1}:c&&
!0===c.details&&(c.details={type:"inline"}),this.c=b.extend(!0,{},u.defaults,z.defaults.responsive,c),a.responsive=this,this._constructor())};b.extend(u.prototype,{_constructor:function(){var a=this,c=this.s.dt,d=c.settings()[0],f=b(k).innerWidth();c.settings()[0]._responsive=this;b(k).on("resize.dtr orientationchange.dtr",z.util.throttle(function(){var g=b(k).innerWidth();g!==f&&(a._resize(),f=g)}));d.oApi._fnCallbackReg(d,"aoRowCreatedCallback",function(g,l,h){-1!==b.inArray(!1,a.s.current)&&b(">td, >th",
g).each(function(e){e=c.column.index("toData",e);!1===a.s.current[e]&&b(this).css("display","none")})});c.on("destroy.dtr",function(){c.off(".dtr");b(c.table().body()).off(".dtr");b(k).off("resize.dtr orientationchange.dtr");c.cells(".dtr-control").nodes().to$().removeClass("dtr-control");b.each(a.s.current,function(g,l){!1===l&&a._setColumnVis(g,!0)})});this.c.breakpoints.sort(function(g,l){return g.width<l.width?1:g.width>l.width?-1:0});this._classLogic();this._resizeAuto();d=this.c.details;!1!==
d.type&&(a._detailsInit(),c.on("column-visibility.dtr",function(){a._timer&&clearTimeout(a._timer);a._timer=setTimeout(function(){a._timer=null;a._classLogic();a._resizeAuto();a._resize(!0);a._redrawChildren()},100)}),c.on("draw.dtr",function(){a._redrawChildren()}),b(c.table().node()).addClass("dtr-"+d.type));c.on("column-reorder.dtr",function(g,l,h){a._classLogic();a._resizeAuto();a._resize(!0)});c.on("column-sizing.dtr",function(){a._resizeAuto();a._resize()});c.on("preXhr.dtr",function(){var g=
[];c.rows().every(function(){this.child.isShown()&&g.push(this.id(!0))});c.one("draw.dtr",function(){a._resizeAuto();a._resize();c.rows(g).every(function(){a._detailsDisplay(this,!1)})})});c.on("draw.dtr",function(){a._controlClass()}).on("init.dtr",function(g,l,h){"dt"===g.namespace&&(a._resizeAuto(),a._resize(),b.inArray(!1,a.s.current)&&c.columns.adjust())});this._resize()},_columnsVisiblity:function(a){var c=this.s.dt,d=this.s.columns,f,g=d.map(function(t,v){return{columnIdx:v,priority:t.priority}}).sort(function(t,
v){return t.priority!==v.priority?t.priority-v.priority:t.columnIdx-v.columnIdx}),l=b.map(d,function(t,v){return!1===c.column(v).visible()?"not-visible":t.auto&&null===t.minWidth?!1:!0===t.auto?"-":-1!==b.inArray(a,t.includeIn)}),h=0;var e=0;for(f=l.length;e<f;e++)!0===l[e]&&(h+=d[e].minWidth);e=c.settings()[0].oScroll;e=e.sY||e.sX?e.iBarWidth:0;h=c.table().container().offsetWidth-e-h;e=0;for(f=l.length;e<f;e++)d[e].control&&(h-=d[e].minWidth);var r=!1;e=0;for(f=g.length;e<f;e++){var q=g[e].columnIdx;
"-"===l[q]&&!d[q].control&&d[q].minWidth&&(r||0>h-d[q].minWidth?(r=!0,l[q]=!1):l[q]=!0,h-=d[q].minWidth)}g=!1;e=0;for(f=d.length;e<f;e++)if(!d[e].control&&!d[e].never&&!1===l[e]){g=!0;break}e=0;for(f=d.length;e<f;e++)d[e].control&&(l[e]=g),"not-visible"===l[e]&&(l[e]=!1);-1===b.inArray(!0,l)&&(l[0]=!0);return l},_classLogic:function(){var a=this,c=this.c.breakpoints,d=this.s.dt,f=d.columns().eq(0).map(function(h){var e=this.column(h),r=e.header().className;h=d.settings()[0].aoColumns[h].responsivePriority;
e=e.header().getAttribute("data-priority");h===n&&(h=e===n||null===e?1E4:1*e);return{className:r,includeIn:[],auto:!1,control:!1,never:r.match(/\bnever\b/)?!0:!1,priority:h}}),g=function(h,e){h=f[h].includeIn;-1===b.inArray(e,h)&&h.push(e)},l=function(h,e,r,q){if(!r)f[h].includeIn.push(e);else if("max-"===r)for(q=a._find(e).width,e=0,r=c.length;e<r;e++)c[e].width<=q&&g(h,c[e].name);else if("min-"===r)for(q=a._find(e).width,e=0,r=c.length;e<r;e++)c[e].width>=q&&g(h,c[e].name);else if("not-"===r)for(e=
0,r=c.length;e<r;e++)-1===c[e].name.indexOf(q)&&g(h,c[e].name)};f.each(function(h,e){for(var r=h.className.split(" "),q=!1,t=0,v=r.length;t<v;t++){var B=r[t].trim();if("all"===B){q=!0;h.includeIn=b.map(c,function(w){return w.name});return}if("none"===B||h.never){q=!0;return}if("control"===B||"dtr-control"===B){q=!0;h.control=!0;return}b.each(c,function(w,D){w=D.name.split("-");var x=B.match(new RegExp("(min\\-|max\\-|not\\-)?("+w[0]+")(\\-[_a-zA-Z0-9])?"));x&&(q=!0,x[2]===w[0]&&x[3]==="-"+w[1]?l(e,
D.name,x[1],x[2]+x[3]):x[2]!==w[0]||x[3]||l(e,D.name,x[1],x[2]))})}q||(h.auto=!0)});this.s.columns=f},_controlClass:function(){if("inline"===this.c.details.type){var a=this.s.dt,c=b.inArray(!0,this.s.current);a.cells(null,function(d){return d!==c},{page:"current"}).nodes().to$().filter(".dtr-control").removeClass("dtr-control");a.cells(null,c,{page:"current"}).nodes().to$().addClass("dtr-control")}},_detailsDisplay:function(a,c){var d=this,f=this.s.dt,g=this.c.details;if(g&&!1!==g.type){var l=g.display(a,
c,function(){return g.renderer(f,a[0],d._detailsObj(a[0]))});!0!==l&&!1!==l||b(f.table().node()).triggerHandler("responsive-display.dt",[f,a,l,c])}},_detailsInit:function(){var a=this,c=this.s.dt,d=this.c.details;"inline"===d.type&&(d.target="td.dtr-control, th.dtr-control");c.on("draw.dtr",function(){a._tabIndexes()});a._tabIndexes();b(c.table().body()).on("keyup.dtr","td, th",function(g){13===g.keyCode&&b(this).data("dtr-keyboard")&&b(this).click()});var f=d.target;d="string"===typeof f?f:"td, th";
if(f!==n||null!==f)b(c.table().body()).on("click.dtr mousedown.dtr mouseup.dtr",d,function(g){if(b(c.table().node()).hasClass("collapsed")&&-1!==b.inArray(b(this).closest("tr").get(0),c.rows().nodes().toArray())){if("number"===typeof f){var l=0>f?c.columns().eq(0).length+f:f;if(c.cell(this).index().column!==l)return}l=c.row(b(this).closest("tr"));"click"===g.type?a._detailsDisplay(l,!1):"mousedown"===g.type?b(this).css("outline","none"):"mouseup"===g.type&&b(this).trigger("blur").css("outline","")}})},
_detailsObj:function(a){var c=this,d=this.s.dt;return b.map(this.s.columns,function(f,g){if(!f.never&&!f.control)return f=d.settings()[0].aoColumns[g],{className:f.sClass,columnIndex:g,data:d.cell(a,g).render(c.c.orthogonal),hidden:d.column(g).visible()&&!c.s.current[g],rowIndex:a,title:null!==f.sTitle?f.sTitle:b(d.column(g).header()).text()}})},_find:function(a){for(var c=this.c.breakpoints,d=0,f=c.length;d<f;d++)if(c[d].name===a)return c[d]},_redrawChildren:function(){var a=this,c=this.s.dt;c.rows({page:"current"}).iterator("row",
function(d,f){c.row(f);a._detailsDisplay(c.row(f),!0)})},_resize:function(a){var c=this,d=this.s.dt,f=b(k).innerWidth(),g=this.c.breakpoints,l=g[0].name,h=this.s.columns,e,r=this.s.current.slice();for(e=g.length-1;0<=e;e--)if(f<=g[e].width){l=g[e].name;break}var q=this._columnsVisiblity(l);this.s.current=q;g=!1;e=0;for(f=h.length;e<f;e++)if(!1===q[e]&&!h[e].never&&!h[e].control&&!1===!d.column(e).visible()){g=!0;break}b(d.table().node()).toggleClass("collapsed",g);var t=!1,v=0;d.columns().eq(0).each(function(B,
w){!0===q[w]&&v++;if(a||q[w]!==r[w])t=!0,c._setColumnVis(B,q[w])});t&&(this._redrawChildren(),b(d.table().node()).trigger("responsive-resize.dt",[d,this.s.current]),0===d.page.info().recordsDisplay&&b("td",d.table().body()).eq(0).attr("colspan",v));c._controlClass()},_resizeAuto:function(){var a=this.s.dt,c=this.s.columns;if(this.c.auto&&-1!==b.inArray(!0,b.map(c,function(e){return e.auto}))){b.isEmptyObject(A)||b.each(A,function(e){e=e.split("-");y(a,1*e[0],1*e[1])});a.table().node();var d=a.table().node().cloneNode(!1),
f=b(a.table().header().cloneNode(!1)).appendTo(d),g=b(a.table().body()).clone(!1,!1).empty().appendTo(d);d.style.width="auto";var l=a.columns().header().filter(function(e){return a.column(e).visible()}).to$().clone(!1).css("display","table-cell").css("width","auto").css("min-width",0);b(g).append(b(a.rows({page:"current"}).nodes()).clone(!1)).find("th, td").css("display","");if(g=a.table().footer()){g=b(g.cloneNode(!1)).appendTo(d);var h=a.columns().footer().filter(function(e){return a.column(e).visible()}).to$().clone(!1).css("display",
"table-cell");b("<tr/>").append(h).appendTo(g)}b("<tr/>").append(l).appendTo(f);"inline"===this.c.details.type&&b(d).addClass("dtr-inline collapsed");b(d).find("[name]").removeAttr("name");b(d).css("position","relative");d=b("<div/>").css({width:1,height:1,overflow:"hidden",clear:"both"}).append(d);d.insertBefore(a.table().node());l.each(function(e){e=a.column.index("fromVisible",e);c[e].minWidth=this.offsetWidth||0});d.remove()}},_responsiveOnlyHidden:function(){var a=this.s.dt;return b.map(this.s.current,
function(c,d){return!1===a.column(d).visible()?!0:c})},_setColumnVis:function(a,c){var d=this.s.dt;c=c?"":"none";b(d.column(a).header()).css("display",c);b(d.column(a).footer()).css("display",c);d.column(a).nodes().to$().css("display",c);b.isEmptyObject(A)||d.cells(null,a).indexes().each(function(f){y(d,f.row,f.column)})},_tabIndexes:function(){var a=this.s.dt,c=a.cells({page:"current"}).nodes().to$(),d=a.settings()[0],f=this.c.details.target;c.filter("[data-dtr-keyboard]").removeData("[data-dtr-keyboard]");
"number"===typeof f?a.cells(null,f,{page:"current"}).nodes().to$().attr("tabIndex",d.iTabIndex).data("dtr-keyboard",1):("td:first-child, th:first-child"===f&&(f=">td:first-child, >th:first-child"),b(f,a.rows({page:"current"}).nodes()).attr("tabIndex",d.iTabIndex).data("dtr-keyboard",1))}});u.breakpoints=[{name:"desktop",width:Infinity},{name:"tablet-l",width:1024},{name:"tablet-p",width:768},{name:"mobile-l",width:480},{name:"mobile-p",width:320}];u.display={childRow:function(a,c,d){if(c){if(b(a.node()).hasClass("parent"))return a.child(d(),
"child").show(),!0}else{if(a.child.isShown())return a.child(!1),b(a.node()).removeClass("parent"),!1;a.child(d(),"child").show();b(a.node()).addClass("parent");return!0}},childRowImmediate:function(a,c,d){if(!c&&a.child.isShown()||!a.responsive.hasHidden())return a.child(!1),b(a.node()).removeClass("parent"),!1;a.child(d(),"child").show();b(a.node()).addClass("parent");return!0},modal:function(a){return function(c,d,f){if(d)b("div.dtr-modal-content").empty().append(f());else{var g=function(){l.remove();
b(m).off("keypress.dtr")},l=b('<div class="dtr-modal"/>').append(b('<div class="dtr-modal-display"/>').append(b('<div class="dtr-modal-content"/>').append(f())).append(b('<div class="dtr-modal-close">&times;</div>').click(function(){g()}))).append(b('<div class="dtr-modal-background"/>').click(function(){g()})).appendTo("body");b(m).on("keyup.dtr",function(h){27===h.keyCode&&(h.stopPropagation(),g())})}a&&a.header&&b("div.dtr-modal-content").prepend("<h2>"+a.header(c)+"</h2>")}}};var A={};u.renderer=
{listHiddenNodes:function(){return function(a,c,d){var f=b('<ul data-dtr-index="'+c+'" class="dtr-details"/>'),g=!1;b.each(d,function(l,h){h.hidden&&(b("<li "+(h.className?'class="'+h.className+'"':"")+' data-dtr-index="'+h.columnIndex+'" data-dt-row="'+h.rowIndex+'" data-dt-column="'+h.columnIndex+'"><span class="dtr-title">'+h.title+"</span> </li>").append(b('<span class="dtr-data"/>').append(p(a,h.rowIndex,h.columnIndex))).appendTo(f),g=!0)});return g?f:!1}},listHidden:function(){return function(a,
c,d){return(a=b.map(d,function(f){var g=f.className?'class="'+f.className+'"':"";return f.hidden?"<li "+g+' data-dtr-index="'+f.columnIndex+'" data-dt-row="'+f.rowIndex+'" data-dt-column="'+f.columnIndex+'"><span class="dtr-title">'+f.title+'</span> <span class="dtr-data">'+f.data+"</span></li>":""}).join(""))?b('<ul data-dtr-index="'+c+'" class="dtr-details"/>').append(a):!1}},tableAll:function(a){a=b.extend({tableClass:""},a);return function(c,d,f){c=b.map(f,function(g){return"<tr "+(g.className?
'class="'+g.className+'"':"")+' data-dt-row="'+g.rowIndex+'" data-dt-column="'+g.columnIndex+'"><td>'+g.title+":</td> <td>"+g.data+"</td></tr>"}).join("");return b('<table class="'+a.tableClass+' dtr-details" width="100%"/>').append(c)}}};u.defaults={breakpoints:u.breakpoints,auto:!0,details:{display:u.display.childRow,renderer:u.renderer.listHidden(),target:0,type:"inline"},orthogonal:"display"};var C=b.fn.dataTable.Api;C.register("responsive()",function(){return this});C.register("responsive.index()",
function(a){a=b(a);return{column:a.data("dtr-index"),row:a.parent().data("dtr-index")}});C.register("responsive.rebuild()",function(){return this.iterator("table",function(a){a._responsive&&a._responsive._classLogic()})});C.register("responsive.recalc()",function(){return this.iterator("table",function(a){a._responsive&&(a._responsive._resizeAuto(),a._responsive._resize())})});C.register("responsive.hasHidden()",function(){var a=this.context[0];return a._responsive?-1!==b.inArray(!1,a._responsive._responsiveOnlyHidden()):
!1});C.registerPlural("columns().responsiveHidden()","column().responsiveHidden()",function(){return this.iterator("column",function(a,c){return a._responsive?a._responsive._responsiveOnlyHidden()[c]:!1},1)});u.version="2.2.9";b.fn.dataTable.Responsive=u;b.fn.DataTable.Responsive=u;b(m).on("preInit.dt.dtr",function(a,c,d){"dt"===a.namespace&&(b(c.nTable).hasClass("responsive")||b(c.nTable).hasClass("dt-responsive")||c.oInit.responsive||z.defaults.responsive)&&(a=c.oInit.responsive,!1!==a&&new u(c,
b.isPlainObject(a)?a:{}))});return u});


/*! DataTables styling wrapper for Responsive
 * ©2018 SpryMedia Ltd - datatables.net/license
 */

(function( factory ){
	if ( typeof define === 'function' && define.amd ) {
		// AMD
		define( ['jquery', 'datatables.net-dt', 'datatables.net-responsive'], function ( $ ) {
			return factory( $, window, document );
		} );
	}
	else if ( typeof exports === 'object' ) {
		// CommonJS
		module.exports = function (root, $) {
			if ( ! root ) {
				root = window;
			}

			if ( ! $ || ! $.fn.dataTable ) {
				$ = require('datatables.net-dt')(root, $).$;
			}

			if ( ! $.fn.dataTable.Responsive ) {
				require('datatables.net-responsive')(root, $);
			}

			return factory( $, root, root.document );
		};
	}
	else {
		// Browser
		factory( jQuery, window, document );
	}
}(function( $, window, document, undefined ) {

return $.fn.dataTable;

}));

/*!
   Copyright 2011-2021 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license/mit

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 Scroller 2.0.4
 ©2011-2021 SpryMedia Ltd - datatables.net/license
*/
(function(e){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(h){return e(h,window,document)}):"object"===typeof exports?module.exports=function(h,m){h||(h=window);m&&m.fn.dataTable||(m=require("datatables.net")(h,m).$);return e(m,h,h.document)}:e(jQuery,window,document)})(function(e,h,m,q){var u=e.fn.dataTable,k=function(a,b){this instanceof k?(b===q&&(b={}),a=e.fn.dataTable.Api(a),this.s={dt:a.settings()[0],dtApi:a,tableTop:0,tableBottom:0,redrawTop:0,redrawBottom:0,
autoHeight:!0,viewportRows:0,stateTO:null,stateSaveThrottle:function(){},drawTO:null,heights:{jump:null,page:null,virtual:null,scroll:null,row:null,viewport:null,labelFactor:1,labelHeight:0},topRowFloat:0,scrollDrawDiff:null,loaderVisible:!1,forceReposition:!1,baseRowTop:0,baseScrollTop:0,mousedown:!1,lastScrollTop:0},this.s=e.extend(this.s,k.oDefaults,b),this.s.heights.row=this.s.rowHeight,this.dom={force:m.createElement("div"),label:e('<div class="dts_label">0</div>'),scroller:null,table:null,loader:null},
this.s.dt.oScroller||(this.s.dt.oScroller=this,this.construct())):alert("Scroller warning: Scroller must be initialised with the 'new' keyword.")};e.extend(k.prototype,{measure:function(a){this.s.autoHeight&&this._calcRowHeight();var b=this.s.heights;b.row&&(b.viewport=this._parseHeight(e(this.dom.scroller).css("max-height")),this.s.viewportRows=parseInt(b.viewport/b.row,10)+1,this.s.dt._iDisplayLength=this.s.viewportRows*this.s.displayBuffer);var c=this.dom.label.outerHeight(),d=this.dom.scroller.offsetHeight-
this.dom.scroller.clientHeight;b.labelHeight=c;b.labelFactor=(b.viewport-c-d)/b.scroll;(a===q||a)&&this.s.dt.oInstance.fnDraw(!1)},pageInfo:function(){var a=this.dom.scroller.scrollTop,b=this.s.dt.fnRecordsDisplay(),c=Math.ceil(this.pixelsToRow(a+this.s.heights.viewport,!1,this.s.ani));return{start:Math.floor(this.pixelsToRow(a,!1,this.s.ani)),end:b<c?b-1:c-1}},pixelsToRow:function(a,b,c){a-=this.s.baseScrollTop;c=c?(this._domain("physicalToVirtual",this.s.baseScrollTop)+a)/this.s.heights.row:a/this.s.heights.row+
this.s.baseRowTop;return b||b===q?parseInt(c,10):c},rowToPixels:function(a,b,c){a-=this.s.baseRowTop;c=c?this._domain("virtualToPhysical",this.s.baseScrollTop):this.s.baseScrollTop;c+=a*this.s.heights.row;return b||b===q?parseInt(c,10):c},scrollToRow:function(a,b){var c=this,d=!1,f=this.rowToPixels(a),g=a-(this.s.displayBuffer-1)/2*this.s.viewportRows;0>g&&(g=0);(f>this.s.redrawBottom||f<this.s.redrawTop)&&this.s.dt._iDisplayStart!==g&&(d=!0,f=this._domain("virtualToPhysical",a*this.s.heights.row),
this.s.redrawTop<f&&f<this.s.redrawBottom&&(this.s.forceReposition=!0,b=!1));b===q||b?(this.s.ani=d,e(this.dom.scroller).animate({scrollTop:f},function(){setTimeout(function(){c.s.ani=!1},250)})):e(this.dom.scroller).scrollTop(f)},construct:function(){var a=this,b=this.s.dtApi;if(this.s.dt.oFeatures.bPaginate){this.dom.force.style.position="relative";this.dom.force.style.top="0px";this.dom.force.style.left="0px";this.dom.force.style.width="1px";this.dom.scroller=e("div."+this.s.dt.oClasses.sScrollBody,
this.s.dt.nTableWrapper)[0];this.dom.scroller.appendChild(this.dom.force);this.dom.scroller.style.position="relative";this.dom.table=e(">table",this.dom.scroller)[0];this.dom.table.style.position="absolute";this.dom.table.style.top="0px";this.dom.table.style.left="0px";e(b.table().container()).addClass("dts DTS");this.s.loadingIndicator&&(this.dom.loader=e('<div class="dataTables_processing dts_loading">'+this.s.dt.oLanguage.sLoadingRecords+"</div>").css("display","none"),e(this.dom.scroller.parentNode).css("position",
"relative").append(this.dom.loader));this.dom.label.appendTo(this.dom.scroller);this.s.heights.row&&"auto"!=this.s.heights.row&&(this.s.autoHeight=!1);this.s.ingnoreScroll=!0;e(this.dom.scroller).on("scroll.dt-scroller",function(f){a._scroll.call(a)});e(this.dom.scroller).on("touchstart.dt-scroller",function(){a._scroll.call(a)});e(this.dom.scroller).on("mousedown.dt-scroller",function(){a.s.mousedown=!0}).on("mouseup.dt-scroller",function(){a.s.labelVisible=!1;a.s.mousedown=!1;a.dom.label.css("display",
"none")});e(h).on("resize.dt-scroller",function(){a.measure(!1);a._info()});var c=!0,d=b.state.loaded();b.on("stateSaveParams.scroller",function(f,g,l){c&&d?(l.scroller=d.scroller,c=!1):l.scroller={topRow:a.s.topRowFloat,baseScrollTop:a.s.baseScrollTop,baseRowTop:a.s.baseRowTop,scrollTop:a.s.lastScrollTop}});d&&d.scroller&&(this.s.topRowFloat=d.scroller.topRow,this.s.baseScrollTop=d.scroller.baseScrollTop,this.s.baseRowTop=d.scroller.baseRowTop);this.measure(!1);a.s.stateSaveThrottle=a.s.dt.oApi._fnThrottle(function(){a.s.dtApi.state.save()},
500);b.on("init.scroller",function(){a.measure(!1);a.s.scrollType="jump";a._draw();b.on("draw.scroller",function(){a._draw()})});b.on("preDraw.dt.scroller",function(){a._scrollForce()});b.on("destroy.scroller",function(){e(h).off("resize.dt-scroller");e(a.dom.scroller).off(".dt-scroller");e(a.s.dt.nTable).off(".scroller");e(a.s.dt.nTableWrapper).removeClass("DTS");e("div.DTS_Loading",a.dom.scroller.parentNode).remove();a.dom.table.style.position="";a.dom.table.style.top="";a.dom.table.style.left=
""})}else this.s.dt.oApi._fnLog(this.s.dt,0,"Pagination must be enabled for Scroller")},_calcRowHeight:function(){var a=this.s.dt,b=a.nTable,c=b.cloneNode(!1),d=e("<tbody/>").appendTo(c),f=e('<div class="'+a.oClasses.sWrapper+' DTS"><div class="'+a.oClasses.sScrollWrapper+'"><div class="'+a.oClasses.sScrollBody+'"></div></div></div>');e("tbody tr:lt(4)",b).clone().appendTo(d);var g=e("tr",d).length;if(1===g)d.prepend("<tr><td>&#160;</td></tr>"),d.append("<tr><td>&#160;</td></tr>");else for(;3>g;g++)d.append("<tr><td>&#160;</td></tr>");
e("div."+a.oClasses.sScrollBody,f).append(c);a=this.s.dt.nHolding||b.parentNode;e(a).is(":visible")||(a="body");f.find("input").removeAttr("name");f.appendTo(a);this.s.heights.row=e("tr",d).eq(1).outerHeight();f.remove()},_draw:function(){var a=this,b=this.s.heights,c=this.dom.scroller.scrollTop,d=e(this.s.dt.nTable).height(),f=this.s.dt._iDisplayStart,g=this.s.dt._iDisplayLength,l=this.s.dt.fnRecordsDisplay();this.s.skip=!0;!this.s.dt.bSorted&&!this.s.dt.bFiltered||0!==f||this.s.dt._drawHold||(this.s.topRowFloat=
0);c="jump"===this.s.scrollType?this._domain("virtualToPhysical",this.s.topRowFloat*b.row):c;this.s.baseScrollTop=c;this.s.baseRowTop=this.s.topRowFloat;var n=c-(this.s.topRowFloat-f)*b.row;0===f?n=0:f+g>=l&&(n=b.scroll-d);this.dom.table.style.top=n+"px";this.s.tableTop=n;this.s.tableBottom=d+this.s.tableTop;d=(c-this.s.tableTop)*this.s.boundaryScale;this.s.redrawTop=c-d;this.s.redrawBottom=c+d>b.scroll-b.viewport-b.row?b.scroll-b.viewport-b.row:c+d;this.s.skip=!1;this.s.dt.oFeatures.bStateSave&&
null!==this.s.dt.oLoadedState&&"undefined"!=typeof this.s.dt.oLoadedState.scroller?((b=!this.s.dt.sAjaxSource&&!a.s.dt.ajax||this.s.dt.oFeatures.bServerSide?!1:!0)&&2==this.s.dt.iDraw||!b&&1==this.s.dt.iDraw)&&setTimeout(function(){e(a.dom.scroller).scrollTop(a.s.dt.oLoadedState.scroller.scrollTop);setTimeout(function(){a.s.ingnoreScroll=!1},0)},0):a.s.ingnoreScroll=!1;this.s.dt.oFeatures.bInfo&&setTimeout(function(){a._info.call(a)},0);e(this.s.dt.nTable).triggerHandler("position.dts.dt",n);this.dom.loader&&
this.s.loaderVisible&&(this.dom.loader.css("display","none"),this.s.loaderVisible=!1)},_domain:function(a,b){var c=this.s.heights;if(c.virtual===c.scroll||1E4>b)return b;if("virtualToPhysical"===a&&b>=c.virtual-1E4)return a=c.virtual-b,c.scroll-a;if("physicalToVirtual"===a&&b>=c.scroll-1E4)return a=c.scroll-b,c.virtual-a;c=(c.virtual-1E4-1E4)/(c.scroll-1E4-1E4);var d=1E4-1E4*c;return"virtualToPhysical"===a?(b-d)/c:c*b+d},_info:function(){if(this.s.dt.oFeatures.bInfo){var a=this.s.dt,b=a.oLanguage,
c=this.dom.scroller.scrollTop,d=Math.floor(this.pixelsToRow(c,!1,this.s.ani)+1),f=a.fnRecordsTotal(),g=a.fnRecordsDisplay();c=Math.ceil(this.pixelsToRow(c+this.s.heights.viewport,!1,this.s.ani));c=g<c?g:c;var l=a.fnFormatNumber(d),n=a.fnFormatNumber(c),r=a.fnFormatNumber(f),t=a.fnFormatNumber(g);l=0===a.fnRecordsDisplay()&&a.fnRecordsDisplay()==a.fnRecordsTotal()?b.sInfoEmpty+b.sInfoPostFix:0===a.fnRecordsDisplay()?b.sInfoEmpty+" "+b.sInfoFiltered.replace("_MAX_",r)+b.sInfoPostFix:a.fnRecordsDisplay()==
a.fnRecordsTotal()?b.sInfo.replace("_START_",l).replace("_END_",n).replace("_MAX_",r).replace("_TOTAL_",t)+b.sInfoPostFix:b.sInfo.replace("_START_",l).replace("_END_",n).replace("_MAX_",r).replace("_TOTAL_",t)+" "+b.sInfoFiltered.replace("_MAX_",a.fnFormatNumber(a.fnRecordsTotal()))+b.sInfoPostFix;(b=b.fnInfoCallback)&&(l=b.call(a.oInstance,a,d,c,f,g,l));d=a.aanFeatures.i;if("undefined"!=typeof d)for(f=0,g=d.length;f<g;f++)e(d[f]).html(l);e(a.nTable).triggerHandler("info.dt")}},_parseHeight:function(a){var b,
c=/^([+-]?(?:\d+(?:\.\d+)?|\.\d+))(px|em|rem|vh)$/.exec(a);if(null===c)return 0;a=parseFloat(c[1]);c=c[2];"px"===c?b=a:"vh"===c?b=a/100*e(h).height():"rem"===c?b=a*parseFloat(e(":root").css("font-size")):"em"===c&&(b=a*parseFloat(e("body").css("font-size")));return b?b:0},_scroll:function(){var a=this,b=this.s.heights,c=this.dom.scroller.scrollTop;if(!this.s.skip&&!this.s.ingnoreScroll&&c!==this.s.lastScrollTop)if(this.s.dt.bFiltered||this.s.dt.bSorted)this.s.lastScrollTop=0;else{this._info();clearTimeout(this.s.stateTO);
this.s.stateTO=setTimeout(function(){a.s.dtApi.state.save()},250);this.s.scrollType=Math.abs(c-this.s.lastScrollTop)>b.viewport?"jump":"cont";this.s.topRowFloat="cont"===this.s.scrollType?this.pixelsToRow(c,!1,!1):this._domain("physicalToVirtual",c)/b.row;0>this.s.topRowFloat&&(this.s.topRowFloat=0);if(this.s.forceReposition||c<this.s.redrawTop||c>this.s.redrawBottom){var d=Math.ceil((this.s.displayBuffer-1)/2*this.s.viewportRows);d=parseInt(this.s.topRowFloat,10)-d;this.s.forceReposition=!1;0>=d?
d=0:d+this.s.dt._iDisplayLength>this.s.dt.fnRecordsDisplay()?(d=this.s.dt.fnRecordsDisplay()-this.s.dt._iDisplayLength,0>d&&(d=0)):0!==d%2&&d++;this.s.targetTop=d;d!=this.s.dt._iDisplayStart&&(this.s.tableTop=e(this.s.dt.nTable).offset().top,this.s.tableBottom=e(this.s.dt.nTable).height()+this.s.tableTop,d=function(){a.s.dt._iDisplayStart=a.s.targetTop;a.s.dt.oApi._fnDraw(a.s.dt)},this.s.dt.oFeatures.bServerSide?(this.s.forceReposition=!0,clearTimeout(this.s.drawTO),this.s.drawTO=setTimeout(d,this.s.serverWait)):
d(),this.dom.loader&&!this.s.loaderVisible&&(this.dom.loader.css("display","block"),this.s.loaderVisible=!0))}else this.s.topRowFloat=this.pixelsToRow(c,!1,!0);this.s.lastScrollTop=c;this.s.stateSaveThrottle();"jump"===this.s.scrollType&&this.s.mousedown&&(this.s.labelVisible=!0);this.s.labelVisible&&this.dom.label.html(this.s.dt.fnFormatNumber(parseInt(this.s.topRowFloat,10)+1)).css("top",c+c*b.labelFactor).css("display","block")}},_scrollForce:function(){var a=this.s.heights;a.virtual=a.row*this.s.dt.fnRecordsDisplay();
a.scroll=a.virtual;1E6<a.scroll&&(a.scroll=1E6);this.dom.force.style.height=a.scroll>this.s.heights.row?a.scroll+"px":this.s.heights.row+"px"}});k.defaults={boundaryScale:.5,displayBuffer:9,loadingIndicator:!1,rowHeight:"auto",serverWait:200};k.oDefaults=k.defaults;k.version="2.0.4";e(m).on("preInit.dt.dtscroller",function(a,b){if("dt"===a.namespace){a=b.oInit.scroller;var c=u.defaults.scroller;if(a||c)c=e.extend({},a,c),!1!==a&&new k(b,c)}});e.fn.dataTable.Scroller=k;e.fn.DataTable.Scroller=k;var p=
e.fn.dataTable.Api;p.register("scroller()",function(){return this});p.register("scroller().rowToPixels()",function(a,b,c){var d=this.context;if(d.length&&d[0].oScroller)return d[0].oScroller.rowToPixels(a,b,c)});p.register("scroller().pixelsToRow()",function(a,b,c){var d=this.context;if(d.length&&d[0].oScroller)return d[0].oScroller.pixelsToRow(a,b,c)});p.register(["scroller().scrollToRow()","scroller.toPosition()"],function(a,b){this.iterator("table",function(c){c.oScroller&&c.oScroller.scrollToRow(a,
b)});return this});p.register("row().scrollTo()",function(a){var b=this;this.iterator("row",function(c,d){c.oScroller&&(d=b.rows({order:"applied",search:"applied"}).indexes().indexOf(d),c.oScroller.scrollToRow(d,a))});return this});p.register("scroller.measure()",function(a){this.iterator("table",function(b){b.oScroller&&b.oScroller.measure(a)});return this});p.register("scroller.page()",function(){var a=this.context;if(a.length&&a[0].oScroller)return a[0].oScroller.pageInfo()});return k});


/*!
 SearchPanes 1.3.0
 2019-2021 SpryMedia Ltd - datatables.net/license
*/
(function(){function G(n){l=n;A=n.fn.dataTable}function H(n){p=n;B=n.fn.dataTable}var l,A,E=function(){function n(a,b,f,c,e,d){var g=this;void 0===d&&(d=null);if(!A||!A.versionCheck||!A.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");if(!A.select)throw Error("SearchPane requires Select");a=new A.Api(a);this.classes=l.extend(!0,{},n.classes);this.c=l.extend(!0,{},n.defaults,b);void 0!==b&&void 0!==b.hideCount&&void 0===b.viewCount&&(this.c.viewCount=!this.c.hideCount);
this.customPaneSettings=d;this.s={cascadeRegen:!1,clearing:!1,colOpts:[],deselect:!1,displayed:!1,dt:a,dtPane:void 0,filteringActive:!1,forceViewTotal:!1,index:f,indexes:[],lastCascade:!1,lastSelect:!1,listSet:!1,name:void 0,redraw:!1,rowData:{arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0},scrollTop:0,searchFunction:void 0,selectPresent:!1,serverSelect:[],serverSelecting:!1,showFiltered:!1,tableLength:null,updating:!1};b=a.columns().eq(0).toArray().length;
this.colExists=this.s.index<b;this.c.layout=c;b=parseInt(c.split("-")[1],10);this.dom={buttonGroup:l("<div/>").addClass(this.classes.buttonGroup),clear:l('<button type="button">&#215;</button>').addClass(this.classes.disabledButton).attr("disabled","true").addClass(this.classes.paneButton).addClass(this.classes.clearButton),container:l("<div/>").addClass(this.classes.container).addClass(this.classes.layout+(10>b?c:c.split("-")[0]+"-9")),countButton:l('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.countButton),
dtP:l("<table><thead><tr><th>"+(this.colExists?l(a.column(this.colExists?this.s.index:0).header()).text():this.customPaneSettings.header||"Custom Pane")+"</th><th/></tr></thead></table>"),lower:l("<div/>").addClass(this.classes.subRow2).addClass(this.classes.narrowButton),nameButton:l('<button type="button"></button>').addClass(this.classes.paneButton).addClass(this.classes.nameButton),panesContainer:e,searchBox:l("<input/>").addClass(this.classes.paneInputButton).addClass(this.classes.search),searchButton:l('<button type = "button" class="'+
this.classes.searchIcon+'"></button>').addClass(this.classes.paneButton),searchCont:l("<div/>").addClass(this.classes.searchCont),searchLabelCont:l("<div/>").addClass(this.classes.searchLabelCont),topRow:l("<div/>").addClass(this.classes.topRow),upper:l("<div/>").addClass(this.classes.subRow1).addClass(this.classes.narrowSearch)};this.s.displayed=!1;a=this.s.dt;this.selections=[];this.s.colOpts=this.colExists?this._getOptions():this._getBonusOptions();var h=this.s.colOpts;c=l('<button type="button">X</button>').addClass(this.classes.paneButton);
l(c).text(a.i18n("searchPanes.clearPane",this.c.i18n.clearPane));this.dom.container.addClass(h.className);this.dom.container.addClass(null!==this.customPaneSettings&&void 0!==this.customPaneSettings.className?this.customPaneSettings.className:"");this.s.name=void 0!==this.s.colOpts.name?this.s.colOpts.name:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.name?this.customPaneSettings.name:this.colExists?l(a.column(this.s.index).header()).text():this.customPaneSettings.header||"Custom Pane";
l(e).append(this.dom.container);var m=a.table(0).node();this.s.searchFunction=function(k,r,v,t){if(0===g.selections.length||k.nTable!==m)return!0;k=null;g.colExists&&(k=r[g.s.index],"filter"!==h.orthogonal.filter&&(k=g.s.rowData.filterMap.get(v),k instanceof l.fn.dataTable.Api&&(k=k.toArray())));return g._search(k,v)};l.fn.dataTable.ext.search.push(this.s.searchFunction);if(this.c.clear)l(c).on("click",function(){g.dom.container.find("."+g.classes.search.replace(/\s+/g,".")).each(function(){l(this).val("");
l(this).trigger("input")});g.clearPane()});a.on("draw.dtsp",function(){g.adjustTopRow()});a.on("buttons-action",function(){g.adjustTopRow()});a.on("column-reorder.dtsp",function(k,r,v){g.s.index=v.mapping[g.s.index]});return this}n.prototype.addRow=function(a,b,f,c,e,d,g){for(var h,m=0,k=this.s.indexes;m<k.length;m++){var r=k[m];r.filter===b&&(h=r.index)}void 0===h&&(h=this.s.indexes.length,this.s.indexes.push({filter:b,index:h}));return this.s.dtPane.row.add({className:g,display:""!==a?a:this.emptyMessage(),
filter:b,index:h,shown:f,sort:e,total:c,type:d})};n.prototype.adjustTopRow=function(){var a=this.dom.container.find("."+this.classes.subRowsContainer.replace(/\s+/g,".")),b=this.dom.container.find("."+this.classes.subRow1.replace(/\s+/g,".")),f=this.dom.container.find("."+this.classes.subRow2.replace(/\s+/g,".")),c=this.dom.container.find("."+this.classes.topRow.replace(/\s+/g,"."));(252>l(a[0]).width()||252>l(c[0]).width())&&0!==l(a[0]).width()?(l(a[0]).addClass(this.classes.narrow),l(b[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowSearch),
l(f[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowButton)):(l(a[0]).removeClass(this.classes.narrow),l(b[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowSearch),l(f[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowButton))};n.prototype.clearData=function(){this.s.rowData={arrayFilter:[],arrayOriginal:[],arrayTotals:[],bins:{},binsOriginal:{},binsTotal:{},filterMap:new Map,totalOptions:0}};n.prototype.clearPane=function(){this.s.dtPane.rows({selected:!0}).deselect();
this.updateTable();return this};n.prototype.destroy=function(){l(this.s.dtPane).off(".dtsp");l(this.s.dt).off(".dtsp");l(this.dom.nameButton).off(".dtsp");l(this.dom.countButton).off(".dtsp");l(this.dom.clear).off(".dtsp");l(this.dom.searchButton).off(".dtsp");l(this.dom.container).remove();for(var a=l.fn.dataTable.ext.search.indexOf(this.s.searchFunction);-1!==a;)l.fn.dataTable.ext.search.splice(a,1),a=l.fn.dataTable.ext.search.indexOf(this.s.searchFunction);void 0!==this.s.dtPane&&this.s.dtPane.destroy();
this.s.listSet=!1};n.prototype.emptyMessage=function(){var a=this.c.i18n.emptyMessage;this.c.emptyMessage&&(a=this.c.emptyMessage);!1!==this.s.colOpts.emptyMessage&&null!==this.s.colOpts.emptyMessage&&(a=this.s.colOpts.emptyMessage);return this.s.dt.i18n("searchPanes.emptyMessage",a)};n.prototype.getPaneCount=function(){return void 0!==this.s.dtPane?this.s.dtPane.rows({selected:!0}).data().toArray().length:0};n.prototype.rebuildPane=function(a,b,f,c){void 0===a&&(a=!1);void 0===b&&(b=null);void 0===
f&&(f=null);void 0===c&&(c=!1);this.clearData();var e=[];this.s.serverSelect=[];var d=null;void 0!==this.s.dtPane&&(c&&(this.s.dt.page.info().serverSide?this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray():e=this.s.dtPane.rows({selected:!0}).data().toArray()),this.s.dtPane.clear().destroy(),d=l(this.dom.container).prev(),this.destroy(),this.s.dtPane=void 0,l.fn.dataTable.ext.search.push(this.s.searchFunction));this.dom.container.removeClass(this.classes.hidden);this.s.displayed=
!1;this._buildPane(this.s.dt.page.info().serverSide?this.s.serverSelect:e,a,b,f,d);return this};n.prototype.removePane=function(){this.s.displayed=!1;l(this.dom.container).hide()};n.prototype.resize=function(a){this.c.layout=a;var b=parseInt(a.split("-")[1],10);l(this.dom.container).removeClass().addClass(this.classes.container).addClass(this.classes.layout+(10>b?a:a.split("-")[0]+"-9")).addClass(this.s.colOpts.className).addClass(null!==this.customPaneSettings&&void 0!==this.customPaneSettings.className?
this.customPaneSettings.className:"").addClass(this.classes.show);this.adjustTopRow()};n.prototype.setCascadeRegen=function(a){this.s.cascadeRegen=a};n.prototype.setClear=function(a){this.s.clearing=a};n.prototype.updatePane=function(a){void 0===a&&(a=!1);this.s.updating=!0;this._updateCommon(a);this.s.updating=!1};n.prototype.updateTable=function(){this.selections=this.s.dtPane.rows({selected:!0}).data().toArray();this._searchExtras();(this.c.cascadePanes||this.c.viewTotal)&&this.updatePane()};n.prototype._setListeners=
function(){var a=this,b=this.s.rowData,f;this.s.dtPane.on("select.dtsp",function(){clearTimeout(f);a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.scrollTop=l(a.s.dtPane.table().node()).parent()[0].scrollTop,a.s.selectPresent=!0,a.s.dt.draw(!1)):(l(a.dom.clear).removeClass(a.classes.disabledButton).removeAttr("disabled"),a.s.updating||(a.s.selectPresent=!0,a._makeSelection(),a.s.selectPresent=!1))});this.s.dtPane.on("deselect.dtsp",
function(){f=setTimeout(function(){a.s.dt.page.info().serverSide&&!a.s.updating?a.s.serverSelecting||(a.s.serverSelect=a.s.dtPane.rows({selected:!0}).data().toArray(),a.s.deselect=!0,a.s.dt.draw(!1)):(a.s.deselect=!0,0===a.s.dtPane.rows({selected:!0}).data().toArray().length&&l(a.dom.clear).addClass(a.classes.disabledButton).attr("disabled","true"),a._makeSelection(),a.s.deselect=!1,a.s.dt.state.save())},50)});this.s.dt.on("stateSaveParams.dtsp",function(c,e,d){if(l.isEmptyObject(d))a.s.dtPane.state.clear();
else{c=[];if(void 0!==a.s.dtPane){c=a.s.dtPane.rows({selected:!0}).data().map(function(r){return r.filter.toString()}).toArray();var g=l(a.dom.searchBox).val();var h=a.s.dtPane.order();var m=b.binsOriginal;var k=b.arrayOriginal}void 0===d.searchPanes&&(d.searchPanes={});void 0===d.searchPanes.panes&&(d.searchPanes.panes=[]);for(e=0;e<d.searchPanes.panes.length;e++)d.searchPanes.panes[e].id===a.s.index&&(d.searchPanes.panes.splice(e,1),e--);d.searchPanes.panes.push({arrayFilter:k,bins:m,id:a.s.index,
order:h,searchTerm:g,selected:c})}});this.s.dtPane.on("user-select.dtsp",function(c,e,d,g,h){h.stopPropagation()});this.s.dtPane.on("draw.dtsp",function(){a.adjustTopRow()});l(this.dom.nameButton).on("click.dtsp",function(){var c=a.s.dtPane.order()[0][1];a.s.dtPane.order([0,"asc"===c?"desc":"asc"]).draw();a.s.dt.state.save()});l(this.dom.countButton).on("click.dtsp",function(){var c=a.s.dtPane.order()[0][1];a.s.dtPane.order([1,"asc"===c?"desc":"asc"]).draw();a.s.dt.state.save()});l(this.dom.clear).on("click.dtsp",
function(){a.dom.container.find("."+a.classes.search.replace(/ /g,".")).each(function(){l(this).val("");l(this).trigger("input")});a.clearPane()});l(this.dom.searchButton).on("click.dtsp",function(){l(a.dom.searchBox).focus()});l(this.dom.searchBox).on("input.dtsp",function(){var c=l(a.dom.searchBox).val();a.s.dtPane.search(c).draw();0<c.length||0===c.length&&0<a.s.dtPane.rows({selected:!0}).data().toArray().length?a.dom.clear.removeClass(a.classes.disabledButton).removeAttr("disabled"):a.dom.clear.addClass(a.classes.disabledButton).attr("disabled",
"true");a.s.dt.state.save()});this.s.dt.state.save();return!0};n.prototype._addOption=function(a,b,f,c,e,d){if(Array.isArray(a)||a instanceof A.Api)if(a instanceof A.Api&&(a=a.toArray(),b=b.toArray()),a.length===b.length)for(var g=0;g<a.length;g++)d[a[g]]?d[a[g]]++:(d[a[g]]=1,e.push({display:b[g],filter:a[g],sort:f[g],type:c[g]})),this.s.rowData.totalOptions++;else throw Error("display and filter not the same length");else"string"===typeof this.s.colOpts.orthogonal?(d[a]?d[a]++:(d[a]=1,e.push({display:b,
filter:a,sort:f,type:c})),this.s.rowData.totalOptions++):e.push({display:b,filter:a,sort:f,type:c})};n.prototype._buildPane=function(a,b,f,c,e){var d=this;void 0===a&&(a=[]);void 0===b&&(b=!1);void 0===f&&(f=null);void 0===c&&(c=null);void 0===e&&(e=null);this.selections=[];var g=this.s.dt,h=g.column(this.colExists?this.s.index:0),m=this.s.colOpts,k=this.s.rowData,r=g.i18n("searchPanes.count",this.c.i18n.count),v=g.i18n("searchPanes.countFiltered",this.c.i18n.countFiltered),t=g.state.loaded();this.s.listSet&&
(t=g.state());if(this.colExists){var u=-1;if(t&&t.searchPanes&&t.searchPanes.panes)for(var q=0;q<t.searchPanes.panes.length;q++)if(t.searchPanes.panes[q].id===this.s.index){u=q;break}if((!1===m.show||void 0!==m.show&&!0!==m.show)&&-1===u)return this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1;if(!0===m.show||-1!==u)this.s.displayed=!0;if(!this.s.dt.page.info().serverSide&&(null===f||null===f.searchPanes||null===f.searchPanes.options)){if(0===k.arrayFilter.length){this._populatePane(b);
this.s.rowData.totalOptions=0;this._detailsPane();if(t&&t.searchPanes&&t.searchPanes.panes&&-1===u){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}k.arrayOriginal=k.arrayTotals;k.binsOriginal=k.binsTotal}q=Object.keys(k.binsOriginal).length;b=this._uniqueRatio(q,g.rows()[0].length);if(!1===this.s.displayed&&((void 0===m.show&&null===m.threshold?b>this.c.threshold:b>m.threshold)||!0!==m.show&&1>=q)){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}this.c.viewTotal&&
0===k.arrayTotals.length?(this.s.rowData.totalOptions=0,this._detailsPane()):k.binsTotal=k.bins;this.dom.container.addClass(this.classes.show);this.s.displayed=!0}else if(null!==f&&null!==f.searchPanes&&null!==f.searchPanes.options){if(void 0!==f.tableLength)this.s.tableLength=f.tableLength,this.s.rowData.totalOptions=this.s.tableLength;else if(null===this.s.tableLength||g.rows()[0].length>this.s.tableLength)this.s.tableLength=g.rows()[0].length,this.s.rowData.totalOptions=this.s.tableLength;b=g.column(this.s.index).dataSrc();
if(void 0!==f.searchPanes.options[b])for(q=0,b=f.searchPanes.options[b];q<b.length;q++)u=b[q],this.s.rowData.arrayFilter.push({display:u.label,filter:u.value,sort:u.label,type:u.label}),this.s.rowData.bins[u.value]=this.c.viewTotal||this.c.cascadePanes?u.count:u.total,this.s.rowData.binsTotal[u.value]=u.total;q=Object.keys(k.binsTotal).length;b=this._uniqueRatio(q,this.s.tableLength);if(!1===this.s.displayed&&((void 0===m.show&&null===m.threshold?b>this.c.threshold:b>m.threshold)||!0!==m.show&&1>=
q)){this.dom.container.addClass(this.classes.hidden);this.s.displayed=!1;return}this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter;this.s.rowData.binsOriginal=this.s.rowData.bins;this.s.displayed=!0}}else this.s.displayed=!0;this._displayPane();if(!this.s.listSet)this.dom.dtP.on("stateLoadParams.dt",function(x,C,y){l.isEmptyObject(g.state.loaded())&&l.each(y,function(z,I){delete y[z]})});null!==e&&0<l(this.dom.panesContainer).has(e).length?l(this.dom.container).insertAfter(e):l(this.dom.panesContainer).prepend(this.dom.container);
q=l.fn.dataTable.ext.errMode;l.fn.dataTable.ext.errMode="none";e=A.Scroller;this.s.dtPane=l(this.dom.dtP).DataTable(l.extend(!0,{columnDefs:[{className:"dtsp-nameColumn",data:"display",render:function(x,C,y){if("sort"===C)return y.sort;if("type"===C)return y.type;var z=(d.s.filteringActive||d.s.showFiltered)&&d.c.viewTotal||d.c.viewTotal&&d.s.forceViewTotal?v.replace(/{total}/,y.total):r.replace(/{total}/,y.total);for(z=z.replace(/{shown}/,y.shown);-1!==z.indexOf("{total}");)z=z.replace(/{total}/,
y.total);for(;-1!==z.indexOf("{shown}");)z=z.replace(/{shown}/,y.shown);y='<span class="'+d.classes.pill+'">'+z+"</span>";d.c.viewCount&&m.viewCount||(y="");return"filter"===C?"string"===typeof x&&null!==x.match(/<[^>]*>/)?x.replace(/<[^>]*>/g,""):x:'<div class="'+d.classes.nameCont+'"><span title="'+("string"===typeof x&&null!==x.match(/<[^>]*>/)?x.replace(/<[^>]*>/g,""):x)+'" class="'+d.classes.name+'">'+x+"</span>"+y+"</div>"},targets:0,type:void 0!==g.settings()[0].aoColumns[this.s.index]?g.settings()[0].aoColumns[this.s.index]._sManualType:
null},{className:"dtsp-countColumn "+this.classes.badgePill,data:"shown",orderData:[1,2],targets:1,visible:!1},{data:"total",targets:2,visible:!1}],deferRender:!0,dom:"t",info:!1,language:this.s.dt.settings()[0].oLanguage,paging:e?!0:!1,scrollX:!1,scrollY:"200px",scroller:e?!0:!1,select:!0,stateSave:g.settings()[0].oFeatures.bStateSave?!0:!1},this.c.dtOpts,void 0!==m?m.dtOpts:{},void 0===this.s.colOpts.options&&this.colExists?void 0:{createdRow:function(x,C,y){l(x).addClass(C.className)}},null!==
this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts?this.customPaneSettings.dtOpts:{},l.fn.dataTable.versionCheck("2")?{layout:{bottomLeft:null,bottomRight:null,topLeft:null,topRight:null}}:{}));l(this.dom.dtP).addClass(this.classes.table);e="Custom Pane";this.customPaneSettings&&this.customPaneSettings.header?e=this.customPaneSettings.header:m.header?e=m.header:this.colExists&&(e=l.fn.dataTable.versionCheck("2")?g.column(this.s.index).title():g.settings()[0].aoColumns[this.s.index].sTitle);
this.dom.searchBox.attr("placeholder",e);l.fn.dataTable.select.init(this.s.dtPane);l.fn.dataTable.ext.errMode=q;if(this.colExists){h=(h=h.search())?h.substr(1,h.length-2).split("|"):[];var w=0;k.arrayFilter.forEach(function(x){""===x.filter&&w++});q=0;for(e=k.arrayFilter.length;q<e;q++){h=!1;u=0;for(var D=this.s.serverSelect;u<D.length;u++)b=D[u],b.filter===k.arrayFilter[q].filter&&(h=!0);if(this.s.dt.page.info().serverSide&&(!this.c.cascadePanes||this.c.cascadePanes&&0!==k.bins[k.arrayFilter[q].filter]||
this.c.cascadePanes&&null!==c||h))for(h=this.addRow(k.arrayFilter[q].display,k.arrayFilter[q].filter,c?k.binsTotal[k.arrayFilter[q].filter]:k.bins[k.arrayFilter[q].filter],this.c.viewTotal||c?String(k.binsTotal[k.arrayFilter[q].filter]):k.bins[k.arrayFilter[q].filter],k.arrayFilter[q].sort,k.arrayFilter[q].type),u=0,D=this.s.serverSelect;u<D.length;u++)b=D[u],b.filter===k.arrayFilter[q].filter&&(this.s.serverSelecting=!0,h.select(),this.s.serverSelecting=!1);else this.s.dt.page.info().serverSide||
!k.arrayFilter[q]||void 0===k.bins[k.arrayFilter[q].filter]&&this.c.cascadePanes?this.s.dt.page.info().serverSide||this.addRow("",w,w,"","",""):this.addRow(k.arrayFilter[q].display,k.arrayFilter[q].filter,k.bins[k.arrayFilter[q].filter],k.binsTotal[k.arrayFilter[q].filter],k.arrayFilter[q].sort,k.arrayFilter[q].type)}}A.select.init(this.s.dtPane);(void 0!==m.options||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options)&&this._getComparisonRows();this.s.dtPane.draw();this.adjustTopRow();
this.s.listSet||(this._setListeners(),this.s.listSet=!0);for(c=0;c<a.length;c++)if(k=a[c],void 0!==k)for(q=0,e=this.s.dtPane.rows().indexes().toArray();q<e.length;q++)h=e[q],void 0!==this.s.dtPane.row(h).data()&&k.filter===this.s.dtPane.row(h).data().filter&&(this.s.dt.page.info().serverSide?(this.s.serverSelecting=!0,this.s.dtPane.row(h).select(),this.s.serverSelecting=!1):this.s.dtPane.row(h).select());this.s.dt.page.info().serverSide&&this.s.dtPane.search(l(this.dom.searchBox).val()).draw();if(t&&
t.searchPanes&&t.searchPanes.panes&&(null===f||1===f.draw))for(this.c.cascadePanes||this._reloadSelect(t),f=0,t=t.searchPanes.panes;f<t.length;f++)a=t[f],a.id===this.s.index&&(l(this.dom.searchBox).val(a.searchTerm),l(this.dom.searchBox).trigger("input"),this.s.dtPane.order(a.order).draw());this.s.dt.state.save();return!0};n.prototype._detailsPane=function(){var a=this.s.dt;this.s.rowData.arrayTotals=[];this.s.rowData.binsTotal={};var b=this.s.dt.settings()[0];a=a.rows().indexes();if(!this.s.dt.page.info().serverSide)for(var f=
0;f<a.length;f++)this._populatePaneArray(a[f],this.s.rowData.arrayTotals,b,this.s.rowData.binsTotal)};n.prototype._displayPane=function(){var a=this.dom.container,b=this.s.colOpts,f=parseInt(this.c.layout.split("-")[1],10);l(this.dom.topRow).empty();l(this.dom.dtP).empty();l(this.dom.topRow).addClass(this.classes.topRow);3<f&&l(this.dom.container).addClass(this.classes.smallGap);l(this.dom.topRow).addClass(this.classes.subRowsContainer);l(this.dom.upper).appendTo(this.dom.topRow);l(this.dom.lower).appendTo(this.dom.topRow);
l(this.dom.searchCont).appendTo(this.dom.upper);l(this.dom.buttonGroup).appendTo(this.dom.lower);(!1===this.c.dtOpts.searching||void 0!==b.dtOpts&&!1===b.dtOpts.searching||!this.c.controls||!b.controls||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&!this.customPaneSettings.dtOpts.searching)&&l(this.dom.searchBox).removeClass(this.classes.paneInputButton).addClass(this.classes.disabledButton).attr("disabled","true");l(this.dom.searchBox).appendTo(this.dom.searchCont);
this._searchContSetup();this.c.clear&&this.c.controls&&b.controls&&l(this.dom.clear).appendTo(this.dom.buttonGroup);this.c.orderable&&b.orderable&&this.c.controls&&b.controls&&l(this.dom.nameButton).appendTo(this.dom.buttonGroup);this.c.viewCount&&b.viewCount&&this.c.orderable&&b.orderable&&this.c.controls&&b.controls&&l(this.dom.countButton).appendTo(this.dom.buttonGroup);l(this.dom.topRow).prependTo(this.dom.container);l(a).append(this.dom.dtP);l(a).show()};n.prototype._getBonusOptions=function(){return l.extend(!0,
{},n.defaults,{orthogonal:{threshold:null},threshold:null},void 0!==this.c?this.c:{})};n.prototype._getComparisonRows=function(){var a=this.s.colOpts;a=void 0!==a.options?a.options:null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options?this.customPaneSettings.options:void 0;if(void 0!==a){var b=this.s.dt.rows({search:"applied"}).data().toArray(),f=this.s.dt.rows({search:"applied"}),c=this.s.dt.rows().data().toArray(),e=this.s.dt.rows(),d=[];this.s.dtPane.clear();for(var g=0;g<a.length;g++){var h=
a[g],m=""!==h.label?h.label:this.emptyMessage(),k=h.className,r=m,v="function"===typeof h.value?h.value:[],t=0,u=m,q=0;if("function"===typeof h.value){for(var w=0;w<b.length;w++)h.value.call(this.s.dt,b[w],f[0][w])&&t++;for(w=0;w<c.length;w++)h.value.call(this.s.dt,c[w],e[0][w])&&q++;"function"!==typeof v&&v.push(h.filter)}(!this.c.cascadePanes||this.c.cascadePanes&&0!==t)&&d.push(this.addRow(r,v,t,q,u,m,k))}return d}};n.prototype._getOptions=function(){var a=this.s.dt.settings()[0].aoColumns[this.s.index].searchPanes,
b=l.extend(!0,{},n.defaults,{emptyMessage:!1,orthogonal:{threshold:null},threshold:null},a);void 0!==a&&void 0!==a.hideCount&&void 0===a.viewCount&&(b.viewCount=!a.hideCount);return b};n.prototype._makeSelection=function(){this.updateTable();this.s.updating=!0;this.s.dt.draw();this.s.updating=!1};n.prototype._populatePane=function(a){void 0===a&&(a=!1);var b=this.s.dt;this.s.rowData.arrayFilter=[];this.s.rowData.bins={};var f=this.s.dt.settings()[0];if(!this.s.dt.page.info().serverSide){var c=0;for(a=
(!this.c.cascadePanes&&!this.c.viewTotal||this.s.clearing||a?b.rows().indexes():b.rows({search:"applied"}).indexes()).toArray();c<a.length;c++)this._populatePaneArray(a[c],this.s.rowData.arrayFilter,f)}};n.prototype._populatePaneArray=function(a,b,f,c){void 0===c&&(c=this.s.rowData.bins);var e=this.s.colOpts;if("string"===typeof e.orthogonal)f=f.oApi._fnGetCellData(f,a,this.s.index,e.orthogonal),this.s.rowData.filterMap.set(a,f),this._addOption(f,f,f,f,b,c);else{var d=f.oApi._fnGetCellData(f,a,this.s.index,
e.orthogonal.search);null===d&&(d="");"string"===typeof d&&(d=d.replace(/<[^>]*>/g,""));this.s.rowData.filterMap.set(a,d);c[d]?c[d]++:(c[d]=1,this._addOption(d,f.oApi._fnGetCellData(f,a,this.s.index,e.orthogonal.display),f.oApi._fnGetCellData(f,a,this.s.index,e.orthogonal.sort),f.oApi._fnGetCellData(f,a,this.s.index,e.orthogonal.type),b,c));this.s.rowData.totalOptions++}};n.prototype._reloadSelect=function(a){if(void 0!==a){for(var b,f=0;f<a.searchPanes.panes.length;f++)if(a.searchPanes.panes[f].id===
this.s.index){b=f;break}if(void 0!==b){f=this.s.dtPane;var c=f.rows({order:"index"}).data().map(function(g){return null!==g.filter?g.filter.toString():null}).toArray(),e=0;for(a=a.searchPanes.panes[b].selected;e<a.length;e++){b=a[e];var d=-1;null!==b&&(d=c.indexOf(b.toString()));-1<d&&(this.s.serverSelecting=!0,f.row(d).select(),this.s.serverSelecting=!1)}}}};n.prototype._search=function(a,b){for(var f=this.s.colOpts,c=this.s.dt,e=0,d=this.selections;e<d.length;e++){var g=d[e];"string"===typeof g.filter&&
"string"===typeof a&&(g.filter=g.filter.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"'));if(Array.isArray(a)){if(-1!==a.indexOf(g.filter))return!0}else if("function"===typeof g.filter)if(g.filter.call(c,c.row(b).data(),b)){if("or"===f.combiner)return!0}else{if("and"===f.combiner)return!1}else if(a===g.filter||("string"!==typeof a||0!==a.length)&&a==g.filter||null===g.filter&&"string"===typeof a&&""===a)return!0}return"and"===f.combiner?!0:!1};n.prototype._searchContSetup=
function(){this.c.controls&&this.s.colOpts.controls&&l(this.dom.searchButton).appendTo(this.dom.searchLabelCont);!1===this.c.dtOpts.searching||!1===this.s.colOpts.dtOpts.searching||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.dtOpts&&void 0!==this.customPaneSettings.dtOpts.searching&&!this.customPaneSettings.dtOpts.searching||l(this.dom.searchLabelCont).appendTo(this.dom.searchCont)};n.prototype._searchExtras=function(){var a=this.s.updating;this.s.updating=!0;var b=this.s.dtPane.rows({selected:!0}).data().pluck("filter").toArray(),
f=b.indexOf(this.emptyMessage()),c=l(this.s.dtPane.table().container());-1<f&&(b[f]="");0<b.length?c.addClass(this.classes.selected):0===b.length&&c.removeClass(this.classes.selected);this.s.updating=a};n.prototype._uniqueRatio=function(a,b){return 0<b&&(0<this.s.rowData.totalOptions&&!this.s.dt.page.info().serverSide||this.s.dt.page.info().serverSide&&0<this.s.tableLength)?a/this.s.rowData.totalOptions:1};n.prototype._updateCommon=function(a){void 0===a&&(a=!1);if(!(this.s.dt.page.info().serverSide||
void 0===this.s.dtPane||this.s.filteringActive&&!this.c.cascadePanes&&!0!==a||!0===this.c.cascadePanes&&!0===this.s.selectPresent||this.s.lastSelect&&this.s.lastCascade)){var b=this.s.colOpts,f=this.s.dtPane.rows({selected:!0}).data().toArray();a=l(this.s.dtPane.table().node()).parent()[0].scrollTop;var c=this.s.rowData;this.s.dtPane.clear();if(this.colExists){0===c.arrayFilter.length?this._populatePane(!this.s.filteringActive):this.c.cascadePanes&&this.s.dt.rows().data().toArray().length===this.s.dt.rows({search:"applied"}).data().toArray().length?
(c.arrayFilter=c.arrayOriginal,c.bins=c.binsOriginal):(this.c.viewTotal||this.c.cascadePanes)&&this._populatePane(!this.s.filteringActive);this.c.viewTotal?this._detailsPane():c.binsTotal=c.bins;this.c.viewTotal&&!this.c.cascadePanes&&(c.arrayFilter=c.arrayTotals);for(var e=function(m){if(m&&(void 0!==c.bins[m.filter]&&0!==c.bins[m.filter]&&d.c.cascadePanes||!d.c.cascadePanes||d.s.clearing)){var k=d.addRow(m.display,m.filter,d.c.viewTotal?void 0!==c.bins[m.filter]?c.bins[m.filter]:0:c.bins[m.filter],
d.c.viewTotal?String(c.binsTotal[m.filter]):c.bins[m.filter],m.sort,m.type),r=f.findIndex(function(v){return v.filter===m.filter});-1!==r&&(k.select(),f.splice(r,1))}},d=this,g=0,h=c.arrayFilter;g<h.length;g++)e(h[g])}if(void 0!==b.searchPanes&&void 0!==b.searchPanes.options||void 0!==b.options||null!==this.customPaneSettings&&void 0!==this.customPaneSettings.options)for(e=function(m){var k=f.findIndex(function(r){if(r.display===m.data().display)return!0});-1!==k&&(m.select(),f.splice(k,1))},g=0,
h=this._getComparisonRows();g<h.length;g++)b=h[g],e(b);for(e=0;e<f.length;e++)b=f[e],b=this.addRow(b.display,b.filter,0,this.c.viewTotal?b.total:0,b.display,b.display),this.s.updating=!0,b.select(),this.s.updating=!1;this.s.dtPane.draw();this.s.dtPane.table().node().parentNode.scrollTop=a}};n.version="1.3.0";n.classes={buttonGroup:"dtsp-buttonGroup",buttonSub:"dtsp-buttonSub",clear:"dtsp-clear",clearAll:"dtsp-clearAll",clearButton:"clearButton",container:"dtsp-searchPane",countButton:"dtsp-countButton",
disabledButton:"dtsp-disabledButton",hidden:"dtsp-hidden",hide:"dtsp-hide",layout:"dtsp-",name:"dtsp-name",nameButton:"dtsp-nameButton",nameCont:"dtsp-nameCont",narrow:"dtsp-narrow",paneButton:"dtsp-paneButton",paneInputButton:"dtsp-paneInputButton",pill:"dtsp-pill",search:"dtsp-search",searchCont:"dtsp-searchCont",searchIcon:"dtsp-searchIcon",searchLabelCont:"dtsp-searchButtonCont",selected:"dtsp-selected",smallGap:"dtsp-smallGap",subRow1:"dtsp-subRow1",subRow2:"dtsp-subRow2",subRowsContainer:"dtsp-subRowsContainer",
title:"dtsp-title",topRow:"dtsp-topRow"};n.defaults={cascadePanes:!1,clear:!0,combiner:"or",container:function(a){return a.table().container()},controls:!0,dtOpts:{},emptyMessage:null,hideCount:!1,i18n:{clearPane:"&times;",count:"{total}",countFiltered:"{shown} ({total})",emptyMessage:"<em>No data</em>"},layout:"auto",name:void 0,orderable:!0,orthogonal:{display:"display",filter:"filter",hideCount:!1,search:"filter",show:void 0,sort:"sort",threshold:.6,type:"type",viewCount:!0},preSelect:[],threshold:.6,
viewCount:!0,viewTotal:!1};return n}(),p,B,F=function(){function n(a,b,f){var c=this;void 0===f&&(f=!1);this.regenerating=!1;if(!B||!B.versionCheck||!B.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");if(!B.select)throw Error("SearchPane requires Select");var e=new B.Api(a);this.classes=p.extend(!0,{},n.classes);this.c=p.extend(!0,{},n.defaults,b);this.dom={clearAll:p('<button type="button">Clear All</button>').addClass(this.classes.clearAll),container:p("<div/>").addClass(this.classes.panes).text(e.i18n("searchPanes.loadMessage",
this.c.i18n.loadMessage)),emptyMessage:p("<div/>").addClass(this.classes.emptyMessage),options:p("<div/>").addClass(this.classes.container),panes:p("<div/>").addClass(this.classes.container),title:p("<div/>").addClass(this.classes.title),titleRow:p("<div/>").addClass(this.classes.titleRow),wrapper:p("<div/>")};this.s={colOpts:[],dt:e,filterCount:0,filterPane:-1,page:0,panes:[],selectionList:[],serverData:{},stateRead:!1,updating:!1};if(void 0===e.settings()[0]._searchPanes){this._getState();if(this.s.dt.page.info().serverSide)e.on("preXhr.dt",
function(d,g,h){void 0===h.searchPanes&&(h.searchPanes={});void 0===h.searchPanes_null&&(h.searchPanes_null={});d=0;for(g=c.s.selectionList;d<g.length;d++){var m=g[d],k=c.s.dt.column(m.index).dataSrc();void 0===h.searchPanes[k]&&(h.searchPanes[k]={});void 0===h.searchPanes_null[k]&&(h.searchPanes_null[k]={});for(var r=0;r<m.rows.length;r++)h.searchPanes[k][r]=m.rows[r].filter,null===h.searchPanes[k][r]&&(h.searchPanes_null[k][r]=!0)}});e.on("xhr",function(d,g,h,m){h&&h.searchPanes&&h.searchPanes.options&&
(c.s.serverData=h,c.s.serverData.tableLength=h.recordsTotal,c._serverTotals())});e.settings()[0]._searchPanes=this;this.dom.clearAll.text(e.i18n("searchPanes.clearMessage",this.c.i18n.clearMessage));if(this.s.dt.settings()[0]._bInitComplete||f)this._paneDeclare(e,a,b);else e.one("preInit.dt",function(d){c._paneDeclare(e,a,b)});return this}}n.prototype.clearSelections=function(){this.dom.container.find("."+this.classes.search.replace(/\s+/g,".")).each(function(){p(this).val("");p(this).trigger("input")});
for(var a=[],b=0,f=this.s.panes;b<f.length;b++){var c=f[b];void 0!==c.s.dtPane&&a.push(c.clearPane())}return a};n.prototype.getNode=function(){return this.dom.container};n.prototype.rebuild=function(a,b){void 0===a&&(a=!1);void 0===b&&(b=!1);p(this.dom.emptyMessage).remove();var f=[];!1===a&&p(this.dom.panes).empty();for(var c=0,e=this.s.panes;c<e.length;c++){var d=e[c];if(!1===a||d.s.index===a)d.clearData(),f.push(d.rebuildPane(void 0!==this.s.selectionList[this.s.selectionList.length-1]?d.s.index===
this.s.selectionList[this.s.selectionList.length-1].index:!1,this.s.dt.page.info().serverSide?this.s.serverData:void 0,null,b)),p(this.dom.panes).append(d.dom.container)}this.c.cascadePanes||this.c.viewTotal?this.redrawPanes(!0):this._updateSelection();this._updateFilterCount();this._attachPaneContainer();this.s.dt.draw();this.resizePanes();return 1===f.length?f[0]:f};n.prototype.redrawPanes=function(a){void 0===a&&(a=!1);var b=this.s.dt;if(!this.s.updating&&!this.s.dt.page.info().serverSide){for(var f=
!0,c=this.s.filterPane,e=null,d=0,g=this.s.panes;d<g.length;d++){var h=g[d];void 0!==h.s.dtPane&&(e+=h.s.dtPane.rows({selected:!0}).data().toArray().length)}if(0===e&&b.rows({search:"applied"}).data().toArray().length===b.rows().data().toArray().length)f=!1;else if(this.c.viewTotal){d=0;for(g=this.s.panes;d<g.length;d++)if(h=g[d],void 0!==h.s.dtPane){var m=h.s.dtPane.rows({selected:!0}).data().toArray().length;if(0===m)for(var k=0,r=this.s.selectionList;k<r.length;k++){var v=r[k];v.index===h.s.index&&
0!==v.rows.length&&(m=v.rows.length)}0<m&&-1===c?c=h.s.index:0<m&&(c=null)}0===e&&(c=null)}g=void 0;d=[];if(this.regenerating){b=-1;1===d.length&&null!==e&&0!==e&&(b=d[0].index);a=0;for(d=this.s.panes;a<d.length;a++)if(h=d[a],void 0!==h.s.dtPane){g=!0;h.s.filteringActive=!0;if(-1!==c&&null!==c&&c===h.s.index||!1===f||h.s.index===b)g=!1,h.s.filteringActive=!1;h.updatePane(g?f:g)}this._updateFilterCount()}else{m=0;for(k=this.s.panes;m<k.length;m++)if(h=k[m],h.s.selectPresent){this.s.selectionList.push({index:h.s.index,
protect:!1,rows:h.s.dtPane.rows({selected:!0}).data().toArray()});b.state.save();break}else h.s.deselect&&(g=h.s.index,r=h.s.dtPane.rows({selected:!0}).data().toArray(),0<r.length&&this.s.selectionList.push({index:h.s.index,protect:!0,rows:r}));if(0<this.s.selectionList.length)for(b=this.s.selectionList[this.s.selectionList.length-1].index,m=0,k=this.s.panes;m<k.length;m++)h=k[m],h.s.lastSelect=h.s.index===b;for(h=0;h<this.s.selectionList.length;h++)if(this.s.selectionList[h].index!==g||!0===this.s.selectionList[h].protect){b=
!1;for(m=h+1;m<this.s.selectionList.length;m++)this.s.selectionList[m].index===this.s.selectionList[h].index&&(b=!0);b||(d.push(this.s.selectionList[h]),this.s.selectionList[h].protect=!1)}b=-1;1===d.length&&null!==e&&0!==e&&(b=d[0].index);m=0;for(k=this.s.panes;m<k.length;m++)if(h=k[m],void 0!==h.s.dtPane){g=!0;h.s.filteringActive=!0;if(-1!==c&&null!==c&&c===h.s.index||!1===f||h.s.index===b)g=!1,h.s.filteringActive=!1;h.updatePane(g?f:!1)}this._updateFilterCount();if(0<d.length&&(d.length<this.s.selectionList.length||
a))for(this._cascadeRegen(d,e),b=d[d.length-1].index,c=0,a=this.s.panes;c<a.length;c++)h=a[c],h.s.lastSelect=h.s.index===b;else if(0<d.length)for(h=0,a=this.s.panes;h<a.length;h++)if(d=a[h],void 0!==d.s.dtPane){g=!0;d.s.filteringActive=!0;if(-1!==c&&null!==c&&c===d.s.index||!1===f||d.s.index===b)g=!1,d.s.filteringActive=!1;d.updatePane(g?f:g)}}f&&0!==e||(this.s.selectionList=[])}};n.prototype.resizePanes=function(){if("auto"===this.c.layout){var a=p(this.s.dt.searchPanes.container()).width(),b=Math.floor(a/
260);a=1;for(var f=0,c=[],e=0,d=this.s.panes;e<d.length;e++){var g=d[e];g.s.displayed&&c.push(g.s.index)}g=c.length;if(b===g)a=b;else for(;1<b;b--)if(e=g%b,0===e){a=b;f=0;break}else e>f&&(a=b,f=e);c=0!==f?c.slice(c.length-f,c.length):[];b=0;for(e=this.s.panes;b<e.length;b++)g=e[b],g.s.displayed&&(d="columns-"+(-1===c.indexOf(g.s.index)?a:f),g.resize(d))}else for(a=0,f=this.s.panes;a<f.length;a++)g=f[a],g.adjustTopRow();return this};n.prototype._attach=function(){var a=this;p(this.dom.container).removeClass(this.classes.hide);
p(this.dom.titleRow).removeClass(this.classes.hide);p(this.dom.titleRow).remove();p(this.dom.title).appendTo(this.dom.titleRow);this.c.clear&&(p(this.dom.clearAll).appendTo(this.dom.titleRow),p(this.dom.clearAll).on("click.dtsps",function(){a.clearSelections()}));p(this.dom.titleRow).appendTo(this.dom.container);for(var b=0,f=this.s.panes;b<f.length;b++)p(f[b].dom.container).appendTo(this.dom.panes);p(this.dom.panes).appendTo(this.dom.container);0===p("div."+this.classes.container).length&&p(this.dom.container).prependTo(this.s.dt);
return this.dom.container};n.prototype._attachExtras=function(){p(this.dom.container).removeClass(this.classes.hide);p(this.dom.titleRow).removeClass(this.classes.hide);p(this.dom.titleRow).remove();p(this.dom.title).appendTo(this.dom.titleRow);this.c.clear&&p(this.dom.clearAll).appendTo(this.dom.titleRow);p(this.dom.titleRow).appendTo(this.dom.container);return this.dom.container};n.prototype._attachMessage=function(){try{var a=this.s.dt.i18n("searchPanes.emptyPanes",this.c.i18n.emptyPanes)}catch(b){a=
null}if(null===a)p(this.dom.container).addClass(this.classes.hide),p(this.dom.titleRow).removeClass(this.classes.hide);else return p(this.dom.container).removeClass(this.classes.hide),p(this.dom.titleRow).addClass(this.classes.hide),p(this.dom.emptyMessage).text(a),this.dom.emptyMessage.appendTo(this.dom.container),this.dom.container};n.prototype._attachPaneContainer=function(){for(var a=0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed)return this._attach();return this._attachMessage()};n.prototype._cascadeRegen=
function(a,b){this.regenerating=!0;var f=-1;1===a.length&&null!==b&&0!==b&&(f=a[0].index);for(var c=0,e=this.s.panes;c<e.length;c++)b=e[c],b.setCascadeRegen(!0),b.setClear(!0),(void 0!==b.s.dtPane&&b.s.index===f||void 0!==b.s.dtPane)&&b.clearPane(),b.setClear(!1);this.s.dt.draw();f=this.s.dt.rows({search:"applied"}).data().toArray().length;c=this.s.dt.rows().data().toArray().length;if(c!==f){e=0;for(var d=this.s.panes;e<d.length;e++)b=d[e],b.s.forceViewTotal=!0}e=0;for(d=this.s.panes;e<d.length;e++)b=
d[e],b.updatePane(!0);this._makeCascadeSelections(a);this.s.selectionList=a;a=0;for(e=this.s.panes;a<e.length;a++)b=e[a],b.setCascadeRegen(!1);this.regenerating=!1;if(c!==f)for(a=0,f=this.s.panes;a<f.length;a++)b=f[a],b.s.forceViewTotal=!1};n.prototype._checkMessage=function(){for(var a=0,b=this.s.panes;a<b.length;a++)if(!0===b[a].s.displayed){p(this.dom.emptyMessage).remove();p(this.dom.titleRow).removeClass(this.classes.hide);return}return this._attachMessage()};n.prototype._getState=function(){var a=
this.s.dt.state.loaded();a&&a.searchPanes&&void 0!==a.searchPanes.selectionList&&(this.s.selectionList=a.searchPanes.selectionList)};n.prototype._makeCascadeSelections=function(a){for(var b=0;b<a.length;b++)for(var f=function(g){if(g.s.index===a[b].index&&void 0!==g.s.dtPane){b===a.length-1&&(g.s.lastCascade=!0);0<g.s.dtPane.rows({selected:!0}).data().toArray().length&&void 0!==g.s.dtPane&&(g.setClear(!0),g.clearPane(),g.setClear(!1));for(var h=function(r){var v=!1;g.s.dtPane.rows().every(function(t){void 0!==
g.s.dtPane.row(t).data()&&void 0!==r&&g.s.dtPane.row(t).data().filter===r.filter&&(v=!0,g.s.dtPane.row(t).select())});v||g.addRow(r.display,r.filter,0,r.total,r.sort,r.type,r.className).select()},m=0,k=a[b].rows;m<k.length;m++)h(k[m]);g.s.dtPane.draw();c._updateFilterCount();g.s.lastCascade=!1}},c=this,e=0,d=this.s.panes;e<d.length;e++)f(d[e]);this.s.dt.state.save()};n.prototype._paneDeclare=function(a,b,f){var c=this;a.columns(0<this.c.columns.length?this.c.columns:void 0).eq(0).each(function(h){c.s.panes.push(new E(b,
f,h,c.c.layout,c.dom.panes))});for(var e=a.columns().eq(0).toArray().length,d=this.c.panes.length,g=0;g<d;g++)this.s.panes.push(new E(b,f,e+g,this.c.layout,this.dom.panes,this.c.panes[g]));if(0<this.c.order.length)for(e=this.c.order.map(function(h,m,k){return c._findPane(h)}),this.dom.panes.empty(),this.s.panes=e,e=0,d=this.s.panes;e<d.length;e++)this.dom.panes.append(d[e].dom.container);this.s.dt.settings()[0]._bInitComplete?this._startup(a):this.s.dt.settings()[0].aoInitComplete.push({fn:function(){c._startup(a)}})};
n.prototype._findPane=function(a){for(var b=0,f=this.s.panes;b<f.length;b++){var c=f[b];if(a===c.s.name)return c}};n.prototype._serverTotals=function(){for(var a=!1,b=!1,f=this.s.dt,c=0,e=this.s.panes;c<e.length;c++){var d=e[c];if(d.s.selectPresent){this.s.selectionList.push({index:d.s.index,protect:!1,rows:d.s.dtPane.rows({selected:!0}).data().toArray()});f.state.save();d.s.selectPresent=!1;a=!0;break}else d.s.deselect&&(b=d.s.dtPane.rows({selected:!0}).data().toArray(),0<b.length&&this.s.selectionList.push({index:d.s.index,
protect:!0,rows:b}),b=a=!0)}if(a){f=[];for(c=0;c<this.s.selectionList.length;c++){d=!1;for(e=c+1;e<this.s.selectionList.length;e++)this.s.selectionList[e].index===this.s.selectionList[c].index&&(d=!0);if(!d){e=!1;a=0;for(var g=this.s.panes;a<g.length;a++)d=g[a],d.s.index===this.s.selectionList[c].index&&0<d.s.dtPane.rows({selected:!0}).data().toArray().length&&(e=!0);e&&f.push(this.s.selectionList[c])}}this.s.selectionList=f}else this.s.selectionList=[];f=-1;if(b&&1===this.s.selectionList.length)for(b=
0,c=this.s.panes;b<c.length;b++)d=c[b],d.s.lastSelect=!1,d.s.deselect=!1,void 0!==d.s.dtPane&&0<d.s.dtPane.rows({selected:!0}).data().toArray().length&&(f=d.s.index);else if(0<this.s.selectionList.length)for(b=this.s.selectionList[this.s.selectionList.length-1].index,c=0,e=this.s.panes;c<e.length;c++)d=e[c],d.s.lastSelect=d.s.index===b,d.s.deselect=!1;else if(0===this.s.selectionList.length)for(b=0,c=this.s.panes;b<c.length;b++)d=c[b],d.s.lastSelect=!1,d.s.deselect=!1;p(this.dom.panes).empty();b=
0;for(c=this.s.panes;b<c.length;b++)d=c[b],d.s.lastSelect?d._setListeners():d.rebuildPane(void 0,this.s.dt.page.info().serverSide?this.s.serverData:void 0,d.s.index===f?!0:null,!0),p(this.dom.panes).append(d.dom.container),void 0!==d.s.dtPane&&(p(d.s.dtPane.table().node()).parent()[0].scrollTop=d.s.scrollTop,p.fn.dataTable.select.init(d.s.dtPane));this._updateSelection()};n.prototype._startup=function(a){var b=this;p(this.dom.container).text("");this._attachExtras();p(this.dom.container).append(this.dom.panes);
p(this.dom.panes).empty();var f=this.s.dt.state.loaded();if(this.c.viewTotal&&!this.c.cascadePanes&&null!==f&&void 0!==f&&void 0!==f.searchPanes&&void 0!==f.searchPanes.panes){for(var c=!1,e=0,d=f.searchPanes.panes;e<d.length;e++){var g=d[e];if(0<g.selected.length){c=!0;break}}if(c)for(c=0,e=this.s.panes;c<e.length;c++)g=e[c],g.s.showFiltered=!0}c=0;for(e=this.s.panes;c<e.length;c++)g=e[c],g.rebuildPane(void 0,0<Object.keys(this.s.serverData).length?this.s.serverData:void 0),p(this.dom.panes).append(g.dom.container);
"auto"===this.c.layout&&this.resizePanes();this.s.stateRead||null===f||void 0===f||(this.s.dt.page(f.start/this.s.dt.page.len()),this.s.dt.draw("page"));this.s.stateRead=!0;if(this.c.viewTotal&&!this.c.cascadePanes)for(f=0,c=this.s.panes;f<c.length;f++)g=c[f],g.updatePane();this._updateFilterCount();this._checkMessage();a.on("preDraw.dtsps",function(){b.s.updating||(b._updateFilterCount(),!b.c.cascadePanes&&!b.c.viewTotal||b.s.dt.page.info().serverSide?b._updateSelection():b.redrawPanes(b.c.viewTotal),
b.s.filterPane=-1)});p(window).on("resize.dtsp",B.util.throttle(function(){b.resizePanes()}));this.s.dt.on("stateSaveParams.dtsp",function(h,m,k){void 0===k.searchPanes&&(k.searchPanes={});k.searchPanes.selectionList=b.s.selectionList});if(this.s.dt.page.info().serverSide)a.off("page"),a.on("page",function(){b.s.page=b.s.dt.page()}),a.off("preXhr.dt"),a.on("preXhr.dt",function(h,m,k){void 0===k.searchPanes&&(k.searchPanes={});void 0===k.searchPanes_null&&(k.searchPanes_null={});m=h=0;for(var r=b.s.panes;m<
r.length;m++){var v=r[m],t=b.s.dt.column(v.s.index).dataSrc();void 0===k.searchPanes[t]&&(k.searchPanes[t]={});void 0===k.searchPanes_null[t]&&(k.searchPanes_null[t]={});if(void 0!==v.s.dtPane){v=v.s.dtPane.rows({selected:!0}).data().toArray();for(var u=0;u<v.length;u++)k.searchPanes[t][u]=v[u].filter,null===k.searchPanes[t][u]&&(k.searchPanes_null[t][u]=!0),h++}}b.c.viewTotal&&b._prepViewTotal(h);0<h&&(h!==b.s.filterCount?(k.start=0,b.s.page=0):k.start=b.s.page*b.s.dt.page.len(),b.s.dt.page(b.s.page),
b.s.filterCount=h)});else a.on("preXhr.dt",function(h,m,k){h=0;for(m=b.s.panes;h<m.length;h++)m[h].clearData()});this.s.dt.on("xhr",function(h,m,k,r){if(m.nTable===b.s.dt.table().node()){var v=!1;if(!b.s.dt.page.info().serverSide)b.s.dt.one("preDraw",function(){if(!v){var t=b.s.dt.page();v=!0;b.s.updating=!0;p(b.dom.panes).empty();for(var u=0,q=b.s.panes;u<q.length;u++){var w=q[u];w.clearData();w.rebuildPane(void 0!==b.s.selectionList[b.s.selectionList.length-1]?w.s.index===b.s.selectionList[b.s.selectionList.length-
1].index:!1,void 0,void 0,!0);p(b.dom.panes).append(w.dom.container)}b.s.dt.page.info().serverSide||b.s.dt.draw();b.s.updating=!1;b.c.cascadePanes||b.c.viewTotal?b.redrawPanes(b.c.cascadePanes):b._updateSelection();b._checkMessage();b.s.dt.one("draw",function(){b.s.updating=!0;b.s.dt.page(t).draw(!1);b.s.updating=!1})}})}});f=0;for(c=this.s.panes;f<c.length;f++)if(g=c[f],void 0!==g&&void 0!==g.s.dtPane&&(void 0!==g.s.colOpts.preSelect&&0<g.s.colOpts.preSelect.length||null!==g.customPaneSettings&&
void 0!==g.customPaneSettings.preSelect&&0<g.customPaneSettings.preSelect.length)){e=g.s.dtPane.rows().data().toArray().length;for(d=0;d<e;d++)(-1!==g.s.colOpts.preSelect.indexOf(g.s.dtPane.cell(d,0).data())||null!==g.customPaneSettings&&void 0!==g.customPaneSettings.preSelect&&-1!==g.customPaneSettings.preSelect.indexOf(g.s.dtPane.cell(d,0).data()))&&g.s.dtPane.row(d).select();g.updateTable()}if(void 0!==this.s.selectionList&&0<this.s.selectionList.length)for(f=this.s.selectionList[this.s.selectionList.length-
1].index,c=0,e=this.s.panes;c<e.length;c++)g=e[c],g.s.lastSelect=g.s.index===f;0<this.s.selectionList.length&&this.c.cascadePanes&&this._cascadeRegen(this.s.selectionList,this.s.selectionList.length);this._updateFilterCount();a.on("destroy.dtsps",function(){for(var h=0,m=b.s.panes;h<m.length;h++)m[h].destroy();a.off(".dtsps");p(b.dom.clearAll).off(".dtsps");p(b.dom.container).remove();b.clearSelections()});if(this.c.clear)p(this.dom.clearAll).on("click.dtsps",function(){b.clearSelections()});a.settings()[0]._searchPanes=
this;this.s.dt.state.save()};n.prototype._prepViewTotal=function(a){for(var b=this.s.filterPane,f=!1,c=0,e=this.s.panes;c<e.length;c++){var d=e[c];if(void 0!==d.s.dtPane){var g=d.s.dtPane.rows({selected:!0}).data().toArray().length;0<g&&-1===b?(b=d.s.index,f=!0):0<g&&(b=null)}}null!==a&&0!==a&&(b=null);a=0;for(c=this.s.panes;a<c.length;a++)if(d=c[a],void 0!==d.s.dtPane&&(d.s.filteringActive=!0,-1!==b&&null!==b&&b===d.s.index||!1===f))d.s.filteringActive=!1};n.prototype._updateFilterCount=function(){for(var a=
0,b=0,f=this.s.panes;b<f.length;b++){var c=f[b];void 0!==c.s.dtPane&&(a+=c.getPaneCount())}b=this.s.dt.i18n("searchPanes.title",this.c.i18n.title,a);p(this.dom.title).text(b);void 0!==this.c.filterChanged&&"function"===typeof this.c.filterChanged&&this.c.filterChanged.call(this.s.dt,a);0===a?p(this.dom.clearAll).addClass(this.classes.disabledButton).attr("disabled","true"):p(this.dom.clearAll).removeClass(this.classes.disabledButton).removeAttr("disabled")};n.prototype._updateSelection=function(){this.s.selectionList=
[];for(var a=0,b=this.s.panes;a<b.length;a++){var f=b[a];void 0!==f.s.dtPane&&this.s.selectionList.push({index:f.s.index,protect:!1,rows:f.s.dtPane.rows({selected:!0}).data().toArray()})}this.s.dt.state.save()};n.version="1.3.0";n.classes={clear:"dtsp-clear",clearAll:"dtsp-clearAll",container:"dtsp-searchPanes",disabledButton:"dtsp-disabledButton",emptyMessage:"dtsp-emptyMessage",hide:"dtsp-hidden",panes:"dtsp-panesContainer",search:"dtsp-search",title:"dtsp-title",titleRow:"dtsp-titleRow"};n.defaults=
{cascadePanes:!1,clear:!0,columns:[],container:function(a){return a.table().container()},filterChanged:void 0,i18n:{clearMessage:"Clear All",clearPane:"&times;",collapse:{0:"SearchPanes",_:"SearchPanes (%d)"},count:"{total}",countFiltered:"{shown} ({total})",emptyMessage:"<em>No data</em>",emptyPanes:"No SearchPanes",loadMessage:"Loading Search Panes...",title:"Filters Active - %d"},layout:"auto",order:[],panes:[],viewTotal:!1};return n}();(function(n){"function"===typeof define&&define.amd?define(["jquery",
"datatables.net"],function(a){return n(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net")(a,b).$);return n(b,a,a.document)}:n(window.jQuery,window,document)})(function(n,a,b){function f(e,d,g){void 0===d&&(d=null);void 0===g&&(g=!1);e=new c.Api(e);d=d?d:e.init().searchPanes||c.defaults.searchPanes;return(new F(e,d,g)).getNode()}G(n);H(n);var c=n.fn.dataTable;n.fn.dataTable.SearchPanes=F;n.fn.DataTable.SearchPanes=
F;n.fn.dataTable.SearchPane=E;n.fn.DataTable.SearchPane=E;a=n.fn.dataTable.Api.register;a("searchPanes()",function(){return this});a("searchPanes.clearSelections()",function(){return this.iterator("table",function(e){e._searchPanes&&e._searchPanes.clearSelections()})});a("searchPanes.rebuildPane()",function(e,d){return this.iterator("table",function(g){g._searchPanes&&g._searchPanes.rebuild(e,d)})});a("searchPanes.resizePanes()",function(){var e=this.context[0];return e._searchPanes?e._searchPanes.resizePanes():
null});a("searchPanes.container()",function(){var e=this.context[0];return e._searchPanes?e._searchPanes.getNode():null});n.fn.dataTable.ext.buttons.searchPanesClear={action:function(e,d,g,h){d.searchPanes.clearSelections()},text:"Clear Panes"};n.fn.dataTable.ext.buttons.searchPanes={action:function(e,d,g,h){e.stopPropagation();this.popover(h._panes.getNode(),{align:"dt-container"});h._panes.rebuild(void 0,!0)},config:{},init:function(e,d,g){var h=new n.fn.dataTable.SearchPanes(e,n.extend({filterChanged:function(k){e.button(d).text(e.i18n("searchPanes.collapse",
h.c.i18n.collapse,k))}},g.config)),m=e.i18n("searchPanes.collapse",h.c.i18n.collapse,0);e.button(d).text(m);g._panes=h},text:"Search Panes"};n(b).on("preInit.dt.dtsp",function(e,d,g){"dt"===e.namespace&&(d.oInit.searchPanes||c.defaults.searchPanes)&&(d._searchPanes||f(d,null,!0))});c.ext.feature.push({cFeature:"P",fnInit:f});c.ext.features&&c.ext.features.register("searchPanes",f)})})();


/*!
 Bootstrap integration for DataTables' SearchPanes
 ©2016 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-dt","datatables.net-searchpanes"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-dt")(a,b).$);b.fn.dataTable.SearchPanes||require("datatables.net-searchpanes")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b){return c.fn.dataTable.searchPanes});


