{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "settings": {"appearanceTools": true, "color": {"defaultDuotone": false, "defaultPalette": false, "defaultGradients": false, "duotone": [{"colors": ["#111111", "#ffffff"], "slug": "duotone-1", "name": "Black and white"}, {"colors": ["#111111", "#C2A990"], "slug": "duotone-2", "name": "Black and sandstone"}, {"colors": ["#111111", "#D8613C"], "slug": "duotone-3", "name": "Black and rust"}, {"colors": ["#111111", "#B1C5A4"], "slug": "duotone-4", "name": "Black and sage"}, {"colors": ["#111111", "#B5BDBC"], "slug": "duotone-5", "name": "Black and pastel blue"}], "gradients": [{"slug": "gradient-1", "gradient": "linear-gradient(to bottom, #cfcabe 0%, #F9F9F9 100%)", "name": "Vertical soft beige to white"}, {"slug": "gradient-2", "gradient": "linear-gradient(to bottom, #C2A990 0%, #F9F9F9 100%)", "name": "Vertical soft sandstone to white"}, {"slug": "gradient-3", "gradient": "linear-gradient(to bottom, #D8613C 0%, #F9F9F9 100%)", "name": "Vertical soft rust to white"}, {"slug": "gradient-4", "gradient": "linear-gradient(to bottom, #B1C5A4 0%, #F9F9F9 100%)", "name": "Vertical soft sage to white"}, {"slug": "gradient-5", "gradient": "linear-gradient(to bottom, #B5BDBC 0%, #F9F9F9 100%)", "name": "Vertical soft mint to white"}, {"slug": "gradient-6", "gradient": "linear-gradient(to bottom, #A4A4A4 0%, #F9F9F9 100%)", "name": "Vertical soft pewter to white"}, {"slug": "gradient-7", "gradient": "linear-gradient(to bottom, #cfcabe 50%, #F9F9F9 50%)", "name": "Vertical hard beige to white"}, {"slug": "gradient-8", "gradient": "linear-gradient(to bottom, #C2A990 50%, #F9F9F9 50%)", "name": "Vertical hard sandstone to white"}, {"slug": "gradient-9", "gradient": "linear-gradient(to bottom, #D8613C 50%, #F9F9F9 50%)", "name": "Vertical hard rust to white"}, {"slug": "gradient-10", "gradient": "linear-gradient(to bottom, #B1C5A4 50%, #F9F9F9 50%)", "name": "Vertical hard sage to white"}, {"slug": "gradient-11", "gradient": "linear-gradient(to bottom, #B5BDBC 50%, #F9F9F9 50%)", "name": "Vertical hard mint to white"}, {"slug": "gradient-12", "gradient": "linear-gradient(to bottom, #A4A4A4 50%, #F9F9F9 50%)", "name": "Vertical hard pewter to white"}], "palette": [{"color": "#ffffff", "name": "Base", "slug": "base"}, {"color": "#374641", "name": "Contrast", "slug": "contrast"}, {"color": "#C68566", "name": "Primary", "slug": "primary"}, {"color": "#B79B91", "name": "Primary-1", "slug": "primary-1"}, {"color": "#131C19", "name": "Secondary", "slug": "secondary"}, {"color": "#4C4C65", "name": "Secondary-1", "slug": "secondary-1"}, {"color": "#D7DAD9", "name": "Gray / 100", "slug": "gray-100"}, {"color": "#F2EBE3", "name": "BG / 1", "slug": "bg-1"}, {"color": "#F7F0E8", "name": "BG / 2", "slug": "bg-2"}, {"color": "#F7F7F7", "name": "BG / 3", "slug": "bg-3"}, {"color": "#EDE8F6", "name": "BG / 4", "slug": "bg-4"}, {"color": "#FCFAF6", "name": "BG / 5", "slug": "bg-5"}, {"color": "#55694A", "name": "Ebony", "slug": "ebony"}, {"color": "#E8F098", "name": "Key Lime", "slug": "key-lime"}, {"color": "#4bb1cf", "name": "Info", "slug": "info"}, {"color": "#15CD72", "name": "Success", "slug": "success"}, {"color": "#EDE04D", "name": "Warning", "slug": "warning"}, {"color": "#ED4F32", "name": "Danger", "slug": "danger"}]}, "layout": {"contentSize": "924px", "wideSize": "1400px"}, "spacing": {"defaultSpacingSizes": false, "margin": true, "padding": true, "spacingSizes": [{"name": "1", "size": "1rem", "slug": "10"}, {"name": "2", "size": "min(1.5rem, 2vw)", "slug": "20"}, {"name": "3", "size": "min(2.5rem, 3vw)", "slug": "30"}, {"name": "4", "size": "min(4rem, 5vw)", "slug": "40"}, {"name": "5", "size": "min(6.5rem, 8vw)", "slug": "50"}, {"name": "6", "size": "min(10.5rem, 13vw)", "slug": "60"}], "units": ["%", "px", "em", "rem", "vh", "vw"]}, "typography": {"fluid": true, "defaultFontSizes": false, "fontFamilies": [{"fontFace": [{"fontFamily": "Outfit", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "400 500 600 700", "src": ["file:./assets/fonts/Outfit/Outfit-VariableFont_wght.woff2"]}], "fontFamily": "\"Outfit\", sans-serif", "name": "Outfit", "slug": "body"}, {"fontFace": [{"fontFamily": "<PERSON>", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "400 500 600 700", "src": ["file:./assets/fonts/Sen/Sen-VariableFont_wght.woff2"]}], "fontFamily": "\"Sen\", sans-serif", "name": "<PERSON>", "slug": "heading"}, {"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "400 500", "src": ["file:./assets/fonts/Jost/Jost-VariableFont_wght.woff2"]}], "fontFamily": "\"Jost\", sans-serif", "name": "<PERSON><PERSON>", "slug": "jost"}, {"fontFamily": "-apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif", "name": "System Sans-serif", "slug": "system-sans-serif"}, {"fontFamily": "Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-Serif"}], "fontSizes": [{"name": "Medium", "slug": "medium", "size": "15px"}, {"name": "<PERSON><PERSON><PERSON>", "size": "16px", "slug": "default"}, {"fluid": false, "name": "Grande", "size": "20px", "slug": "grande"}, {"fluid": false, "name": "Large", "size": "24px", "slug": "large"}, {"fluid": {"min": "26px", "max": "40px"}, "name": "Extra Large", "size": "40px", "slug": "x-large"}, {"fluid": {"min": "40px", "max": "80px"}, "name": "Extra Extra Large", "size": "80px", "slug": "xx-large"}], "writingMode": true}, "useRootPaddingAwareAlignments": true}, "styles": {"blocks": {"woocommerce/product-image": {"spacing": {"margin": {"bottom": "27px"}}}, "woocommerce/product-button": {"typography": {"lineHeight": "1.867"}, "css": "& .wc-block-components-product-button__button {line-height: 1.867;}"}, "woocommerce/customer-account": {"css": "padding:0; & a .wc-block-customer-account__account-icon{padding:0;} & a {gap:10px;} & .wp-block-woocommerce-customer-account a{color:currentcolor;}"}, "woocommerce/filter-wrapper": {"typography": {"fontSize": "17px"}, "css": "& .wc-block-components-checkbox label{font-size:17px;} & .wc-block-components-checkbox-list li {margin:0;} & .wc-block-components-checkbox {margin-top:12px;} & .wc-block-attribute-filter {margin-bottom:0;}"}, "core/query-pagination": {"spacing": {"margin": {"bottom": "0", "right": "0"}}}, "core/query-pagination-numbers": {"spacing": {"margin": {"bottom": "0 !important", "right": "0 !important"}}}, "core/query-pagination-next": {"spacing": {"margin": {"bottom": "0 !important", "right": "0 !important"}}}, "core/query-pagination-previous": {"spacing": {"margin": {"bottom": "0 !important", "right": "0 !important"}}}, "core/avatar": {"border": {"radius": "90px"}}, "woocommerce/product-categories": {"typography": {"fontSize": "17px"}, "css": "& .wc-block-product-categories-list-item:not(:last-child){margin-bottom:10px}"}, "core/calendar": {"color": {"text": "var(--wp--preset--color--contrast)"}, "css": ".wp-block-calendar table:where(:not(.has-text-color)) th{background-color:var(--wp--preset--color--contrast);color:var(--wp--preset--color--base);border-color:var(--wp--preset--color--contrast)} & table:where(:not(.has-text-color)) td{border-color:var(--wp--preset--color--contrast)}"}, "core/categories": {"spacing": {"padding": {"left": "0px", "right": "0px"}}, "css": "& {list-style-type:none;} & li{margin-bottom: 0.5rem;}"}, "core/code": {"border": {"color": "var(--wp--preset--color--contrast)", "radius": "0"}, "color": {"background": "var(--wp--preset--color--base-2)", "text": "var(--wp--preset--color--contrast)"}, "spacing": {"padding": {"bottom": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "left": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "right": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "top": "calc(var(--wp--preset--spacing--30) + 0.75rem)"}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontWeight": "400", "lineHeight": "1.6"}}, "core/comment-author-name": {"color": {"text": "var(--wp--preset--color--contrast)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontWeight": "600"}}, "core/comment-content": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}, "spacing": {"margin": {"top": "var(--wp--preset--spacing--20)", "bottom": "var(--wp--preset--spacing--20)"}}}, "core/comment-date": {"color": {"text": "var(--wp--preset--color--contrast)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}, "spacing": {"margin": {"top": "0px", "bottom": "0px"}}}, "core/comment-edit-link": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/comment-reply-link": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-comments-form": {"css": "& textarea, input{border-radius:.33rem}"}, "core/comments-pagination": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/comments-pagination-next": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/comments-pagination-numbers": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/comments-pagination-previous": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/footnotes": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/gallery": {"spacing": {"margin": {"bottom": "var(--wp--preset--spacing--50)"}}}, "core/image": {"variations": {"rounded": {"border": {"radius": "var(--wp--preset--spacing--20)"}}}}, "core/list": {"spacing": {"padding": {"left": "var(--wp--preset--spacing--10)"}}}, "core/loginout": {"css": "& input{border-radius:.33rem;padding:calc(0.667em + 2px);border:1px solid #949494;}"}, "core/navigation": {"elements": {"link": {"typography": {"textDecoration": "none"}}}}, "core/post-author": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-author-name": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-date": {"color": {"text": "var(--wp--preset--color--contrast)"}, "elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-excerpt": {"typography": {"lineHeight": "1.6"}}, "core/post-featured-image": {"border": {"radius": "var(--wp--preset--spacing--20)"}}, "core/post-terms": {"elements": {"link": {"typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-title": {"elements": {"link": {"typography": {"textDecoration": "none"}}}}, "core/pullquote": {"border": {"radius": "0", "width": "0"}, "elements": {"cite": {"typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal"}}}, "spacing": {"padding": {"bottom": "var(--wp--preset--spacing--10)", "top": "var(--wp--preset--spacing--10)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--x-large)", "fontStyle": "italic", "fontWeight": "400", "letterSpacing": "0em", "lineHeight": "1.5"}}, "core/query-title": {"css": "& span {font-style: italic;}"}, "core/query-no-results": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--30)"}}}, "core/quote": {"border": {"radius": "0", "color": "var(--wp--preset--color--primary)", "left": {"width": "5px"}}, "color": {"background": "var(--wp--preset--color--bg-1)"}, "css": "& :where(p) {margin-block-start:0;margin-block-end:calc(var(--wp--preset--spacing--10) + 0.5rem);} & :where(:last-child) {margin-block-end:0;} &.has-text-align-right.is-style-plain, .rtl .is-style-plain.wp-block-quote:not(.has-text-align-center):not(.has-text-align-left){border-width: 0 2px 0 0;padding-left:calc(var(--wp--preset--spacing--20) + 0.5rem);padding-right:calc(var(--wp--preset--spacing--20) + 0.5rem);} &.has-text-align-left.is-style-plain, body:not(.rtl) .is-style-plain.wp-block-quote:not(.has-text-align-center):not(.has-text-align-right){border-width: 0 0 0 2px;padding-left:calc(var(--wp--preset--spacing--20) + 0.5rem);padding-right:calc(var(--wp--preset--spacing--20) + 0.5rem)}", "elements": {"cite": {"typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal"}}}, "spacing": {"padding": {"bottom": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "left": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "right": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "top": "calc(var(--wp--preset--spacing--30) + 0.75rem)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "italic", "lineHeight": "1.3"}, "variations": {"plain": {"border": {"color": "var(--wp--preset--color--contrast)", "radius": "0", "style": "solid", "width": "0"}, "color": {"background": "transparent"}, "spacing": {"padding": {"bottom": "var(--wp--preset--spacing--20)", "left": "var(--wp--preset--spacing--20)", "right": "var(--wp--preset--spacing--20)", "top": "var(--wp--preset--spacing--20)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontStyle": "normal", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.5"}}}}, "core/search": {"css": "& .wp-block-search__input{border-radius:.33rem}", "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}, "elements": {"button": {"border": {"radius": {"ref": "styles.elements.button.border.radius"}}}}}, "core/separator": {"border": {"color": "currentColor", "style": "solid", "width": "0 0 1px 0"}, "color": {"text": "var(--wp--preset--color--contrast)"}, "css": " &:not(.is-style-wide):not(.is-style-dots):not(.alignwide):not(.alignfull){width: var(--wp--preset--spacing--60)}"}, "core/site-tagline": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/site-title": {"elements": {"link": {":hover": {"typography": {"textDecoration": "none"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "1.2rem", "fontStyle": "normal", "fontWeight": "600"}}, "core/button": {"variations": {"outline": {"border": {"width": "1px", "color": "var(--wp--preset--color--primary)"}, "color": {"text": "var(--wp--preset--color--primary)"}, "spacing": {"padding": {"bottom": "13.6px", "left": "1rem", "right": "1rem", "top": "13.6px"}}}}, "css": " &{min-width:150px;} &.wc-block-components-product-button__button{line-height:1.867;border-radius: 0;}"}, "core/buttons": {"spacing": {"blockGap": "0.7rem"}}}, "color": {"background": "var(--wp--preset--color--base)", "text": "var(--wp--preset--color--contrast)"}, "elements": {"button": {":active": {"color": {"background": "var(--wp--preset--color--secondary)", "text": "var(--wp--preset--color--base)"}}, ":focus": {"color": {"background": "var(--wp--preset--color--secondary)", "text": "var(--wp--preset--color--base)"}, "border": {"color": "var(--wp--preset--color--secondary)"}}, ":hover": {"color": {"background": "var(--wp--preset--color--secondary)", "text": "var(--wp--preset--color--base)"}, "border": {"color": "var(--wp--preset--color--secondary)"}}, "border": {"radius": "0", "width": "1px", "style": "solid !important", "color": "var(--wp--preset--color--primary)"}, "color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--base)"}, "css": " &{min-width:150px;}", "spacing": {"padding": {"bottom": "12px", "left": "1rem", "right": "1rem", "top": "12px"}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontFamily": "var(--wp--preset--font-family--heading)", "fontWeight": "400", "lineHeight": "1.867", "textTransform": "uppercase"}}, "caption": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "0.8rem"}}, "h1": {"typography": {"fontSize": "clamp(1.125rem, -0.4464rem + 2.4554vw, 2.5rem)", "lineHeight": "1.2"}}, "h2": {"typography": {"fontSize": "clamp(1.125rem, 0.125rem + 1.5625vw, 2rem)"}}, "h3": {"typography": {"fontSize": "clamp(1.125rem, 0.4107rem + 1.1161vw, 1.75rem)"}}, "h4": {"typography": {"fontSize": "clamp(1.125rem, 0.6964rem + 0.6696vw, 1.5rem)"}}, "h5": {"typography": {"fontSize": "clamp(1rem, 0.7143rem + 0.4464vw, 1.25rem)"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--normal)"}}, "heading": {"color": {"text": "var(--wp--preset--color--secondary)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontWeight": "500"}}, "link": {"typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "none"}}, "color": {"text": "var(--wp--preset--color--secondary)"}}}, "spacing": {"blockGap": "1.2rem", "padding": {"left": "var(--wp--preset--spacing--50)", "right": "var(--wp--preset--spacing--50)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontWeight": "400", "lineHeight": "1.4"}}, "templateParts": [{"area": "header", "name": "header", "title": "Header"}, {"area": "header", "name": "header-v1", "title": "Header v1"}, {"area": "header", "name": "header-v2", "title": "Header v2"}, {"area": "header", "name": "header-v3", "title": "Header v3"}, {"area": "header", "name": "header-v4", "title": "Header v4"}, {"area": "footer", "name": "footer", "title": "Footer"}, {"area": "footer", "name": "footer-v2", "title": "Footer v2"}, {"area": "footer", "name": "footer-v3", "title": "Footer v3"}, {"area": "footer", "name": "footer-v4", "title": "Footer v4"}, {"area": "uncategorized", "name": "sidebar", "title": "Sidebar"}, {"area": "uncategorized", "name": "post-meta", "title": "Post Meta"}], "customTemplates": [{"name": "home-v1", "postTypes": ["page"], "title": "Home v1"}, {"name": "home-v2", "postTypes": ["page"], "title": "Home v2"}, {"name": "home-v3", "postTypes": ["page"], "title": "Home v3"}, {"name": "shop-full-width", "postTypes": ["post", "page"], "title": "Shop Full Width"}, {"name": "contact", "postTypes": ["page"], "title": "Contact"}, {"name": "about", "postTypes": ["page"], "title": "About"}, {"name": "page-with-sidebar", "postTypes": ["page"], "title": "Page With Sidebar"}, {"name": "page-wide", "postTypes": ["page"], "title": "Page with wide Image"}, {"name": "single-with-sidebar", "postTypes": ["post"], "title": "Single with Sidebar"}]}