<?php
/**
 * Title: Home v3 Love Skin
 * Slug: glowess/home-v3-love-skin
 * Categories: featured
 * Keywords: Home v3 Love Skin
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Home v3 love skin"},"align":"wide","className":"home-v3-love-skin","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"29px","left":"0","right":"0"},"blockGap":"0"}},"layout":{"type":"constrained","contentSize":"1384px"}} -->
<div class="wp-block-group alignwide home-v3-love-skin" style="padding-top:var(--wp--preset--spacing--30);padding-right:0;padding-bottom:29px;padding-left:0"><!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|30","padding":{"left":"0","top":"2px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:2px;padding-left:0"><!-- wp:group {"style":{"spacing":{"blockGap":"0","padding":{"bottom":"0px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-bottom:0px"><!-- wp:heading {"textAlign":"center","style":{"typography":{"fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}}} -->
<h2 class="wp-block-heading has-text-align-center" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Love Your Skin Naturally', 'glowess' ); ?></h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"align":"center","style":{"typography":{"fontSize":"15px","fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"padding":{"top":"10px"}}}} -->
<p class="has-text-align-center" style="padding-top:10px;font-size:15px;font-style:normal;font-weight:400;line-height:2"><?php echo esc_html__( 'Our universally agreed, most-loved products.', 'glowess' ); ?></p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"vertical-slider slick-nav-group","style":{"spacing":{"margin":{"top":"30px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group vertical-slider slick-nav-group" style="margin-top:30px"><!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:group {"className":"flex-sm-wrap row","style":{"spacing":{"blockGap":"var:preset|spacing|30","padding":{"right":"0","left":"0","top":"0"}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"left"}} -->
<div class="wp-block-group flex-sm-wrap row" style="padding-top:0;padding-right:0;padding-left:0"><!-- wp:image {"id":566,"width":"auto","height":"805px","sizeSlug":"full","linkDestination":"none","align":"center","className":"height-img","style":{"layout":{"selfStretch":"fit","flexSize":null},"border":{"radius":"15px"}}} -->
<figure class="wp-block-image aligncenter size-full is-resized has-custom-border height-img"><img src="https://transvelo.github.io/glowess/assets/images/image-82.png" alt="" class="wp-image-566" style="border-radius:15px;width:auto;height:805px"/></figure>
<!-- /wp:image -->

<!-- wp:group {"className":"pl-0","style":{"spacing":{"padding":{"right":"0","left":"0"},"blockGap":"0","margin":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group pl-0" style="margin-top:0;margin-bottom:0;padding-right:0;padding-left:0"><!-- wp:woocommerce/single-product -->
<div class="wp-block-woocommerce-single-product"><!-- wp:column {"width":"","style":{"spacing":{"padding":{"top":"0","bottom":"0"},"blockGap":"var:preset|spacing|10"}}} -->
<div class="wp-block-column" style="padding-top:0;padding-bottom:0"><!-- wp:group {"className":"height-img","style":{"spacing":{"padding":{"left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group height-img" style="padding-left:3px"><!-- wp:woocommerce/product-image {"showSaleBadge":false,"isDescendentOfSingleProductBlock":true,"width":"","height":"400px","style":{"border":{"radius":"12px"},"spacing":{"padding":{"top":"0","bottom":"0"}}}} /--></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"0","padding":{"top":"0","bottom":"0","left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:0;padding-bottom:0;padding-left:3px"><!-- wp:post-title {"textAlign":"left","isLink":true,"style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"6px","bottom":"1px"}}},"__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->

<!-- wp:woocommerce/product-rating {"isDescendentOfSingleProductBlock":true,"textColor":"secondary","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0","bottom":"0","left":"0","right":"0"}}}} /-->

<!-- wp:woocommerce/product-price {"textAlign":"left","isDescendentOfSingleProductBlock":true,"fontFamily":"heading","style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"2px","bottom":"0","left":"0","right":"0"}}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:woocommerce/single-product --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:group {"className":"flex-sm-wrap row","style":{"spacing":{"blockGap":"var:preset|spacing|30","padding":{"right":"0","left":"0","top":"0"}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"left"}} -->
<div class="wp-block-group flex-sm-wrap row" style="padding-top:0;padding-right:0;padding-left:0"><!-- wp:image {"id":566,"width":"auto","height":"805px","sizeSlug":"full","linkDestination":"none","align":"center","className":"height-img","style":{"layout":{"selfStretch":"fit","flexSize":null},"border":{"radius":"15px"}}} -->
<figure class="wp-block-image aligncenter size-full is-resized has-custom-border height-img"><img src="https://transvelo.github.io/glowess/assets/images/image-82.png" alt="" class="wp-image-566" style="border-radius:15px;width:auto;height:805px"/></figure>
<!-- /wp:image -->

<!-- wp:group {"className":"pl-0","style":{"spacing":{"padding":{"right":"0","left":"0"},"blockGap":"0","margin":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group pl-0" style="margin-top:0;margin-bottom:0;padding-right:0;padding-left:0"><!-- wp:woocommerce/single-product -->
<div class="wp-block-woocommerce-single-product"><!-- wp:column {"width":"","style":{"spacing":{"padding":{"top":"0","bottom":"0"},"blockGap":"var:preset|spacing|10"}}} -->
<div class="wp-block-column" style="padding-top:0;padding-bottom:0"><!-- wp:group {"className":"height-img","style":{"spacing":{"padding":{"left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group height-img" style="padding-left:3px"><!-- wp:woocommerce/product-image {"showSaleBadge":false,"isDescendentOfSingleProductBlock":true,"width":"","height":"400px","style":{"border":{"radius":"12px"},"spacing":{"padding":{"top":"0","bottom":"0"}}}} /--></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"0","padding":{"top":"0","bottom":"0","left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:0;padding-bottom:0;padding-left:3px"><!-- wp:post-title {"textAlign":"left","isLink":true,"style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"6px","bottom":"1px"}}},"__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->

<!-- wp:woocommerce/product-rating {"isDescendentOfSingleProductBlock":true,"textColor":"secondary","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0","bottom":"0","left":"0","right":"0"}}}} /-->

<!-- wp:woocommerce/product-price {"textAlign":"left","isDescendentOfSingleProductBlock":true,"fontFamily":"heading","style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"2px","bottom":"0","left":"0","right":"0"}}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:woocommerce/single-product --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:group {"className":"flex-sm-wrap row","style":{"spacing":{"blockGap":"var:preset|spacing|30","padding":{"right":"0","left":"0","top":"0"}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"left"}} -->
<div class="wp-block-group flex-sm-wrap row" style="padding-top:0;padding-right:0;padding-left:0"><!-- wp:image {"id":566,"width":"auto","height":"805px","sizeSlug":"full","linkDestination":"none","align":"center","className":"height-img","style":{"layout":{"selfStretch":"fit","flexSize":null},"border":{"radius":"15px"}}} -->
<figure class="wp-block-image aligncenter size-full is-resized has-custom-border height-img"><img src="https://transvelo.github.io/glowess/assets/images/image-82.png" alt="" class="wp-image-566" style="border-radius:15px;width:auto;height:805px"/></figure>
<!-- /wp:image -->

<!-- wp:group {"className":"pl-0","style":{"spacing":{"padding":{"right":"0","left":"0"},"blockGap":"0","margin":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group pl-0" style="margin-top:0;margin-bottom:0;padding-right:0;padding-left:0"><!-- wp:woocommerce/single-product -->
<div class="wp-block-woocommerce-single-product"><!-- wp:column {"width":"","style":{"spacing":{"padding":{"top":"0","bottom":"0"},"blockGap":"var:preset|spacing|10"}}} -->
<div class="wp-block-column" style="padding-top:0;padding-bottom:0"><!-- wp:group {"className":"height-img","style":{"spacing":{"padding":{"left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group height-img" style="padding-left:3px"><!-- wp:woocommerce/product-image {"showSaleBadge":false,"isDescendentOfSingleProductBlock":true,"width":"","height":"400px","style":{"border":{"radius":"12px"},"spacing":{"padding":{"top":"0","bottom":"0"}}}} /--></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"0","padding":{"top":"0","bottom":"0","left":"3px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group" style="padding-top:0;padding-bottom:0;padding-left:3px"><!-- wp:post-title {"textAlign":"left","isLink":true,"style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"6px","bottom":"1px"}}},"__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->

<!-- wp:woocommerce/product-rating {"isDescendentOfSingleProductBlock":true,"textColor":"secondary","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0","bottom":"0","left":"0","right":"0"}}}} /-->

<!-- wp:woocommerce/product-price {"textAlign":"left","isDescendentOfSingleProductBlock":true,"fontFamily":"heading","style":{"typography":{"fontSize":"16px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.75"},"spacing":{"padding":{"top":"2px","bottom":"0","left":"0","right":"0"}}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:woocommerce/single-product --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
 