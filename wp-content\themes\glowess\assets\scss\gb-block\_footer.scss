/*
################
* === Footer Style ===
################
*/
.footer-v1,
.footer-v2 {
	.wp-block-navigation__container {
		li {
			a {
				&:hover,
				&:focus {
					color: var(--wp--preset--color--primary);
				}
			}
		}
	}

	.copyright-text {
		a {
			&:hover,
			&:focus {
				color: var(--wp--preset--color--primary);
			}
		}
	}
}

.footer-language.wp-block-navigation {
	.has-child {
		.wp-block-navigation__submenu-container {
			top: auto;
    		bottom: 100%;
    		width: max-content;
		    padding: 12px 10px;
		    border: 1px solid var(--wp--preset--color--base);
		    border-radius: 0;
		    box-shadow: 0px 10px 60px 0 rgba(5, 16, 54, 0.05);
		    min-width: 130px;
		    margin-bottom: 10px;

		    a {
		    	&:hover,
		    	&:focus {
		    		color: var(--wp--preset--color--primary);
		    	}
		    }
		}

		.wp-block-navigation__submenu-icon {
	    	margin-left: 10px;
	    }

	    > a {
	    	display: flex;
		    align-items: center;
		    gap: 10px;

	    	&:before {
	    		content: "";
	    		background-image: url('./assets/images/globe.svg');
	    		width: 16px;
	    		height: 16px;
	    		display: inline-block;
	    	}
	    }
	}
}

@media(min-width: 1400px) {
	.footer-widgets .widget + .widget {
		padding-left: var(--wp--preset--spacing--30) !important;
	}
}

.footer-v4 .footer-language.wp-block-navigation .has-child > a:before,
.dark-mode.footer-v2 .footer-language.wp-block-navigation .has-child > a:before {
    filter: invert(1);
}

.footer-v3 input[type=text] {
    border-radius: 10px !important;
}

@media(max-width: 767px){
	.footer-widgets .widget {
		flex: 0 0 auto;
		width: 45%;
		margin-bottom: 25px;
	}
		
	.footer-widgets {
		justify-content: initial;
		  gap: 25px !important;
		align-items: initial;
	}
}