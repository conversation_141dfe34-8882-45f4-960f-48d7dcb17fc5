/*
################
* === BASE STYLE  ===
################
*/

*, *::before, *::after {
    box-sizing: border-box;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    --drawer-width:560px;
    --neg-drawer-width: calc(var(--drawer-width)*-2);
}

img {
    max-width: 100%;
    height: auto;
}

ins {
    text-decoration: none;
}

a,button,.wp-element-button {
    transition: .3s ease-in-out;
}

pre {
    overflow: auto;
}

iframe {
    max-width: 100%;
}

.wc-block-components-notice-banner a {
    text-decoration: none !important;
}

table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
}

table:not([class]),
table.mce-item-table {
    thead th {
        background: var(--wp--preset--color--gray-100);
        font-weight: 500;
    }

    th {
        font-weight: normal;
    }

    tbody td, th {
        padding: .5rem 1rem;
        border: 1px solid var(--wp--preset--color--gray-100);
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
 -webkit-appearance: none;
}

input[type=text],
input[type=password],
input[type=email],
input[type=number],
input[type=url],
input[type=search],
input[type=tel],
div.wpforms-container-full.wpforms-block textarea,
div.wpforms-container-full.wpforms-block :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),
.wc-block-components-form .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),
.wc-block-components-form .wc-block-components-text-input.is-active :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]), .wc-block-components-text-input :is(input[type=email], input[type=number], input[type=tel], input[type=text], input[type=url]),
textarea {
    display: block;
    width: 100%;
    padding: 14px 20px;
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
    color: var(--wp--preset--color--gray-200);
    background-color: var(--wp--preset--color--base);
    background-clip: padding-box;
    border: 1px solid var(--wp--preset--color--gray-100);
    appearance: none;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    border-radius: 0;
    font-family: var(--wp--preset--font-family--body);
    max-height: 54px;

    // @media (min-width:768px) {
    //     font-size: 17px;
    // }

    &::placeholder {
        color: var(--wp--preset--color--contrast);
        font-size: 15px;
        font-family: var(--wp--preset--font-family--body);

        // @media (min-width:768px) {
        //     font-size: 17px;
        // }
    }

    &:is(:focus,:focus:invalid) {
        color: var(--wp--preset--color--gray-200);
        background-color: var(--wp--preset--color--base);
        outline: 0;
        box-shadow: none;
        padding: 14px 20px;
        border-color: var(--wp--preset--color--contrast);
        box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
    }
}


textarea {
    max-height: initial;
}

.wc-block-components-form .wc-block-components-text-input input:-webkit-autofill+label, 
.wc-block-components-form .wc-block-components-text-input.is-active label, 
.wc-block-components-text-input input:-webkit-autofill+label, 
.wc-block-components-text-input.is-active label {
    transform: translateY(-12px) scale(1);
}

.wc-block-components-form .wc-block-components-text-input, 
.wc-block-components-text-input {
    label {
        left: 20px;
        font-size: 15px;
        text-transform: capitalize;

        // @media (min-width:768px) {
        //     font-size: 17px;
        // }
    }
    
    &.is-active {
        label {
            font-size: 15px;
            left: 14px;
            color: var(--wp--preset--color--contrast);
            background-color: var(--wp--preset--color--base);
            padding: 2px 11px;
        }
    }
}

.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, 
.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true], 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true] {
    box-shadow:none;
    background-color: var(--wp--preset--color--base);
}

.wc-block-components-combobox .wc-block-components-combobox-control .components-form-token-field__suggestions-list, .wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control .components-form-token-field__suggestions-list {
    border-color: var(--wp--preset--color--contrast);
    background-color: var(--wp--preset--color--base);
    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
    left: 1px;
}

.wc-block-components-form .wc-block-components-text-input, .wc-block-components-text-input,
.wc-block-components-country-input,
.wc-block-components-state-input {
    margin-top: 20px;
}


.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, 
.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true], 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input:focus, 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input[aria-expanded=true] {
    border-color: var(--wp--preset--color--contrast);
    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);

}

.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input, 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input {
    height: inherit;
    padding: 14.5px 20px;
    line-height: inherit;
    background-color: var(--wp--preset--color--base);
    color: var(--wp--preset--color--secondary);
    font-size: 15px;
}

.wc-block-components-form .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]), .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]),
div.wpforms-container-full.wpforms-block :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]), .wc-block-components-text-input :is(input[type=email],input[type=number],input[type=tel],input[type=text],input[type=url]) {
    height: inherit;
}

.wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input, 
.wc-block-components-form .wc-block-components-combobox .wc-block-components-combobox-control input.components-combobox-control__input {
    border-color: var(--wp--preset--color--gray-100);
    border-radius: 0;
}

.wc-block-components-combobox.is-active .wc-block-components-combobox-control label.components-base-control__label, 
.wc-block-components-combobox:focus-within .wc-block-components-combobox-control label.components-base-control__label, 
.wc-block-components-form .wc-block-components-combobox.is-active .wc-block-components-combobox-control label.components-base-control__label, 
.wc-block-components-form .wc-block-components-combobox:focus-within .wc-block-components-combobox-control label.components-base-control__label {
    transform: translateY(-10px) scale(1);
    left: 10px;
    color: var(--wp--preset--color--contrast);
    background-color: var(--wp--preset--color--base);
    padding: 2px 11px;
}

select {
    display: block;
    width: 100%;
    max-width: 100%;
    padding: 12.42px 40px 12.42px 20px;
    -moz-padding-start: calc(1rem - 3px);
    font-size: 15px;
    //font-weight: 500;
    line-height: 1.6;
    color: var(--wp--preset--color--contrast);
    background-color: var(--wp--preset--color--base);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23282931' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right .65rem center;
    background-size: 26px 12px;
    border: 1px solid var(--wp--preset--color--gray-100);
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    appearance: none;
    cursor: pointer;
    word-wrap: normal;

    &:focus {
        outline: 0;
        border-color: var(--wp--preset--color--contrast);
        box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
    }
}

// .woocommerce.wc-block-catalog-sorting select.orderby {
//     font-size: 17px;
// }

.wc-block-components-form .wc-block-components-text-input label, 
.wc-block-components-text-input label {
    transform: translateY(17px);
    color: var(--wp--preset--color--contrast);
}

.wc-block-components-button:not(.is-link):focus {
    box-shadow: none;
}

.components-combobox-control__suggestions-container:focus-within {
    box-shadow: none;
}

.select2-container--default {
    font-size: 15px;
    text-align: left;

    .select2-selection--single {
        border: 1px solid var(--wp--preset--color--gray-100);
        padding: 8px;
        border-radius: 0;

        &,.select2-selection__arrow {
            height: 54px;
        }

        .select2-selection__arrow {
            right: 16px;
        }

        .select2-selection__rendered {
            color: var(--wp--preset--color--secondary);
            padding-left: 12px;
            line-height: 2.2;
            font-size: 15px;
        }
    }

    &.select2-container--open {
        &.select2-container--below .select2-selection--single,
        &.select2-container--below .select2-selection--multiple {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }

        &.select2-container--above .select2-selection--single,
        &.select2-container--above .select2-selection--multiple {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }

        .select2-selection--single,
        .select2-selection {
            border-color: var(--wp--preset--color--contrast);
            box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
        }
    }

    .select2-results__option[aria-selected=true],
    .select2-results__option[data-selected=true] {
        background-color: var(--wp--preset--color--gray-100);
    }

    .select2-results__option--highlighted[aria-selected], 
    .select2-results__option--highlighted[data-selected] {
        color: inherit;
    }
}

.select2-dropdown {
    border-color: var(--wp--preset--color--gray-100);
    background-color: var(--wp--preset--color--base);
}

.select2-container--open .select2-dropdown {
    border-color: var(--wp--preset--color--contrast);
    box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
}

.select2-search--dropdown {
    .select2-search__field {
        padding: 10px 16px;
    }
}

.wc-block-components-radio-control,
body:not(.block-editor-page),
body:not(.edit-post-layout__metaboxes),
body:not(.edit-post-sidebar__panel),
.wc-block-checkbox-list .wc-block-components-checkbox,
div.wc-block-components-checkbox {
    input[type=checkbox],
    .wc-block-components-radio-control__input,
    .wc-block-components-checkbox__input[type=checkbox] {
        width: 14px;
        height: 14px;
        min-width: 14px;
        min-height: 14px;
        border-radius: 0;
    }
    
    input[type=checkbox],
    input[type=radio],
    .wc-block-components-checkbox input[type=checkbox] {
        margin: 0 15px 0 0;
        background-color: transparent;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        border: 1px solid var(--wp--preset--color--contrast);
        appearance: none;
        vertical-align: middle;
        cursor: pointer;
    }

    input:checked[type=radio] {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
        background-size: 20px;
    }
    
    input[type=radio]  {
        width: 14px;
        height: 14px;
        padding: 0;
        border-radius: 50%;
    }

    .wc-block-components-radio-control__option-checked {
        font-weight: 500;
    }

    .wc-block-components-radio-control__input:checked:before {
        background-color: var(--wp--preset--color--base);
        min-width: 10px;
        min-height: 10px;
    }
    
    input[type=checkbox]:active,
    input[type=radio]:active {
        filter: 100%;
    }
    
    input[type=checkbox]:focus,
    .wc-block-components-checkbox__input[type=checkbox]:focus,
    input[type=radio]:focus {
        outline: 0;
    }
    
    input[type=checkbox]:checked,
    .wc-block-components-checkbox__input[type=checkbox]:checked,
    input[type=radio]:checked,
    input[type=checkbox]:indeterminate {
        border-color: var(--wp--preset--color--contrast);   
    }

    input[type=radio]:checked {
        border-width: 3px;
    }

    input:checked[type=checkbox] {
        background-image: url("data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.29078 0.971021C9.01232 0.692189 8.56015 0.692365 8.28131 0.971021L3.73799 5.51452L1.71868 3.49523C1.43985 3.21639 0.987865 3.21639 0.709033 3.49523C0.4302 3.77406 0.4302 4.22604 0.709033 4.50487L3.23306 7.0289C3.37239 7.16823 3.55509 7.23807 3.7378 7.23807C3.92051 7.23807 4.10338 7.16841 4.24271 7.0289L9.29078 1.98065C9.56962 1.70201 9.56962 1.24984 9.29078 0.971021Z' fill='primary'/%3E%3C/svg%3E%0A");
        background-size: 17px;
        background-repeat: no-repeat;
        background-position: 50% 50%;
    }
    
    input:checked[type=checkbox],
    .wc-block-components-checkbox__input[type=checkbox]:checked {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
        background-color: var(--wp--preset--color--contrast);
        border-color: var(--wp--preset--color--contrast);
    }
    
    input[type=checkbox]:indeterminate,
    .wc-block-components-checkbox__input[type=checkbox]:indeterminate {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3e%3c/svg%3e");
    }
    
    input[type=checkbox]:disabled,
    input[type=radio]:disabled {
        pointer-events: none;
        filter: none;
        opacity: .5;
    }
}

.wc-block-components-checkbox .wc-block-components-checkbox__mark {
    fill: var(--wp--preset--color--contrast);
}

.wc-block-components-checkbox .wc-block-components-checkbox__mark {
    display: none; 
}