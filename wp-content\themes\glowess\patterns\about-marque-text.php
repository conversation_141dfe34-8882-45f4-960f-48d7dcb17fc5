<?php
/**
 * Title: About Marque Text
 * Slug: glowess/about-marque-text
 * Categories: featured
 * Keywords: About Marque Text
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"About - Marque - text"},"align":"full","style":{"spacing":{"padding":{"top":"46px","left":"var:preset|spacing|40","right":"var:preset|spacing|40"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="padding-top:46px;padding-right:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--40)"><!-- wp:group {"align":"full","style":{"spacing":{"padding":{"right":"8px","left":"8px"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignfull" style="padding-right:8px;padding-left:8px"><!-- wp:columns {"className":"glowess-scroll","style":{"spacing":{"blockGap":{"left":"0px"}}}} -->
<div class="wp-block-columns glowess-scroll"><!-- wp:column {"width":"16%"} -->
<div class="wp-block-column" style="flex-basis:16%"><!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"40px","lineHeight":"1.21","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|primary-1"}}}},"textColor":"primary-1"} -->
<h2 class="wp-block-heading has-text-align-center has-primary-1-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.21;text-transform:uppercase"><?php echo esc_html__( 'hydrate', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":"7%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:7%"><!-- wp:image {"id":166,"sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/vector-1.svg'; ?>" alt="" class="wp-image-166"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"16%"} -->
<div class="wp-block-column" style="flex-basis:16%"><!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.21"},"elements":{"link":{"color":{"text":"var:preset|color|secondary-1"}}}},"textColor":"secondary-1"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-1-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.21;text-transform:uppercase"><?php echo esc_html__( 'refresh', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":"7%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:7%"><!-- wp:image {"id":166,"sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/vector-1.svg'; ?>" alt="" class="wp-image-166"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"16%"} -->
<div class="wp-block-column" style="flex-basis:16%"><!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.21"},"elements":{"link":{"color":{"text":"var:preset|color|ebony"}}}},"textColor":"ebony","fontSize":"x-large"} -->
<h2 class="wp-block-heading has-text-align-center has-ebony-color has-text-color has-link-color has-x-large-font-size" style="font-style:normal;font-weight:500;line-height:1.21;text-transform:uppercase"><?php echo esc_html__( 'nourish', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":"7%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:7%"><!-- wp:image {"id":166,"sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/vector-1.svg'; ?>" alt="" class="wp-image-166"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"16%"} -->
<div class="wp-block-column" style="flex-basis:16%"><!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontSize":"40px","fontStyle":"normal","fontWeight":"500","lineHeight":"1.21"},"elements":{"link":{"color":{"text":"var:preset|color|primary-1"}}}},"textColor":"primary-1"} -->
<h2 class="wp-block-heading has-text-align-center has-primary-1-color has-text-color has-link-color" style="font-size:40px;font-style:normal;font-weight:500;line-height:1.21;text-transform:uppercase"><?php echo esc_html__( 'hydrate', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","width":"7%"} -->
<div class="wp-block-column is-vertically-aligned-center" style="flex-basis:7%"><!-- wp:image {"id":166,"sizeSlug":"full","linkDestination":"none","align":"center"} -->
<figure class="wp-block-image aligncenter size-full"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/vector-1.svg'; ?>" alt="" class="wp-image-166"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"width":"16%"} -->
<div class="wp-block-column" style="flex-basis:16%"><!-- wp:heading {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.21"},"elements":{"link":{"color":{"text":"var:preset|color|secondary-1"}}}},"textColor":"secondary-1","fontSize":"x-large"} -->
<h2 class="wp-block-heading has-text-align-center has-secondary-1-color has-text-color has-link-color has-x-large-font-size" style="font-style:normal;font-weight:500;line-height:1.21;text-transform:uppercase"><?php echo esc_html__( 'refresh', 'glowess' ); ?></h2>
<!-- /wp:heading --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
