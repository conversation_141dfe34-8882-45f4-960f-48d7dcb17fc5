<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit93f232098150841ceb81f2253d4e25ea
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WeLabs\\MetadataViewer\\' => 22,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WeLabs\\MetadataViewer\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'WeLabs\\MetadataViewer\\Assets' => __DIR__ . '/../..' . '/includes/Assets.php',
        'WeLabs\\MetadataViewer\\Helpers' => __DIR__ . '/../..' . '/includes/Helpers.php',
        'WeLabs\\MetadataViewer\\MetadataViewer' => __DIR__ . '/../..' . '/includes/MetadataViewer.php',
        'WeLabs\\MetadataViewer\\OrderMetaData' => __DIR__ . '/../..' . '/includes/OrderMetaData.php',
        'WeLabs\\MetadataViewer\\PostMetaData' => __DIR__ . '/../..' . '/includes/PostMetaData.php',
        'WeLabs\\MetadataViewer\\UserMetaData' => __DIR__ . '/../..' . '/includes/UserMetaData.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit93f232098150841ceb81f2253d4e25ea::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit93f232098150841ceb81f2253d4e25ea::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit93f232098150841ceb81f2253d4e25ea::$classMap;

        }, null, ClassLoader::class);
    }
}
