{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "Enable customers to filter the product collection by selecting one or more %s terms.": ["Müşterilerin bir veya daha fazla %s terimi seçerek ürün koleksiyonunu filtrelemesini sağlayın."], "%s Filter": ["%s Filtre"], "Please select a taxonomy to use this filter!": ["Lütfen bu filtreyi kullanmak için bir sınıflandırma seçin!"], "There are no products associated with %s.": ["Şununla ilişkilendirilmiş ürün yok: %s"], "Hide items with no products": ["Ürün içermeyen öğeleri gizle"], "Name (Z to A)": ["Ad (<PERSON><PERSON>den A'ya)"], "Name (A to Z)": ["Ad (<PERSON>'dan <PERSON>)"], "Count (Low to High)": ["Sayı (Düşükten Yükseğe)"], "Count (High to Low)": ["Sayı (Yüksekten Düşüğe)"], "Sort Order": ["Sıralama"], "Select a taxonomy": ["Sınıflandırma seçin"], "Select a taxonomy to filter by.": ["Filtrelenecek bir sınıflandırma seçin."], "Taxonomy Filter Settings": ["Sınıflandırma Filtresi Ayarları"], "Taxonomy": ["Sınıflandırma"], "Product counts": ["<PERSON><PERSON><PERSON><PERSON>"], "Display Style": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stili"]}}, "comment": {"reference": "assets/client/blocks/product-filter-taxonomy.js"}}