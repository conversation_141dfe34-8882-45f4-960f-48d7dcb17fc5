# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Turkish
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-07 16:51:13+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: tr\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: class-akismet-compatible-plugins.php:86
msgid "Error getting compatible plugins."
msgstr "Uyumlu eklentiler alınırken hata oluştu."

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "Paketi yükselt"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "Sitenizin ticari olarak sınıflandırılmaması gerektiğini düşünüyorsanız <a href=\"%s\">lütfen bizimle iletişime geçin</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "Mevcut aboneliğiniz <a href=\"%s\">kişisel, ticari olmayan kullanıma</a> yöneliktir. Akismet'i kullanmaya devam etmek için lütfen paketinizi yükseltin."

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "Sitenizde ticari etkinlik tespit edildi"

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "Neredeyse bitti! Akismet'i yapılandırın ve istenmeyen içeriklere elveda deyin"

#: views/setup.php:7
msgid "Choose an Akismet plan"
msgstr "Bir Akismet planı seçin"

#: class.akismet-admin.php:761
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "İzin verilmeyen yorum listesi tarafından yakalandığından bu yorum, teslim edildiğinde Akismet'e gönderilmedi."

#: class.akismet-admin.php:758
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "Başka bir şey tarafından yakalandığından bu yorum, teslim edildiğinde Akismet'e gönderilmedi."

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "Akismet kullanmaya başlamak için lütfen <a href=\"%s\" target=\"_blank\">bir paket seçin</a>."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "Sitenizi istenmeyen öğelerden koruyabilmesi için API anahtarınızın bir Akismet paketi olmalı."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "Birden yorum eşleştirme talebi."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "Eşleşen yorum bulunamadı."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "\"Yorumlar\" parametresi bir dizi olmalıdır."

#: class.akismet-admin.php:755
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet bir yeniden kontrol esnasında bu yorumu temizledi. Başka bir kullanıcı veya eklenti tarafından zaten değiştirildiğinden yorum durumu güncellenmedi."

#: class.akismet-admin.php:752
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet bir yeniden kontrol esnasında bunun istenmeyen yorum olduğunu belirledi. Başka bir kullanıcı veya eklenti tarafından zaten değiştirildiğinden yorum durumu güncellenmedi."

#: class.akismet-admin.php:749
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "Akismet bu yorumu temizledi ve web kancası üzerinden durumunu güncelledi."

#: class.akismet-admin.php:746
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "Akismet bunu istenmeyen yorum olarak yakaladı ve web kancası üzerinden durumunu güncelledi."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "Akismet artık sitenizi spamdan koruyor."

#: views/config.php:304
msgid "Account overview"
msgstr "Hesaba genel bakış"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "%1$s içinde %2$d günden fazla kalan istenmeyen iletiler otomatik olarak silinir."
msgstr[1] "%1$s içinde %2$d günden fazla kalan istenmeyen iletiler otomatik olarak silinir."

#: views/config.php:187
msgid "spam folder"
msgstr "istenmeyen klasörü"

#: views/stats.php:11
msgid "Akismet detailed stats"
msgstr "Ayrıntılı Akismet istatistikleri"

#: views/stats.php:6
msgid "Back to settings"
msgstr "Ayarlara geri dön"

#: views/config.php:268
msgid "Subscription type"
msgstr "Abonelik türü"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "GDPR gibi gizlilik yasaları kapsamında sitenizin şeffaf olmasına yardımcı olmak için Akismet, yorum formlarınızın altında kullanıcılarınıza bir bildirim gösterebilir."

#: views/config.php:154
msgid "Spam filtering"
msgstr "İstenmeyen içerik filtreleme"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "API anahtarı"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Akismet İstatistikleri"

#. Author of the plugin
#: akismet.php
msgid "Automattic - Anti-spam Team"
msgstr "Otomatik - Anti-spam Ekibi"

#. Plugin Name of the plugin
#: akismet.php
msgid "Akismet Anti-spam: Spam Protection"
msgstr "Akismet Anti-spam: Spam Koruması"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "WP-Cron, DISABLE_WP_CRON sabiti kullanılarak devre dışı bırakıldı. Yorumları yeniden kontrol etme düzgün çalışmayabilir."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:793
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s "

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(yeni bir sekmede açılır)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "%s sürümüne yükselt"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "Abonelik seviyenizi yükseltin"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "Kullanım sınırları hakkında daha fazla bilgi alın."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "%1$s tarihinden beri hesabınız %2$s API çağrısı yaptı, ancak paketinizin sınırı %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Akismet kullanımınız arka arkaya üç ay boyunca paketinizin sınırının üzerindeydi. Ayın geri kalanı için hesabınızı kısıtladık. Akismet'in istenmeyenleri engellemeye devam edebilmesi için paketinizi yükseltin."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Akismet kullanımınız arka arkaya üçüncü ay için paketinizin sınırına yaklaşıyor. Sınıra ulaştığınızda hesabınız kısıtlanacak. Akismet'in istenmeyenleri engellemeye devam edebilmesi için paketinizi yükseltin."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "Akismet kullanımınız arka arkaya iki ay boyunca paketinizin sınırının üzerindeydi. Gelecek ay sınıra ulaştığınızda hesabınız kısıtlanacak. Lütfen paketinizi yükseltmeyi göz önünde bulundurun."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "Hesabınız kısıtlandı"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "Akismet hesabı kullanımınız paketinizin sınırına yaklaşıyor"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "Akismet hesabı kullanımınız, paketinizin sınırının üzerinde"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Lütfen yeni bir anahtar girin veya <a href=\"%s\" target=\"_blank\">Akismet desteği ile iletişime geçin</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "API anahtarınız artık geçerli değil."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "İstenmeyen için kontrol yapılıyor (%1$s%)"

#: class.akismet-admin.php:809
msgid "No comment history."
msgstr "Yorum geçmişi yok."

#: class.akismet-admin.php:742
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet bu yorumu tekrar kontrol edemedi."

#: class.akismet-admin.php:734
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet bu yorumu kontrol edemedi ancak daha sonra otomatik olarak tekrar deneyecek."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:703
msgid "Comment was caught by %s."
msgstr "Yorum %s tarafından yakalandı."

#: class.akismet.php:802
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet yapılandırılmadı. Lütfen bir API anahtarı girin."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "API anahtarınızı girin"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "Farklı bir hesap oluşturun"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Bu sitede istenmeyen filtrelemesini etkinleştirmek için Akismet hesabınızı ayarlayın."

#: class.akismet-admin.php:1332
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet istenmeyen yorumlarınızı kontrol edemedi."

#: class.akismet-admin.php:514
msgid "You don&#8217;t have permission to do that."
msgstr "Bu işlemi gerçekleştirme izniniz yok."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "İstatistiklerin yanıtı çözülemedi."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "Şu anda istatistik getirilemiyor. Lütfen tekrar deneyin."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "İstatistiklerin getirileceği API anahtarını ayarlanmalıdır."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "Gizlilik bildirimini görüntülemeyin."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "Yorum formlarınızın altında bir gizlilik bildirimi görüntüleyin."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "Akismet gizlilik bildirimi"

#: views/config.php:206
msgid "Privacy"
msgstr "Gizlilik"

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "Akismet Anti-spam hizmetimizi kullanan Siteler üzerinde yorum yapan ziyaretçiler hakkında bilgi topluyoruz. Topladığımız bilgiler, Kullanıcının Site için Akismet'i nasıl kurduğuna bağlıdır ancak genellikle yorumcunun IP adresi, tarayıcı kimliği, yönlendiren ve Site URL'si (adı, kullanıcı adı, e-posta adresi ve yorumun kendisi gibi yorumcu tarafından doğrudan sağlanan diğer bilgilerle beraber) bu bilgilere dahildir."

#: class.akismet.php:430
msgid "Comment discarded."
msgstr "Yorum silindi."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "Bu sitenin API anahtarı sabit kodlu ve silinemez."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "Sağlanan değer, geçerli ve kayıtlı bir API anahtarı değil."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "Bu sitenin API anahtarı sabit kodlu ve API üzerinden değiştirilemez."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "İstatistiklerin alınacağı zaman dilimi. Seçenekler: 60 gün, 6 ay, tümü"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "Doğru ise yorum listesi sayfasındaki her bir yorum yazarının yanında onaylanan yorum sayısını göster."

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "Doğru ise Akismet en kötü spamı spam klasörüne koymak yerine otomatik olarak siler."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "12 karakterli bir Akismet API anahtarı. akismet.com/get/ adresinde mevcut"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Siteniz Akismet sunucularına bağlanamıyor."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "Bu site için %s dosyasında bir Akismet API anahtarı tanımlandı."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Manuel yapılandırma"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "Bu sayfada Akismet ayarlarınızı güncelleyebilir ve spam istatistiklerini görüntüleyebilirsiniz."

#. Description of the plugin
#: akismet.php
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Milyonlarca kişi tarafından kullanılan Akismet, blogunuzu <strong>spam'den</strong> korumanın muhtemelen dünyadaki en iyi yoludur. Akismet Anti-spam siz uyurken bile sitenizi korur. Başlamak için: Akismet eklentisini etkinleştirin ve ardından API anahtarınızı ayarlamak için Akismet Ayarları sayfanıza gidin."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "Akismet Anti-spam"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "API anahtarı ile bağlan"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "%s olarak bağlısınız."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "Jetpack ile bağlan"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Akismet'i kurmak için Jetpack bağlantınızı kullanın."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Sitenizdeki istenmeyen girdilerden kurtulun"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Bekleyen yorumları <a href=\"%s\">denetlemek</a> ister misiniz?"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "Akismet hesabınızı kurun"

#: views/config.php:36
msgid "Detailed stats"
msgstr "Ayrıntılı İstatistikler"

#: views/config.php:31
msgid "Statistics"
msgstr "İstatistikler"

#: class.akismet-admin.php:1448
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Milyonlarca kullanıcı tarafından tercih edilen Akismet <strong>blogunuzu spamden korumanın</strong> en iyi yoludur. Siteniz siz uyurken bile korunmaya devam eder. Başlamak için <a href=\"admin.php?page=akismet-key-config\">Akismet Ayarları sayfanızdan</a> API anahtarınızı oluşturun."

#: class.akismet-admin.php:1446
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Milyonlarca kullanıcı tarafından tercih edilen Akismet <strong>blogunuzu spamden korumanın</strong> en iyi yoludur. Siteniz siz uyurken bile denetlenmekte ve korunmaktadır."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1326
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s yorum spam olarak yakalandı."
msgstr[1] "%s yorum spam olarak yakalandı."

#: class.akismet-admin.php:1323
msgid "No comments were caught as spam."
msgstr "Spam olarak yakalanan yorum yok."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1319
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet %s yorum kontrol etti."
msgstr[1] "Akismet %s yorum kontrol etti."

#: class.akismet-admin.php:1316
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "Kontrol edilecek yorum yok. Akismet yalnızca denetleme bekleyen yorumları kontrol eder."

#: class.akismet.php:808
msgid "Comment not found."
msgstr "Yorum bulunamadı."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d yorum kontrol edilemedi."
msgstr[1] "%d yorum kontrol edilemedi."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d yorum istenmeyene taşındı."
msgstr[1] "%d yorum istenmeyene taşındı."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "%d yorum yapıldı."
msgstr[1] "%d yorum işlendi."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "Yorum #%d kontrol edilemedi."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "Aksimet ile bağlantı kurulmadı."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Yorum #%d spam değil."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Yorum #%d spam."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s yanlış görüntü"
msgstr[1] "%s yanlış görüntü"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s kaçırılan istenmeyen"
msgstr[1] "%s kaçırılan istenmeyen"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "Bir Akismet planınız yok."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "Akismet aboneliğiniz askıya alındı."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "Akismet planınız iptal edildi."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "Ödemenizi alamadık. Lütfen <a href=\"%s\" target=\"_blank\">ödeme bilgilerinizi güncelleyin</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "Lütfen ödeme bilgilerinizi güncelleyin."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet %d dakikanızı kurtardı!"
msgstr[1] "Akismet %d dakikanızı kurtardı!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet %d saatinizi kurtardı!"
msgstr[1] "Akismet %d saatinizi kurtardı!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1220
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet %s gününüzü kurtardı!"
msgstr[1] "Akismet %s gününüzü kurtardı!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet spamları filtreler, böylece daha önemli şeylere odaklanabilirsiniz."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "akismet.com'a bağlanılamadı. Lütfen <a href=\"%s\" target=\"_blank\">güvenlik duvarları hakkındaki rehberimizi</a> inceleyin ve sunucu yapılandırmanızı kontrol edin."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "Girdiğiniz API anahtarı doğrulanamadı."

#: views/config.php:121
msgid "All systems functional."
msgstr "Tüm sistemler işlevsel."

#: views/config.php:120
msgid "Enabled."
msgstr "Etkinleştirildi."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet daha önceki bir SSL isteğinde problemle karşılaşmış ve geçici olarak etkisizleştirmiş. Kısa bir süre sonra SSL isteklerini tekrar devreye alacaktır."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "Geçici olarak etkisiz."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Sunucunuz SSL istekleri yapamıyor; servis sağlayıcınız ile görüşün ve SSL istekleri için desteği açmalarını isteyin."

#: views/config.php:111
msgid "Disabled."
msgstr "Etkisizleştirildi."

#: views/config.php:108
msgid "SSL status"
msgstr "SSL Durumu"

#: class.akismet-admin.php:720
msgid "This comment was reported as not spam."
msgstr "Bu yorum istenmeyen yorum değil olarak bildirildi."

#: class.akismet-admin.php:712
msgid "This comment was reported as spam."
msgstr "Bu yorum istenmeyen yorum olarak bildirildi."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "http://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "http://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "API anahtarını elle gir"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "Akismet destekle iletişim kur"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "Endişe etmeyin! İletişime geçin ve bu problemi çözmek için yardım edelim."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "%s için olan aboneliğiniz askıya alındı."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "%s için aboneliğiniz iptal edildi."

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "Girdiğiniz anahtar geçersiz. Lütfen iki kere kontrol edin."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "API anahtarınızla ilgili problem var."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "<a href=\"%s\" target=\"_blank\">Sembolik bir katkıda bulunarak</a> istenmeyen e-postalarla mücadele etmemize yardım edebilir ve hesabınızı yükseltebilirsiniz."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Yardım için lütfen <a href=\"%s\" target=\"_blank\">Akismet destek</a> ile iletişim kurun."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Üyeliğinizi tekrar etkinleştirmek için lütfen <a href=\"%s\" target=\"_blank\">Akismet hesap sayfasını</a> ziyaret edin."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Güvenlik duvarınız, Akismet'in API'sine bağlanmasını engelliyor olabilir. Lütfen yöneticinizle iletişim kurun ve <a href=\"%s\" target=\"_blank\">güvenlik duvarları hakkındaki kılavuzumuza</a> bakın."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Web ana bilgisayar veya sunucu yöneticiniz PHP'nin <code>gethostbynamel</code> fonksiyonlarını devre dışı bırakmıştır.  <strong>Bu durum düzeltilene kadar Akismet doğru çalışamaz.</strong>  Lütfen web ana bilgisayar veya güvenlik duvarı yöneticinizle iletişim kurarak onlara <a href=\"%s\" target=\"_blank\">Akismet’in sistem gereksinimleri hakkındaki bu bilgiyi</a> verin."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "Ağ fonksiyonları engellenmiş."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "Daha fazla bilgi için: %s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Akismet Hata Kodu: %s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Bazı yorumlarda Akismet tarafından henüz spam kontrolü yapılmamıştır. Denetlenmek üzere geçici olarak alıkonulmuştur ve daha sonra otomatik olarak yeniden kontrol edilecektir."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "Akismet bir problem tespit etti."

#: views/config.php:312
msgid "Change"
msgstr "Değiştir"

#: views/config.php:312
msgid "Upgrade"
msgstr "Yükselt"

#: views/config.php:293
msgid "Next billing date"
msgstr "Sonraki Faturalama Tarihi"

#: views/config.php:286
msgid "Active"
msgstr "Aktif"

#: views/config.php:284
msgid "No subscription found"
msgstr "Abonelik Bulunamadı"

#: views/config.php:282
msgid "Missing"
msgstr "Kayıp"

#: views/config.php:280
msgid "Suspended"
msgstr "Askıya alındı"

#: views/config.php:278
msgid "Cancelled"
msgstr "İptal edildi"

#: views/config.php:249
msgid "Save changes"
msgstr "Değişiklikleri kaydet"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "Bu hesabın bağlantısını kes"

#: views/config.php:180
msgid "Note:"
msgstr "Not:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "İstenmeyen e-postayı her zaman incelenmek üzere Spam klasörüne taşı."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "En kötü ve en yayılmış olan spam’i asla görmeyeceğim şekilde sessizce at."

#: views/config.php:159
msgid "Akismet Anti-spam strictness"
msgstr "Akismet anti-spam seviyesi"

#: views/config.php:146
msgid "Show the number of approved comments beside each comment author."
msgstr "Her yorum yazarının yanında onaylanan yorumlarının sayısını göster"

#: views/config.php:59
msgid "Accuracy"
msgstr "Doğruluk"

#: views/config.php:54
msgid "All time"
msgstr "Tüm zamanlar"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "İstenmeyen yorum engellendi"
msgstr[1] "İstenmeyen yorum engellendi"

#: views/config.php:49
msgid "Past six months"
msgstr "Son altı ay"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1732
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "Lütfen WordPress&#8217;i <a href=\"%1$s\">güncel sürüme yükseltin</a>, veya Akismet eklentisini <a href=\"%2$s\">2.4 sürümüne indirin</a>."

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1730
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "Akismet %1$s, WordPress %2$s veya daha yüksek sürüm gerektirir."

#: class.akismet-admin.php:727
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet otomatik tekrar sırasında bu yorumu temizledi."

#: class.akismet-admin.php:724
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet otomatik deneme sırasında bu yorumu istenmeyen olarak yakaladı."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:718
msgid "%s reported this comment as not spam."
msgstr "%s bu yorumun istenmeyen olmadığını bildirdi."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:710
msgid "%s reported this comment as spam."
msgstr "%s bu yorumu istenmeyen yorum olarak bildirdi."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:775
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s bu yorumun durumunu %2$s olarak değiştirdi."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:732
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet bu yorumu kontrol edemiyor (cevap: %s) ama daha sonra otomatik olarak tekrar deneyecek."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "Akismet bu yorumu temizledi."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:769
msgid "Comment status was changed to %s"
msgstr "Yorum durumu %s olarak değiştirildi"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "Akismet bu yorumu istenmeyen olarak yakaladı."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:135
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s istenmeyen</strong> yorum <strong>Akismet</strong> tarafından engellendi"
msgstr[1] "<strong class=\"count\">%1$s istenmeyen</strong> yorum <strong>Akismet</strong> tarafından engellendi"

#: class.akismet-widget.php:99
msgid "Title:"
msgstr "Başlık:"

#: class.akismet-widget.php:94 class.akismet-widget.php:116
msgid "Spam Blocked"
msgstr "İstenmeyen yorum engellendi"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "Akismet&#8217;in yakaladığı istenmeyen yorumların sayısını göster"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Akismet bileşeni"

#: class.akismet-admin.php:1216
msgid "Cleaning up spam takes time."
msgstr "İstenmeyen yorumları temizlemek zaman alır."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1088
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Lütfen <a href=\"%s\">Akismet yapılandırmanızı</a> kontrol edin ve sorunların devam etmesi durumunda web yöneticinizle iletişime geçin."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:789
msgid "%s ago"
msgstr "%s önce"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s onaylanan"
msgstr[1] "%s onaylanan"

#: class.akismet-admin.php:638
msgid "History"
msgstr "Geçmiş"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "Yorum geçmişini göster"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "%s tarafından istenmeyen işareti kaldırıldı"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "%s tarafından istenmeyen olarak işaretlenmiş"

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "Akismet ile temizlenmiştir"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "Akismet tarafından istenmeyen olarak işaretlenmiş yorum"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "İstenmeyen kontrolü bekliyor"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:740
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet bu yorumu tekrar kontrol edemiyor (cevap: %s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet tekrar kontrol etti ve bu yorumu temizledi."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet tekrar kontrol etti ve bu yorumu istenmeyen olarak yakaladı."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "İstenmeyen yorumları kontrol et"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "Şu an istenmeyen <a href='%s'>yorum kuyruğunda</a> hiçbir şey yok."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "Şu an istenmeyen yorum kuyruğunda <a href=\"%2$s\">%1$s yorum</a> var."
msgstr[1] "Şu an istenmeyen yorum kuyruğunda <a href=\"%2$s\">%1$s yorum</a> var."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> blogunuzu istenmeyen içerikten korur. "

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> sitenizi %2$s istenmeyen yorumdan korumuş. "
msgstr[1] "<a href=\"%1$s\">Akismet</a> sitenizi %2$s istenmeyen yorumdan korumuş. "

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a> sitenizi <a href=\"%2$s\">%3$s istenmeyen yorumdan</a> korudu."
msgstr[1] "<a href=\"%1$s\">Akismet</a> sitenizi <a href=\"%2$s\">%3$s istenmeyen yorumdan</a> korudu."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "İstenmeyen"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "Hile mi yapıyorsun?"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "Akismet destek"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "Akismet S.S.S."

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "Daha fazla bilgi için:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "Abonelik durumu - aktif, iptal edilmiş veya askıya alınmış"

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "Durum"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "Akismet abonelik planı"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "Abonelik türü"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "Hesap"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "En kötü spam’i otomatik olarak atmayı veya tüm spam’leri bir spam klasörüne koymayı seçin."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "Katılık"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Yorum listesi sayfasındaki her bir yorum yazarının yanında onaylanan yorum sayısını göster."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "Yorumlar"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "API anahtarı ekle/kaldır."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "API Anahtarı"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "Akismet Yapılandırması"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "Bu sayfada, sitenizde filtrelenen istenmeyen e-postalar hakkındaki istatistikleri görebilirsiniz."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "Akismet İstatistikleri"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "Bu Anahtarı Kullan düğmesine tıklayın."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "API anahtarını kopyalayıp metin alanına yapıştırın."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "Zaten bir API anahtarınız varsa"

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "Bir API anahtarı girin"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "Bir API anahtarı edinmek için %s adresinden kayıt olun."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "Akismet servisini sitenizde etkinleştirmek için API anahtarını girmeniz gerekli."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "Yeni Akismet kullanıcısı"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "Bu sayfada Akismet eklentisini ayarlayabilirsiniz."

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "Akismet Kurulumu"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "Genel bakış"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "Tekrar ekleniyor..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(geri al)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "Adres kaldırıldı"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "Kaldırılıyor..."

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "Bu Adresi Kaldır"

#: class.akismet-admin.php:107 class.akismet-admin.php:1463
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:816 views/config.php:83
msgid "Settings"
msgstr "Ayarlar"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "Yorum Geçmişi"
