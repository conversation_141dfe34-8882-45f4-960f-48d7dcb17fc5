/*
################
* === Checkout Style  ===
################
*/

.woocommerce-checkout, 
.wp-block-woocommerce-checkout:not(.block-editor-block-list__block) {
    .woocommerce:not(.wc-block-breadcrumbs) {
        margin: clamp(2.75rem, 2.0556rem + 2.7037vw, 3rem) 0;
    }

    #order_review_heading {
        font-size: 20px;
        font-weight: 500;
        margin-top: 0px;
        font-family: var(--wp--preset--font-family--heading);
    }

    wc-order-attribution-inputs {
        display: none;
    }

    .wc-block-components-product-price__value.is-discounted {
        margin-left: 0;
    }

    form.checkout {
        margin-top: 40px;
    }

    .wc-block-components-sidebar-layout.wc-block-checkout,
    form.checkout {
        @media (min-width:1024px) {
            display: grid;
            grid-template-columns: repeat(12, minmax(0, 1fr));
            gap: 30px;

            .wc-block-components-main,
            #customer_details {
                grid-column: span 8 / span 8;
                padding: 0 !important;
            }

            .wc-block-components-sidebar, #order_review {
                grid-column: span 4 / span 4;
            }

            .wc-block-components-main, .wc-block-components-sidebar {
                width:100%;
            }
        }

        @media (min-width:1200px) {
            column-gap: 76px;
        }

        .woocommerce-billing-fields,
        .woocommerce-additional-fields,
        .woocommerce-shipping-fields {
            h3 {
                font-size: 20px;
                font-weight: 500;
                line-height: 30px;
                margin: 0px 0px 27px 0px;
                font-family: var(--wp--preset--font-family--heading);
            }
        }

        .woocommerce-shipping-fields {
           label.woocommerce-form__label-for-checkbox {
                display: flex;
                align-items: center;
            }
        }
            
        .woocommerce-billing-fields__field-wrapper,
        .woocommerce-shipping-fields__field-wrapper,
        .woocommerce-additional-fields__field-wrapper {
             > p:not([class]) {
                display: none;
            }
            
            p {
                margin-top: 0;
                margin-bottom: 24px;
            }

            @media (min-width: 768px) {
                display: grid;
                column-gap: 30px;
                grid-template-columns: repeat(12, minmax(0, 1fr));
            }

            .form-row:not(.form-row-first):not(.form-row-last) {
                @media (min-width: 768px) {
                    grid-column: span 12/span 12;
                }
            }

            .form-row-first, .form-row-last {
                @media (min-width: 768px) {
                    grid-column: span 6/span 6;
                }
            }

            .form-row.notes {
                @media (min-width: 1025px) {
                    margin: 0;
                }
            }

            label {
                font-size: 15px;
                font-weight: 400;
                margin-bottom: 10px !important;
                display: inline-block;
                color: var(--wp--preset--color--secondary);
            }
        }
    }

    .woocommerce-info {
        margin-bottom: 30px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: left;
    }

    form.checkout_coupon {
        margin-top: 20px;
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        gap: 14px;
        margin-bottom: 30px;

        p:first-child {
            width: 100%;
            margin-bottom: 0;
            margin-top: 0;
        }

        .form-row {
            display: flex;
        }
    }

    .wc-block-checkout__sidebar,
    .woocommerce-checkout-review-order {
        border: 1px solid var(--wp--preset--color--gray-100);
        border-radius: 0 !important;
        padding: 20px;
        align-self: flex-start;

        @media(min-width: 1200px) {
             padding: 30px 40px 35px;
        }

        .wc-block-components-order-summary__button-text {
            font-size: 20px;
            font-weight: 500;
            line-height: 30px;
            font-family: var(--wp--preset--font-family--heading);
        }

        .wc-block-components-panel__button {
            cursor: pointer;
        }

        .wc-block-components-order-summary__content .wc-block-components-order-summary-item__image {
            width: 70px;
            padding-bottom: 0;

            img {
                height: 70px;
                min-width: 70px;
                background-color: var(--wp--preset--color--base);
            }
        }

        .wc-block-components-order-summary-item__description,
        .wc-block-components-order-summary-item__total-price {
            margin-top: 10px;
            font-size: 15px;
        }

        .wc-block-components-order-summary-item__total-price {
            font-weight:400;
        }

        .wc-block-components-product-metadata {
            display: none;
        }
        
        .wc-block-components-totals-coupon-link{
            font-weight: 500;
            text-transform: capitalize;

            &:hover { color:var(--wp--preset--color--secondary); }
        }

        .wc-block-components-order-summary-item__quantity {
            font-size: 12px;
            background-color: var(--wp--preset--color--secondary);
            border: 0;
            color: var(--wp--preset--color--base);
        }

        .wp-block-woocommerce-checkout-order-summary-cart-items-block {
            border-width: 0;
            padding-top: 0;
        }

        .wc-block-components-totals-wrapper:last-child {
            padding-bottom: 0;
            padding-top: 0;
            border-top: 0;
        }
    }

    .woocommerce-checkout-review-order {
        .shop_table thead tr {
            font-weight: 600;
            letter-spacing: 0em;
            text-align: left;
        }

        table {
            thead tr {
                font-size: 15px;
                line-height: 26px;
                border-bottom: 1px solid var(--wp--preset--color--gray-100);
                color: var(--wp--preset--color--secondary);
                text-transform: uppercase;

                th {
                    padding-bottom: 14px;
                    font-weight: 500;
                }
            }

            tbody {
                color: var(--wp--preset--color--secondary); 
                border-bottom: 1px solid var(--wp--preset--color--gray-100);               
                tr:first-child td {
                    // padding-top: 20px;
                    padding: 20px 1px;
                }

                tr td {
                    // padding-bottom: 20px;
                    padding: 1px 1px 20px 1px;
                }
            }

            tfoot {
                color: var(--wp--preset--color--secondary);

                tr {
                    & + tr {
                        border-bottom: 1px solid var(--wp--preset--color--gray-100);
                        border-top: 1px solid var(--wp--preset--color--gray-100);
                    }
                }
            }

            tr th:last-child,
            tr td:last-child {
                text-align: right;
            }

            .cart-subtotal, .shipping {
                th,td  {
                    padding: 14px 0;
                }
            }

            .order-total {
                // border-bottom: 1px solid var(--wp--preset--color--gray-100);
                // border-top: 1px solid var(--wp--preset--color--gray-100);

                th {
                    font-weight: 500;
                    font-size: 18px;
                }

                td {
                    font-weight: 500;
                    font-size: 18px;

                    strong {
                        font-weight: 500;
                    }
                }

                th,td {
                    padding: 24px 0;
                }
            }

            .cart-subtotal, .shipping {
                th, td {
                    font-weight: 400;
                }
            }

            .shipping {
                ul {
                    list-style: none;
                    padding-left: 0;
                    margin: 0;

                    li label {
                        padding: 5px 0;
                        cursor: pointer;
                    }
                }
            }
        }

        #payment {
            ul {
                list-style: none;
                padding-left: 0;

                li label {
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 26px;
                    letter-spacing: 0em;
                    text-align: left;
                    cursor: pointer;
                }

                li:not(:last-child) {
                    margin-bottom: 22px;
                }

                li {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;

                    .payment_box {
                        line-height: 24px;
                        width: 100%;
                    }
                }
            }

            .woocommerce-terms-and-conditions-wrapper {
                padding-bottom: 1px;
                margin-bottom: 18px;
            }

            .place-order .button {
                width: 100%;
                background-color: var(--wp--preset--color--primary);
                color: var(--wp--preset--color--base);
                padding: 12px;
                font-size: 15px;

                &:hover { background-color:var(--wp--preset--color--secondary); color: var(--wp--preset--color--base);}
            }
        }
    }

    .wc-block-components-product-price__regular {
        margin-right: 10px !important;
    }

    .woocommerce-NoticeGroup-checkout, 
    .woocommerce-checkout form.checkout #wc-stripe-payment-request-wrapper, 
    .woocommerce-checkout form.checkout #wc-stripe-payment-request-button-separator {
        @media (min-width: 768px) {
            grid-column: span 12/span 12;
        }
    }

    .woocommerce-error, .wc-block-components-notice-banner.is-error {
        background-color: #fdeeee;
        color: #d54848;
        border-color: #f6a8a8;
        font-size: 14px;

        svg {
         display: none;
        }

        .wc-block-components-notice-banner__content {
            align-self: center;
            flex-basis: 100%;
            padding-right: 16px;
            white-space: normal;

            ul {
                margin: 0;
                
                li {
                    list-style: none;
                }
            }
        }
    }

    #payment .is-info {
        margin-bottom: 0;
    }

    .woocommerce-shipping-methods label {
        font-size: 14px;
        line-height: 30px;
    }
}

.wp-block-woocommerce-checkout {
    .wc-block-components-sidebar .wc-block-components-panel {
        padding: 0!important;
    }

    .wc-block-components-radio-control__option:before,
    .wc-block-components-address-card {
        border-color: var(--wp--preset--color--gray-100);
    }

    .wc-block-components-totals-wrapper {
        color: var(--wp--preset--color--secondary);
    }

    .is-large .wc-block-components-sidebar .wc-block-components-panel, 
    .is-large .wc-block-components-sidebar .wc-block-components-totals-coupon, 
    .is-large .wc-block-components-sidebar .wc-block-components-totals-item {
        padding-left: 0px;
        padding-right: 0px;
    }

    .wc-block-checkout__sidebar .wc-block-components-order-summary-item__quantity, 
    .woocommerce-checkout .woocommerce-checkout-review-order .wc-block-components-order-summary-item__quantity {       
        line-height:21px;
        font-weight: 700;
    }

    .wc-block-checkout__use-address-for-billing label {
        margin-left:0 !important;
    }

    .wc-block-components-checkbox .wc-block-components-checkbox__mark {
        display: none;
    }

    .wc-block-components-checkout-step__description {
        font-size: 15px;
        display: inline-block;
    }

    .wc-block-components-textarea {
        border-color: var(--wp--preset--color--gray-100);
    }

    .wc-block-components-shipping-rates-control,
    .wc-block-components-radio-control {
        .wc-block-components-radio-control__label-group {
            font-size: 15px;
        }
    }

    .wp-block-woocommerce-checkout-order-summary-block {
        border: 0;

        .wp-block-woocommerce-checkout-order-summary {
            &-subtotal-block,
            &-coupon-form-block {
                padding: 12px 0;
            }

            &-shipping-block {
                padding: 10px 0;
            }
        }

        .wc-block-components-totals-item,
        .wc-block-components-totals-shipping {
            .wc-block-components-totals-item__value {
                font-weight: 400;
            }
        } 

        .wc-block-components-totals-footer-item {
            @media(min-width: 1024px) {
                padding-top: 32px;
            }

            .wc-block-components-totals-item__value {
                font-size: 24px;
                line-height: 18px;
                font-weight: 500;
            }
        }

        .wc-block-components-totals-item {
            align-items: center;
        }

        .wp-block-woocommerce-checkout-order-summary-subtotal-block {
            border-bottom: 1px solid hsla(0,0%,7%,.11) !important;
        }
    }

    .wc-block-checkout__main {
        width: 70%;

        .wc-block-components-checkout-step__title {
            font-family: var(--wp--preset--font-family--heading);
            font-weight: 500;
            font-size: var(--wp--preset--font-size--grande);
        }

        .wc-block-components-form .wc-block-components-text-input, 
        .wc-block-components-text-input, 
        .wc-block-components-country-input, 
        .wc-block-components-state-input {
            margin-top: 30px;
        }

        .wc-block-components-checkbox label {
            font-size: 14px;
            align-items: center;
        }

        .wc-block-components-radio-control-accordion-content {
            padding: 20px 16px;
        }
    }

    .wc-block-checkout__shipping-option {
        .wc-block-components-radio-control__option {
            padding-left: 35px !important;
        }
    }

    .wc-block-checkout__payment-method .wc-block-components-radio-control__option {
        padding-left: 38px;
    }

    .wc-block-components-checkout-step__heading {
        margin: 30px 0 20px;
    }

    .wc-block-components-checkout-step__container {
        > p.wc-block-components-checkout-step__description {
            margin-bottom: 30px;
        } 
    }

    .wc-block-checkout__sidebar, 
    .woocommerce-checkout-review-order {
        border-radius: 0;
        padding: 20px;
        align-self: flex-start;
        width: 30%;

        @media (min-width:1200px) {
            padding: 30px 35px;
        }
    }
}

.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form, 
.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form, 
.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form, 
.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form, 
.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form, 
.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form {
    gap: 0 30px;
}

.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-large .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, 
.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-large .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input, 
.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-medium .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, 
.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-medium .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input, 
.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-small .wc-block-checkout__billing-fields .wc-block-components-address-form .wc-block-components-text-input, 
.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-select-input, 
.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-state-input, 
.is-small .wc-block-checkout__shipping-fields .wc-block-components-address-form .wc-block-components-text-input  {
    flex: 1 0 calc(50% - 15px);
}

.wc-item-meta { 
    list-style: none;
    padding-left: 0px;
    margin: 5px 0 0;

    li {
        display: flex;
        align-items: center;
        gap: 5px;

        .wc-item-meta-label {
            font-weight: 700;
        }
        p { margin: 0px;}
    }

    li + li {
        margin-top: 5px;
    }
}

.wc-block-checkout__payment-method .wc-block-components-radio-control {
    border-color: var(--wp--preset--color--gray-100);
}

.wc-block-components-totals-footer-item .wc-block-components-totals-item__label {
    font-weight: 500;
}

.is-mobile .wp-block-woocommerce-checkout-order-summary-cart-items-block.wc-block-components-totals-wrapper {

    @media(max-width: 1023px) {
         padding-left: 0 !important; 
         padding-right: 0 !important; 
    }   
}

.wp-block-woocommerce-checkout {
    .wc-block-components-form .wc-block-components-text-input:not(.is-active) label, .wc-block-components-text-input:not(.is-active) label {
        transform: translateY(-8px) !important;
    }
    .wc-blocks-components-select {
        .wc-blocks-components-select__select{
            border: 1px solid var(--wp--preset--color--gray-100);
            padding: 12.42px 40px 12.42px 20px;
            border-radius: 0;
            &:focus {
                border: 1px solid var(--wp--preset--color--contrast);
                box-shadow: 0px 0px 0px 1px var(--wp--preset--color--contrast);
            }
        }
        .wc-blocks-components-select__container{
            height: 54px;
            margin-top: 0;
        }
        .wc-blocks-components-select__label {
            top: -14px;
            font-size: 20px;
            background-color: var(--wp--preset--color--base);
            padding: 2px 11px;
            left: 14px;
        }
    } 
    .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-checkout-order-summary__title {
        margin: 0;
        .wc-block-components-checkout-order-summary__title-text{
            margin: 0 0 8px 0;
            padding: 0 0 8px 0;
            font-size: 20px;
            font-weight: 500;
            line-height: 30px;
        }
    }   
    .checkout-order-summary-block-fill .wc-block-components-totals-coupon__input.wc-block-components-text-input {
        margin-top: auto !important;
    }
    .wc-block-components-radio-control__label-group {
        box-sizing: border-box;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        .wc-block-components-radio-control__label {
            flex-grow: 1;
        }
    }

    .wc-block-components-order-summary {
        padding: 0;
    }
}    

//ss
.woocommerce-page.woocommerce-checkout main .woocommerce {
    max-width: var(--wp--style--global--wide-size);
    form .form-row input.input-text {
        height: 54px;
    }
    .select2-container .select2-selection {
        padding: 8px;
        height: 54px;
    }
    form .form-row {
        padding: 0;
    }
    form.checkout .woocommerce-billing-fields__field-wrapper label {
        line-height: 1.4;
    }
    #payment div.form-row {
        padding: 0;
        margin: 0;
    }
    form.checkout_coupon {
        border: none;
        padding: 0;
    }
    .checkout.woocommerce-checkout {
        .col2-set .col-1, .col2-set .col-2 {
            float: none;
            width: 100%;
        }
        #billing_first_name_field, #billing_last_name_field, #shipping_first_name_field, #shipping_last_name_field {
            width: 100%;
        }
        form .form-row input.input-text {
            height: 54px;
        }
    }
    .woocommerce-checkout-review-order {
        #payment {
            background: none;
        }
        table.shop_table {
            width: 100%;
            border: none;
            th {
                padding: 1px 1px 14px 1px;
            }
            td {
                border-top: 0;
            }
        }
    }
    .woocommerce-shipping-totals {
        td {
            width: 50%;
        }
        ul#shipping_method li input {
            margin: 2px 5px 5px 5px;
            vertical-align: middle;
        }
    }
    .woocommerce-checkout-review-order #payment ul li label {
        margin: 0;
    }
}
