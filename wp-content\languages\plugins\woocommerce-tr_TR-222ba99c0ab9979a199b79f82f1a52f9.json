{"translation-revision-date": "2025-08-14 10:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "tr"}, "The quantity of \"%1$s\" was changed to %2$s.": ["\"%1$s\" miktarı %2$s olarak değiştirildi."], "\"%s\" was removed from your cart.": ["\"%s\" sepetinizden kaldırıldı."], "Flat rate shipping": ["Sabit fiyatlı gönderim"], "T-Shirt": ["Tişört"], "Hoodie with Pocket": ["Cepli Kapşonlu Svetşört"], "Hoodie with Logo": ["Logolu Kapşonlu Svetşört"], "Hoodie with Zipper": ["Fermuarlı Kapüşonlu Svetşört"], "Long Sleeve Tee": ["U<PERSON>n Kollu Tişö<PERSON>"], "Polo": ["Polo"], "%s (optional)": ["%s (isteğe bağlı)"], "There was an error registering the payment method with id '%s': ": ["\"%s\" k<PERSON><PERSON><PERSON><PERSON>yle ödeme yöntemini kaydederken bir hata oluştu: "], "Orange": ["<PERSON><PERSON><PERSON>"], "Lightweight baseball cap": ["Hafif beyzbol şapkası"], "Cap": ["Şapka"], "Yellow": ["Sarı"], "Warm hat for winter": ["Kış için sıcak tutan şapka"], "Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie with Logo": ["<PERSON><PERSON><PERSON>"], "Something went wrong. Please contact us to get assistance.": ["Bir yanlışlık oldu. Yardım almak için lütfen bizimle iletişime geçin."], "The response is not a valid JSON response.": ["Yanıt geçerli bir JSON yanıtı değil."], "Unable to get cart data from the API.": ["API'den alışveriş sepeti verileri alınamıyor."], "Sales tax": ["Satış vergisi"], "Color": ["Renk"], "Small": ["Küçük"], "Size": ["<PERSON><PERSON>"], "Free shipping": ["Ücretsiz gönderim"], "Shipping": ["<PERSON><PERSON><PERSON><PERSON>"], "Fee": ["Ücret"], "Local pickup": ["Mağazadan teslim"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}