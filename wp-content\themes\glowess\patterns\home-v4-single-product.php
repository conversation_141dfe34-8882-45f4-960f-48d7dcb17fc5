<?php
/**
 * Title: Home v4 Single Product
 * Slug: glowess/home-v4-single-product
 * Categories: featured
 * Keywords: Home v4 Single Product
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"home-v4-single-product"},"align":"wide","className":"v4-single-product","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"4px"},"margin":{"bottom":"0"}}},"layout":{"type":"default"}} -->
<div class="wp-block-group alignwide v4-single-product" style="margin-bottom:0;padding-top:var(--wp--preset--spacing--30);padding-bottom:4px"><!-- wp:group {"align":"full","className":"grid","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull grid"><!-- wp:group {"align":"full","className":"grid-span-full","style":{"spacing":{"padding":{"right":"0","left":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull grid-span-full" style="padding-right:0;padding-left:0"><!-- wp:woocommerce/single-product {"align":"full"} -->
<div class="wp-block-woocommerce-single-product alignfull"><!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"var:preset|spacing|40"},"margin":{"top":"0","bottom":"0"}}}} -->
<div class="wp-block-columns" style="margin-top:0;margin-bottom:0"><!-- wp:column {"width":"48.9%","style":{"spacing":{"blockGap":"0"}}} -->
<div class="wp-block-column" style="flex-basis:48.9%"><!-- wp:woocommerce/product-image {"showSaleBadge":false,"isDescendentOfSingleProductBlock":true,"width":"","height":"","className":"relative","style":{"border":{"radius":"16px"}}} /-->

<!-- wp:image {"id":174,"width":"250px","sizeSlug":"full","linkDestination":"none","className":"absolute","style":{"border":{"radius":"8px"}}} -->
<figure class="wp-block-image size-full is-resized has-custom-border absolute"><img src="https://transvelo.github.io/glowess/assets/images/v4-single-product-1.jpg" alt="" class="wp-image-174" style="border-radius:8px;width:250px"/></figure>
<!-- /wp:image --></div>
<!-- /wp:column -->

<!-- wp:column {"verticalAlignment":"center","style":{"spacing":{"blockGap":"0"}}} -->
<div class="wp-block-column is-vertically-aligned-center"><!-- wp:group {"style":{"spacing":{"blockGap":"0"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group"><!-- wp:post-terms {"term":"product_cat","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"1.75"},"spacing":{"margin":{"bottom":"var:preset|spacing|20"}}},"textColor":"secondary","fontSize":"default","fontFamily":"heading"} /-->

<!-- wp:post-title {"isLink":true,"className":"product-title","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500","textDecoration":"underline","lineHeight":"1"},"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|20"}}},"textColor":"secondary","fontSize":"x-large","fontFamily":"heading","__woocommerceNamespace":"woocommerce/product-query/product-title"} /-->

<!-- wp:group {"className":"product-detail","style":{"spacing":{"margin":{"bottom":"var:preset|spacing|20"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group product-detail" style="margin-bottom:var(--wp--preset--spacing--20)"><!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"spacing":{"margin":{"top":"0","bottom":"0"}}},"textColor":"secondary","fontSize":"x-large","fontFamily":"heading"} -->
<p class="has-secondary-color has-text-color has-link-color has-heading-font-family has-x-large-font-size" style="margin-top:0;margin-bottom:0;font-style:normal;font-weight:500;line-height:1.2"><?php echo esc_html__( 'Vitamin A Serum', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"lineHeight":"1.2","fontStyle":"normal","fontWeight":"500"}},"textColor":"secondary","fontSize":"x-large","fontFamily":"heading"} -->
<p class="has-secondary-color has-text-color has-link-color has-heading-font-family has-x-large-font-size" style="font-style:normal;font-weight:500;line-height:1.2">Generation Glow Daily</p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:woocommerce/product-rating {"isDescendentOfSingleProductBlock":true,"textColor":"secondary","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|20"}}}} /-->

<!-- wp:group {"style":{"spacing":{"blockGap":"0","margin":{"top":"0","bottom":"var:preset|spacing|20"}}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group" style="margin-top:0;margin-bottom:var(--wp--preset--spacing--20)"><!-- wp:post-title {"className":"d-none d-lg-block","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500","lineHeight":"1.5"}},"textColor":"secondary","fontSize":"grande","fontFamily":"heading","__woocommerceNamespace":"woocommerce/product-collection/product-title"} /-->

<!-- wp:paragraph {"className":"d-none d-lg-block","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"fontStyle":"normal","fontWeight":"500"}},"textColor":"secondary"} -->
<p class="d-none d-lg-block has-secondary-color has-text-color has-link-color" style="font-style:normal;font-weight:500">  -  </p>
<!-- /wp:paragraph -->

<!-- wp:woocommerce/product-price {"textColor":"secondary","fontFamily":"heading","fontSize":"grande","style":{"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}},"typography":{"lineHeight":"1.5","fontStyle":"normal","fontWeight":"500"}}} -->
<div class="is-loading"></div>
<!-- /wp:woocommerce/product-price --></div>
<!-- /wp:group -->

<!-- wp:post-excerpt {"moreText":"","excerptLength":100,"style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast"}}},"typography":{"fontStyle":"normal","fontWeight":"400","lineHeight":"2"},"spacing":{"margin":{"top":"0","bottom":"var:preset|spacing|20"},"padding":{"bottom":"8px"}}},"textColor":"contrast","fontSize":"medium","__woocommerceNamespace":"woocommerce/product-query/product-summary"} /-->

<!-- wp:buttons {"className":"inline-img","style":{"spacing":{"margin":{"top":"0","bottom":"0"}}}} -->
<div class="wp-block-buttons inline-img" style="margin-top:0;margin-bottom:0"><!-- wp:button {"textColor":"base","width":50,"style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"border":{"radius":"8px","width":"0px","style":"none"}}} -->
<div class="wp-block-button has-custom-width wp-block-button__width-50"><a class="wp-block-button__link has-base-color has-text-color has-link-color wp-element-button" style="border-style:none;border-width:0px;border-radius:8px"><?php echo esc_html__( 'VIEW PRODUCT', 'glowess' ); ?><img class="wp-image-374" style="width: 14px;" src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/arrow-white.svg'; ?>" alt=""></a></div>
<!-- /wp:button --></div>
<!-- /wp:buttons --></div>
<!-- /wp:group --></div>
<!-- /wp:column --></div>
<!-- /wp:columns --></div>
<!-- /wp:woocommerce/single-product --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

