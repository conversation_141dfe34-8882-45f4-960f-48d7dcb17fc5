<?php
/**
 * Title: Footer v3
 * Slug: glowess/footer-v3
 * Categories: footer
 * Block Types: core/template-part/footer-v3
 *
 * @package glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Footer v3"},"align":"full","className":"light-mode footer-v3","style":{"spacing":{"padding":{"top":"25px","bottom":"0","left":"0","right":"0"},"blockGap":"0"},"border":{"top":{"width":"0px","style":"none"}}},"backgroundColor":"base","layout":{"type":"default"}} -->
<div class="wp-block-group alignfull light-mode footer-v3 has-base-background-color has-background" style="border-top-style:none;border-top-width:0px;padding-top:25px;padding-right:0;padding-bottom:0;padding-left:0"><!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|40","bottom":"var:preset|spacing|50"},"margin":{"bottom":"17px"}}},"layout":{"type":"constrained","contentSize":""}} -->
<div class="wp-block-group" style="margin-bottom:17px;padding-top:var(--wp--preset--spacing--40);padding-bottom:var(--wp--preset--spacing--50)"><!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"5px"},"blockGap":"40px"}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide" style="padding-top:5px"><!-- wp:group {"style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:site-title {"style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"}},"fontSize":"x-large","fontFamily":"heading"} /-->

<!-- wp:group {"layout":{"type":"constrained","contentSize":"380px","justifyContent":"left"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"none","fontStyle":"normal","fontWeight":"300","lineHeight":"2"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"medium"} -->
<p class="widgets-title has-secondary-color has-text-color has-link-color has-medium-font-size" style="font-style:normal;font-weight:300;line-height:2;text-transform:none">Add short newsletter content for your customers to encourage them to sign up for newsletter.</p>
<!-- /wp:paragraph --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"dimensions":{"minHeight":"59px"},"border":{"color":"#ffffff00"}},"layout":{"type":"default"}} -->
<div class="wp-block-group has-border-color" style="border-color:#ffffff00;min-height:59px"><!-- wp:wpforms/form-selector {"clientId":"7fb7389f-4caa-4015-b7a5-8f5ff115ba18","formId":"100","theme":"default-copy-1","themeName":"Default (Copy)","copyPasteJsonValue":"{\u0022displayTitle\u0022:false,\u0022displayDesc\u0022:false,\u0022theme\u0022:\u0022default-copy-1\u0022,\u0022themeName\u0022:\u0022Default (Copy)\u0022,\u0022fieldSize\u0022:\u0022medium\u0022,\u0022backgroundImage\u0022:\u0022none\u0022,\u0022backgroundPosition\u0022:\u0022center center\u0022,\u0022backgroundRepeat\u0022:\u0022no-repeat\u0022,\u0022backgroundSizeMode\u0022:\u0022cover\u0022,\u0022backgroundSize\u0022:\u0022cover\u0022,\u0022backgroundWidth\u0022:\u0022100px\u0022,\u0022backgroundHeight\u0022:\u0022100px\u0022,\u0022backgroundUrl\u0022:\u0022url()\u0022,\u0022backgroundColor\u0022:\u0022rgba( 0, 0, 0, 0 )\u0022,\u0022fieldBorderRadius\u0022:\u002220px\u0022,\u0022fieldBorderStyle\u0022:\u0022solid\u0022,\u0022fieldBorderSize\u0022:\u00221px\u0022,\u0022fieldBackgroundColor\u0022:\u0022#ffffff\u0022,\u0022fieldBorderColor\u0022:\u0022rgba( 0, 0, 0, 0.25 )\u0022,\u0022fieldTextColor\u0022:\u0022rgba( 0, 0, 0, 0.7 )\u0022,\u0022fieldMenuColor\u0022:\u0022#ffffff\u0022,\u0022labelSize\u0022:\u0022medium\u0022,\u0022labelColor\u0022:\u0022rgba( 0, 0, 0, 0.85 )\u0022,\u0022labelSublabelColor\u0022:\u0022rgba( 0, 0, 0, 0.55 )\u0022,\u0022labelErrorColor\u0022:\u0022#d63637\u0022,\u0022buttonSize\u0022:\u0022medium\u0022,\u0022buttonBorderStyle\u0022:\u0022none\u0022,\u0022buttonBorderSize\u0022:\u00221px\u0022,\u0022buttonBorderRadius\u0022:\u002220px\u0022,\u0022buttonBackgroundColor\u0022:\u0022#066aab\u0022,\u0022buttonTextColor\u0022:\u0022#ffffff\u0022,\u0022buttonBorderColor\u0022:\u0022#066aab\u0022,\u0022pageBreakColor\u0022:\u0022#066aab\u0022,\u0022containerPadding\u0022:\u00220px\u0022,\u0022containerBorderStyle\u0022:\u0022none\u0022,\u0022containerBorderWidth\u0022:\u00221px\u0022,\u0022containerBorderColor\u0022:\u0022#000000\u0022,\u0022containerBorderRadius\u0022:\u002230px\u0022,\u0022containerShadowSize\u0022:\u0022none\u0022,\u0022customCss\u0022:\u0022\u0022}","fieldBorderRadius":"20px","containerBorderRadius":"30px","buttonBorderRadius":"20px"} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"footer-widgets","style":{"spacing":{"blockGap":"var:preset|spacing|50"}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between","verticalAlignment":"stretch"}} -->
<div class="wp-block-group footer-widgets"><!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontSize":"default","fontFamily":"heading"} -->
<p class="widgets-title has-secondary-color has-text-color has-link-color has-heading-font-family has-default-font-size" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Shop All', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":604,"textColor":"secondary","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="widgets-title has-secondary-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Explore', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":606,"textColor":"secondary","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="widgets-title has-secondary-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'More', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":15,"textColor":"secondary","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"widget","style":{"spacing":{"blockGap":"18px"}},"layout":{"type":"default"}} -->
<div class="wp-block-group widget"><!-- wp:paragraph {"className":"widgets-title","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary","fontFamily":"heading"} -->
<p class="widgets-title has-secondary-color has-text-color has-link-color has-heading-font-family" style="font-style:normal;font-weight:500;text-transform:uppercase"><?php echo esc_html__( 'Connect', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:group {"layout":{"type":"default"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":21,"textColor":"secondary","overlayMenu":"never","style":{"spacing":{"blockGap":"9px"},"typography":{"fontStyle":"normal","fontWeight":"300","lineHeight":"1.6"}},"layout":{"type":"flex","orientation":"vertical"}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"style":{"border":{"top":{"color":"var:preset|color|gray-100","width":"1px"},"right":{},"bottom":{},"left":{}}},"layout":{"type":"constrained","contentSize":""}} -->
<div class="wp-block-group" style="border-top-color:var(--wp--preset--color--gray-100);border-top-width:1px"><!-- wp:group {"align":"wide","style":{"spacing":{"padding":{"top":"29px","bottom":"29px"}}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide" style="padding-top:29px;padding-bottom:29px"><!-- wp:paragraph {"style":{"typography":{"fontStyle":"normal","fontWeight":"300","fontSize":"13px"},"elements":{"link":{"color":{"text":"var:preset|color|secondary"}}}},"textColor":"secondary"} -->
<p class="has-secondary-color has-text-color has-link-color" style="font-size:13px;font-style:normal;font-weight:300"><?php echo esc_html__( '© 2024 - All rights reserved. Made by', 'glowess' ); ?><a href="#"><?php echo esc_html__( 'Glowess.', 'glowess' ); ?></a></p>
<!-- /wp:paragraph -->

<!-- wp:navigation {"ref":116,"textColor":"secondary","overlayMenu":"never","className":"footer-language","style":{"typography":{"fontSize":"13px","fontStyle":"normal","fontWeight":"300"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
