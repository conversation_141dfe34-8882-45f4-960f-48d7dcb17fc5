{"wpforms-activecampaign": {"title": "ActiveCampaign Addon", "slug": "wpforms-activecampaign", "url": "https://wpforms.com/addons/activecampaign-addon/", "version": "1.6.0", "image": "https://wpforms.com/wp-content/uploads/2020/03/addon-icon.png", "excerpt": "The WPForms ActiveCampaign addon lets you add contacts to your account, record events, add notes to contacts, and more.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-activecampaign-addon-with-wpforms/", "id": 729633, "license": ["agency", "elite", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.6.0 (2024-08-29)</h4><ul> <li>Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms ActiveCampaign 1.6.0. Failure to do that will disable WPForms ActiveCampaign functionality.</li> <li>Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms ActiveCampaign 1.6.0. Failure to do that will disable WPForms ActiveCampaign functionality.</li> <li>Added: Account and Job Title contact fields can now be mapped on the Form Builder &gt; Marketing &gt; ActiveCampaign screen.</li> <li>Added: Dynamic filter <code>wpforms_active_campaign_api_v3_endpoints_resource_get_limit_$type</code> to change the limit of any resource to retrieve - custom fields, tags, contacts, lists and events.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: PHP notice generated when trying to send a form with a selected but not set up custom meta field.</li></ul>", "<h4>1.5.0 (2022-09-20)</h4><ul> <li>Fixed: Warning notice on form submissions when no ActiveCampaign account was selected.</li></ul>", "<h4>1.4.0 (2022-06-28)</h4><ul> <li>Added: Possibility to use Smart Tags in Notes.</li> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Changed: Reorganized locations of 3rd party libraries.</li></ul>", "<h4>1.3.0 (2022-05-26)</h4><ul> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms ActiveCampaign. Failure to do that will disable the new WPForms ActiveCampaign functionality.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Changed: Replaced <code>jQuery.isFunction()</code> (deprecated as of jQuery 3.3) usages with a recommended counterpart.</li> <li>Fixed: Properly handle the situation when trying to change the template for the same form multiple times.</li> <li>Fixed: Send to ActiveCampaign form submission data even when the \"Entry storage\" option is disabled in the Form Builder.</li> <li>Fixed: CSS improvements of Add New Event button when a connection with the \"Event Tracking\" action was created.</li></ul>", "<h4>1.2.1 (2020-08-05)</h4><ul> <li>Fixed: API key/token expiration detection missing.</li> <li>Fixed: Properly initialize a template inside the Form Builder addon configuration area for conditional logic.</li> <li>Fixed: Allow a simple format of the form \"Name\" field to correctly pass data to ActiveCampaign \"Full Name\" field.</li></ul>", "<h4>1.2.0 (2020-04-29)</h4><ul> <li>Changed: Greatly improved tags management UI.</li> <li>Changed: Improved various field labels/descriptions inside the Form Builder.</li> <li>Fixed: Compatibility with WordPress Multisite when WPForms activated Network-wide, and addon - site-wide.</li> <li>Fixed: Do not send Event-related ActiveCampaign API requests when there are no required account ID and event key.</li></ul>", "<h4>1.1.0 (2020-03-16)</h4><ul> <li>Added: Process smart tags inside notes to be recorded for each new/updated contact.</li> <li>Changed: Increase the number of records returned from ActiveCampaign API from 20 to 100.</li></ul>", "<h4>1.0.0 (2020-03-03)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-activecampaign.png"}, "wpforms-authorize-net": {"title": "Authorize.Net Addon", "slug": "wpforms-authorize-net", "url": "https://wpforms.com/addons/authorize-net-addon/", "version": "1.10.0", "image": "https://wpforms.com/wp-content/uploads/2020/05/icon-provider-authorize-net.png", "excerpt": "The WPForms Authorize.Net addon allows you to connect your WordPress site with Authorize.Net to easily collect payments, donations, and online orders.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-authorize-net-addon-with-wpforms/", "id": 845517, "license": ["agency", "elite", "ultimate"], "category": ["payments"], "changelog": ["<h4>1.10.0 (2025-03-05)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed; The minimum WPForms version supported is 1.9.1.</li> <li>Changed: Updated Authorize.Net PHP SDK to v2.0.4.</li></ul>", "<h4>1.9.0 (2024-08-21)</h4><ul> <li>Added: Notice in the Form Builder when Authorize.Net is not connected or payments processing is not configured.</li> <li>Added: Notice in the WPForms &gt; Settings &gt; Payments admin page when a selected currency is not supported by Authorize.Net.</li> <li>Changed: Updated logo to reflect the company's rebranding.</li> <li>Changed: Improved rendering of Payment fields according to W3C requirements.</li> <li>Changed: Updated Authorize.Net PHP SDK to v2.0.3.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Better compatibility with default Block themes.</li> <li>Fixed: There was an error on multi-payment form submission.</li> <li>Fixed: There was an API error in the case of a long subscription title.</li></ul>", "<h4>1.8.0 (2023-09-27)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Authorize.Net 1.8.0. Failure to do that will disable WPForms Authorize.Net functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Authorize.Net 1.8.0. Failure to do that will disable WPForms Authorize.Net functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.7.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Card type for payment method was missing on the single Payment page.</li> <li>Fixed: Payment field was not displayed in the Elementor Builder.</li></ul>", "<h4>1.6.1 (2023-06-09)</h4><ul> <li>Fixed: There were situations when PHP notices were generated on the Single Payment page.</li></ul>", "<h4>1.6.0 (2023-06-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.2.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li> <li>Fixed: Payment error was displayed too close to the Description field.</li> <li>Fixed: JavaScript error occurred when the user was asked to enter verification information for a payment form locked with the Form Locker addon.</li></ul>", "<h4>1.5.1 (2023-03-23)</h4><ul> <li>Fixed: There was a styling conflict with PayPal Commerce field preview in the Form Builder.</li> <li>Fixed: Subfield validation error messages were overlapping each other in certain themes.</li></ul>", "<h4>1.5.0 (2023-03-21)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Fixed: In some cases validation errors were not removed after correcting the values and submitting the form again.</li> <li>Fixed: Local validation error messages overlapped Authorize.Net API error messages.</li> <li>Fixed: Authorize.Net validation error codes are now displayed only in the console.</li> <li>Fixed: On multi-page forms it was possible to continue to the next page even if the field validation failed.</li> <li>Fixed: Expiration and Security Code subfields were too narrow in the Form Builder preview.</li></ul>", "<h4>1.4.0 (2022-10-05)</h4><ul> <li>Changed: Show settings in the Form Builder only if they are enabled.</li> <li>Changed: On form preview, display an alert message with an error when payment configurations are missing.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: The form couldn't be submitted if several configured payment gateways were executed according to Conditional Logic.</li> <li>Fixed: Completed payment email notifications were sent for non-completed payments.</li></ul>", "<h4>1.3.0 (2022-06-28)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Changed: Reorganized locations of 3rd party libraries.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li></ul>", "<h4>1.2.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li> <li>Changed: Updated Authorize.Net PHP SDK to v2.0.2 for PHP 7.4 and PHP 8 support.</li> <li>Changed: Minimum WPForms version supported is *******.</li></ul>", "<h4>1.1.0 (2021-03-31)</h4><ul> <li>Added: Transaction-specific errors logging to make payment issues identification easier.</li> <li>Added: Account credentials validation on WPForms Payments settings page.</li> <li>Added: Optional address field mapping for Authorize.Net accounts requiring customer billing address.</li> <li>Added: Email Notifications option to limit to completed payments only.</li></ul>", "<h4>1.0.2 (2020-08-06)</h4><ul> <li>Fixed: Card field can be mistakenly processed as hidden under some conditional logic configurations.</li></ul>", "<h4>1.0.1 (2020-08-05)</h4><ul> <li>Fixed: Conditionally hidden Authorize.net field should not be processed on form submission.</li></ul>", "<h4>1.0.0 (2020-05-27)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.1"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-authorize-net.png"}, "wpforms-aweber": {"title": "<PERSON><PERSON><PERSON><PERSON>", "slug": "wpforms-aweber", "url": "https://wpforms.com/addons/aweber-addon/", "version": "2.2.0", "image": "https://wpforms.com/wp-content/uploads/2016/02/addon-icon-aweber-1.png", "excerpt": "The WPForms AWeber addon allows you to create AWeber newsletter signup forms in WordPress, so you can grow your email list. ", "doc": "https://wpforms.com/docs/install-use-aweber-addon-wpforms/", "id": 154, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>2.2.0 (2025-06-05)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Added: WPForms 1.9.6 compatibility.</li> <li>Changed: The minimum WPForms version supported is 1.9.6.</li> <li>Fixed: Name fields weren't sent to AWeber custom fields.</li></ul>", "<h4>2.1.0 (2024-08-29)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms AWeber 2.1.0. Failure to do that will disable WPForms AWeber functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing AWeber 2.1.0. Failure to do that will disable WPForms AWeber functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Add entry field data to the Log error message in case if transaction was stopped by conditional logic.</li> <li>Fixed: Compatibility with PHP 8.2 and 8.3.</li></ul>", "<h4>2.0.1 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Changed: It's no longer possible to create new AWeber (Legacy) OAuth1 account connections.</li> <li>Fixed: There is no more a fatal error when creating a connection in the Form Builder.</li></ul>", "<h4>2.0.0 (2023-07-06)</h4><ul> <li>Added: Compatibility with AWeber OAuth2 authentication.</li> <li>Changed: Minimum WPForms version supported is *******.</li></ul>", "<h4>1.3.2 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.3.1 (2022-08-31)</h4><ul> <li>Fixed: PHP warnings were generated during attempts to add already connected accounts.</li> <li>Fixed: Addon used to generate errors when the <code>cURL</code> PHP extension was not installed on a server.</li></ul>", "<h4>1.3.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Improved translatable strings escaping.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues..</li></ul>", "<h4>1.0.7 (2018-03-15)</h4><ul> <li>Fixed: Error when adding account from Settings &gt; Integrations tab.</li></ul>", "<h4>1.0.6 (2017-03-09)</h4><ul> <li>Changed: Adjust display order so that the providers show in alphabetical order</li></ul>", "<h4>1.0.5 (2017-02-09)</h4><ul> <li>Added: Support for tagging</li></ul>", "<h4>1.0.4 (2016-07-07)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.3 (2016-06-23)</h4><ul> <li>Changed: Prevent plugin from running if WPForms Pro is not activated</li></ul>", "<h4>1.0.2 (2016-04-12)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.1 (2016-04-06)</h4><ul> <li>Fixed: Issue with Aweber submission failing without custom fields</li></ul>", "<h4>1.0.0 (2016-03-11)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.6"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-aweber.png"}, "wpforms-sendinblue": {"title": "Brevo Addon", "slug": "wpforms-sendinblue", "url": "https://wpforms.com/addons/sendinblue-addon/", "version": "1.4.0", "image": "https://wpforms.com/wp-content/uploads/2020/12/icon-brevo.png", "excerpt": "The WPForms Brevo addon helps you organize your leads, automate your marketing, and engage your subscribers.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-sendinblue-addon-with-wpforms/", "id": 1126732, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.4.0 (2024-11-07)</h4><ul> <li>Added: Multiple-choice type attribute support for the Multiple Choice, Dropdown, and Checkboxes fields.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: Compatibility with other marketing addons.</li> <li>Fixed: Add entry field data to the Log error message if conditional logic stops the transaction.</li> <li>Fixed: Some fields were incompatible with contact attributes of category type.</li></ul>", "<h4>1.3.0 (2023-10-24)</h4><ul> <li>IMPORTANT: Updated logo and name to reflect the company's rebranding from Sendinblue to Brevo.</li> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Brevo 1.3.0. Failure to do that will disable WPForms Brevo functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Brevo 1.3.0. Failure to do that will disable WPForms Brevo functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Fixed: Account reconnection was required in WPForms when the first workflow was being setup.</li> <li>Fixed: Double opt-in was not working correctly when multiple connections were configured.</li></ul>", "<h4>1.2.0 (2022-10-03)</h4><ul> <li>Added: The Name field now has 4 options: full, first, middle, and last.</li> <li>Fixed: An error occurred in the Form Builder when a user saved Sendinblue configurations with empty fields.</li></ul>", "<h4>1.1.0 (2022-08-31)</h4><ul> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Sendinblue. Failure to do that will disable the new WPForms Sendinblue functionality.</li> <li>Added: New feature: Double Opt-In.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Form data can now be filtered for the track event action using a new <code>wpforms_sendinblue_provider_actions_trackeventaction_get_form_data</code> filter.</li> <li>Changed: Minimum WPForms version supported is 1.7.6.</li> <li>Changed: Errors logging was improved.</li> <li>Fixed: The addon was not properly handling the situation when a form template was changed multiple times for the same form.</li> <li>Fixed: Form submission was not sent to Sendinblue when the \"Entry storage\" option was disabled in the form settings.</li> <li>Fixed: List field did not refresh properly.</li> <li>Fixed: Form submission to Sendinblue was failing when non-required fields were empty.</li> <li>Fixed: Mapping a dropdown field to a Sendinblue contact attribute did not work.</li> <li>Fixed: A incorrect account was not properly processed.</li></ul>", "<h4>1.0.0 (2020-12-17)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.1"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-brevo.png"}, "wpforms-calculations": {"title": "Calculations Addon", "slug": "wpforms-calculations", "url": "https://wpforms.com/addons/calculations-addon/", "version": "1.7.0", "image": "https://wpforms.com/wp-content/uploads/2023/10/icon-calculations.png", "excerpt": "WPForms Calculations addon lets you create custom calculations for shipping, quotes, lead magnets, and more. Get more sales and leads.", "doc": "https://wpforms.com/docs/calculations-addon/", "id": 2671501, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.7.0 (2025-07-08)</h4><ul>  <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you must upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li>  <li>Added: AI Forms can now generate forms that use WPForms Calculations with an auto-generated formula (requires WPForms 1.9.7).</li>  <li>Changed: Prompt input field now resizes when a user provides a long text.</li>  <li>Changed: Updated <code>luxon</code> library to v3.6.1.</li>  <li>Fixed: Calculations involving <code>||</code> and <code>&amp;&amp;</code> operators gave a different result in the front-end than in the computed saved entry.</li>  <li>Fixed: Generated formula on RTL languages had incorrect formatting.</li>  <li>Fixed: Generated formula in the calculation chat window could have the wrong width overflowing the dialog item.</li>  <li>Fixed: Dropdown, Checkboxes, and Multiple Choice fields with icons couldn't be used inside an advanced calculation formula.</li>  <li>Fixed: Performance issue on large forms containing numerous different fields.</li>  <li>Fixed: Default values of the Single Item field were reset after reloading the form builder screen.</li></ul>", "<h4>1.6.0 (2025-03-11)</h4><ul> <li>Added: AI Calculations. Now you are able to leverage AI to generate new formulas and correct issues with manually generated formulas.</li> <li>Changed: The validation error text to a more appropriate one when the field was moved to the Repeater.</li> <li>Fixed: Calculation formula validation was not checking correctness of the subfield key (except checkbox fields).</li> <li>Fixed: Duplicated fields had incorrectly initialized calculation editor.</li></ul>", "<h4>1.5.0 (2025-02-25)</h4><ul> <li>Added: Compatibility with WPForms version 1.9.4.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li></ul>", "<h4>1.4.1 (2025-01-14)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.3.</li></ul>", "<h4>1.4.0 (2024-12-03)</h4><ul> <li>Fixed: There were errors in some cases during processing the Date/Time field value used in calculation.</li> <li>Fixed: Changes were lost when editing entries with Conditional logic fields and Coupons + Calculations addons enabled.</li> <li>Fixed: The Payment Single Item field had a non-empty value when it was hidden by Conditional Logic.</li> <li>Fixed: Calculation results of <code>0</code> was stored as an empty value in the number fields.</li> <li>Fixed: Inconsistent results on frontend and backend in case of division by zero.</li> <li>Fixed: A JavaScript error occurred when the field used in the formula was placed inside the Repeater field.</li></ul>", "<h4>1.3.0 (2024-09-24)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Changed: Updated <code>luxon</code> library to v3.5.0.</li> <li>Added: Coupons addon compatibility.</li> <li>Fixed: Results from calculations were not displayed in the confirmation message with a very long formula.</li> <li>Fixed: Entry Preview was displaying an empty calculations result if fields being calculated were not in the same pagebreak wrap with result field.</li> <li>Fixed: Conditional Logic applied for Repeater field was not reflected on single entry views.</li> <li>Fixed: The <code>date_diff</code> function sometimes returned an incorrect result that did not match the result on the frontend.</li> <li>Fixed: The truncate() function worked incorrectly with multibyte strings.</li> <li>Fixed: The Repeater field sometimes caused console errors on the front end.</li> <li>Fixed: Fields sorting changed to the form fields order in settings with field mapping and smart tags.</li> <li>Fixed: It was possible to use the Total field in the Single Item field formula.</li> <li>Fixed: Resolved W3C error for the Number field.</li> <li>Fixed: Certain math function results had discrepancies between the front-end and back-end.</li> <li>Fixed: The Single Item field displayed amount rounding discrepancies between the front-end and back-end.</li></ul>", "<h4>1.2.0 (2024-04-03)</h4><ul> <li>Added: The new filter `wpforms_calculations_process_filter` to allow post-calculation entry processing.</li> <li>Fixed: Automatic update for the addon did not work.</li> <li>Fixed: The Authorize.Net credit card field threw an error on the entry edit page.</li> <li>Fixed: Payment Single item field was not included in the Entry Preview, Confirmations and Notifications.</li></ul>", "<h4>1.1.0 (2024-01-24)</h4><ul> <li>Added: Improved support and processing for the Single Item field.</li> <li>Added: Compatibility with the upcoming WPForms v1.8.7 release.</li> <li>Changed: Updated PHP-Parser library to 4.18.0.</li> <li>Changed: Updated Luxon library to 3.4.4.</li> <li>Fixed: It was impossible to edit a formula after duplicating the Layout Field with the Calculation Field inside.</li> <li>Fixed: In some cases, the calculated field values were inconsistent between displayed value on the front-end and saved value in the database.</li> <li>Fixed: Calculations were not using correct values when the option \"Show values\" for selectable fields was set.</li> <li>Fixed: Line breaks and other special characters were not preserved in the formula code and in the calculation result.</li> <li>Fixed: The formula validation returned the false positive result in some cases when the form was not saved before validation.</li> <li>Fixed: In some cases, incorrect calculation results were shown in the confirmation message.</li> <li>Fixed: The Validate Formula button AJAX calls failed on the servers that do not support <code>$_SERVER[HTTP_REFERER]</code>.</li> <li>Fixed: \"Illegal numeric literal\" error appeared in the error.log when the field value was numeric and started with 0.</li> <li>Fixed: The Error Handler threw the invalid callback error in some rare cases.</li> <li>Fixed: Math functions were throwing a TypeError in some rare cases.</li></ul>", "<h4>1.0.0 (2023-10-24)</h4><ul> <li>Added: Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.4"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": true, "new": true, "icon": "addon-icon-calculations.png"}, "wpforms-campaign-monitor": {"title": "Campaign Monitor <PERSON><PERSON>", "slug": "wpforms-campaign-monitor", "url": "https://wpforms.com/addons/campaign-monitor-addon/", "version": "1.3.0", "image": "https://wpforms.com/wp-content/uploads/2016/06/addon-icon-campaign-monitor.png", "excerpt": "The WPForms Campaign Monitor addon allows you to create Campaign Monitor newsletter signup forms in WordPress, so you can grow your email list. ", "doc": "https://wpforms.com/docs/how-to-install-and-use-campaign-monitor-addon-with-wpforms/", "id": 4918, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.3.0 (2024-08-20)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Campaign Monitor 1.3.0. Failure to do that will disable WPForms Campaign Monitor functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Campaign Monitor 1.3.0. Failure to do that will disable WPForms Campaign Monitor functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Changed: Updated logo to reflect the company's new branding.</li></ul>", "<h4>1.2.3 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.2.2 (2022-08-30)</h4><ul> <li>Fixed: PHP Notice was generated when the Full Name field wasn't mapped for a connection.</li> <li>Fixed: PHP Error was generated when the user tried to connect with empty or invalid API credentials.</li></ul>", "<h4>1.2.1 (2019-08-05)</h4><ul> <li>Fixed: Forms field mapping with Checkbox field.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues..</li></ul>", "<h4>1.0.4 (2018-03-15)</h4><ul> <li>Changed: Improved display order of account fields.</li> <li>Fixed: Error when adding account from Settings &gt; Integrations tab.</li></ul>", "<h4>1.0.3 (2017-03-09)</h4><ul> <li>Changed: Adjust display order so that the providers show in alphabetical order</li></ul>", "<h4>1.0.2</h4><ul> <li>Fixed: Improved checking for other Campaign Monitor plugins to avoid conflicts</li></ul>", "<h4>1.0.1 (2016-07-07)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.0 (2016-06-16)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-campaign-monitor.png"}, "wpforms-conversational-forms": {"title": "Conversational Forms Addon", "slug": "wpforms-conversational-forms", "url": "https://wpforms.com/addons/conversational-forms-addon/", "version": "1.17.0", "image": "https://wpforms.com/wp-content/uploads/2019/02/addon-conversational-forms.png", "excerpt": "Want to improve your form completion rate? Conversational Forms addon by WPForms helps make your web forms feel more human, so you can improve your conversions. Interactive web forms made easy.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-conversational-forms-addon/", "id": 391235, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.17.0 (2025-01-16)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Added: Compatibility with the upcoming WPForms 1.9.3.</li> <li>Changed: Improved compatibility with the Offline Forms addon.</li> <li>Fixed: Mobile devices styling issues.</li> <li>Fixed: Stripe Payment element UX issues.</li></ul>", "<h4>1.16.0 (2024-09-26)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Changed: Updated <code>inputmask</code> library to v5.0.9.</li> <li>Fixed: Compatibility issue with the WPML plugin.</li> <li>Fixed: Resolved PHP Warnings when duplicating forms created prior to the initial activation of Conversational Forms.</li> <li>Fixed: Form page with enabled Save &amp; Resume addon was reloaded after completing hCaptcha.</li> <li>Fixed: Monster Insights compatibility in the form preview.</li> <li>Fixed: Conversational Forms background color was ignored in WordPress 6.6.</li> <li>Fixed: Enhanced Color Scheme compatibility for Stripe Payment Elements.</li> <li>Fixed: Corrected field sequence numbering when modern spam protection is enabled.</li> <li>Fixed: Color Themes support for Single Item and Total payment fields.</li></ul>", "<h4>1.15.0 (2024-04-16)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.8.</li> <li>Fixed: Payment single item overlap in case of long label.</li></ul>", "<h4>1.14.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.7.</li> <li>Changed: The minimum WPForms version supported is 1.8.7.</li> <li>Fixed: The Form Builder settings screen had visual issues when an RTL language was used.</li></ul>", "<h4>1.13.0 (2024-01-09)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Conversational Forms 1.13.0. Failure to do that will disable WPForms Conversational Forms functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Conversational Forms 1.13.0. Failure to do that will disable WPForms Conversational Forms functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.6.</li> <li>Fixed: Compatibility with the Popup Maker plugin.</li> <li>Fixed: Compatibility with Link by Stripe.- Improved Rich Text, File Upload and Number fields compatibility with color themes and dark mode.</li> <li>Fixed: In rare cases Turnstile Captcha was not displayed correctly when it expired and was refreshed.</li> <li>Fixed: Incorrect error text was displayed when uploading a file of an illegal format in the Form Builder.</li></ul>", "<h4>1.12.0 (2023-08-22)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Scrolling to the form error message was not working in some cases.</li> <li>Fixed: Some deprecation notices were generated with PHP 8.2.</li> <li>Fixed: Dropdowns on tablets and mobiles had 2 down-arrows.</li></ul>", "<h4>1.11.0 (2023-06-28)</h4><ul> <li>Added: Compatibility with the WPForms Coupons addon.</li></ul>", "<h4>1.10.0 (2023-03-27)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Changed: The Content field did not have instructions to proceed to the next step.</li> <li>Changed: Disable automatic scroll to the first field when the form description is larger than the viewport.</li> <li>Fixed: The Header Logo preview was not displayed in the Form Builder if the form contains any field with Image Choices turned on.</li> <li>Fixed: Fields with Icon or Image choices configured did not apply line breaks to manually forma</li> <li>Fixed: Appearance of Likert Scale and Net Promoter Score fields was improved in dark themes.</li></ul>", "<h4>1.9.0 (2023-01-03)</h4><ul> <li>Added: Compatibility with Icon Choices feature for Checkboxes, Multiple Choice, Checkbox Items, and Multiple Items payment fields.</li> <li>Fixed: The AJAX spinner was not centered relative to the Submit button.</li> <li>Fixed: Improved compatibility with the Content field.</li> <li>Fixed: Next field wasn't focused after selecting the Likert Scale field option.</li> <li>Fixed: Checkboxes with image labels were displayed enormously big.</li> <li>Fixed: Incorrect positioning if form header contains long-form content.</li> <li>Fixed: Improved handling of line breaks to avoid text overflowing on small screens.</li></ul>", "<h4>1.8.0 (2022-10-06)</h4><ul> <li>Changed: Do not allow enabling the Conversational Form mode when a Layout field was added to the list of form fields.</li></ul>", "<h4>1.7.1 (2022-08-31)</h4><ul> <li>Fixed: Color Scheme setting value was missing from the color picker's input after the Form Builder page refresh.</li> <li>Fixed: Color picker caused a broken conversational form page.</li> <li>Fixed: Field after radio button or dropdown was not correctly selected.</li> <li>Fixed: Font size for Single Item and Total fields now matches other fields.</li> <li>Fixed: Certain buttons sometimes overlapped the Conversational Forms footer.</li> <li>Fixed: Color schemes compatibility with the Rich Text field was improved.</li> <li>Fixed: The <code>{page_title}</code> smart tag was getting the incorrect title.</li> <li>Fixed: Incorrect information was displayed in conversational form social previews.</li></ul>", "<h4>1.7.0 (2022-06-28)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms Conversational Forms. Failure to do that will disable the WPForms Conversational Forms plugin.</li> <li>Added: New filter <code>wpforms_conversational_forms_frontend_handle_request_form_data</code> that can be used to improve multi-language support.</li> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Changed: Reorganized locations of 3rd party libraries.</li> <li>Changed: Date field can be filled in when using the Date Picker with custom date formats.</li> <li>Fixed: Incorrect canonical and <code>og:url</code> page meta values produced by the Yoast SEO plugin.</li> <li>Fixed: Users with editor permissions were unable to save Conversational Forms slugs.</li> <li>Fixed: Improved an error message color for the modern file upload field.</li> <li>Fixed: Missing styles for links added to the Conversational Message.</li> <li>Fixed: Conditional logic was processed incorrectly for the Multiple Dropdown field.</li> <li>Fixed: Correctly display a placeholder for the Modern Dropdown field in the Firefox browser.</li> <li>Fixed: Single Dropdown field didn't work on mobile devices.</li> <li>Fixed: Date/Time field didn't support flatpickr's <code>range</code> and <code>multiple</code> modes.</li> <li>Fixed: Date/Time field with 24h format for the timepicker wasn't working properly on mobile devices.</li> <li>Fixed: Form couldn't be submitted when a dropdown date option is selected for the required Date/Time field and Conditional Logic applied to the field.</li> <li>Fixed: Opening a mobile device's keyboard for text fields removed focus from the field which was closing keyboard.</li> <li>Fixed: Improved compatibility with Entry Preview and Rich Text fields.</li></ul>", "<h4>1.6.0 (2021-03-31)</h4><ul> <li>Changed: Visual difference between radio and checkbox elements of Likert Scale field.</li> <li>Changed: \"Next/Previous\" footer buttons are bigger for small screens.</li> <li>Changed: Radio inputs and select elements look more like traditional HTML elements on mobile.</li> <li>Changed: Improved styling for Authorize.Net and legacy Stripe CC fields on desktop and mobile.</li> <li>Changed: Disable autogenerated the <code>og:description</code> meta tag in the Rank Math plugin.</li> <li>Changed: The LikertScale field with a single response per row scrolls to the next row/field on change.</li> <li>Changed: Radio/Checkbox field items scroll into view while selecting with arrow keys.</li> <li>Changed: Form Locker UI enhancement when used in conversational mode.</li> <li>Fixed: Compatibility issue with Google v2 reCAPTCHA on certain mobile devices.</li> <li>Fixed: The <code>nav_menu_item</code> post type is included in the pool when checking the Conversational Form page slug for uniqueness.</li> <li>Fixed: Textarea and page footer appearance in IE11.</li> <li>Fixed: <code>blockquote</code>, <code>ul</code>, <code>ol</code> elements styling in a form description and a confirmation message.</li> <li>Fixed: Page footer logo appearance in portrait and landscape mobile layouts.</li> <li>Fixed: For the fields without a label, the number indicator is not shown.</li> <li>Fixed: The \"Hide label\" option is not processed for the fields.</li> <li>Fixed: Horizontal line before the Submit button.</li> <li>Fixed: If the checkbox has a label and no options, a long horizontal box appears.</li> <li>Fixed: The Number Slider field appearance.</li> <li>Fixed: Smart Phone field does not display a list of countries when clicking the flag.</li> <li>Fixed: TwentySeventeen and TwentyTwenty themes introduce style conflicts with Conversational Form pages.</li> <li>Fixed: Image Choices field scrolling position is set incorrectly in a rare combination of image/screen size and field order.</li> <li>Fixed: Date dropdown field processing issue.</li> <li>Fixed: Focusing and positioning for Stripe CC field.</li> <li>Fixed: Dropdown focusing issue on iPhone X and iPhone SE.</li> <li>Fixed: A conditionally hidden field doesn't get focus if triggered to show by a Dropdown field.</li> <li>Fixed: Field sub-Labels do not hide when enabling the \"Hide Sub Label\" option in Advanced Field Settings.</li></ul>", "<h4>1.5.0 (2020-08-05)</h4><ul> <li>Added: Show a notice if permalinks are not configured.</li> <li>Added: Easy access to Conversational Forms classes (e.g. <code>wpforms_conversational_forms()-&gt;frontend</code>).</li> <li>Added: New <code>wpforms_conversational_form_detected</code> hook right before Conversational Form hooks are registered.</li> <li>Changed: Page Title tag and meta tags always use Conversational Page Title if set.</li> <li>Changed: oEmbed links are now removed from Conversational Page HTML.</li> <li>Fixed: Occasionally the form scrolls past or does not activate the conditionally appearing field.</li></ul>", "<h4>1.4.0 (2020-01-09)</h4><ul> <li>Added: Meta tag 'robots' with filterable <code>noindex,nofollow</code> value for all Conversational Forms pages.</li> <li>Fixed: Mobile: Virtual keyboard appearing inconsistently while interacting with the fields which have the sub-fields.</li> <li>Fixed: Popup Maker popup content displays in Conversational Forms.</li></ul>", "<h4>1.3.1 (2019-11-07)</h4><ul> <li>Added: Basic compatibility with WPForms modern file uploader.</li> <li>Fixed: \"error404\" body class may appear if the custom permalink structure is used.</li> <li>Fixed: Form preview buttons open two tabs in the Edge browser.</li> <li>Fixed: \"Cannot read property 'addEventListener' of null\" JS error on form creation.</li></ul>", "<h4>1.3.0 (2019-10-14)</h4><ul> <li>Added: Hexcode option to color picker.</li></ul>", "<h4>1.2.0 (2019-07-24)</h4><ul> <li>Added: \"Enter or down arrow to go to the next field\" message for HTML, Section Divider, Payment Single and Payment Total blocks.</li> <li>Changed: Dropdown appearance altered to better mimic a traditional <code>select</code> element.</li> <li>Changed: Sublabel placed closer to the input area that it relates to for better visual perception.</li> <li>Changed: Dropdown chevron icon click and \"down arrow\" key open a list with all options visible.</li> <li>Changed: Form scrolls to selected subfields inside multi-input fields for both mobile and desktop.</li> <li>Changed: Active field is considered completed in footer progress bar calculation.</li> <li>Fixed: Image choices in a Conversational Form shows two-column layout no matter what the Choice Layout selection is.</li> <li>Fixed: Conditional logic not working properly on dropdown fields.</li> <li>Fixed: Conversational forms doesn't accept correct credit card expiration date.</li> <li>Fixed: Inconsistent percentage progress bar behavior (negative values) with conditionally hidden form fields.</li> <li>Fixed: Conversational forms won't submit if the field is required and is hidden by conditional logic.</li> <li>Fixed: Multiline form description is displayed as a single line on a frontend.</li> <li>Fixed: Some themes override Conversational Form's templates.</li></ul>", "<h4>1.1.0 (2019-02-28)</h4><ul> <li>Added: Left/Right arrow navigation support for Checkboxes, Radios, Rating, NetPromoter fields.</li> <li>Added: Esc for unhighlighting an option previously highlighted by arrow keys in Checkboxes, Radios, Rating, NetPromoter fields.</li> <li>Added: Space for selecting (same as Enter) options in Checkboxes, Radios, Rating, NetPromoter fields.</li> <li>Added: Shift+Tab to go to a previous option/subfield (same as Up Arrow).</li> <li>Changed: Shift+Enter to go to the next field for Checkboxes (Enter just selects/unselects checkboxes now).</li> <li>Changed: Dropdowns (desktop version) are not auto opening on focus now.</li> <li>Changed: More consistent arrow logic for Checkbox and Radio based fields.</li> <li>Changed: Mobile-native dropdowns are used for mobile devices now.</li> <li>Changed: Layout is optimized to use screen space more effectively on smaller screens.</li> <li>Changed: Tweaked virtual keyboard interaction on mobile devices for better mobile UX.</li> <li>Changed: Mobile Textarea doesn't have \"new line\" capability now due to mobile UI restrictions.</li> <li>Changed: Changed tooltip messages in admin area to be more explanatory.</li> <li>Changed: Changed how mobile/desktop browsers are detected (mobile-detect.js).</li> <li>Changed: Footer \"Up/Down\" buttons iterate through subfields on multi-field inputs now instead of instantly skipping to the next field.</li> <li>Fixed: Form's last field (conditionally hidden) was getting focus when trying to go up from \"Submit\" block.</li> <li>Fixed: Rating had no multi-digit keys support (e.g. impossible to select 10).</li> <li>Fixed: \"Active\" key navigation star was the same color as the selected one in Rating field.</li> <li>Fixed: Header was overlapping form content on Firefox and Edge browsers.</li> <li>Fixed: Mobile field focusing issues.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.1"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-conversational-forms.png"}, "wpforms-coupons": {"title": "Coupons <PERSON>", "slug": "wpforms-coupons", "url": "https://wpforms.com/addons/coupons-addon/", "version": "1.6.0", "image": "https://wpforms.com/wp-content/uploads/2023/06/icon-coupons-1.png", "excerpt": "The WPForms Coupons addon makes it easy to drive more sales by offering coupon discounts.", "doc": "https://wpforms.com/docs/coupons-addon/", "id": 2520337, "license": ["agency", "elite", "pro", "ultimate"], "category": ["payments"], "changelog": ["<h4>1.6.0 (2025-02-25)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li> <li>Changes were not preserved when editing Entries belonging to Forms with Conditional Logic fields in combination with the Coupons and Calculations addons.</li></ul>", "<h4>1.5.0 (2024-09-24)</h4><ul> <li>Added: Calculations addon compatibility.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: A potential PHP fatal error that occurred when the Coupon field was hidden by conditional logic.</li> <li>Fixed: Corrected the calculation of discounts passed to the Square dashboard for currencies with no decimal places (zero-decimal currencies).</li></ul>", "<h4>1.4.0 (2024-08-06)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Changed: Improved compatibility with 3rd-party plugins.</li> <li>Fixed: RTL support for the Coupon field.</li> <li>Fixed: MySQL errors might have occurred when creating a table in some unique configurations.</li> <li>Fixed: Hide \"Press to select\" label from the Allowed Coupons dropdown in Form Builder.</li></ul>", "<h4>1.3.1 (2024-04-25)</h4><ul> <li>Added: A label to indicate the User Template in the list of allowed forms for the coupon.</li> <li>Fixed: The total amount was sometimes incorrect when the percentage coupon type was applied.</li> <li>Fixed: Incorrect discount was applied for Square payments when the amount was a fractional value.</li> <li>Fixed: Compatibility with the Divi page builder.</li></ul>", "<h4>1.3.0 (2024-04-25)</h4><ul> <li>Added: A label to indicate the User Template in the list of allowed forms for the coupon.</li> <li>Fixed: The total amount was sometimes incorrect when the percentage coupon type was applied.</li> <li>Fixed: Incorrect discount was applied for Square payments when the amount was a fractional value.</li> <li>Fixed: Compatibility with the Divi page builder.</li></ul>", "<h4>1.3.0 (2024-04-16)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.8.8.</li> <li>Added: Compatibility with the WPForms 1.8.8.</li> <li>Fixed: The form was not saved in the Form Builder when following the prompt to create the first Coupon.</li> <li>Fixed: Time input fields appeared too narrow on the Edit Coupon page.</li> <li>Fixed: RTL problems in the form builder.</li> <li>Fixed: Removed redundant DB queries from the addon happening on non-related admin pages.</li> <li>Fixed: Discount amount was wrong when the form contains calculated payment fields.</li></ul>", "<h4>1.2.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with the WPForms 1.8.7.</li> <li>Changed: The minimum WPForms version supported is 1.8.7.</li> <li>Changed: Added Coupon field to allowed fields for use in <code>wpforms_get_form_fields()</code> function.</li> <li>Changed: Improve Coupons page display on mobile devices.</li> <li>Fixed: Space between Currency and Amount in Coupon field was removed.</li> <li>Fixed: Various issues in the user interface when an RTL language was used.</li></ul>", "<h4>1.1.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Coupons 1.1.0. Failure to do that will disable WPForms Coupons functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Coupons 1.1.0. Failure to do that will disable WPForms Coupons functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Changed: The Coupon field has an improved preview in the Form Builder.</li> <li>Changed: Front-end validation and the process of applying a coupon has a better UX.</li> <li>Fixed: Smart Logic was not working when the Coupon field value was used to show/hide other fields.</li> <li>Fixed: Show an Allowed Forms error notice if there is no enabled form, only if the coupon is already created.</li> <li>Fixed: The coupon was not applied when the maximum limit was reached and then increased again.</li> <li>Fixed: The <code>wpforms_coupons_admin_coupons_edit_date_format</code> filter was not changing the date format on the Edit Coupon page.</li> <li>Fixed: The Coupon field became unavailable in the Form Builder if drag-n-drop action started and stopped.</li></ul>", "<h4>1.0.0 (2023-06-28)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.4"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-coupons.png"}, "wpforms-captcha": {"title": "Custom <PERSON><PERSON>", "slug": "wpforms-cap<PERSON>a", "url": "https://wpforms.com/addons/custom-captcha-addon/", "version": "1.8.0", "image": "https://wpforms.com/wp-content/uploads/2016/08/addon-icon-captcha.png", "excerpt": "The WPForms Custom Captcha addon allows you to define custom questions or use random math questions as captcha to reduce spam form submissions.", "doc": "https://wpforms.com/docs/how-to-install-and-use-custom-captcha-addon-in-wpforms/", "id": 7499, "license": ["agency", "basic", "elite", "plus", "pro", "ultimate"], "category": [], "changelog": ["<h4>1.8.0 (2023-08-15)</h4><ul> <li>Added: The Captcha field in the Form Builder can now be searched by name or related keywords.</li> <li>Changed: Minimum WPForms version supported is 1.8.3.</li></ul>", "<h4>1.7.0 (2023-03-13)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.1.</li> <li>Fixed: Improved compatibility with Elementor popups v3.9+.</li></ul>", "<h4>1.6.0 (2022-09-21)</h4><ul> <li>Added: Custom Captcha's \"Math\" type is now supported and works properly inside Elementor popups.</li> <li>Changed: Minimum WPForms version is now 1.7.5.</li> <li>Fixed: Empty questions and answers had incorrect validation.</li></ul>", "<h4>1.5.0 (2022-08-29)</h4><ul> <li>Changed: Do not add a second question with an empty question and answer values that were added by default.</li> <li>Changed: Empty questions are now removed from the list on form save.</li> <li>Fixed: Implemented various fixes to prevent questions with an empty question or answer values from being saved or displayed.</li></ul>", "<h4>1.4.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.7.1 and displaying the field on the Entry Edit page.</li> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li></ul>", "<h4>1.3.2 (2021-09-07)</h4><ul> <li>Changed: Prevent saving empty values for \"Question and Answer\" Captcha fields.</li> <li>Fixed: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Fixed: Incorrect \"Math\" Captcha preview in the Block Editor (Gutenberg).</li> <li>Fixed: Incorrect \"Question and Answer\" Captcha preview in Builder.</li></ul>", "<h4>1.3.1 (2021-03-31)</h4><ul> <li><span data-preserver-spaces=\"true\">Fixed: Empty Form Builder preview when the \"Questions and Answer\" type has been selected and the first question has been removed.</span></li> <li><span data-preserver-spaces=\"true\">Fixed: \"Questions and Answers\" section may not be displayed for some users in the Form Builder when the \"Questions and Answers\" type has been selected.</span></li></ul>", "<h4>1.3.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Fixed: <code>`wpforms_math_captcha`</code> filter running too early.</li></ul>", "<h4>1.2.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues..</li></ul>", "<h4>1.1.2 (2018-12-27)</h4><ul> <li>Changed: Captcha field display priority in the form builder.</li></ul>", "<h4>1.1.1 (2018-03-19)</h4><ul> <li>Fixed: JS file not loading on frontend, causing error.</li></ul>", "<h4>1.1.0 (2018-03-15)</h4><ul> <li>Changed: Refactored addon and improved code.</li> <li>Fixed: Zero (0) math equation answers not being allowed.</li></ul>", "<h4>1.0.3 (2017-06-15)</h4><ul> <li>Fixed: Issue with QA PHP validation</li></ul>", "<h4>1.0.2 (2017-06-15)</h4><ul> <li>Fixed: Missing input class causing equation validation issues</li></ul>", "<h4>1.0.1 (2017-06-13)</h4><ul> <li>Changed: Updated captcha field to new field class format</li></ul>", "<h4>1.0.0 (2016-08-03)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.2", "php": "5.6", "wpforms": "1.8.3"}, "form_builder": {"category": []}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-captcha.png"}, "wpforms-drip": {"title": "<PERSON><PERSON>", "slug": "wpforms-drip", "url": "https://wpforms.com/addons/drip-addon/", "version": "1.10.0", "image": "https://wpforms.com/wp-content/uploads/2018/06/addon-icon.png", "excerpt": "The WPForms Drip addon allows you to create Drip newsletter signup forms in WordPress, so you can grow your email list. ", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-drip-addon-in-wpforms/", "id": 209878, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.10.0 (2025-08-13)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>IMPORTANT: <PERSON>don requires the cURL extension to work. If you are running PHP without the cURL extension, you MUST enable it before upgrading this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li> <li>Changed: Error message text when the account cannot be connected due to empty API token.</li></ul>", "<h4>1.9.0 (2024-11-07)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: Compatibility with other marketing addons.</li></ul>", "<h4>1.8.0 (2024-08-13)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Drip 1.8.0. Failure to do that will disable WPForms Drip functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Drip 1.8.0. Failure to do that will disable WPForms Drip functionality.</li> <li>Added: Ability to unsubscribe a customer from all mailings in Drip.</li> <li>Added: Ability to select available custom fields from the Drip's API.</li> <li>Added: Ability to create custom and use predefined custom field names.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Changed: Load Drip data only when needed in the Form Builder to improve performance.</li> <li>Changed: Data is now sent asynchronously to Drip after form submission.</li> <li>Fixed: Customers who unsubscribe through an email unsubscribe link or the Drip dashboard cannot resubscribe.</li> <li>Fixed: Prevent API errors when new email and email field values are equal.</li> <li>Fixed: Drip connections could be duplicated when switching between the Marketing and Settings tabs in the Builder.</li> <li>Fixed: Ensure payment values are correctly formatted and properly escaped before sending to Drip.</li></ul>", "<h4>1.7.0 (2023-06-14)</h4><ul> <li>Added: Deprecation tooltip for the Lead Scoring feature by Drip.</li> <li>Changed: Checkboxes have been replaced with fancy toggles on the addon settings screen in the Form Builder.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li></ul>", "<h4>1.6.0 (2022-10-03)</h4><ul> <li>Added: GDPR-related data is added to the campaign subscriber meta information.</li> <li>Fixed: Events were duplicated in the list after adding a new custom event for Drip.</li></ul>", "<h4>1.5.0 (2022-08-29)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms Drip and WPForms 1.7.6 (that the addon is relying on). Failure to do that will disable the WPForms Drip plugin.</li> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Drip. Failure to do that will disable the new WPForms Drip functionality.</li> <li>Fixed: Compatibility with WPForms 1.7.3 and below.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li></ul>", "<h4>1.4.2 (2020-05-05)</h4><ul> <li>Fixed: Properly initialize a template inside the Form Builder addon configuration area for conditional logic.</li></ul>", "<h4>1.4.1 (2020-03-03)</h4><ul> <li>IMPORTANT: Support for PHP 5.4 has been discontinued. If you are running PHP 5.4, you MUST upgrade PHP before installing the new WPForms Drip and WPForms 1.5.9 (that the addon is relying on). Failure to do that will disable the WPForms Drip plugin.</li> <li>Fixed: Compatibility with WPForms 1.5.9</li></ul>", "<h4>1.4.0 (2020-01-09)</h4><ul> <li>Added: Send to Drip eu_consent and eu_consent_message values for each subscriber if GDPR is enabled in plugin settings.</li> <li>Changed: Improved custom field name sanitization according to Drip specifications.</li> <li>Changed: Display errors in a Drip account addition popup in form Builder &gt; Marketing tab when something went wrong.</li> <li>Fixed: PHP warnings/errors that triggered in some use cases.</li> <li>Fixed: Issue duplicating a form connected to Drip.</li> <li>Fixed: Field value not passing to Drip if field ID was #0.</li></ul>", "<h4>1.3.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.2.1 (2019-02-22)</h4><ul> <li>Fixed: Drip API library not properly loading.</li></ul>", "<h4>1.2.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.1.0 (2018-08-28)</h4><ul> <li>Added: Ability to add a Drip account from the Form Builder.</li> <li>Changed: Lead Score field is now visible only if the Prospect option is checked.</li> <li>Changed: Improved settings screen description and helpful texts.</li> <li>Fixed: Multiple conditional logic rules now works fine for different Drip connections.</li></ul>", "<h4>1.0.0 (2018-06-04)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-drip.png"}, "wpforms-dropbox": {"title": "Dropbox Addon", "slug": "wpforms-dropbox", "url": "https://wpforms.com/addons/dropbox-addon/", "version": "1.1.0", "image": "https://wpforms.com/wp-content/uploads/2024/12/icon-dropbox.png", "excerpt": "The WPForms Dropbox addon lets you receive user-submitted files directly to your Dropbox account to help you streamline your file management.", "doc": "https://wpforms.com/docs/dropbox-addon/", "id": 3012541, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.1.0 (2025-02-25)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li> <li>Changed: Improved authorization flow.</li> <li>Fixed: Smart tag values for files uploaded to Dropbox were incorrect in some cases.</li></ul>", "<h4>1.0.0 (2024-12-17)</h4><ul> <li>Added: Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.4"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": true}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-dropbox.png"}, "wpforms-entry-automation": {"title": "Entry Automation Addon", "slug": "wpforms-entry-automation", "url": "https://wpforms.com/addons/entry-automation-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/06/icon-entry-automation.png", "excerpt": "With the WPForms Entry Automation addon, you can set your form entries to be automatically exported on a daily, weekly, or regular basis of your choice. ", "doc": "https://wpforms.com/docs/entry-automation-addon/", "id": 3106573, "license": ["agency", "elite", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.0.0 (2025-06-24)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.6.1"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": true}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-entry-automation.png"}, "wpforms-form-abandonment": {"title": "Form Abandonment Addon", "slug": "wpforms-form-abandonment", "url": "https://wpforms.com/addons/form-abandonment-addon/", "version": "1.12.0", "image": "https://wpforms.com/wp-content/uploads/2017/02/addon-icon-form-abandonment.png", "excerpt": "Unlock more leads by capturing partial entries from your forms. Easily follow up with interested leads and turn them into loyal customers.", "doc": "https://wpforms.com/docs/how-to-install-and-use-form-abandonment-with-wpforms/", "id": 27685, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.12.0 (2024-06-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li></ul>", "<h4>1.11.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.7.</li> <li>Fixed: Address autocomplete was not saved when a user abandoned the form.</li></ul>", "<h4>1.10.0 (2023-12-11)</h4><ul> <li>Fixed: Survey reports contained incorrect data when abandoned entries existed.</li></ul>", "<h4>1.9.0 (2023-10-24)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Form Abandonment 1.9.0. Failure to do that will disable WPForms Form Abandonment functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Form Abandonment 1.9.0. Failure to do that will disable WPForms Form Abandonment functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.8.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Some Smart tags weren't working in Form Abandonment emails.</li> <li>Fixed: Form was not marked as abandoned on mobile devices when changing a browser tab or closing the browser.</li> <li>Fixed: Forms now store abandoned entries if more than one email field is preset.</li></ul>", "<h4>1.7.1 (2023-02-14)</h4><ul> <li>Added: WPForms 1.8.0 compatibility.</li> <li>Changed: Disable \"Resend Notifications\" link on the Entry page instead of hiding it.</li></ul>", "<h4>1.7.0 (2022-08-30)</h4><ul> <li>Added: New filter to set a time before the Form Abandonment email is sent.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Changed: Check GDPR settings before trying to use a cookie.</li> <li>Fixed: Abandoned Entries were saved while ignoring the \"Save Only If Email Address\" setting.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li> <li>Fixed: Email notifications were being sent for both abandoned and completed forms instead of only one.</li></ul>", "<h4>1.6.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: New JavaScript event <code>wpformsFormAbandonmentDataSent</code> triggered when successfully sending the abandoned data to be saved.</li> <li>Added: Compatibility with WPForms 1.7.3 and its search functionality on the Entries page.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Changed: Do not store Abandoned Entries and do not send related notifications when Entry storage is disabled.</li> <li>Fixed: Some smart tags are not rendered correctly in the abandonment notifications.</li> <li>Fixed: Abandoned Entries were not saved when a user clicks on any internal link inside the Firefox browser.</li></ul>", "<h4>1.5.0 (2021-03-31)</h4><ul> <li>Added: Notification's \"Enable for abandoned forms entries\" option compatibility improvements with payment addons adding own rules whether to send a notification email.</li> <li>Changed: Replaced <code>jQuery.ready()</code> function with recommended way since jQuery 3.0.</li></ul>", "<h4>1.4.4 (2020-12-17)</h4><ul> <li>Fixed: Form abandonment via external links not always detected on some mobile devices.</li></ul>", "<h4>1.4.3 (2020-08-05)</h4><ul> <li>Changed: Password field values are no longer stored by default, can be enabled with <code>wpforms_process_save_password_form_abandonment</code> filter.</li> <li>Fixed: Abandoned entries are counting towards entry limits defined via Form Locker addon settings.</li> <li>Fixed: Prevent abandoned entry duplicates creation when 2 AJAX-based forms are present on the same page and only one of them was submitted.</li></ul>", "<h4>1.4.2 (2020-06-10)</h4><ul> <li>Fixed: Entry must have the \"completed\" type after its creation through non-ajax form.</li></ul>", "<h4>1.4.1 (2020-04-30)</h4><ul> <li>Fixed: Prevent 'Abandoned' (duplicate) entry on successful form submit.</li></ul>", "<h4>1.4.0 (2020-01-15)</h4><ul> <li>Changed: Access Controls compatibility (WPForms 1.5.8).</li></ul>", "<h4>1.3.0 (2020-01-09)</h4><ul> <li>Added: Tracking closing of the window or tab by listening to the <code>beforeunload</code> event.</li> <li>Changed: Do not send duplicate abandonment notifications if 'no duplicates' option is enabled.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Fixed: Form abandonment records only last checkbox field selection.</li> <li>Fixed: \"Prevent duplicate abandoned entries\" saves duplicate entries.</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.2 (2018-02-12)</h4><ul> <li>Fixed: Conflict with email notifications configured with conditional logic causing notifications to send when they should not.</li></ul>", "<h4>1.0.1 (2017-02-01)</h4><ul> <li>Fixed: Incorrect version in updater which caused WordPress to think an update was available.</li></ul>", "<h4>1.0.0 (2017-02-01)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.8.9"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-form-abandonment.png"}, "wpforms-form-locker": {"title": "Form Locker <PERSON>", "slug": "wpforms-form-locker", "url": "https://wpforms.com/addons/form-locker-addon/", "version": "2.9.0", "image": "https://wpforms.com/wp-content/uploads/2018/09/addon-icons-locker.png", "excerpt": "The WPForms Form Locker addon allows you to lock your WordPress forms with various permissions and access control rules including passwords, members-only, specific date / time, max entry limit, and more.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-form-locker-addon-in-wpforms/", "id": 265700, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>2.9.0 (2025-01-16)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: Error message for the required and unique Name and Phone fields sometimes disappeared.</li> <li>Fixed: Closed message wasn't shown when the Total Entry Limit was reached.</li></ul>", "<h4>2.8.0 (2024-08-06)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: The Form Builder settings screen had visual issues when RTL language was used.</li> <li>Fixed: Email verification could end up sending multiple emails due to a conflict with some plugins.</li> <li>Fixed: 500 Internal Server Error occurred when there was a huge number of entries for a form with the Unique Answers setting.</li> <li>Fixed: Now unique answers are supported in the Repeater Field.</li> <li>Fixed: Incorrect unique answer validation was triggered for valid values.</li> <li>Fixed: Locker fields were clickable in the Gutenberg editor preview.</li> <li>Fixed: The Form Builder settings screen had minor visual issues.</li> <li>Fixed: A new installation of an addon may have caused a database error.</li></ul>", "<h4>2.7.0 (2023-12-11)</h4><ul> <li>Changed: Improved frontend validation performance</li> <li>Fixed: Unique answer restriction now applies only to completed form entries.</li> <li>Fixed: Form Scheduling Start/End date and time were not properly validated.</li></ul>", "<h4>2.6.0 (2023-11-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li></ul>", "<h4>2.5.0 (2023-09-27)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Form Locker 2.5.0. Failure to do that will disable WPForms Form Locker functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Form Locker 2.5.0. Failure to do that will disable WPForms Form Locker functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Changed: Old deprecated <code>wpmu_new_blog</code> hook was replaced with the <code>wp_initialize_site</code> hook.</li> <li>Fixed: The form was incorrectly locked when having payment entries.</li></ul>", "<h4>2.4.0 (2023-03-27)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Fixed: Password and Age verification fields were not highlighted on validation failure.</li></ul>", "<h4>2.3.0 (2023-01-11)</h4><ul> <li>Added: Entry Limits &amp; Restrictions now have new \"till the end of the day/week/month/year\" locker options.</li> <li>Added: New filter <code>wpforms_locker_lockers_entry_limit_exclude_not_allowed_entries_excluded_statuses</code> allows to exclude entry statuses for entry total limit and entry user limit lockers.</li> <li>Changed: Error markup for the Password locker was modified according to other fields' errors.</li> <li>Changed: A message generated by the Email locker now has the same styles as an entry submission confirmation message.</li> <li>Changed: \"per day/week/month/year\" locker options for Entry Limits &amp; Restrictions were renamed to \"per 24 hours/7 days/~30 days/~365 days\".</li></ul>", "<h4>2.2.0 (2022-08-30)</h4><ul> <li>Changed: Improved the look and feel of the messages displayed to users when the form is closed.</li> <li>Changed: Minimum WPForms version is now 1.7.6.</li> <li>Changed: When the \"Enable user entry limit\" is turned on, selecting one of the associated checkboxes is now required.</li> <li>Fixed: Entry limit validation allows negative values to be set.</li> <li>Fixed: Next button was not clickable when a name field was empty and the field was non-required, but required a unique answer.</li> <li>Fixed: Restrict by email address did not exclude abandoned/partial entries.</li> <li>Fixed: Plugin install did not run properly.</li></ul>", "<h4>2.1.0 (2022-06-28)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms Form Locker. Failure to do that will disable the WPForms Form Locker plugin.</li> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Changed: Reorganized locations of 3rd party libraries.</li> <li>Fixed: Restrict by email address didn't work if the Confirmation setting was enabled for the Email field.</li> <li>Fixed: Form Scheduling datepickers didn't work if custom formats were set in the WordPress General Settings page.</li> <li>Fixed: Smart Phone field that required a unique value didn't get validated.</li></ul>", "<h4>2.0.3 (2022-02-10)</h4><ul> <li>Changed: Improved compatibility with PHP 8.</li> <li>Fixed: Correctly handle \"Unique answer\" feature comparisons of strings having special characters.</li></ul>", "<h4>2.0.2 (2021-09-16)</h4><ul> <li>Changed: Properly handle the UI part of the IP-related setting - when IP storage is disabled, do not allow enabling IP-based entry limit.</li> <li>Fixed: Do not globally cache all entry submission limits when Entry Limit by IP/email is enabled.</li></ul>", "<h4>2.0.1 (2021-09-14)</h4><ul> <li>Changed: Adjusted various styles on the Form Builder &gt; Settings &gt; Form Locker screen.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li> <li>Fixed: Start date should always be less than End date and vice versa.</li> <li>Fixed: Correctly handle global site time format changes when rendering a form with Form Schedule enabled.</li> <li>Fixed: Correctly handle time comparison in AM/PM format when setting Scheduling End Date if the user input is incorrect.</li> <li>Fixed: Object cache was breaking Entry Limits logic.</li></ul>", "<h4>2.0.0 (2021-08-03)</h4><ul> <li>Added: Age verification locker.</li> <li>Added: Email verification locker.</li> <li>Added: Entry limit based on user IP.</li> <li>Added: Entry limit based on an email field value.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Frontend UI enhancement when used with Conversational Forms addon.</li> <li>Changed: Improved compatibility with jQuery 3.5 and no jQuery Migrate plugin.</li> <li>Changed: Further improved selective JS script loading to take into account Form Locker settings.</li></ul>", "<h4>1.2.3 (2020-08-05)</h4><ul> <li>Added: Filter <code>wpforms_form_locker_submit_label</code> to change the submit button label.</li> <li>Fixed: Line breaks not correctly displayed on front-end with Form Locker messages (form settings).</li> <li>Fixed: The scheduling feature does not work with some custom date formats.</li> <li>Fixed: Unique Answer feature does not work with complex Name fields.</li></ul>", "<h4>1.2.2 (2020-03-03)</h4><ul> <li>Changed: Improved time delta detection in the Scheduling section.</li></ul>", "<h4>1.2.1 (2020-01-09)</h4><ul> <li>Fixed: Minor layout issues of the 'clear' button in the Scheduling section.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Changed: Form Locker Scheduling UI improvements.</li></ul>", "<h4>1.1.1 (2019-02-08)</h4><ul> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.0 (2018-11-12)</h4><ul> <li>Fixed: PHP fatal error if using PHP 5.4.</li> <li>Fixed: Conflict with multiple password-protected forms on the same page.</li></ul>", "<h4>1.0.0 (2018-10-09)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-form-locker.png"}, "wpforms-form-pages": {"title": "Form Pages Addon", "slug": "wpforms-form-pages", "url": "https://wpforms.com/addons/form-pages-addon/", "version": "1.11.0", "image": "https://wpforms.com/wp-content/uploads/2019/01/addon-icon-form-pages.png", "excerpt": "Want to improve your form conversions? WPForms Form Pages addon allows you to create completely custom \"distraction-free\" form landing pages to boost conversions (without writing any code).", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-form-pages-addon/", "id": 362485, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.11.0 (2025-07-31)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li> <li>Fixed: Compatibility issue with WPML plugin.</li> <li>Fixed: PHP warnings were displayed when user duplicated forms created before Form Pages were activated for the first time.</li> <li>Fixed: Cards requiring the 3D Secure modal were not processed.</li></ul>", "<h4>1.10.0 (2024-04-16)</h4><ul> <li>Added: Compatibility with the WPForms 1.8.8.</li> <li>Fixed: Minor Date/Time field UI issues on the frontend.</li> <li>Fixed: Some colors were not inherited properly from the color theme.</li></ul>", "<h4>1.9.0 (2024-02-22)</h4><ul> <li>Added: Compatibility with the WPForms 1.8.7.</li> <li>Changed: The minimum WPForms version supported is 1.8.7.</li> <li>Fixed: The Form Builder settings screen had visual issues when RTL language was used.</li> <li>Fixed: List indicators in Form Page message were placed outside the paragraph pane.</li></ul>", "<h4>1.8.0 (2024-01-09)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Form Pages 1.8.0. Failure to do that will disable WPForms Form Pages functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Form Pages 1.8.0. Failure to do that will disable WPForms Form Pages functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.6.</li> <li>Fixed: The Submit button had incorrect styling on Form Pages when the Classic markup was set up.</li> <li>Fixed: Incorrect error text was displayed when uploading a file of an illegal format in the Form Builder.</li></ul>", "<h4>1.7.0 (2023-08-15)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: There was an incorrect link style on the form page in footer.</li> <li>Fixed: <PERSON><PERSON> generated deprecation notices on the frontend when running on PHP 8.2.</li></ul>", "<h4>1.6.0 (2023-03-09)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Fixed: Head Logo preview was not displayed in the Form Builder if the form contained any field with Image Choices turned on.</li></ul>", "<h4>1.5.1 (2022-08-31)</h4><ul> <li>Fixed: Form page permalink was saved incorrectly when a custom menu link with the same permalink existed.</li> <li>Fixed: Incorrect information was displayed in form pages social previews.</li> <li>Fixed: Text in Paragraph Text field turned red when a custom error message was used.</li> <li>Fixed:  The<code>{page_title}</code> smart tag used form title instead of the title set in the addon.</li></ul>", "<h4>1.5.0 (2022-05-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms Form Pages. Failure to do that will disable the WPForms Form Pages plugin.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: New filter <code>wpforms_form_pages_frontend_handle_request_form_data</code> that can be used to improve multi-language support.</li></ul>", "<h4>1.4.1 (2020-11-16)</h4><ul> <li>Fixed: Display a form name instead of a form page title when using <code>{form_name}</code> smart tag inside the Message section.</li></ul>", "<h4>1.4.0 (2020-08-05)</h4><ul> <li>Added: Show a notice if permalinks are not configured.</li> <li>Changed: Page Title tag and meta tags should always use Form Pages Title if set.</li> <li>Changed: oEmbed links are removed from the Form Page HTML.</li></ul>", "<h4>1.3.1 (2020-02-18)</h4><ul> <li>Fixed: Datepicker is not working for \"Date/Time\" field.</li></ul>", "<h4>1.3.0 (2020-01-09)</h4><ul> <li>Added: Meta tag 'robots' with filterable <code>noindex,nofollow</code> value for all Form Pages.</li> <li>Fixed: Popup Maker popup content displays in Form Pages.</li></ul>", "<h4>1.2.1 (2019-11-07)</h4><ul> <li>Fixed: \"error404\" body class may appear if the custom permalink structure is used.</li> <li>Fixed: Form preview buttons open two tabs in the Edge browser.</li> <li>Fixed: \"Cannot read property 'addEventListener' of null\" JS error on form creation.</li></ul>", "<h4>1.2.0 (2019-10-14)</h4><ul> <li>Added: Hexcode option to the color picker.</li></ul>", "<h4>1.1.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, German, French and Portuguese (Brazilian).</li> <li>Fixed: Some themes override Form Page's templates.</li></ul>", "<h4>1.0.2 (2019-02-21)</h4><ul> <li>Added: Compatibility with Conversational Forms addon.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li> <li>Fixed: CSS enqueues filtering includes parents of the child themes.</li></ul>", "<h4>1.0.1 (2019-02-06)</h4><ul> <li>Fixed: Missing <code>title</code> tag.</li> <li>Fixed: Compatibility issue with Yoast SEO.</li></ul>", "<h4>1.0.0 (2019-01-22)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-form-pages.png"}, "wpforms-geolocation": {"title": "Geolocation Addon", "slug": "wpforms-geolocation", "url": "https://wpforms.com/addons/geolocation-addon/", "version": "2.11.0", "image": "https://wpforms.com/wp-content/uploads/2016/08/addon-icon-geolocation.png", "excerpt": "The WPForms Geolocation addon allows you to store your visitors' geolocation data with their form submission. You can also turn on address autocomplete to make forms easier to fill out.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-geolocation-addon-with-wpforms/", "id": 7501, "license": ["agency", "elite", "pro", "ultimate"], "category": [], "changelog": ["<h4>2.11.0 (2025-03-27)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li> <li>Fixed: Address data from a resumed link is now correctly restored if Address Autocomplete settings are enabled.</li> <li>Fixed: When a Geolocation field was added to Google Sheets, the smart tag was not converted.</li> <li>Fixed: State subfield in the address field now is hidden if user is filling address in the country which does not have states.</li> <li>Fixed: Several issues with the visibility of the address State subfield.</li> <li>Fixed: Address autocomplete didn't work properly in some cases.</li></ul>", "<h4>2.10.0 (2024-06-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li> <li>Fixed: The Address autocomplete field did not work without the map for the MapBox provider.</li> <li>Fixed: The Geolocation data flag icon was broken on the entry page.</li></ul>", "<h4>2.9.0 (2024-04-16)</h4><ul> <li>Fixed: The map was not initialized when opening the popup in Elementor.</li> <li>Fixed: Error in the console when the Display map setting was disabled.</li> <li>Fixed: Various RTL problems in the admin dashboard.</li> <li>Fixed: Layout issue on Edit Entry page.</li> <li>Fixed: The map address list was not available when opening the popup in Elementor.</li> <li>Fixed: The Mapbox address autocomplete didn't work when the search query didn't contain digits.</li></ul>", "<h4>2.8.0 (2024-01-09)</h4><ul> <li>Added: Compatibility with WPForms 1.8.6.</li></ul>", "<h4>2.7.0 (2023-11-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.5.</li></ul>", "<h4>2.6.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Geolocation 2.6.0. Failure to do that will disable WPForms Geolocation functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Geolocation 2.6.0. Failure to do that will disable WPForms Geolocation functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Fixed: Entry geolocation value was not added to the CSV file for the email notifications.</li></ul>", "<h4>2.5.0 (2023-08-22)</h4><ul> <li>Added: Ability to display Geolocation data when printing and displaying an entry data.</li> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Map for the Address field was displayed incorrectly when a form contained page breaks.</li></ul><span style=\"color: #444444; font-family: Arvo, Georgia, Times, serif; font-size: 22px;\">2.4.0 (2023-03-13)</span><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.1.</li></ul>", "<h4>2.3.1 (2022-08-30)</h4><ul> <li>Fixed: Error when using location with autocomplete.</li></ul>", "<h4>2.3.0 (2022-07-05)</h4><ul> <li>IMPORTANT: Algolia Places has been discontinued by Algolia. All Algolia functionality in the addon has been deprecated and removed.</li> <li>Added: New Places Provider - Mapbox.</li> <li>Added: Preview area on the Settings &gt; Geolocation admin page.</li> <li>Added: New filters to change the map appearance and location sources.</li> <li>Changed: Increased minimum WPForms supported version to 1.7.5.</li> <li>Changed: <PERSON><PERSON><PERSON> no longer automatically completes the Text field if Address Autocomplete is enabled.</li> <li>Changed: Improved detection of the user's current location.</li> <li>Changed: In the address and text field search, users can now hit the Enter key to select an address.</li> <li>Fixed: Map styling inside the Full Site Editor in WordPress 6.0.</li> <li>Fixed: Geolocation coordinates were incorrect for Address Autocomplete with a custom scheme.</li> <li>Fixed: Address autocomplete didn't fill in the Address &gt; City subfield.</li> <li>Fixed: <code>{entry_geolocation}</code> smart tag works in Confirmation messages.</li> <li>Fixed: Compatibility with the Conversational Forms addon has been improved.</li></ul>", "<h4>2.2.0 (2022-05-11)</h4><ul> <li>IMPORTANT: Algolia Places has been discontinued by Algolia. If you are using it you need to switch to Google Places to prevent disruptions in form geolocation features.</li> <li>Added: New filter <code>wpforms_geolocation_places_providers_google_places_query_args</code> that can be used to improve multi-language support.</li> <li>Fixed: Users geolocation detection on the Entry page was working incorrectly with KeyCDN API.</li></ul>", "<h4>2.1.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: Address field filling.</li> <li>Fixed: Value with mask is not saved in a Text field when Address Autocomplete is enabled.</li> <li>Fixed: Various typos reported by translators.</li></ul>", "<h4>2.0.0 (2021-02-18)</h4><ul> <li>Added: New Places Providers selection: Google Places, Algolia Places.</li> <li>Added: Address and Text fields can have address autocomplete enabled on typing.</li> <li>Added: Display a map before or after the field to select a location on a map without typing.</li> <li>Added: Retrieve user's current location with a browser prompt and prefill address/text fields with address autocomplete enabled.</li> <li>Added: Added own WPForms geolocation API endpoint to retrieve user's geolocation based on their IP address.</li> <li>Changed: Removed map image preview from email notifications due to Google API restrictions.</li> <li>Fixed: Geolocation: display and save only existing data (sometimes ZIP code may be missing).</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.1 (2019-02-26)</h4><ul> <li>Fixed: Geolocation provider fallback logic.</li> <li>Fixed: Referencing geolocation providers no longer accessible.</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.1 (2016-08-04)</h4><ul> <li>Fixed: Bug preventing IP addresses from processing</li></ul>", "<h4>1.0.0 (2016-08-03)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.4"}, "form_builder": {"category": []}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": true, "new": false, "icon": "addon-icon-geolocation.png"}, "wpforms-getresponse": {"title": "GetResponse Addon", "slug": "wpforms-getresponse", "url": "https://wpforms.com/addons/getresponse-addon/", "version": "1.8.0", "image": "https://wpforms.com/wp-content/uploads/2016/04/addon-icons-getresponse-1.png", "excerpt": "The WPForms GetResponse addon allows you to create GetResponse newsletter signup forms in WordPress, so you can grow your email list. ", "doc": "https://wpforms.com/docs/how-to-install-and-use-getresponse-addon-with-wpforms/", "id": 2565, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.8.0 (2025-06-05)</h4><ul>  <li>IMPORTANT: Support for PHP 7.2 has been discontinued. If you are running PHP 7.2, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li>  <li>IMPORTANT: <PERSON>don requires the cURL extension to work. If you are running PHP without the cURL extension, you MUST enable it before upgrading this addon. Failure to do that will disable addon functionality.</li>  <li>Changed: Updated <code>getresponse/sdk-php</code> library to v3.0.0.</li>  <li>Changed: The minimum WPForms version supported is 1.9.6.</li>  <li>Fixed: Compatibility with PHP 8.2 and 8.3.</li>  <li>Fixed: Custom Fields now support all formats of the Name Field (Full, First, Middle, Last) as separate options.</li></ul>", "<h4>1.7.0 (2024-06-17)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms GetResponse 1.7.0. Failure to do that will disable WPForms GetResponse functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms GetResponse 1.7.0. Failure to do that will disable WPForms GetResponse functionality.</li> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li> <li>Fixed: Create or Update action could lose subscriber tags and custom fields if a subscriber was already in the GetResponse list.</li></ul>", "<h4>1.6.0 (2023-08-15)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: The fatal error was thrown when users were trying to create a connection in the Form Builder with an expired API key.</li></ul>", "<h4>1.5.1 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.5.0 (2022-05-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms GetResponse and WPForms 1.7.3 (that the addon is relying on). Failure to do that will disable the WPForms GetResponse plugin.</li> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms GetResponse. Failure to do that will disable the new WPForms GetResponse functionality.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li> <li>Fixed: Properly handle the situation when trying to change the template for the same form multiple times.</li> <li>Fixed: Send to GetResponse form submission data even when the \"Entry storage\" option is disabled in the Form Builder.</li></ul>", "<h4>1.4.0 (2021-03-31)</h4><ul> <li>Added: Send a subscriber IP address to GetResponse if GDPR options configured to allow that.</li></ul>", "<h4>1.3.0 (2020-12-10)</h4><ul> <li>IMPORTANT: Support for PHP 5.4 and below has been discontinued. If you are running anything older than PHP 5.5, you MUST upgrade PHP before installing the WPForms GetResponse. Failure to do that will disable addon functionality.</li> <li>Added: Updated GetResponse API from v2 to v3.</li> <li>Added: Ability to update subscribers with data from the form submission.</li> <li>Added: Ability to add tags to subscribers.</li> <li>Added: Ability to define the \"Day of Cycle\" - Autoresponder day.</li> <li>Added: Ability to set custom fields in GetResponse using entry data.</li></ul>", "<h4>1.2.0 (2018-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.4 (2018-03-15)</h4><ul> <li>Fixed: Error when adding account from Settings &gt; Integrations tab.</li></ul>", "<h4>1.0.3 (2017-03-09)</h4><ul> <li>Changed: Adjust display order so that the providers show in alphabetical order</li></ul>", "<h4>1.0.2 (2016-07-07)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.1 (2016-06-23)</h4><ul> <li>Changed: Prevent plugin from running if WPForms Pro is not activated</li></ul>", "<h4>1.0.0 (2016-04-26)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.3", "wpforms": "1.9.6"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-getresponse.png"}, "wpforms-google-calendar": {"title": "Google Calendar Addon", "slug": "wpforms-google-calendar", "url": "https://wpforms.com/addons/google-calendar-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/08/icon-google-calendar.png", "excerpt": "The Google Calendar Addon for WPForms allows you to send form submissions and create events in your linked Google calendars at regularly scheduled intervals selected by you. ", "doc": "https://wpforms.com/docs/google-calendar-addon/", "id": 3125650, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.0.0 (2025-08-19)</h4><ul><li>Initial release</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.7"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-google-calendar.png"}, "wpforms-google-drive": {"title": "Google Drive Addon", "slug": "wpforms-google-drive", "url": "https://wpforms.com/addons/google-drive-addon/", "version": "1.1.0", "image": "https://wpforms.com/wp-content/uploads/2025/05/icon-google-drive.png", "excerpt": "The WPForms Google Drive addon allows you to automatically filter and export form entries and file uploads to your Google Drive.\r\n", "doc": "https://wpforms.com/docs/google-drive-addon/", "id": 3095021, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.1.0 (2025-07-15)</h4><ul> <li>Added: \"Retry\" option when an attempt to upload files to Drive fails.</li> <li>Added: The wpforms_google_drive_provider_settings_form_builder_enabled_shared_drives filter. The filter allows you to select a shared directory.</li> <li>Fixed: Multiple uploaded file links did not redirect to the right drive link in email notifications.</li></ul>", "<h4>1.0.0 (2025-05-20)</h4><ul> <li>Initial release</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-google-drive.png"}, "wpforms-google-sheets": {"title": "Google Sheets Addon", "slug": "wpforms-google-sheets", "url": "https://wpforms.com/addons/google-sheets-addon/", "version": "2.4.0", "image": "https://wpforms.com/wp-content/uploads/2022/10/icon-google-sheets.png", "excerpt": "The WPForms Google Sheets addon makes it easy to send form entries and leads to a Google Sheet.", "doc": "https://wpforms.com/docs/google-sheets-addon/", "id": 2225879, "license": ["agency", "elite", "pro", "ultimate"], "category": ["providers", "settings"], "changelog": ["<h4>2.4.0 (2025-05-06)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>IMPORTANT: Addon requires the cURL extension to work. If you are running PHP without the cURL extension, you MUST enable it before upgrading this addon. Failure to do that will disable addon functionality.</li> <li>Added: Compatibility with WPForms 1.9.5.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li> <li>Changed: Error handling was improved.</li> <li>Fixed: The logic for displaying addon settings on the Integrations page.</li> <li>Fixed: Unsaved changes modal no longer appears after saving the form.</li></ul>", "<h4>2.3.1 (2025-02-10)</h4><ul> <li>Custom fields disappeared when you saved a form before connections loaded.</li> <li>The custom fields were removed after duplicating the form.</li></ul>", "<h4>2.3.0 (2024-12-03)</h4><ul> <li>Fixed: The <code>{entry_user_journey}</code> smart tag value.</li></ul>", "<h4>2.2.0 (2024-09-24)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Google Sheets 2.2.0. Failure to do that will disable WPForms Google Sheets functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Google Sheets 2.2.0. Failure to do that will disable WPForms Google Sheets functionality.</li> <li>Added: Support for authorizing \".dev\" websites.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: An issue where the Single Item field only returned the Item Price when data was sent to Google Sheets.</li> <li>Fixed: A bug where Google Sheets connections were inadvertently removed when changing the form template.</li> <li>Fixed: Corrected field sorting to match the form fields order in settings when using field mapping and smart tags.</li></ul>", "<h4>2.1.0 (2024-06-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Added: New filter `wpforms_google_sheets_api_client_auth_args` to modify authorization and reauthorization URL arguments.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li> <li>Fixed: Various RTL problems in the admin dashboard.</li></ul>", "<h4>2.0.1 (2024-01-12)</h4><ul> <li>IMPORTANT: All users who are sending entries to Google Sheets will need to re-authenticate their Google connection once version 2.0.1 is installed to avoid interruptions in service.</li> <li>Changed: Lowered minimum WPForms version supported to 1.8.3.</li> <li>Changed: Lowered minimum PHP version supported to 5.6.</li> <li>Changed: Lowered minimum WordPress version supported to 5.2.</li></ul>", "<h4>2.0.0 (2024-01-11)</h4><ul> <li>IMPORTANT: All users who are sending entries to Google Sheets will need to re-authenticate their Google connection once version 2.0.0 is installed to avoid interruptions in service.</li> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Google Sheets 2.0.0. Failure to do that will disable WPForms Google Sheets functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Google Sheets 2.0.0. Failure to do that will disable WPForms Google Sheets functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.1.0 (2023-04-20)</h4><ul> <li>Added: Support for form field smart tags in field mapping of custom values.</li> <li>Fixed: Attempt to activate the addon with WPForms version prior to 1.7.3 resulted in a fatal error.</li> <li>Fixed: Field Mapping layout had incorrect subfield widths in Safari.</li></ul>", "<h4>1.0.0 (2022-10-25)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": true, "new": false, "icon": "addon-icon-google-sheets.png"}, "wpforms-hubspot": {"title": "HubSpot Addon", "slug": "wpforms-hubspot", "url": "https://wpforms.com/addons/hubspot-addon/", "version": "1.4.0", "image": "https://wpforms.com/wp-content/uploads/2022/03/addon-icon-hubspot.png", "excerpt": "The WPForms HubSpot addon lets you send leads from WordPress directly to your HubSpot CRM.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-hubspot-addon-in-wpforms/", "id": 1846183, "license": ["agency", "elite", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.4.0 (2025-07-01)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Added: It's now possible to map all formats of the Name field (Full, First, Middle, Last) separately to HubSpot available properties.</li> <li>Added: Prevent selection of duplicate field mappings to avoid related errors.</li> <li>Added: New <code>wpforms_hubspot_api_allow_property</code> filter is added to customize contact properties.</li> <li>Changed: The minimum WPForms version supported is 1.9.6.</li> <li>Changed: Improved handling of expired or invalid tokens to prevent data submission errors.</li> <li>Fixed: Date field mapped to Hubspot Custom Date property was not processed correctly.</li></ul>", "<h4>1.3.0 (2024-12-03)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.1.</li></ul>", "<h4>1.2.0 (2023-10-10)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms HubSpot 1.2.0. Failure to do that will disable WPForms HubSpot functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms HubSpot 1.2.0. Failure to do that will disable WPForms HubSpot functionality.</li> <li>Added: Compatibility with the upcoming HubSpot API changes.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.1.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li></ul>", "<h4>1.0.2 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.0.1 (2022-08-31)</h4><ul> <li>Fixed: Checkbox fields mapped to HubSpot Multiple Checkbox fields were not processed correctly.</li></ul>", "<h4>1.0.0 (2022-03-10)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.6"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["crm", "email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-hubspot.png"}, "wpforms-convertkit": {"title": "<PERSON>", "slug": "wpforms-convertkit", "url": "https://wpforms.com/addons/convertkit-addon/", "version": "1.1.0", "image": "https://wpforms.com/wp-content/uploads/2023/12/icon-provider-kit.png", "excerpt": "The WPForms Kit addon lets you collect subscribers to grow your mailing list, automate email marketing, and connect with your audience.", "doc": "https://wpforms.com/docs/convertkit-addon/", "id": 2744716, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.1.0 (2024-11-07)</h4><ul> <li>IMPORTANT: Updated logo and name to reflect the company's rebranding from ConvertKit to Kit.</li> <li>Added: WPForms 1.9.1 compatibility.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: In some cases incomplete addon configuration data could prevent the form with payments to be submitted.</li> <li>Fixed: In rare cases PHP warning could be thrown if custom fields were mapped to form fields no longer available due to license downgrade.</li> <li>Fixed: Ensure payment values are correctly formatted and properly escaped before sending to Kit.</li> <li>Fixed: Conditional Logic disappeared for newly added connections.</li></ul>", "<h4>1.0.0 (2023-12-13)</h4><ul> <li>Added: Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.4", "wpforms": "1.9.1"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-kit.png"}, "wpforms-lead-forms": {"title": "Lead Forms Addon", "slug": "wpforms-lead-forms", "url": "https://wpforms.com/addons/lead-forms-addon/", "version": "1.6.0", "image": "https://wpforms.com/wp-content/uploads/2023/01/icon-lead-forms.png", "excerpt": "Want to increase conversions and generate leads for your business? The WPForms Lead Forms Addon lets you embed beautiful forms that show one question at a time.", "doc": "https://wpforms.com/docs/lead-forms-addon/", "id": 2320054, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.6.0 (2025-06-05)</h4><ul>  <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li>  <li>Changed: The minimum WPForms version supported is 1.9.1.</li>  <li>Changed: Improved compatibility with the Astra theme.</li>  <li>Fixed: Font color of the selected choice was incorrect in modern Dropdown with Lead Forms.</li>  <li>Fixed: Required Net Promoter Score field validation failure is now displayed before proceeding to the next step of the form.</li>  <li>Fixed: Missed styles for the PayPal subscription submit button.</li>  <li>Fixed: Country code alignment of the Phone field in the form builder.</li></ul>", "<h4>1.5.0 (2024-04-25)</h4><ul> <li>Fixed: Advanced styles were not applied to the Entry Preview and Order Summary in the confirmation message.</li> <li>Fixed: The Single Item payment field had extra line space in case of a long label.</li> <li>Fixed: Compatibility with the Signature field.</li> <li>Fixed: Lead Forms styles were not applied for the Order Summary table.</li></ul>", "<h4>1.4.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.7.</li> <li>Fixed: Authorize.Net subfields alignment in Classic frontend mode.</li> <li>Fixed: Active state of the Net Promoter Score field in Classic frontend mode.</li> <li>Fixed: Geolocation map preview breaking out of container.</li> <li>Fixed: In rare cases, Turnstile Captcha was not displayed correctly when it expired and was refreshed.</li> <li>Fixed: The Field Size option was not blocked if it was in the Layout element.</li> <li>Fixed: Regular Stripe card number field was not inheriting colors correctly.</li></ul>", "<h4>1.3.0 (2023-09-27)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Lead Forms 1.3.0. Failure to do that will disable WPForms Lead Forms functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Lead Forms 1.3.0. Failure to do that will disable WPForms Lead Forms functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Changed: Lead Forms and Coupons addons are now working together nicely.</li> <li>Changed: The Coupon field preview in the Form Builder now looks much better.</li> <li>Fixed: Lead Forms styles weren't loaded in the Elementor builder preview.</li> <li>Fixed: Required Rich Text field was broken in certain cases related to errors on the page.</li> <li>Fixed: Field size option was enabled for fields inside the Layout field in the Form Builder after page refresh.</li> <li>Fixed: Turnstile Captcha overlapped the \"Submit\" button in Lead Forms.</li> <li>Fixed: Additional border appeared after unchecking a Likert Scale option.</li> <li>Fixed: Long field labels overlapped the \"Duplicate Field\" and \"Delete Field\" icons in the Form Builder.</li> <li>Fixed: The Rich Text field style was reset when Lead Forms was enabled.</li> <li>Fixed: The Rich Text field in Visual mode didn't inherit secondary text color and focus styles.</li> <li>Fixed: The Square Credit Card field had a larger height than it should.</li> <li>Fixed: The Date/Time field was rendered incorrectly on the front end when in Classic markup mode.</li> <li>Fixed: The legacy Credit Card field layout was partially broken on the front end.</li> <li>Fixed: External Stripe payment field styles were not fully overridden by the addon styles.</li> <li>Fixed: The progress bar wasn't updated properly when the form was used in an Elementor popup.</li> <li>Fixed: The \"Lead Forms Enabled\" notice was duplicated if Lead Forms was turned off, the form was changed and converted back to Lead Forms again.</li></ul>", "<h4>1.2.0 (2023-06-28)</h4><ul> <li>Added: Compatibility with the WPForms Coupons addon.</li></ul>", "<h4>1.1.0 (2023-03-21)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Added: A new option to control scrolling to the top of the form when proceeding to the next page, disabled by default.</li></ul>", "<h4>1.0.0 (2023-01-11)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.1"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-lead-forms.png"}, "wpforms-mailchimp": {"title": "Mailchimp Addon", "slug": "wpforms-mailchimp", "url": "https://wpforms.com/addons/mailchimp-addon/", "version": "2.5.1", "image": "https://wpforms.com/wp-content/uploads/2016/02/addon-icon-mailchimp-1.png", "excerpt": "The WPForms Mailchimp addon allows you to create Mailchimp newsletter signup forms in WordPress, so you can grow your email list. ", "doc": "https://wpforms.com/docs/install-use-mailchimp-addon-wpforms/", "id": 153, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>2.5.1 (2025-07-01)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.6.</li> <li>Fixed: It was impossible to map any of the Name-related fields when the \"First Name\" and/or \"Last Name\" fields were marked as required in Mailchimp.</li></ul>", "<h4>2.5.0 (2025-05-06)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>IMPORTANT: <PERSON>don requires the cURL extension to work. If you are running PHP without the cURL extension, you MUST enable it before upgrading this addon. Failure to do that will disable addon functionality.</li> <li>Changed: Improved UI/UX for the List field in the builder.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li></ul>", "<h4>2.4.1 (2025-04-25)</h4><ul> <li>Fixed: Address was not saved in the Mailchimp audience if the state subfield was not set.</li></ul>", "<h4>2.4.0 (2024-10-10)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Mailchimp 2.4.0. Failure to do that will disable WPForms Mailchimp functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Mailchimp 2.4.0. Failure to do that will disable WPForms Mailchimp functionality.</li> <li>Added: WPForms 1.9.1 compatibility.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li></ul>", "<h4>2.3.0 (2023-06-12)</h4><ul> <li>Changed: Checkboxes have been replaced with fancy toggles on the addon settings screen in the Form Builder.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li></ul>", "<h4>2.2.0 (2022-09-22)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms Mailchimp and WPForms ******* (that the addon is relying on). Failure to do that will disable the WPForms Mailchimp plugin.</li> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Mailchimp. Failure to do that will disable the new WPForms Mailchimp functionality.</li> <li>Changed: Improved translations by removing confusion if non-translatable placeholders are used.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: PHP Fatal error occurred in certain cases during a form processing.</li> <li>Fixed: Smart Tags could not be inserted in the Note setting.</li></ul>", "<h4>2.1.1 (2021-09-07)</h4><ul> <li>Fixed: Compatibility with WordPress Multisite installs.</li> <li>Fixed: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li></ul>", "<h4>2.1.0 (2021-04-08)</h4><ul> <li>Added: Ability to define which tags you wish to remove from contact when updating an existing one in your Audience.</li> <li>Added: New option in the form Mailchimp settings to notify users when they are already subscribed.</li> <li>Changed: Send the note to Mailchimp when the contact is updated because of the \"Update the profile\" option enabled.</li> <li>Fixed: Send to Mailchimp form submission data even when the \"Entry storage\" option is disabled in the Form Builder.</li> <li>Fixed: Properly handle the situation when trying to change the template for the same form multiple times.</li></ul>", "<h4>2.0.0 (2021-03-16)</h4><ul> <li>Added: New actions: unsubscribe, archive, delete, record event.</li> <li>Added: Assign existing or new tags to subscribers.</li> <li>Added: Add a note to subscribers (with support of Smart Tags).</li> <li>Added: Mark subscribers as VIP.</li> <li>Added: Update subscribers' information on Mailchimp if they already exist in your Audience.</li> <li>Added: Map form field values to any Mailchimp custom fields.</li> <li>Changed: Rename \"Lists\" to \"Audiences\".</li> <li>Changed: Improved integration with Mailchimp groups (segments) of your Audience (inheriting their radio/dropdown/checkbox multiple selection status).</li> <li>Fixed: Convert form field values properly when sending data to Mailchimp.</li></ul>", "<h4>1.4.2 (2020-03-03)</h4><ul> <li>Changed: Make the addon consistent with the updated Mailchimp branding (MailChimp to Mailchimp).</li> <li>Fixed: Simultaneous change of Date format and Datepicker type in \"Date/Time\" field may produce an error on Mailchimp form submission.</li></ul>", "<h4>1.4.1 (2020-01-09)</h4><ul> <li>Changed: Upgraded the Mailchimp library version to v2.5.4.</li></ul>", "<h4>1.4.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.3.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.2.0 (2017-12-04)</h4><ul> <li>Changed: Birthday field special integration rules.</li> <li>Changed: Improved localization support and translatable strings.</li> <li>Changed: Update Mailchimp PHP library (2.4).</li></ul>", "<h4>1.1.1 (2017-03-30)</h4><ul> <li>Fixed: Issue for some users connecting v3 accounts</li></ul>", "<h4>1.1.0 (2017-03-30)</h4><ul> <li>Added: Mailchimp API version 3 support</li> <li>Changed: Mailchimp API version 2 support has been deprecated, please reconnect forms using the new version 3</li></ul>", "<h4>1.0.6 (2017-03-09)</h4><ul> <li>Changed: Adjust display order so that the providers show in alphabetical order</li></ul>", "<h4>1.0.5 (2016-10-24)</h4><ul> <li>Changed: Namespaced Mailchimp PHP classes to prevent conflicts</li></ul>", "<h4>1.0.4 (2016-07-07)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.3 (2016-06-23)</h4><ul> <li>Changed: Prevent plugin from running if WPForms Pro is not activated</li> <li>Changed: Corrected AWeber capitilization inside form builder</li> <li></li></ul>", "<h4>1.0.2 (2016-05-30)</h4><ul> <li>Changed: Disable SSL verify which causes issues with some web hosts</li></ul>", "<h4>1.0.1 (2016-04-12)</h4><ul> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.0 (2016-03-11)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.6"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-mailchimp.png"}, "wpforms-mailerlite": {"title": "MailerLite Addon", "slug": "wpforms-mailerlite", "url": "https://wpforms.com/addons/mailerlite-addon/", "version": "1.2.0", "image": "https://wpforms.com/wp-content/uploads/2022/05/icon-provider-mailerlite.png", "excerpt": "The WPForms MailerLite addon makes it easy to send contacts from your WordPress forms to your MailerLite Groups.", "doc": "https://wpforms.com/docs/install-use-mailerlite-addon-wpforms/", "id": 1993604, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.2.0 (2024-08-29)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms MailerLite 1.2.0. Failure to do that will disable WPForms MailerLite functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms MailerLite 1.2.0. Failure to do that will disable WPForms MailerLite functionality.</li> <li>Added: WPForms 1.9.0 compatibility.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Add entry field data to the Log error message in case if transaction was stopped by conditional logic.</li></ul>", "<h4>1.1.0 (2023-08-15)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: The fatal error was thrown when users were trying to create a connection in the Form Builder with an empty API key.</li></ul>", "<h4>1.0.1 (2022-09-21)</h4><ul> <li>Changed: The options in the \"Type\" dropdown should be capitalized.</li> <li>Changed: The multiselect dropdown options inside connections can now be searchable.</li> <li>Changed: Minimum WPForms version is now 1.7.5.</li> <li>Fixed: Invalid date was displayed in MailerLite when a new contact created with the \"Unsubscribe\" type.</li> <li>Fixed: Inside the connection the \"Groups\" dropdown should always display all the existing MailerLite groups.</li></ul>", "<h4>1.0.0 (2022-05-31)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-mailerlite.png"}, "wpforms-mailpoet": {"title": "MailPoet Addon", "slug": "wpforms-mailpoet", "url": "https://wpforms.com/addons/mailpoet-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/03/icon-provider-mailpoet.png", "excerpt": "The WPForms MailPoet addon allows you to integrate email marketing forms with WordPress, enabling you to create subscription forms, manage subscribers, and grow your mailing list.", "doc": "https://wpforms.com/docs/mailpoet-addon/", "id": 3066483, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.0.0 (2025-03-11)</h4><ul> <li>Initial release</li></ul>"], "required_versions": {"wp": "6.6", "php": "7.4", "wpforms": "1.9.4"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["email-marketing"], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-mailpoet.png"}, "wpforms-make": {"title": "Make Addon", "slug": "wpforms-make", "url": "https://wpforms.com/addons/make-addon/", "version": "1.0.1", "image": "https://wpforms.com/wp-content/uploads/2025/05/icon-provider-make.png", "excerpt": "With the WPForms Make addon, you can connect your forms to multiple apps and services, enabling powerful automations to simplify workflows, manage data, and save time.", "doc": "https://wpforms.com/docs/make-addon/", "id": 3093110, "license": ["agency", "elite", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.0.1 (2025-08-14)</h4><ul> <li>Fixed: Submitted form data wasn't delivered due to a failed Action Schedule task when the Modern File Upload field was used.</li></ul>", "<h4>1.0.0 (2025-05-14)</h4><ul> <li>Initial release</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "*******"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-make.png"}, "wpforms-offline-forms": {"title": "Offline Forms Addon", "slug": "wpforms-offline-forms", "url": "https://wpforms.com/addons/offline-forms-addon/", "version": "1.3.0", "image": "https://wpforms.com/wp-content/uploads/2017/09/addon-offline-forms.png", "excerpt": "Never lose leads or data again. Offline Forms addon allows your users to save their entered data offline and submit when their internet connection is restored.", "doc": "https://wpforms.com/docs/how-to-install-and-set-up-the-offline-forms-addon/", "id": 85564, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.3.0 (2024-08-01)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Offline Forms 1.3.0. Failure to do that will disable WPForms Offline Forms functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Offline Forms 1.3.0. Failure to do that will disable WPForms Offline Forms functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Fixed: JS console error was thrown after restoring a form in some cases.</li></ul>", "<h4>1.2.4 (2023-07-03)</h4><ul> <li>Fixed: Offline form was constantly scrolled to the top when the connection was offline.</li> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.2.3 (2021-09-28)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8+ and the updated Form Builder.</li> <li>Changed: Improved compatibility with jQuery 3.5 and no jQuery Migrate plugin.</li> <li>Fixed: Broken Modern Dropdown field value restoration.</li></ul>", "<h4>1.2.2 (2020-08-05)</h4><ul> <li>Fixed: Improved wording in the user-facing text displayed when a browser went offline.</li></ul>", "<h4>1.2.1 (2020-01-09)</h4><ul> <li>Fixed: Prevent multiple form submissions when Offline forms functionality enabled.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.0 (2017-09-27)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.8.4"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-offline-forms.png"}, "wpforms-paypal-commerce": {"title": "PayPal Commerce Addon", "slug": "wpforms-paypal-commerce", "url": "https://wpforms.com/addons/paypal-commerce-addon/", "version": "1.13.0", "image": "https://wpforms.com/wp-content/uploads/2016/02/addon-icon-paypal.png", "excerpt": "The WPForms PayPal Commerce addon makes it easy to accept PayPal and credit card payments on your WordPress site. Quickly set it up with a few clicks.", "doc": "https://wpforms.com/docs/paypal-commerce-addon/", "id": 2113327, "license": ["agency", "elite", "pro", "ultimate"], "category": ["payments"], "changelog": ["<h4>1.13.0 (2025-03-05)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Added: New <code>wpforms_paypal_commerce_process_subscription_ajax_subscription_data</code> filter that allows modifying subscription data before sending to PayPal.</li> <li>Changed: Improved error messaging during a payment form submission.</li> <li>Fixed: Order couldn't be created in some cases.</li></ul>", "<h4>1.12.0 (2024-12-11)</h4><ul> <li>Changed: The minimum WPForms version supported is *******.</li></ul>", "<h4>1.11.0 (2024-09-24)</h4><ul> <li>Added: Notice in the WPForms &gt; Settings &gt; Payment admin page when a selected currency is not supported by PayPal Commerce.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Changed: Improved rendering of Payment Fields according to W3C requirements.</li> <li>Changed: PayPal Commerce value now displays correctly on single entry and payment pages when a transaction is paid through the PayPal Checkout method.</li> <li>Changed: Email setting was removed from the Form Builder &gt; Payments &gt; PayPal Commerce screen due to PayPal API changes.</li> <li>Fixed: PayPal Commerce field was broken in the Elementor editor preview with the left panel collapsed.</li> <li>Fixed: Subscription payment failed for zero-decimal currencies.</li> <li>Fixed: PayPal Commerce payment field was broken in the Elementor popup.</li></ul>", "<h4>1.10.1 (2024-05-21)</h4><ul> <li>Fixed: In some cases, payments couldn't be processed because the account connection was expired or invalid.</li></ul>", "<h4>1.10.0 (2024-04-25)</h4><ul> <li>Changed: Improved field layout on the frontend for better user experience on mobile devices.</li> <li>Fixed: Improved handling of corrupted payment submission data.</li> <li>Fixed: In some cases, payments couldn't be submitted due to expired tokens.</li></ul>", "<h4>1.9.1 (2024-03-07)</h4><ul> <li>Fixed: One-Time payments failed when the visibility of payment fields was configured by conditional logic.</li></ul>", "<h4>1.9.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with the WPForms 1.8.7.</li> <li>Changed: The minimum WPForms version supported is 1.8.7.</li> <li>Fixed: Incorrect payment title was saved in some cases.</li> <li>Fixed: Hidden cardholder name skipped card details validation and did not prevent a form submission.</li> <li>Fixed: Cardholder name validation was skipped when field was not required.</li> <li>Fixed: One-Time payments failed when a Single Item payment field was hidden via conditional logic.</li> <li>Fixed: PHP notice was thrown when the Shipping Address setting was configured.</li></ul>", "<h4>1.8.0 (2023-12-14)</h4><ul> <li>Fixed: Account connection issues blocked a multi-payments form submission that resulted in unexpected issues with other active integrations (like Stripe).</li> <li>Fixed: Account couldn't be connected on the WPForms settings page in various cases.</li> <li>Fixed: The PayPal Checkout button had no distance from the last field.</li></ul>", "<h4>1.7.0 (2023-10-23)</h4><ul> <li>Fixed: Compatibility with the Conversational Forms addon was improved.</li> <li>Fixed: Credit card rejection errors did not prevent a form submission.</li> <li>Fixed: There were situations when PHP notices were generated when a payment form was submitted.</li></ul>", "<h4>1.6.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms PayPal Commerce 1.6.0. Failure to do that will disable WPForms PayPal Commerce functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms PayPal Commerce 1.6.0. Failure to do that will disable WPForms PayPal Commerce functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.5.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: There was no payment method selected by default when the PayPal Commerce credit card field was added to a form.</li></ul>", "<h4>1.4.0 (2023-06-26)</h4><ul> <li>Compatibility with the upcoming WPForms Coupons addon release.</li></ul>", "<h4>1.3.0 (2023-06-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.2.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li> <li>Fixed: In some cases a payment couldn't be submitted due to an API error and expiration of tokens.</li> <li>Fixed: JavaScript error occurred when the user was asked to enter verification information for a payment form locked with the Form Locker addon.</li> <li>Fixed: It was not possible to click the PayPal Checkout button when an API error occurred.</li></ul>", "<h4>1.2.1 (2023-04-06)</h4><ul> <li>Fixed: An error about the empty total amount was displayed for multi-page forms even though the amount was not empty after users' selection.</li></ul>", "<h4>1.2.0 (2023-03-22)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1.</li> <li>Added: New filter <code>wpforms_paypal_commerce_api_http_request_base_url</code> allows to modify the API base URL of HTTP request that might help in case when PayPal blacklisted a customer's server IP.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: Previous page could not be opened without filling Credit Card Number.</li> <li>Fixed: Certain users couldn't connect their business accounts.</li> <li>Fixed: The form could be submitted even though an access token was invalid, which led to storing false payment entries.</li> <li>Fixed: The Cardholder Name field was validated when other Credit Card details were filled in and valid.</li> <li>Fixed: Multistep form now returns to the first page when user tries to submit the form with empty total.</li> <li>Fixed: Payment API error was displayed too close to the Submit button.</li></ul>", "<h4>1.1.0 (2022-09-28)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.7.7.</li> <li>Fixed: Connection error messages were improved to cover more cases.</li> <li>Fixed: The compatibility with the Layout field was improved.</li> <li>Fixed: PayPal Commerce field was broken in the Block Editor in WordPress 5.2-5.4.</li></ul>", "<h4>1.0.0 (2022-08-09)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "*******"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-paypal-commerce.png"}, "wpforms-paypal-standard": {"title": "PayPal Standard Addon", "slug": "wpforms-paypal-standard", "url": "https://wpforms.com/addons/paypal-standard-addon/", "version": "1.10.0", "image": "https://wpforms.com/wp-content/uploads/2016/02/addon-icon-paypal.png", "excerpt": "The WPForms PayPal Standard addon allows you to connect your WordPress site with PayPal to easily collect payments, donations, and online orders.", "doc": "https://wpforms.com/docs/install-use-paypal-addon-wpforms/", "id": 155, "license": ["agency", "elite", "pro", "ultimate"], "category": ["payments"], "changelog": ["<h4>1.10.0 (2024-02-20)</h4><ul> <li>IMPORTANT: The WPForms PayPal Standard integration has been deprecated. We strongly recommend migrating to PayPal Commerce, which provides a seamless user experience and more features!</li> <li>Added: Compatibility with the upcoming WPForms 1.8.7.</li> <li>Changed: The minimum WPForms version supported is 1.8.7.</li></ul>", "<h4>1.9.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms PayPal Standard 1.9.0. Failure to do that will disable WPForms PayPal Standard functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms PayPal Standard 1.9.0. Failure to do that will disable WPForms PayPal Standard functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Fixed: Statuses of refunded payments were not correctly mirrored on the Payments Overview and Single Payment pages.</li> <li>Fixed: Error messages were duplicated when there was no payment field.</li></ul>", "<h4>1.8.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li></ul>", "<h4>1.7.1 (2023-06-09)</h4><ul> <li>Fixed: Blank email notifications were received when the \"Enable for PayPal Standard completed payments\" option was enabled and Conditional Logic for processing was configured.</li></ul>", "<h4>1.7.0 (2023-05-31)</h4><ul> <li>Added: Compatibility with WPForms 1.8.2.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li> <li>Fixed: Unselected payment fields were previously sent to PayPal.</li></ul>", "<h4>1.6.0 (2022-10-06)</h4><ul> <li>Added: If payments are enabled, a warning popup is now displayed when saving the form with no payment field.</li> <li>Changed: Minimum WPForms version supported is *******.</li></ul>", "<h4>1.5.0 (2022-07-20)</h4><ul> <li>IMPORTANT: Support for PHP 5.5 has been discontinued. If you are running PHP 5.5, you MUST upgrade PHP before installing the new WPForms PayPal Standard. Failure to do that will disable the WPForms PayPal Standard plugin.</li> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms PayPal Standard. Failure to do that will disable the new WPForms PayPal Standard functionality.</li> <li>Added: Possibility to save sandbox and production email separately and automatically use the correct one based on the selected mode.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Pass locale of a user to set a language for the checkout page.</li> <li>Changed: 'Note to seller' setting has been deprecated and removed.</li> <li>Changed: Show settings in the Form Builder only if they are enabled.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: Confirmation message was not displayed correctly in form preview mode.</li> <li>Fixed: The \"Invoice has already been paid\" error related to Invoice Number generation should no longer appear.</li> <li>Fixed: Payment status was not updating correctly in the Sandbox mode.</li> <li>Fixed: PayPal payments could be enabled without an email address.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li></ul>", "<h4>1.4.0 (2021-03-31)</h4><ul> <li>Added: Email Notifications option to limit to completed payments only.</li></ul>", "<h4>1.3.4 (2020-08-05)</h4><ul> <li>Fixed: Instruct PayPal to not ask for a shipping address when the \"Don't ask for an address\" option is checked.</li></ul>", "<h4>1.3.3 (2020-01-15)</h4><ul> <li>Fixed: Payment status remains 'Pending' despite PayPal payment completing successfully.</li></ul>", "<h4>1.3.2 (2020-01-09)</h4><ul> <li>Fixed: PHP Warning because of incorrect no shipping and no note processing.</li> <li>Changed: Selected choices of the 'Payment checkbox' field are now included in the PayPal payment title.</li></ul>", "<h4>1.3.1 (2019-09-17)</h4><ul> <li>Fixed: Paypal redirects with Ajax-enabled payment forms.</li></ul>", "<h4>1.3.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.2.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.1.2 (2018-11-12)</h4><ul> <li>Fixed: Processing empty payments.</li></ul>", "<h4>1.1.1 (2018-03-15)</h4><ul> <li>Changed: IPN callback URLs to new URLs PayPal recommends (previous ones are being deprecated/removed).</li> <li>Changed: Processing hook order (decreased priority) to avoid conflicts with the User Registration addon.</li></ul>", "<h4>1.1.0 (2017-09-27)</h4><ul> <li>Added: Donation payments include a description from payment items.</li> <li>Changed: All HTTP requests now validate target sites SSL certificates with WP bundled certificates (since 3.7).</li> <li>Fixed: Email validation issue by converting all email addresses to lowercase first.</li></ul>", "<h4>1.0.9 (2017-01-17)</h4><ul> <li>Added: New hook for completed transactions, <code>wpforms_paypal_standard_process_complete</code>.</li></ul>", "<h4>1.0.8 (2016-12-08)</h4><ul> <li>Added: Support for Dropdown Items payment field.</li></ul>", "<h4>1.0.7 (2016-08-25)</h4><ul> <li>Added: Expanded support for additional currencies.</li> <li>Changed: Removed setting to disable IPN verification.</li> <li>Changed: Improved IPN verification.</li> <li>Fixed: Localization issues/bugs.</li></ul>", "<h4>1.0.6 (2016-08-04)</h4><ul> <li>Changed: Multiple payment items now also include labels of selected choices in the item descriptions.</li> <li>Changed: PayPal BN code.</li></ul>", "<h4>1.0.5 (2016-07-07)</h4><ul> <li>Added: Conditional logic for payments.</li> <li>Changed: Improved error logging.</li></ul>", "<h4>1.0.4 (2016-06-23)</h4><ul> <li>Changed: Prevent plugin from running if WPForms Pro is not activated.</li></ul>", "<h4>1.0.3 (2016-03-28)</h4><ul> <li>Changed: IPN setting has been moved to the new \"Payments\" settings tab.</li></ul>", "<h4>1.0.2 (2016-03-16)</h4><ul> <li>Fixed: Issue with donation transaction types.</li></ul>", "<h4>1.0.1 (2016-03-16)</h4><ul> <li>Fixed: Issue posting to PayPal due to incorrect URL.</li></ul>", "<h4>1.0.0 (2016-03-11)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.8.7"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-paypal-standard.png"}, "wpforms-pdf": {"title": "PDF Addon", "slug": "wpforms-pdf", "url": "https://wpforms.com/addons/pdf-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/08/icon-pdf.png", "excerpt": "Use the WPForms PDF addon to save your email notification entries to a PDF file, complete with your logo and advanced customization options too. ", "doc": "https://wpforms.com/docs/pdf-addon/", "id": 3123247, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.0.0 (2025-08-12)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "*******"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": true}, "recommended": false, "featured": true, "new": true, "icon": "addon-icon-pdf.png"}, "wpforms-pipedrive": {"title": "Pipedrive Addon", "slug": "wpforms-pipedrive", "url": "https://wpforms.com/addons/pipedrive-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/05/icon-pipedrive.png", "excerpt": "The WPForms Pipedrive addon allows you to automate tasks through the Pipedrive CRM to nurture leads, discover insights, and more. ", "doc": "https://wpforms.com/docs/pipedrive-addon/", "id": 3092610, "license": ["agency", "elite", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.0.0 (2025-05-13)</h4><ul> <li>Initial release</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["crm"], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-pipedrive.png"}, "wpforms-post-submissions": {"title": "Post Submissions Addon", "slug": "wpforms-post-submissions", "url": "https://wpforms.com/addons/post-submissions-addon/", "version": "1.8.0", "image": "https://wpforms.com/wp-content/uploads/2016/10/addon-icon-post-submissions.png", "excerpt": "The WPForms Post Submissions addon makes it easy to have user-submitted content in WordPress. This front-end post submission form allow your users to submit blog posts without logging into the admin area.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-post-submissions-addon-in-wpforms/", "id": 11793, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.8.0 (2025-02-25)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: Compatibility with WPForms 1.9.4.</li></ul>", "<h4>1.7.0 (2024-08-06)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Compatibility with WPForms 1.9.0.</li></ul>", "<h4>1.6.0 (2024-05-01)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Post Submissions 1.6.0. Failure to do that will disable WPForms Post Submissions functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any outdated versions, you MUST upgrade WordPress before installing WPForms Post Submissions 1.6.0. Failure to do that will disable WPForms Post Submissions functionality.</li> <li>Changed: The minimum WPForms version supported is 1.8.4.</li> <li>Changed: The help tooltip was removed in the Form Builder for consistency.</li> <li>Fixed: Events were not created if the \"Enable the Gutenberg block editor interface for creating events\" option was enabled in the Events Calendar 6.3.5 plugin.</li></ul>", "<h4>1.5.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed:  Post featured Image field was prone to be set with incorrect settings.</li></ul>", "<h4>1.4.1 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.4.0 (2022-05-26)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Compatibility with the Rich Text field.</li> <li>Changed: Improved compatibility with jQuery 3.5 and no jQuery Migrate plugin.</li> <li>Changed: Show settings in the Form Builder only if they are enabled.</li> <li>Changed: Show a modal in the Form Builder about available additional options only once.</li> <li>Fixed: <code>0</code> values were not allowed to save as custom post meta.</li> <li>Fixed: Images added as Post Featured Images didn't have titles.</li> <li>Fixed: Events Calendar plugin compatibility: start/end times had incorrect timezone.</li></ul>", "<h4>1.3.2 (2020-12-17)</h4><ul> <li>Fixed: Modern file upload processing when files were saved to the custom post meta table.</li></ul>", "<h4>1.3.1 (2020-08-05)</h4><ul> <li>Fixed: Modern file upload is not compatible with the ACF File field when used in custom post meta.</li></ul>", "<h4>1.3.0 (2020-01-15)</h4><ul> <li>Added: Access Controls compatibility (WPForms 1.5.8).</li></ul>", "<h4>1.2.1 (2019-11-07)</h4><ul> <li>Fixed: Compatibility with modern file uploader (WPForms 1.5.6) for featured image.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>1.1.1 (2019-02-26)</h4><ul> <li>Fixed: Post submission featured image not showing up as \"Featured Image\" in WordPress post type.</li> <li>Fixed: Post submission featured image thumbnail not displayed in WordPress Media Library.</li> <li>Fixed: Post can be submitted without an author (now falls back to form author).</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.4 (2017-08-21)</h4><ul> <li>Changed: Template uses new `core` property so it displays with other core templates</li></ul>", "<h4>1.0.3 (2017-03-09)</h4><ul> <li>Added: Improved integration for storing The Events Calendar post meta</li></ul>", "<h4>1.0.2 (2017-02-23)</h4><ul> <li>Fixed: Capitalized letters not being allowed in custom post meta keys</li></ul>", "<h4>1.0.1 (2017-01-17)</h4><ul> <li>Fixed: Possible error if custom meta fields were setup but incomplete</li></ul>", "<h4>1.0.0 (2016-10-05)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.1"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-post-submissions.png"}, "wpforms-salesforce": {"title": "Salesforce Addon", "slug": "wpforms-salesforce", "url": "https://wpforms.com/addons/salesforce-addon/", "version": "1.4.0", "image": "https://wpforms.com/wp-content/uploads/2020/09/addon-icon-salesforce.png", "excerpt": "The WPForms Salesforce addon allows you to easily send your WordPress form contacts and leads to your Salesforce CRM account.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-salesforce-addon-with-wpforms/", "id": 1006060, "license": ["agency", "elite", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.4.0 (2024-08-13)</h4><ul> <li>Added: More descriptive error messages for Salesforce API.</li> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Compatibility with WPForms 1.9.0.</li> <li>Fixed: An error was displayed on the Salesforce panel in Builder if the Salesforce integration was removed on the Settings &gt; Integrations page.</li></ul>", "<h4>1.3.0 (2024-03-20)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Salesforce 1.3.0. Failure to do that will disable WPForms Salesforce functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Salesforce 1.3.0. Failure to do that will disable WPForms Salesforce functionality.</li> <li>Changed: The minimum WPForms version supported is 1.8.4.</li> <li>Changed: Improved PHP 8.2 support.</li> <li>Changed: Improved Salesforce error handling during connection creation.</li> <li>Fixed: There was a conflict with 3rd-party plugins that use the Guzzle library.</li> <li>Fixed: Various RTL problems in the admin dashboard.</li></ul>", "<h4>1.2.0 (2022-09-20)</h4><ul> <li>Added: Salesforce \"Currency\" custom field can now be used when mapping form fields.</li> <li>Fixed: Do not display errors and properly handle situations when no Salesforce account was selected but the form still saved and reloaded.</li></ul>", "<h4>1.1.0 (2022-05-26)</h4><ul> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Salesforce. Failure to do that will disable the new WPForms Salesforce functionality.</li> <li>Added: Compatibility with WPForms 1.6.7 and the updated Form Builder in 1.6.8.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li> <li>Fixed: Properly handle the situation when trying to change the template for the same form multiple times.</li> <li>Fixed: Send to Salesforce form submission data even when the \"Entry storage\" option is disabled in the Form Builder.</li> <li>Fixed: Various typos in the translatable strings reported by translators.</li></ul>", "<h4>1.0.3 (2020-12-24)</h4><ul> <li>Fixed: Incorrectly formatted mapped entry data values before sending them to Salesforce (<code>Cannot deserialize instance of *field_type*</code> error).</li></ul>", "<h4>1.0.2 (2020-12-10)</h4><ul> <li>Fixed: Access token not always refreshing when expired.</li> <li>Fixed: Entries not sending to Salesforce in some edge cases.</li></ul>", "<h4>1.0.1 (2020-09-30)</h4><ul> <li>Fixed: Incorrect plugin build.</li></ul>", "<h4>1.0.0 (2020-09-24)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["crm"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-salesforce.png"}, "wpforms-save-resume": {"title": "Save and Resume Addon", "slug": "wpforms-save-resume", "url": "https://wpforms.com/addons/save-and-resume-addon/", "version": "1.12.0", "image": "https://wpforms.com/wp-content/uploads/2021/10/addon-icon-save-resume.png", "excerpt": "The WPForms Save and Resume addon allows your visitors to save their progress while filling in your forms. When they're ready to continue, they can restore their entry with a click.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-save-and-resume-addon-with-wpforms/", "id": 1611341, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.12.0 (2024-12-11)</h4><ul> <li>Changed: The minimum WPForms version supported is *******.</li> <li>Fixed: Amount mismatch error in case the Single Item field price was adjusted.</li> <li>Fixed: Multiple partial entries were generated when old link was visited.</li></ul>", "<h4>1.11.1 (2024-06-11)</h4><ul> <li>Fixed: Custom form styles were not applied for the Save and Resume confirmation message in Elementor.</li> <li>Fixed: Address data from resumed link is now correctly restored if Address Autocomplete settings are enabled.</li> <li>Fixed: Save and Resume confirmation message was displayed on unrelated pages when the addon was activated.</li></ul>", "<h4>1.11.0 (2024-06-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Added: Save and Resume form can load signature preview when the form is resumed.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li> <li>Fixed: Compatibility with the Divi page builder.</li> <li>Fixed: Not all displayed elements were styled with Form Styles 2.0.</li> <li>Fixed: Users with permissions to only view forms could not submit the form on the Form Preview page.</li></ul>", "<h4>1.10.0 (2024-04-24)</h4><ul> <li>Added: Compatibility with the WPForms 1.8.8.</li> <li>Fixed: Form styles were not applied to the Save &amp; Resume messages.</li></ul>", "<h4>1.9.1 (2024-02-29)</h4><ul> <li>Fixed: Antispam token was passed as a simple form field, not protected from spam bots.</li></ul>", "<h4>1.9.0 (2024-02-20)</h4><ul> <li>Added: Compatibility with WPForms 1.8.7.</li> <li>Fixed: Save and Resume Later label was displayed in the builder preview area when there were no fields added.</li> <li>Fixed: The Form Builder settings screen had visual issues when an RTL language was used.</li></ul>", "<h4>1.8.0 (2024-01-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.6.</li> <li>Changed: Improve the logic of sending emails to increase performance.</li> <li>Changed: Minimum WPForms version supported is 1.8.6.</li> <li>Fixed: Improved expiration error handling if multiple forms with Save and Resume enabled are embedded on a page.</li> <li>Fixed: Form restore link could be missing from the email notification in rare cases.</li> <li>Fixed: Deprecation message while submitting non-AJAX forms with enabled Save and Resume.</li></ul>", "<h4>1.7.0 (2023-11-08)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Save and Resume 1.7.0. Failure to do that will disable WPForms Save and Resume functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Save and Resume 1.7.0. Failure to do that will disable WPForms Save and Resume functionality.</li> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li></ul>", "<h4>1.6.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: The form with saved data was displayed when visiting via expired link.</li></ul>", "<h4>1.5.0 (2023-03-15)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Fixed: Incorrect error message placement if multiple forms were embedded on a page.</li></ul>", "<h4>1.4.1 (2023-02-14)</h4><ul> <li>Added: WPForms 1.8.0 compatibility.</li> <li>Changed: Disable \"Resend Notifications\" link on the Entry page instead of hiding it.</li></ul>", "<h4>1.4.0 (2023-01-10)</h4><ul> <li>Added: Compatibility with the Lead Forms addon.</li> <li>Fixed: In the Firefox browser, Save and Resume link was displayed in the Form Builder even if the feature was not enabled.</li> <li>Fixed: Dropdown, Multiple Choice, and Checkbox fields were forcing their corresponding default values in resumed forms.</li> <li>Fixed: Form submission has expired for non-AJAX forms after submitting the form and refreshing the page.</li> <li>Fixed: The resume link could have been emailed before it was generated.</li> <li>Fixed: Save and Resume link was not displayed alongside the \"Next\" button of the new Page Break fields in the Form Builder.</li></ul>", "<h4>1.3.0 (2022-09-21)</h4><ul> <li>Changed: Minimum WPForms version is now *******.</li> <li>Fixed: Save and resume link representation was broken in the Block Editor on WordPress 5.2-5.4.</li></ul>", "<h4>1.2.0 (2022-07-14)</h4><ul> <li>Added: Display partial entry expiration in the \"Entry Details\" metabox when viewing entry details.</li> <li>Added: Allow copying saved entry link from the \"Entry Details\" metabox when viewing entry details.</li> <li>Changed: Partial entry is now deleted immediately after completing and submitting the form.</li> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Changed: Check GDPR settings before trying to use cookies.</li> <li>Changed: Partial entries do not rely on user cookies anymore.</li> <li>Changed: Improved compatibility with Twenty Twenty-Two theme and Full Site Editing (FSE).</li> <li>Fixed: Partial entry processing had no anti-spam protection.</li> <li>Fixed: Link was displayed in the Form Builder and Elementor widget preview even if the feature was not enabled.</li> <li>Fixed: Incorrect saved entry link was generated on setups with mixed HTTP/HTTPS.</li> <li>Fixed: Incorrect date was displayed in the resumed form.</li> <li>Fixed: Form field labels were underlined when Save and Resume was enabled.</li> <li>Fixed: PHP notice was generated when email notifications were sent.</li></ul>", "<h4>1.1.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li> <li>Added: Compatibility with WPForms 1.7.3 and search functionality on the Entries page.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li></ul>", "<h4>1.0.1 (2021-10-28)</h4><ul> <li>Fixed: Improved Paragraph field text formatting when restored from the saved partial entry.</li> <li>Fixed: Likert Scale field values haven't been restored when multiple responses are enabled.</li> <li>Fixed: Properly handle empty values for the Date / Time field and its Date Dropdown format.</li> <li>Fixed: Properly restore partial entries with dynamic and/or multiple choices in checkboxes and dropdowns fields.</li></ul>", "<h4>1.0.0 (2021-10-21)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "*******"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-save-resume.png"}, "wpforms-signatures": {"title": "Signature Addon", "slug": "wpforms-signatures", "url": "https://wpforms.com/addons/signature-addon/", "version": "1.12.0", "image": "https://wpforms.com/wp-content/uploads/2016/10/wordpress-signature-form-plugin.png", "excerpt": "The WPForms Signature addon makes it easy for users to sign your forms. This WordPress signature plugin will allow your users to sign contracts and other agreements with their mouse or touch screen.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-signature-addon-in-wpforms/", "id": 15383, "license": ["agency", "elite", "pro", "ultimate"], "category": [], "changelog": ["<h4>1.12.0 (2025-02-25)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li> <li>Fixed: The keyboard was shown on mobiles after clicking the Signature field.</li> <li>Fixed: The Field Size setting for the Signature field didn't work properly on the Builder and Form Preview screens.</li></ul>", "<h4>1.11.0 (2024-06-11)</h4><ul> <li>Added: Compatibility with WPForms 1.8.9.</li> <li>Added: Save and Resume form now can load signature preview when the form is resumed.</li> <li>Changed: The minimum WPForms version supported is 1.8.9.</li> <li>Fixed: Signature field's clear icon was not disabled in Divi Builder preview.</li> <li>Fixed: Signatures submitted with dark themes and white pen color were not visible after submission.</li></ul>", "<h4>1.10.0 (2024-04-24)</h4><ul> <li>Fixed: Signature field's clear icon had the wrong position when an RTL language was enabled.</li></ul>", "<h4>1.9.0 (2024-01-09)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.8.6.</li> <li>Fixed: Signature files were not deleted on form deletion.</li></ul>", "<h4>1.8.0 (2023-11-21)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Signatures 1.8.0. Failure to do that will disable WPForms Signatures functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Signatures 1.8.0. Failure to do that will disable WPForms Signatures functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li> <li>Changed: Signature Pad JS library is updated.</li> <li>Fixed: Compatibility with the Divi page builder.</li> <li>Fixed: The signature file was not deleted with the entry.</li> <li>Fixed: The Signature field had the wrong styles with Divi builder.</li></ul>", "<h4>1.7.0 (2023-03-15)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Fixed: Form could be submitted with an empty required Signature field if it was cleared before submission.</li></ul>", "<h4>1.6.0 (2022-09-20)</h4><ul> <li>Added: Compatibility with WPForms 1.7.7 and its new functionality.</li> <li>Fixed: Signature field was broken in the Block Editor in WordPress 5.2-5.4.</li></ul>", "<h4>1.5.0 (2022-05-26)</h4><ul> <li>Fixed: WordPress 6.0 compatibility: Signature field styling fixed inside the Full Site Editor.</li> <li>Fixed: Signature field was reset when a viewport size changed (Portrait/Landscape).</li></ul>", "<h4>1.4.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li> <li>Fixed: Signature canvas initialization when a form is used several times on a page.</li></ul>", "<h4>1.3.1 (2020-12-17)</h4><ul> <li>Fixed: Signature not displaying in the form builder correctly when using \"Small\" format.</li></ul>", "<h4>1.3.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Fixed: Signature canvas scaling issue on window resize.</li></ul>", "<h4>1.2.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.1.4 (2018-12-27)</h4><ul> <li>Changed: Signature field display priority in the form builder.</li></ul>", "<h4>1.1.3 (2018-08-28)</h4><ul> <li>Added: Support for Gutenberg editor with WPForms version 1.4.8.</li></ul>", "<h4>1.1.2 (2018-05-14)</h4><ul> <li>Fixed: Clear signature button display issues when using Base Styling only setting.</li></ul>", "<h4>1.1.1 (2018-04-15)</h4><ul> <li>Fixed: Required signatures fields not validating when inside pagebreaks.</li></ul>", "<h4>1.1.0 (2018-04-15)</h4><ul> <li>Added: Ink color field setting.</li> <li>Changed: Refactored addon and improved code.</li></ul>", "<h4>1.0.4 (2017-08-01)</h4><ul> <li>Fixed: Signature reset when scrolling on some mobile devices</li></ul>", "<h4>1.0.2 (2017-04-09)</h4><ul> <li>Fixed: CSS not correctly applying if using only Base styling</li></ul>", "<h4>1.0.1 (2016-11-17)</h4><ul> <li>Changed: Signature is stored and created in an image file, instead of a data URI</li> <li>Fixed: Signatures not working when hidden on load (conditional logic, pagination)</li> <li>Fixed: Signatures not displaying in Gmail/Google Apps</li></ul>", "<h4>1.0.0 (2016-11-10)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.4"}, "form_builder": {"category": []}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-signatures.png"}, "wpforms-slack": {"title": "<PERSON><PERSON><PERSON>", "slug": "wpforms-slack", "url": "https://wpforms.com/addons/slack-addon/", "version": "1.1.0", "image": "https://wpforms.com/wp-content/uploads/2024/10/icon-slack.png", "excerpt": "The WPForms Slack addon allows you to send direct messages, channel messages, and reminders to a Slack workspace.", "doc": "https://wpforms.com/docs/slack-addon/", "id": 2974081, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.1.0 (2025-08-05)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Added: Ability to post to private channels.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li> <li>Fixed: Made the \"Select Time\" description non-translatable as this feature works only in English.</li> <li>Fixed: PHP notice generated when trying to load deactivated users in the connection settings.</li> <li>Fixed: Limited number of channels and users were available for sending a message.</li> <li>Fixed: Archived channels were not excluded from the list.</li></ul>", "<h4>******* (2024-10-23)</h4><ul> <li>Fixed: Doc link in the Settings &gt; Integrations page.</li></ul>", "<h4>1.0.0 (2024-10-23)</h4><ul> <li>Added: Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-slack.png"}, "wpforms-square": {"title": "Square Pro Addon", "slug": "wpforms-square", "url": "https://wpforms.com/addons/square-addon/", "version": "2.0.0", "image": "https://wpforms.com/wp-content/uploads/2021/09/addon-icon-square.png", "excerpt": "The WPForms Square addon allows you to connect your WordPress site with Square to easily collect payments, donations, and online orders.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-square-addon-with-wpforms/", "id": 1548589, "license": ["agency", "elite", "pro", "ultimate"], "category": ["payments"], "changelog": ["<h4>2.0.0 (2025-04-22)</h4><ul> <li>IMPORTANT: A significant part of the addon has been moved to the WPForms plugin due to the new Square Payments feature.</li> <li>IMPORTANT: Addon requires the cURL extension to work. If you are running PHP without the cURL extension, you MUST enable it before upgrading this addon. Failure to do that will disable addon functionality.</li> <li>Changed: Minimum WPForms version supported is 1.9.5.</li></ul>", "<h4>1.14.0 (2025-03-05)</h4><ul> <li>Changed: Updated Square PHP library to v40.0.0.20250123.</li> <li>Changed: Updated payments processing to support 3-D Secure validation.</li> <li>Changed: Improved error messaging.</li></ul>", "<h4>1.13.0 (2024-12-11)</h4><ul> <li>Changed: The minimum WPForms version supported is *******.</li> <li>Changed: Updated Square PHP library to v38.2.0.20241017.</li> <li>Fixed: The Square field was displayed with incorrect width on mobile devices in the Divi and Gutenberg builders.</li> <li>Fixed: The Hide Sublabels setting for the Credit Card field didn't work on the Block Editor screen.</li></ul>", "<h4>1.12.0 (2024-08-21)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Changed: Notice in the WPForms &gt; Settings &gt; Payments admin page when a selected currency does not match the currency of connected Square business location.</li> <li>Changed: Updated Square PHP library to v37.1.1.20240717.</li> <li>Fixed: PHP deprecation message logged during Square payment submissions in some cases.</li> <li>Fixed: Compatibility with the Divi page builder.</li></ul>", "<h4>1.11.0 (2024-06-17)</h4><ul> <li>Changed: Improved rendering of Payment Fields according to W3C requirements.</li> <li>Fixed: Incorrect styles were applied for the Credit Card field when more than one form was embedded in a page.</li> <li>Fixed: Colors from the Conversational and Lead Forms were not applied to the Credit Card field.</li> <li>Fixed: In some cases, the Credit Card field was not displayed on the Elementor popup.</li> <li>Fixed: There were no styles for Credit Card field on the Elementor editor screen.</li></ul>", "<h4>1.10.0 (2024-04-23)</h4><ul> <li>Changed: Updated Square PHP library to v35.1.0.20240320.</li> <li>Fixed: There was a transparency applied for Credit card field preview in the admin pages.</li></ul>", "<h4>1.9.0 (2024-02-06)</h4><ul> <li>Added: Compatibility with the upcoming WPForms 1.8.7.</li> <li>Added: Compatibility with the new version of the Square PHP library.</li> <li>Changed: Updated Square PHP library to v34.0.1.20240118.</li> <li>Fixed: There was a conflict with 3rd-party plugins that use the Square PHP library.</li> <li>Fixed: Color from the Lead Forms was not applied to the Square credit card number.</li> <li>Fixed: There was no auto scrolling to the Square Credit Card field in case of an error.</li> <li>Fixed: The Square Credit Card field in an Elementor popup was added twice.</li></ul>", "<h4>1.8.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Square 1.8.0. Failure to do that will disable WPForms Square functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing Square 1.8.0. Failure to do that will disable WPForms Square functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.7.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Payment field was not displayed in the Elementor Builder.</li></ul>", "<h4>1.6.0 (2023-06-26)</h4><ul> <li>Compatibility with the upcoming WPForms Coupons addon release.</li></ul>", "<h4>1.5.0 (2023-06-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.2.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li> <li>Fixed: JavaScript error occurred when the user was asked to enter verification information for a payment form locked with the Form Locker addon.</li></ul>", "<h4>1.4.0 (2023-03-21)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Added: Notification if the form is being saved without enabling Square settings.</li> <li>Changed: Field preview in the Form Builder and Block Editor now matches the frontend look.</li> <li>Changed: Minimum WPForms version supported is 1.7.6.</li> <li>Fixed: On multi-page forms the field validation wasn't processed when changing pages.</li> <li>Fixed: Square Card field was displayed too high if used with conditional logic.</li> <li>Fixed: Previous page of the multi-page form could not be opened without filling the Credit Card Number.</li> <li>Fixed: Square field preview in the Block Editor was missing the Card subfield.</li></ul>", "<h4>1.3.0 (2022-08-10)</h4><ul> <li>Added: Compatibility with the WPForms PayPal Commerce addon.</li> <li>Changed: Minimum WPForms version supported is *******.</li></ul>", "<h4>1.2.0 (2022-07-20)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Fixed: The form couldn't be submitted if several configured payment gateways were executed according to the Conditional Logic rules.</li> <li>Fixed: The <code>wpforms_square_refresh_connection</code> Action Scheduler task was missing in some edge cases.</li></ul>", "<h4>1.1.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.7.3.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Fixed: Improved Performance of the Action Scheduler.</li></ul>", "<h4>1.0.0 (2021-09-14)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-square.png"}, "wpforms-stripe": {"title": "Stripe Pro Addon", "slug": "wpforms-stripe", "url": "https://wpforms.com/addons/stripe-addon/", "version": "3.5.0", "image": "https://wpforms.com/wp-content/uploads/2016/03/addon-icon-stripe-1.png", "excerpt": "The Stripe Pro addon grants access to conditional logic for payments and lower transaction fees. Use it alongside our core Stripe integration to easily collect payments, donations, and online orders.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-stripe-addon-with-wpforms/", "id": 1579, "license": ["agency", "elite", "pro", "ultimate"], "category": ["payments"], "changelog": ["<h4>3.5.0 (2025-04-22)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li></ul>", "<h4>3.4.0 (2024-04-23)</h4><ul> <li>Added: Compatibility with WPForms 1.8.8.</li> <li>Changed: Minimum WPForms version supported is 1.8.8.</li></ul>", "<h4>3.3.0 (2024-01-09)</h4><ul> <li>Added: Compatibility with WPForms 1.8.6.</li> <li>Changed: Minimum WPForms version supported is 1.8.6.</li></ul>", "<h4>3.2.0 (2023-11-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li></ul>", "<h4>3.1.0 (2023-09-26)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Stripe Pro 3.1.0. Failure to do that will disable WPForms Stripe Pro functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Stripe Pro 3.1.0. Failure to do that will disable WPForms Stripe Pro functionality.</li> <li>Added: Multiple Subscription Plans can now be configured on the Builder screen!</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Changed: The now deprecated <code>wpmu_new_blog</code> hook was replaced with the <code>wp_initialize_site</code> hook.</li> <li>Fixed: Stripe payment's country field was cropped off.</li></ul>", "<h4>3.0.1 (2023-06-13)</h4><ul> <li>Fixed: Stripe Credit Card field was preventing email notifications about form submission to be sent in certain scenarios.</li></ul>", "<h4>3.0.0 (2023-05-31)</h4><ul> <li>IMPORTANT: A significant part of the addon has been moved to the WPForms plugin due to the new Stripe Payments feature.</li> <li>Added: Compatibility with WPForms 1.8.2.</li> <li>Changed: Minimum WPForms version supported is 1.8.2.</li></ul>", "<h4>2.11.0 (2023-03-23)</h4><ul> <li>Added: Compatibility of Stripe Payment Element with the upcoming WPForms 1.8.1.</li> <li>Changed: Improved compatibility with Lead Forms addon.</li> <li>Fixed: Form was not scrolled to the CC Number, Date and CVV fields if validation for these fields failed.</li> <li>Fixed: Stripe Credit Card with Payment Elements worked for a first payment form only, even though there was more than one form on the same page.</li> <li>Fixed: Fields for verification code were not visible for Stripe Element with Link.</li> <li>Fixed: Placeholders and labels were overlapped for Stripe Element field after page refresh with multipage form.</li> <li>Fixed: Payment Element in Conversational Form was not set up properly on field activation.</li></ul>", "<h4>2.10.0 (2023-02-28)</h4><ul> <li>Added: Stripe Payment Element and Payment Links are supported now!</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: The previous page on the multi-page form could not be opened without filling in the Credit Card Number field.</li> <li>Fixed: Under certain circumstances, a PHP notice was raised when a payment form was submitted.</li></ul>", "<h4>2.9.0 (2023-02-02)</h4><ul> <li>IMPORTANT: Support of the legacy way to collect payments is deprecated and is no longer supported. Payments continue to be processed but will stop working in the future. Please upgrade your forms to the new Stripe Credit Card field to avoid disruptions or failed payments.</li> <li>Fixed: The Credit Card field didn't process validation when navigating between pages in a multi-page form.</li> <li>Fixed: Subscription amount could be incorrect for zero-decimal currencies.</li> <li>Fixed: \"Enable Stripe payments\" prompt didn't work correctly for a restored form.</li></ul>", "<h4>2.8.0 (2022-10-12)</h4><ul> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: The Stripe transaction URL for test mode had the wrong format on the Entry details page.</li></ul>", "<h4>2.7.0 (2022-10-04)</h4><ul> <li>Changed: Stripe Credit Card fields are not shown anymore if Stripe Payments are not enabled or Stripe Keys are not set.</li> <li>Changed: Stripe completed payment notifications are sent for completed payments only.</li> <li>Fixed: Entries Search by Payments Details did not work for Stripe payments for users who upgraded from v2.5.0 to v2.6.0 or v2.6.1.</li></ul>", "<h4>2.6.1 (2022-07-28)</h4><ul> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: Some older migrations were running in incorrect order effectively breaking the Stripe integration by switching users to an older Stripe API version.</li> <li>Fixed: Because of the migration bug, explained above, the Stripe Credit Card field was unavailable in the Builder and was ignored on the front end.</li></ul>", "<h4>2.6.0 (2022-07-20)</h4><ul> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Stripe. Failure to do that will disable the new WPForms Stripe functionality.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Show settings in the Form Builder only if they are enabled.</li> <li>Changed: Connect button UI improvements on Settings &gt; Payments admin page.</li> <li>Changed: Minimum WPForms version supported is 1.7.5.</li> <li>Fixed: Certain strings were not translatable.</li> <li>Fixed: Improved performance on the WPForms Settings pages.</li> <li>Fixed: The \"Name on Card\" placeholder value is not updated in the Form Builder preview.</li> <li>Fixed: Some fonts were not working properly with the Credit Card field.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li></ul>", "<h4>2.5.0 (2021-03-31)</h4><ul> <li>Added: New \"Switch Accounts\" link in the addon settings to change the Stripe account used.</li> <li>Added: Filter to apply custom styles to a Credit Card field.</li> <li>Added: Email Notifications option to limit to completed payments only.</li> <li>Changed: Full object is now logged instead of just a message in case of a Stripe error.</li> <li>Changed: Upgrade Stripe PHP SDK to 7.72.0.</li> <li>Fixed: Edge case when a subscription email is still being required despite changing the payment to single by conditional logic.</li> <li>Fixed: Invalid characters in <code>font-family</code> added by external CSS rules may break the Credit Card field.</li> <li>Fixed: Stripe form with active Captcha fails to submit after Stripe 3DSecure validation.</li></ul>", "<h4>2.4.3 (2020-12-17)</h4><ul> <li>Fixed: Stripe Live/Test modal appears when clicking on any checkbox in WPForms settings while using jQuery 3.0.</li></ul>", "<h4>2.4.2 (2020-09-02)</h4><ul> <li>Changed: Improved the error rate limiting by adding file-based rate-limiting log storage.</li></ul>", "<h4>2.4.1 (2020-08-06)</h4><ul> <li>Fixed: Card field can be mistakenly processed as hidden under some conditional logic configurations.</li></ul>", "<h4>2.4.0 (2020-08-05)</h4><ul> <li>Added: Stripe Elements locale can be set explicitly via the filter.</li> <li>Changed: Improved Stripe error handling during form processing.</li> <li>Fixed: Conditionally hidden Stripe field should not be processed on form submission.</li></ul>", "<h4>2.3.4 (2020-04-30)</h4><ul> <li>Fixed: In some edge cases Stripe payment goes through without creating a form entry.</li></ul>", "<h4>2.3.3 (2020-01-15)</h4><ul> <li>Fixed: Payment form entry details are not updated despite Stripe payment completing successfully.</li></ul>", "<h4>2.3.2 (2020-01-09)</h4><ul> <li>Changed: Improved form builder messaging when Stripe plugin settings have no been configured.</li> <li>Changed: Improved messaging on Stripe plugin settings.</li></ul>", "<h4>2.3.1 (2019-10-14)</h4><ul> <li>Fixed: PHP notice in WPForms settings if user has no Stripe forms.</li> <li>Fixed: Stripe Connect issues switching between Live/Test mode.</li></ul>", "<h4>2.3.0 (2019-09-18)</h4><ul> <li>Added: SCA support.</li> <li>Added: Stripe Elements.</li> <li>Added: Stripe Connect.</li> <li>Added: Rate limiting for failed payments.</li></ul>", "<h4>2.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li></ul>", "<h4>2.1.2 (2019-03-11)</h4><ul> <li>Changed: Stripe API key settings display order, to follow Stripe documentation.</li></ul>", "<h4>2.1.1 (2019-02-08)</h4><ul> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>2.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>2.0.2 (2018-11-27)</h4><ul> <li>Added: Include addon information when connecting to Stripe API.</li></ul>", "<h4>2.0.1 (2018-09-05)</h4><ul> <li>Fixed: Stripe API error</li></ul>", "<h4>2.0.0 (2018-09-04)</h4><ul> <li>Important: The addon structure has been improved and refactored. If you are extending the plugin by accessing the class instance, an update to your code will be required before upgrading (use `wpforms_stripe()`).</li> <li>Added: Recurring subscription payments! ??</li> <li>Changed: Improved metadata sent with charge details.</li> <li>Removed: <code>wpforms_stripe_instance</code> function and <code>WPForms_Stripe::instance()</code>.</li></ul>", "<h4>1.1.3 (2018-05-14)</h4><ul> <li>Changed: Enable Credit Card field when addon is activated; as of WPForms 1.4.6 the credit card field is now disabled/hidden unless explicitly enabled.</li></ul>", "<h4>1.1.2 (2018-04-05)</h4><ul> <li>Changed: Improved enforcement of Stripe processing with required credit card fields.</li></ul>", "<h4>1.1.1 (2017-08-24)</h4><ul> <li>Changed: Remove JS functionality adopted in core plugin</li></ul>", "<h4>1.1.0 (2017-08-01)</h4><ul> <li>Changed: Use settings API for WPForms v1.3.9.</li></ul>", "<h4>1.0.9 (2017-06-13)</h4><ul> <li>Changed: Improved performance when checking for credit card fields in the form builder</li></ul>", "<h4>1.0.8 (2017-03-30)</h4><ul> <li>Changed: Updated Stripe API PHP library</li> <li>Changed: Improved Stripe class instance accessibility</li></ul>", "<h4>1.0.7 (2017-01-17)</h4><ul> <li>Changed: Check for charge object before firing transaction completed hook</li></ul>", "<h4>1.0.6 (2016-12-08)</h4><ul> <li>Added: Support for Dropdown Items payment field</li> <li>Added: New action for completed transactions, <code>wpforms_stripe_process_complete</code></li> <li>Added: New filter stored credit card information, <code>wpforms_stripe_creditcard_value</code></li></ul>", "<h4>1.0.5 (2016-10-07)</h4><ul> <li>Fixed: Javascript processing method to avoid conflicts with core duplicate submit prevention feature</li></ul>", "<h4>1.0.4 (2016-08-25)</h4><ul> <li>Added: Expanded support for additional currencies</li> <li>Fixed: Localization issues/bugs</li></ul>", "<h4>1.0.3 (2016-07-07)</h4><ul> <li>Added: Conditional logic for payments</li> <li>Changed: Improved error logging</li></ul>", "<h4>1.0.2 (2016-06-23)</h4><ul> <li>Changed: Prevent plugin from running if WPForms Pro is not activated</li></ul>", "<h4>1.0.1 (2016-04-01)</h4><ul> <li>Fixed: PHP notices with some configurations</li></ul>", "<h4>1.0.0 (2016-03-28)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["payments"]}, "settings_integrations": {"category": ["payment"], "featured": true}, "recommended": true, "featured": true, "new": false, "icon": "addon-icon-stripe.png"}, "wpforms-surveys-polls": {"title": "Surveys and Polls Addon", "slug": "wpforms-surveys-polls", "url": "https://wpforms.com/addons/surveys-and-polls-addon/", "version": "1.15.1", "image": "https://wpforms.com/wp-content/uploads/2018/02/addon-icons-surveys-polls.png", "excerpt": "The WPForms Survey Addon allows you to add interactive polls and survey forms to your WordPress site. It comes with best-in-class reporting to help you make data-driven decisions.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-surveys-and-polls-addon/", "id": 148223, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.15.1 (2025-03-17)</h4><ul> <li>Fixed: A JS error was preventing Calculating Field Results from being viewed.</li></ul>", "<h4>1.15.0 (2025-02-25)</h4><ul> <li>IMPORTANT: Support for PHP 7.0 has been discontinued. If you are running PHP 7.0, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.4.</li> <li>Changed: Updated <code>Chart.js</code> library to v4.4.4.</li> <li>Fixed: The compatibility with the Layout field was improved.</li> <li>Fixed: Resolved W3C errors and warnings reported for the Likert Scale and Net Promoter Score fields.</li> <li>Fixed: A negative value in S&amp;P results on Entries Details page occurred when some entries were added to Spam.</li> <li>Fixed: There were warnings in the debug log during the form submission in some cases.</li></ul>", "<h4>1.14.0 (2024-08-06)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Compatibility with WPForms 1.9.0.</li></ul>", "<h4>1.13.0 (2024-04-24)</h4><ul> <li>Changed: Improved field layout on the frontend for better user experience on mobile devices.</li> <li>Changed: Bring the frontend markup of the form more in line with the W3C standards to reduce validator errors.</li> <li>Fixed: Net Promoter Score field labels were not editable.</li> <li>Fixed: Error state of the Net Promoter Score field was improved in Classic frontend mode.</li> <li>Fixed: Various RTL problems in the admin dashboard, form builder and a form preview page.</li></ul>", "<h4>1.12.0 (2023-11-21)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Surveys and Polls 1.12.0. Failure to do that will disable WPForms Surveys and Polls functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Surveys and Polls 1.12.0. Failure to do that will disable WPForms Surveys and Polls functionality.</li> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li> <li>Changed: The help tooltip was removed in the Form Builder for consistency.</li></ul>", "<h4>1.11.0 (2023-03-27)</h4><ul> <li>Added: Compatibility with the upcoming WPForms v1.8.1 release.</li> <li>Added: Several new filters were added for developers to modify the output of survey results on the front end.</li> <li>Changed: Improved the way various UI elements handle longer text in different languages.</li> <li>Fixed: Poll results had the same ID when there was more than one field.</li> <li>Fixed: The Likert Scale field values used new lines inconsistently on the Entry view page.</li> <li>Fixed: The Survey Results page displayed an unstyled error message instead of an error page when the form contained no entries.</li> <li>Fixed: The Likert Scale field with multiple responses per row was displaying incorrect values when editing an entry.</li> <li>Fixed: Cache was not cleared for all fields that support Survey Reporting after editing or deleting entries.</li></ul>", "<h4>1.10.0 (2022-09-28)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.7.7.</li> <li>Fixed: Likert and Net Promoter fields were broken in Block Editor in WordPress 5.2-5.4.</li> <li>Fixed: The compatibility with the Layout field was improved.</li> <li>Fixed: The Likert field was displayed incorrectly on mobile devices.</li></ul>", "<h4>1.9.0 (2022-08-30)</h4><ul> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Changed: Improve formatting of Likert Scale entries on the Entries List and Single Entry pages.</li> <li>Fixed: Likert Scale field row/column labels are now updated in the Form Builder preview as you type.</li> <li>Fixed: Reduced code complexity and replaced improperly used variable.</li> <li>Fixed: Fallback value for the Likert Scale field wasn't populated on page refresh after a failed form submission.</li> <li>Fixed: Survey results were broken on mobile.</li> <li>Fixed: Poll results were not shown correctly when the \"Enable Poll Results\" option was enabled for dynamic choices for several fields: Dropdown, Checkbox, and Multiple Choice.</li></ul>", "<h4>1.8.0 (2022-05-26)</h4><ul> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Fixed: WordPress 6.0 compatibility: Likert Scale and Net Promoter Score fields styling fixed inside the Full Site Editor.</li> <li>Fixed: Improved compatibility with WordPress Multisite installations.</li> <li>Fixed: Survey results were shown even if a form was no longer available.</li></ul>", "<h4>1.7.0 (2022-03-16)</h4><ul> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Added: Compatibility with WPForms 1.7.3 and Form Revisions.</li> <li>Added: Compatibility with WPForms 1.7.3 and search functionality on the Entries page.</li> <li>Changed: Minimum WPForms version supported is 1.7.3.</li> <li>Fixed: Incorrect styling of Likert Scale field with long labels.</li></ul>", "<h4>1.6.4 (2021-03-31)</h4><ul> <li>Changed: Replaced <code>jQuery.ready()</code> function with recommended way since jQuery 3.0.</li> <li>Fixed: The \"Export Entries (CSV)\" link on the Survey Results page.</li></ul>", "<h4>1.6.3 (2020-12-17)</h4><ul> <li>Poll results not displaying correctly with AJAX forms.</li> <li>Form scrolls to the top when clicking on the Likert Scale field option with some themes.</li> <li>Poll results incorrectly calculate a select field with multiple selections enabled.</li></ul>", "<h4>1.6.2 (2020-08-05)</h4><ul> <li>Fixed: Survey report cache not always clearing when it should.</li></ul>", "<h4>1.6.1 (2020-04-16)</h4><ul> <li>Fixed: Compatibility check for WPForms v1.6.0.1.</li></ul>", "<h4>1.6.0 (2020-04-15)</h4><ul> <li>Added: Entry editing support for Net Promoter Score and Likert Scale fields.</li> <li>Fixed: Survey report image exports not containing white background color.</li></ul>", "<h4>1.5.1 (2020-03-03)</h4><ul> <li>Changed: Compatibility with a new version of Choices.js library in WPForms core plugin.</li> <li>Fixed: Abandoned form entries increase survey \"skipped\" count.</li></ul>", "<h4>1.5.0 (2020-01-09)</h4><ul> <li>Added: Support for Access Controls.</li> <li>Fixed: Improved messaging on Stripe plugin settings.</li> <li>Fixed: Properly display polls results votes count in a chart using `[wpforms_poll]` shortcode when there are thousands of replies.</li> <li>Fixed: Question numbering on single question print page.</li></ul>", "<h4>1.4.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Added: Display alert when entry storage is disabled and polls are enabled.</li></ul>", "<h4>1.3.2 (2019-02-26)</h4><ul> <li>Fixed: PHP notice when printing survey results.</li></ul>", "<h4>1.3.1 (2019-02-08)</h4><ul> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.3.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.2.2 (2018-12-27)</h4><ul> <li>Changed: Likert and NPS field display priority in the form builder.</li></ul>", "<h4>1.2.1 (2018-10-19)</h4><ul> <li>Fixed: Typos with NPS form templates.</li></ul>", "<h4>1.2.0 (2018-08-28)</h4><ul> <li>Added: Net Promoter Score survey form templates.</li></ul>", "<h4>1.1.0 (2018-06-07)</h4><ul> <li>Added: Net Promoter Score field and reporting.</li> <li>Changed: Minor styling adjustments to Likert to improve theme compatibility.</li> <li>Fixed: Survey report print preview issue hiding empty fields.</li> <li>Fixed: Not Recognizing false poll shortcode attribute values</li></ul>", "<h4>1.0.0 (2018-02-14)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.4"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": true, "new": false, "icon": "addon-icon-surveys-polls.png"}, "wpforms-twilio": {"title": "<PERSON><PERSON><PERSON>", "slug": "wpforms-twilio", "url": "https://wpforms.com/addons/twilio-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/01/icon-twilio.png", "excerpt": "The WPForms Twilio addon lets you easily send confirmations to customers via SMS or WhatsApp upon form submission.", "doc": "https://wpforms.com/docs/twilio-addon/", "id": 3034336, "license": ["agency", "elite", "plus", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.0.0 (2025-01-21)</h4><ul> <li>Added: Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.1", "wpforms": "1.9.3"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-twilio.png"}, "wpforms-user-journey": {"title": "User <PERSON>", "slug": "wpforms-user-journey", "url": "https://wpforms.com/addons/user-journey-addon/", "version": "1.4.0", "image": "https://wpforms.com/wp-content/uploads/2020/11/addon-icon-user-journey.png", "excerpt": "Discover the steps your visitors take before they submit your forms. Right in the WordPress dashboard, you can easily see the content that’s driving the most valuable form conversions.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-user-journey-addon-in-wpforms/", "id": 1071426, "license": ["agency", "elite", "pro", "ultimate"], "category": [], "changelog": ["<h4>1.4.0 (2024-12-03)</h4><ul> <li>Added: The new option on the Export entries page to export user journey data to CSV/XLS.</li> <li>Changed: The minimum WPForms version supported is 1.9.1.</li> <li>Fixed: Data was not saved if the site name contained an apostrophe.</li></ul>", "<h4>1.3.0 (2024-08-06)</h4><ul> <li>Changed: The minimum WPForms version supported is 1.9.0.</li> <li>Fixed: Compatibility with WPForms 1.9.0.</li></ul>", "<h4>1.2.0 (2023-11-08)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms User Journey 1.2.0. Failure to do that will disable WPForms User Journey functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms User Journey 1.2.0. Failure to do that will disable WPForms User Journey functionality</li> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li></ul>", "<h4>1.1.0 (2023-08-22)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Changed: Improved compatibility with PHP 8.1.</li> <li>Fixed: The user journey was not saved when a user visited a page with quote marks in the page title.</li></ul>", "<h4>1.0.6 (2022-10-20)</h4><ul> <li>Fixed: No user journey details were added to new entries after clean installation.</li></ul>", "<h4>1.0.5 (2022-10-04)</h4><ul> <li>Fixed: The <code>{entry_user_journey}</code> smart tag was not rendered in re-sent email notifications when this action was performed on the single Entry view page.</li> <li>Fixed: A fatal error occurred during the addon activation when the core plugin was inactive.</li></ul>", "<h4>1.0.4 (2022-09-29)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.7.7.</li> <li>Fixed: The new <code>{entry_user_journey}</code> smart tag wasn't displayed in a list of available smart tags.</li></ul>", "<h4>1.0.3 (2022-09-21)</h4><ul> <li>Added: User Journey smart tag for Confirmation message and Notification Email (HTML and plain text).</li> <li>Fixed: The addon was generating too big cookies that in certain cases resulted in site being non-operational.</li></ul>", "<h4>1.0.2 (2022-02-10)</h4><ul> <li>Changed: Improved compatibility with PHP 8.</li> <li>Fixed: Issue with JavaScript code on front-end in Internet Explorer 11.</li></ul>", "<h4>1.0.1 (2021-03-31)</h4><ul> <li>Fixed: Issue with JavaScript code for collecting data in cookies in Safari v14.</li></ul>", "<h4>1.0.0 (2020-11-12)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.1"}, "form_builder": {"category": []}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": true, "new": false, "icon": "addon-icon-user-journey.png"}, "wpforms-user-registration": {"title": "User Registration Addon", "slug": "wpforms-user-registration", "url": "https://wpforms.com/addons/user-registration-addon/", "version": "2.7.0", "image": "https://wpforms.com/wp-content/uploads/2016/05/addon-icon-user-registration-1.png", "excerpt": "The WPForms User Registration addon lets you create custom user registration, password reset, and login forms for your website.", "doc": "https://wpforms.com/docs/how-to-install-and-use-user-registration-addon-with-wpforms/", "id": 3280, "license": ["agency", "elite", "pro", "ultimate"], "category": ["settings"], "changelog": ["<h4>2.7.0 (2024-08-06)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.9.0.</li> <li>Changed: When a user follows the expired Reset Password link and submits the form, an email notification with a new link will be sent.</li> <li>Fixed: Compatibility with WPForms 1.9.0.</li></ul>", "<h4>2.6.0 (2024-03-18)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.7.</li> <li>Fixed: In some cases, email mapping validation in the Form Builder could throw a JavaScript error and prevent saving the form.</li> <li>Fixed: User information was missing if Smart Tags were processed in the background via cron.</li> <li>Fixed: Compatibility with Google Sheets addon.</li> <li>Fixed: PHP notice and incorrect password reset link in the email when using the \"User Password Reset Form\" template</li></ul>", "<h4>2.5.0 (2024-01-09)</h4><ul> <li>Changed: The minimum supported WPForms version is 1.8.6.</li> <li>Fixed: The encoded value of the Password field did not work with custom User Login Page.</li> <li>Fixed: The encoded value in the password did not work with the User Registration Form and Reset Password Form.</li></ul>", "<h4>2.4.0 (2023-11-08)</h4><ul> <li>Added: Compatibility with WPForms 1.8.5.</li> <li>Changed: Minimum WPForms version supported is 1.8.5.</li></ul>", "<h4>2.3.0 (2023-10-24)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms User Registration 2.3.0. Failure to do that will disable WPForms User Registration functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms User Registration 2.3.0. Failure to do that will disable WPForms User Registration functionality.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li> <li>Fixed: Email notifications controls in the Form Builder were misaligned.</li> <li>Fixed: Compatibility with our new Calculations addon.</li></ul>", "<h4>2.2.0 (2023-08-08)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Changed: The <code>{site_name}</code> smart tag has been moved to the main WPForms plugin.</li></ul>", "<h4>2.1.2 (2023-07-27)</h4><ul> <li>Compatibility with WPForms Access controls.</li></ul>", "<h4>2.1.1 (2023-07-03)</h4><ul> <li>Fixed: Username validation displayed a misleading error message when the username was automatically created from the email address.</li> <li>Compatibility with WPForms *******.</li></ul>", "<h4>2.1.0 (2022-08-29)</h4><ul> <li>Added: User data can now be modified before the user registration using the new <code>wpforms_user_registration_process_registration_get_data</code> filter.</li> <li>Changed: Validation function for a form ID when Smart Tags are processed.</li> <li>Changed: Treat empty post titles and term names in Dynamic Choices the way WordPress does.</li> <li>Changed: Minimum WPForms version supported is 1.7.6.</li> <li>Fixed: Registration form: Conditional Logic didn't work properly.</li> <li>Fixed: Activation link was missed in an email if Confirmation setting was used for Email field.</li></ul>", "<h4>2.0.0 (2021-12-09)</h4><ul> <li>Added: New Password Reset form template.</li> <li>Added: New smart tags.</li> <li>Added: Ability to hide a form if the user logged in.</li> <li>Added: Registration form: enable auto-login.</li> <li>Added: Registration form: editing for email notifications (subject and message body lines).</li> <li>Added: Registration form: user activation message when already activated.</li> <li>Added: Registration form: Conditional Logic support.</li> <li>Added: New option for site administrators to resend user activation email from the Users page.</li> <li>Added: Modern Email templates support.</li> <li>Added: Login Form template now has the option to enable \"remember me\" functionality.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Registration functionality is available on any form.</li> <li>Changed: Improved compatibility with WPForms Post Submissions and payment addons.</li> <li>Changed: Improved compatibility with jQuery 3.5 and no jQuery Migrate plugin.</li> <li>Changed: Improved translations by removing confusion if non-translatable placeholders are used.</li> <li>Fixed: Sending registration emails to user/admin.</li> <li>Fixed: Set an attachment author for files uploaded through a File Upload field.</li></ul>", "<h4>1.3.3 (2020-12-17)</h4><ul> <li>Changed: Enable antispam protection by default for all newly created forms using the User Login Form template.</li> <li>Fixed: Edge case where user account would be created if late form error was registered via custom code or third-party plugin.</li></ul>", "<h4>1.3.2 (2020-08-05)</h4><ul> <li>Fixed: New filter around user meta processing for advanced customizing.</li></ul>", "<h4>1.3.1 (2020-03-03)</h4><ul> <li>Fixed: Incompatibility with Post Submissions addon.</li></ul>", "<h4>1.3.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Fixed: Name field in Simple format does not pass data to user's profile.</li></ul>", "<h4>1.2.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Changed: Always show forms with Login template inside Gutenberg.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li> <li>Fixed: <code>nickname</code> user meta unable to be assigned after user registration..</li></ul>", "<h4>1.1.2 (2018-12-20)</h4><ul> <li>Fixed: Remove functions deprecated in PHP 7.2.</li></ul>", "<h4>1.1.1 (2018-11-12)</h4><ul> <li>Fixed: User account created when form contains errors.</li></ul>", "<h4>1.1.0 (2018-05-14)</h4><ul> <li>Fixed: Typo in user activation email.</li></ul>", "<h4>1.0.9 (2017-12-19)</h4><ul> <li>Fixed: Login form did not set proper cookie for https:// sites</li></ul>", "<h4>1.0.8 (2017-08-21)</h4><ul> <li>Changed: Template uses new `core` property so it displays with other core templates</li></ul>", "<h4>1.0.7 (2017-08-01)</h4><ul> <li>Fixed: Form builder alert containing misspelling</li></ul>", "<h4>1.0.6 (2017-02-23)</h4><ul> <li>Fixed: Capitalized letters not being allowed in custom user meta keys</li></ul>", "<h4>1.0.5 (2016-12-08)</h4><ul> <li>Changed: Emails sent to site admin/user on account creation now use HTML email template</li> <li>Changed: For new registration forms, the Username field is no longer required; email address used as fallback</li> <li>Changed: Additional user data is passed to <code>wpforms_user_registered</code> action</li></ul>", "<h4>1.0.4 (2016-10-24)</h4><ul> <li>Fixed: Setting for login form template that was not displaying</li></ul>", "<h4>1.0.3 (2016-10-07)</h4><ul> <li>Fixed: Misnamed function causing errors</li></ul>", "<h4>1.0.2 (2016-09-15)</h4><ul> <li>Added: Errors indicating username/email already exist are now filterable</li> <li>Changed: User registration and login form templates load order so it shows after default templates</li></ul>", "<h4>1.0.1 (2016-06-23)</h4><ul> <li>Added: New filters to allow for email customizations</li> <li>Changed: Prevent plugin from running if WPForms Pro is not activated</li></ul>", "<h4>1.0.0 (2016-05-19)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.9.0"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-user-registration.png"}, "wpforms-webhooks": {"title": "Webhooks Addon", "slug": "wpforms-webhooks", "url": "https://wpforms.com/addons/webhooks-addon/", "version": "1.4.0", "image": "https://wpforms.com/wp-content/uploads/2020/07/addon-icon-webhooks-1.png", "excerpt": "The WPForms Webhooks addon allows you to send form entry data to secondary tools and external services. No code required, and no need for a third party connector.", "doc": "https://wpforms.com/docs/how-to-install-and-use-the-webhooks-addon-with-wpforms/", "id": 901410, "license": ["agency", "elite", "ultimate"], "category": ["settings"], "changelog": ["<h4>1.4.0 (2024-08-01)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Webhooks 1.4.0. Failure to do that will disable WPForms Webhooks functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Webhooks 1.4.0. Failure to do that will disable WPForms Webhooks functionality.</li> <li>Changed: The minimum WPForms version supported is 1.8.4.</li> <li>Changed: Secure checkbox UI improvements on the Form Builder &gt; Settings &gt; Webhooks screen.</li> <li>Fixed: Currency symbols were not decoded in payment field values.</li></ul>", "<h4>1.3.0 (2023-08-15)</h4><ul> <li>Changed: Minimum WPForms version supported is 1.8.3.</li> <li>Fixed: Long labels of the webhooks configuration in the Form Builder were moving to the next row.</li></ul>", "<h4>1.2.1 (2023-07-03)</h4><ul> <li>Fixed: Compatibility with WPForms *******.</li></ul>", "<h4>1.2.0 (2022-05-26)</h4><ul> <li>IMPORTANT: Support for WordPress 5.1 has been discontinued. If you are running WordPress 5.1, you MUST upgrade WordPress before installing the new WPForms Webhooks. Failure to do that will disable the new WPForms Webhooks functionality.</li> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Changed: Extend hook arguments: make it possible to use formatted field values.</li></ul>", "<h4>1.1.0 (2021-10-28)</h4><ul> <li>Added: Process smart tags inside Request Headers and Body fields.</li> <li>Added: Compatibility with WPForms 1.6.8 and the updated Form Builder.</li> <li>Changed: Improved compatibility with jQuery 3.5 and no jQuery Migrate plugin.</li> <li>Changed: Improved compatibility with WordPress 5.9 and PHP 8.1.</li> <li>Fixed: Compatibility with WordPress Multisite installations.</li> <li>Fixed: Improve the way webhooks are disabled and displayed when disabled.</li> <li>Fixed: Incorrect width of setting fields in Safari.</li> <li>Fixed: Issue with saving settings.</li></ul>", "<h4>1.0.0 (2020-07-08)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.0", "wpforms": "1.8.4"}, "form_builder": {"category": ["settings"]}, "settings_integrations": {"category": [], "featured": false}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-webhooks.png"}, "wpforms-zapier": {"title": "<PERSON><PERSON><PERSON>", "slug": "wpforms-zapier", "url": "https://wpforms.com/addons/zapier-addon/", "version": "1.7.0", "image": "https://wpforms.com/wp-content/uploads/2016/08/icon-provider-zapier.png", "excerpt": "The WPForms Zapier addon allows you to connect your WordPress forms with over 7,000+ web apps. The integration possibilities here are just endless.", "doc": "https://wpforms.com/docs/how-to-install-and-use-zapier-addon-with-wpforms/", "id": 9141, "license": ["agency", "elite", "pro", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.7.0 (2025-08-13)</h4><ul> <li>IMPORTANT: Support for PHP 7.1 has been discontinued. If you are running PHP 7.1, you MUST upgrade PHP before installing this addon. Failure to do that will disable addon functionality.</li> <li>Changed: The minimum WPForms version supported is 1.9.5.</li> <li>Changed: Improved error and success messages.</li> <li>Fixed: When the connection was configured, the checkmark icon was missing.</li> <li>Fixed: No popular blocks were shown after the Form Builder reload.</li> <li>Fixed: Richtext field was sent to Zapier without HTML markup.</li></ul>", "<h4>1.6.0 (2024-04-16)</h4><ul> <li>IMPORTANT: Support for PHP 5.6 has been discontinued. If you are running PHP 5.6, you MUST upgrade PHP before installing WPForms Zapier 1.6.0. Failure to do that will disable WPForms Zapier functionality.</li> <li>IMPORTANT: Support for WordPress 5.4 and below has been discontinued. If you are running any of those outdated versions, you MUST upgrade WordPress before installing WPForms Zapier 1.6.0. Failure to do that will disable WPForms Zapier functionality.</li> <li>Added: Compatibility with the upcoming WPForms 1.8.8.</li> <li>Changed: Minimum WPForms version supported is 1.8.4.</li></ul>", "<h4>1.5.0 (2023-04-05)</h4><ul> <li>Added: The Site URL needed for Zapier configuration is now displayed inside the Form Builder and on the Settings &gt; Integrations page.</li> <li>Changed: More addon strings are now translatable.</li> <li>Changed: Zapier external assets are now lazy-loaded on demand, only when needed inside the Form Builder.</li> <li>Changed: The Edit Zap link is removed.</li> <li>Fixed: Texts in various places were rephrased and typos were fixed.</li></ul>", "<h4>1.4.0 (2022-08-29)</h4><ul> <li>Changed: Minimum WPForms version supported is *******.</li> <li>Changed: Updated the Zapier logo.</li> <li>Fixed: Incorrect URL to the documentation article was used on the Settings &gt; Integrations page.</li> <li>Fixed: Multi-select dropdown field options were not comma separated.</li> <li>Fixed: '0' values were not processed correctly.</li> <li>Fixed: Files were not properly uploaded to Zapier when more than 1 file was uploaded using a modern style file upload field.</li></ul>", "<h4>1.3.0 (2020-12-11)</h4><ul> <li>Added: Popular Zap templates inside the form builder Zapier settings area.</li></ul>", "<h4>1.2.0 (2019-07-23)</h4><ul> <li>Added: Complete translations for French and Portuguese (Brazilian).</li> <li>Fixed: Incorrect checkbox value is passed to Zapier with \"Show values\" option enabled.</li></ul>", "<h4>1.1.0 (2019-02-06)</h4><ul> <li>Added: Complete translations for Spanish, Italian, Japanese, and German.</li> <li>Fixed: Typos, grammar, and other i18n related issues.</li></ul>", "<h4>1.0.6 (2018-01-07)</h4><ul> <li>Fixed: Entry ID not correctly passing to Zaps.</li> <li>Fixed: Empty Checkbox field choice labels causing PHP error.</li></ul>", "<h4>1.0.5 (2017-10-25)</h4><ul> <li>Fixed: \"Live\" Zap status setting not updating correctly</li> <li>Fixed: \"Edit this Zap\" link incorrect/broken</li></ul>", "<h4>1.0.4 (2017-09-27)</h4><ul> <li>Changed: All HTTP requests now validate target sites SSL certificates with WP bundled certificates (since 3.7)</li> <li>Fixed: Properly decode forms name before sending them to Zapier to display</li> <li>Fixed: Update function incorrectly named which could cause conflicts with MailChimp addon</li> <li>Fixed: Visual display of connected forms on Settings &gt; Integrations page</li></ul>", "<h4>1.0.3 (2017-03-09)</h4><ul> <li>Changed: Adjust display order so that the providers show in alphabetical order</li></ul>", "<h4>1.0.2 (2016-12-08)</h4><ul> <li>Added: Support for Dropdown Items payment field</li></ul>", "<h4>1.0.1 (2016-09-02)</h4><ul> <li>Fixed: Error with Zapier custom field polling</li></ul>", "<h4>1.0.0 (2016-08-30)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.5"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["crm", "email-marketing"], "featured": true}, "recommended": false, "featured": false, "new": false, "icon": "addon-icon-zapier.png"}, "wpforms-zoho-crm": {"title": "Zoho CRM Addon", "slug": "wpforms-zoho-crm", "url": "https://wpforms.com/addons/zoho-crm-addon/", "version": "1.0.0", "image": "https://wpforms.com/wp-content/uploads/2025/07/icon-zoho-crm.png", "excerpt": "Create and update your Zoho CRM contacts, leads, and more, automatically from your WPForms submissions. Filter with field mapping and conditional logic, too. ", "doc": "https://wpforms.com/docs/zoho-crm-addon/", "id": 3114724, "license": ["agency", "elite", "ultimate"], "category": ["providers"], "changelog": ["<h4>1.0.0 (2025-07-16)</h4><ul> <li>Initial release.</li></ul>"], "required_versions": {"wp": "5.5", "php": "7.2", "wpforms": "1.9.6.2"}, "form_builder": {"category": ["providers"]}, "settings_integrations": {"category": ["crm"], "featured": false}, "recommended": false, "featured": false, "new": true, "icon": "addon-icon-zoho-crm.png"}}