/*
################
* === Prodcut Item Style  ===
################
*/


ul.products,
.wc-block-grid__products:not(.slick-slider),
.products-block-post-template:is(.columns-1,.columns-2,.columns-3,.columns-4,.columns-5,.columns-6) {
    column-gap: 15px;
    row-gap: 15px;
    margin: 0;
    display: grid;
    padding: 0;
    list-style: none;
    align-items: flex-start;

    @media (min-width:768px) {
        column-gap: 28px;
        row-gap: 30px;
    }
}

.wc-block-grid__products:not(.slick-slider),
.wp-block-product-template {
    display: grid !important;
}

.wc-block-grid__products:not(.slick-slider) {
    margin: 0;
}

.wc-block-grid__product {
    max-width: 100% !important;
    width: 100%;
}

.wc-block-grid__product,
.wc-block-grid,
.products-block-post-template .product .has-text-align-center {
    text-align: left;
}

.products-block-post-template {
    .product {
        .has-medium-font-size,
        .has-small-font-size {
            font-size: 14px !important;
    
            @media (min-width:768px) {
                font-size: 16px !important;

                &.wp-element-button {
                    font-size: 15px !important;
                }
            }
        }

        > .wc-block-components-product-button {
            display: none;
        }
    
        .wc-block-grid__product-rating .wc-block-grid__product-rating__stars {
            margin-bottom: 0;
        }
    
        .wp-block-post-title a,
        .taxonomy-product_cat {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;  
            overflow: hidden;
        }
    
        .wp-block-post-title,
        .wp-block-woocommerce-product-price {
            font-weight: 500;
        }

        .wp-block-woocommerce-product-price {
            color: var(--wp--preset--color--secondary);
            margin-bottom: 12px;
        }
    
        .wp-block-post-title {
            margin-bottom: 11px;
            line-height: 1.2;

            &:hover {
                text-decoration: underline;
                text-underline-offset: 2px;
            }
        }
    }
}

.wc-block-grid__product-title {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;

    @media (min-width:768px) {
        font-size: 16px;
    }
}

.wc-block-grid .wc-block-grid__products,
ul.products,
.products-block-post-template:is(.columns-3,.columns-4,.columns-5,.columns-6),
.wp-block-woocommerce-product-template:is(.columns-3,.columns-4,.columns-5,.columns-6) {
    grid-template-columns: repeat(2,minmax(0,1fr));
}

@media(max-width: 600px) {
    .products-block-post-template.wp-block-post-template-is-layout-grid {
        grid-template-columns: repeat(2,minmax(0,1fr));
    }
}

.wc-block-grid.has-1-columns .wc-block-grid__products,
ul.products.columns-1,
.products-block-post-template.columns-1 {
    grid-template-columns: repeat(1,minmax(0,1fr));
}

.wc-block-grid.has-2-columns .wc-block-grid__products,
ul.products.columns-2,
.products-block-post-template.columns-2 {
    grid-template-columns: repeat(2,minmax(0,1fr));
}

@media (min-width: 600px) {
    .products-block-post-template.is-flex-container.is-flex-container:is(.columns-2,.columns-3,.columns-4,.columns-5,.columns-6)>li {
        width: 100%;
    }
}

@media (min-width: 768px) {
    .wc-block-grid.has-3-columns .wc-block-grid__products,
    ul.products.columns-3,
    .products-block-post-template.columns-3 {
        grid-template-columns: repeat(3,minmax(0,1fr));
    }

    .wc-block-grid .wc-block-grid__products,
    ul.products,
    .products-block-post-template:is(.columns-4,.columns-5,.columns-6) {
        grid-template-columns: repeat(3,minmax(0,1fr));
    }
}

@media (min-width: 1200px) {
    .wc-block-grid.has-4-columns .wc-block-grid__products,
    ul.products.columns-4,
    .products-block-post-template.columns-4 {
        grid-template-columns: repeat(4,minmax(0,1fr));
    }

    .wc-block-grid.has-5-columns .wc-block-grid__products,
    ul.products.columns-5,
    .products-block-post-template.columns-5 {
        grid-template-columns: repeat(5,minmax(0,1fr));
    }

    .wc-block-grid.has-6-columns .wc-block-grid__products,
    ul.products.columns-6,
    .products-block-post-template.columns-6 {
        grid-template-columns: repeat(6,minmax(0,1fr));
    }
}

// Product Items
.wc-block-grid__product-add-to-cart {
    &.wp-block-button {
        .wp-block-button__link {
            &.added {
                &:after {
                    content:"\F26A";
                }
            }

            &.loading:after,
            &.added:after {
                font-family: "bootstrap-icons";
                font-size: 18px;
                line-height: 1;
            }

            &.loading:after {
                content: "\F116";
            }
        }
    }
}

.wp-block-button.wc-block-components-product-button .wp-block-button__link.loading::after {
    font-family: "bootstrap-icons";
    font-size: 18px;
    line-height: 1;
    content: "\F116";
}

.products-block-post-template,
.wc-block-grid__products,
.wp-block-woocommerce-cart-cross-sells-block {
    .wc-block-grid__product-image,
    .wp-block-cart-cross-sells-product__product-image {
        display: grid;
        transition: 0.3s ease-in-out;

        > * {
            grid-column: 1 / -1;
            grid-row: 1 / -1;
        }

        > .wp-block-button {
            margin: auto auto 10px;
            width: calc(100% - 20px);
            opacity: 0;
            visibility: hidden;
            transition: 0.3s ease-in-out;

            @media (max-width:1199px) {
                display: none;
            }

            .wp-element-button {
                gap: 6px;
                align-items: center;

                &::after {
                    content: " ";
                    display: inline-block;
                    width: 11px;
                    height: 11px;
                    flex-shrink: 0;
                    background-image: url('data:image/svg+xml,<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z" fill="%23131C19"/></svg>');
                }

                &:hover {
                    &::after {
                        background-image: url('data:image/svg+xml,<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z" fill="%23ffffff"/></svg>');
                    }
                }

                &:not(:hover) {
                    background-color: var(--wp--preset--color--base);
                    border-color: var(--wp--preset--color--base);
                }
            }
        }
    }

    .wp-block-post {
        .wc-block-grid__product-image {  
            > .wp-block-button {
                margin-bottom: 0;
            }
        }
    }

    .wp-block-button.wc-block-components-product-button .wc-block-components-product-button__button {
        line-height: 1.867;
    }

    .wp-block-post,
    .wc-block-grid__product,
    .cross-sells-product {
        &:is(:hover) {
            .wc-block-grid__product-image,
            .wp-block-cart-cross-sells-product__product-image {
                > .wp-block-button {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
    }
}

.wc-block-grid.has-4-columns:not(.alignwide):not(.alignfull) .wc-block-grid__product, 
.wc-block-grid.has-5-columns.alignfull .wc-block-grid__product {
    font-size: 16px;
}

.products-block-post-template {
    div[data-block-name="woocommerce/product-button"] {
        width: calc(100% - 40px);
        opacity: 0;
        visibility: hidden;
        align-items: stretch;

        @media (max-width:1199px) {
            display: none;
        }

        .wp-element-button {
            margin-bottom: 10px !important;

            &:not(:hover) {
                background-color: var(--wp--preset--color--base);
                border-color: var(--wp--preset--color--base);
            }
        }
    }

    div[data-block-name="woocommerce/product-button"] {
        button#added {
            display: none;

            ~ span {
                display: block;

                &,a {
                    width: 100%;
                }

                a {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 6px;
                }
            }
        }

        > span {
            margin-bottom: 10px;

            a[title="View cart"] {
                padding: 12.5px 16px;
                text-transform: uppercase;
                background-color: var(--wp--preset--color--base);
                text-align: center;
                display: block;

                &:is(:hover, :focus) {
                    background-color: var(--wp--preset--color--secondary);
                    color: var(--wp--preset--color--base);
                }

                &::after {
                    content: " ";
                    display: inline-block;
                    width: 11px;
                    height: 11px;
                    background-image: url('data:image/svg+xml,<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z" fill="%23131C19"/></svg>');
                }

                &:hover {
                    &::after {
                        background-image: url('data:image/svg+xml,<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.6725 0.691772H2.57656C2.46052 0.691772 2.34924 0.737866 2.2672 0.819913C2.18515 0.90196 2.13906 1.01324 2.13906 1.12927C2.13906 1.2453 2.18515 1.35658 2.2672 1.43863C2.34924 1.52068 2.46052 1.56677 2.57656 1.56677H9.81456L0.837493 10.5443C0.794097 10.5842 0.759225 10.6325 0.734973 10.6863C0.710721 10.74 0.697589 10.7982 0.696368 10.8571C0.695146 10.9161 0.705859 10.9747 0.727864 11.0294C0.749869 11.0841 0.78271 11.1338 0.824415 11.1755C0.866119 11.2172 0.915825 11.2501 0.970545 11.2721C1.02527 11.2941 1.08387 11.3048 1.14284 11.3036C1.2018 11.3024 1.25991 11.2892 1.31367 11.265C1.36744 11.2407 1.41574 11.2059 1.45568 11.1625L10.4332 2.18496V9.4234C10.4332 9.53943 10.4793 9.65071 10.5613 9.73276C10.6434 9.8148 10.7546 9.8609 10.8707 9.8609C10.9867 9.8609 11.098 9.8148 11.18 9.73276C11.2621 9.65071 11.3082 9.53943 11.3082 9.4234V1.3279C11.3079 1.15933 11.2409 0.997733 11.1218 0.878499C11.0026 0.759265 10.8411 0.69212 10.6725 0.691772Z" fill="%23ffffff"/></svg>');
                    }
                }
            }
        }
    }

    .wp-block-post {
        &:is(:hover) {
            div[data-block-name="woocommerce/product-button"] {
                position: relative;
                opacity: 1;
                visibility: visible;
            }
        }
    }
}

.wc-block-grid .wc-block-grid__product-onsale, 
.wc-block-grid__product-image .wc-block-grid__product-onsale {
    color: var(--wp--preset--color--base);
    border-radius: 0;
    border-width: 0;
    font-weight: 500;
    background-color: var(--wp--preset--color--ebony);
    right:10px;
    top: 10px;
    font-size: 12px;
    z-index: 1;
    font-family: var(--wp--preset--font-family--heading);
    padding: 6px 12px;
    line-height: 1.334;
}

.wc-block-grid__product-image {
    background-color: var(--wp--preset--color--bg-3);
}

.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-right, 
.wc-block-components-product-image .wc-block-components-product-sale-badge--align-right {
    right: 10px;
    top: 10px;
}

.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-left, 
.wc-block-components-product-image .wc-block-components-product-sale-badge--align-left {
    left: 10px;
    top: 10px;
    right: auto;
}

.wc-block-grid__products .wc-block-grid__product .wc-block-components-product-image .wc-block-components-product-sale-badge--align-center, 
.wc-block-components-product-image .wc-block-components-product-sale-badge--align-center {
    top: 10px;
    right: auto;
}

.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link,
.products-block-post-template .product .wp-element-button {
    width: 100%;
}

.slick-nav-group .wc-block-grid__product {
    display: flex !important;
}

.wc-block-grid__product {
    border-width: 0;
    display: flex;
    flex-direction: column;

    .wc-block-grid__product-price, 
    .wc-block-grid__product-rating {
        margin-left: 0;
    }

    .wc-block-grid__product-price {
        order: 1;
    }

    .wc-block-grid__product-title {
        margin-bottom:10px;
        font-family: var(--wp--preset--font-family--heading);

        &:hover {
            text-decoration: underline;
            text-underline-offset: 2px;
        }
    }

    > .wc-block-grid__product-add-to-cart {
        display: none;

        a:not(:hover) {
            background-color: var(--wp--preset--color--base);
        }
    }

    .wc-block-grid__product-rating {
        display: flex;

        .star-rating {
            margin-bottom: 0;
        }
    }

    .wc-block-grid__product-price {
        font-size: 16px;
        font-weight: 500;
        color: var(--wp--preset--color--secondary);
    }
}

.wc-block-grid__products {
    .wc-block-grid__product-add-to-cart {
        a.added {
            display: none;
        }

        a.added_to_cart {
            padding: 14.5px 16px;
            text-transform: uppercase;
            background-color: var(--wp--preset--color--base);
            text-align: center;
            display: block;

            &:is(:hover, :focus) {
                background-color: var(--wp--preset--color--contrast);
                color: var(--wp--preset--color--base);
            }
        }
    }
}

.wc-block-grid__products,
.wc-block-all-products,
.wp-block-post {
    .wc-block-grid__product-price {
        display: flex;
        gap: 5px;
        flex-direction: row-reverse;
        justify-content: flex-end;
        align-items: center;
    
        del {
            color: var(--wp--preset--color--contrast);
            font-size: 12px;
            font-weight: 400;
        }
    }

    .wp-block-button .wc-block-components-product-button__button:not(:hover),
    .wc-block-grid__product-add-to-cart a:not(:hover) {
        color: var(--wp--preset--color--secondary);
    }
}

.wc-block-grid__products,
.products-block-post-template {
    .wc-block-grid__product-rating,
    .wc-block-components-product-rating {
        margin-bottom: 8px;
    }

    .woocommerce-Price-amount {
        font-family: var(--wp--preset--font-family--heading);
    }

    .wc-block-components-product-rating__container {
        display: flex;
    }
}

.wc-block-all-products {
    .wc-block-grid__product {
        > .wc-block-grid__product-add-to-cart {
            display: none;
        }
    }
}

.wc-block-components-product-price__value.is-discounted {
    margin-left: 0;
}

.wc-block-grid__product .wc-block-grid__product-image:not(.wc-block-components-product-image) {
    margin-bottom: clamp(16px, 2.237vw - 0.185rem, 27px);
}